import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Select, 
  Space, 
  Button, 
  Tag, 
  Tooltip,
  Alert,
  Spin
} from 'antd';
import { 
  ReloadOutlined, 
  DatabaseOutlined,
  ContainerOutlined 
} from '@ant-design/icons';
import { useClusterPods, useNotifications } from '@/hooks';
import type { Pod } from '@/types/api';

const { Option } = Select;

// Pod选择器属性
export interface PodSelectorProps {
  selectedNamespace?: string;
  selectedPod?: string;
  selectedContainer?: string;
  onNamespaceChange?: (namespace: string) => void;
  onPodChange?: (pod: string, podInfo?: Pod) => void;
  onContainerChange?: (container: string) => void;
  disabled?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Pod选择器组件
 * 
 * 功能特性：
 * - 命名空间选择
 * - Pod选择和筛选
 * - 容器选择
 * - 状态指示
 * - 自动刷新
 */
export const PodSelector: React.FC<PodSelectorProps> = ({
  selectedNamespace,
  selectedPod,
  selectedContainer,
  onNamespaceChange,
  onPodChange,
  onContainerChange,
  disabled = false,
  className,
  style,
}) => {
  const [refreshing, setRefreshing] = useState(false);
  const { showError } = useNotifications();

  // 获取Pod列表
  const { 
    data: pods = [], 
    isLoading, 
    error,
    refetch 
  } = useClusterPods({
    refetchInterval: 30000, // 30秒自动刷新
  });

  // 获取命名空间列表
  const namespaces = Array.from(new Set(pods.map(pod => pod.namespace))).sort();

  // 根据选中的命名空间筛选Pod
  const filteredPods = selectedNamespace 
    ? pods.filter(pod => pod.namespace === selectedNamespace)
    : pods;

  // 获取选中Pod的容器列表
  const selectedPodInfo = filteredPods.find(pod => pod.name === selectedPod);
  const containers = selectedPodInfo?.containers || [];

  // 处理命名空间变化
  const handleNamespaceChange = (namespace: string) => {
    onNamespaceChange?.(namespace);
    // 清空Pod和容器选择
    onPodChange?.('');
    onContainerChange?.('');
  };

  // 处理Pod变化
  const handlePodChange = (podName: string) => {
    const podInfo = filteredPods.find(pod => pod.name === podName);
    onPodChange?.(podName, podInfo);
    // 清空容器选择
    onContainerChange?.('');
    
    // 如果Pod只有一个容器，自动选择
    if (podInfo?.containers.length === 1) {
      onContainerChange?.(podInfo.containers[0].name);
    }
  };

  // 手动刷新
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      showError('刷新失败', '无法获取最新的Pod列表');
    } finally {
      setRefreshing(false);
    }
  };

  // 渲染Pod状态标签
  const renderPodStatus = (pod: Pod) => {
    const statusColors = {
      'Running': 'green',
      'Pending': 'orange',
      'Failed': 'red',
      'Succeeded': 'blue',
      'Unknown': 'default',
    };
    
    return (
      <Tag color={statusColors[pod.status as keyof typeof statusColors] || 'default'} size="small">
        {pod.status}
      </Tag>
    );
  };

  // 错误处理
  if (error) {
    return (
      <Alert
        message="Pod列表加载失败"
        description={error.message || '无法获取Pod列表，请检查网络连接'}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={handleRefresh}>
            重试
          </Button>
        }
      />
    );
  }

  return (
    <Card 
      title="Pod选择器" 
      size="small"
      className={className}
      style={style}
      extra={
        <Button
          type="text"
          size="small"
          icon={<ReloadOutlined />}
          loading={refreshing || isLoading}
          onClick={handleRefresh}
          disabled={disabled}
        >
          刷新
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {/* 命名空间选择 */}
        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
            <DatabaseOutlined style={{ marginRight: '4px' }} />
            命名空间
          </label>
          <Select
            placeholder="选择命名空间"
            value={selectedNamespace}
            onChange={handleNamespaceChange}
            style={{ width: '100%' }}
            disabled={disabled || isLoading}
            loading={isLoading}
            showSearch
            filterOption={(input, option) =>
              (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
            }
          >
            {namespaces.map(namespace => (
              <Option key={namespace} value={namespace}>
                {namespace}
              </Option>
            ))}
          </Select>
        </div>

        {/* Pod选择 */}
        <div>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
            <ContainerOutlined style={{ marginRight: '4px' }} />
            Pod
          </label>
          <Select
            placeholder={selectedNamespace ? "选择Pod" : "请先选择命名空间"}
            value={selectedPod}
            onChange={handlePodChange}
            style={{ width: '100%' }}
            disabled={disabled || isLoading || !selectedNamespace}
            loading={isLoading}
            showSearch
            filterOption={(input, option) =>
              (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
            }
          >
            {filteredPods.map(pod => (
              <Option key={pod.name} value={pod.name}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span>{pod.name}</span>
                  {renderPodStatus(pod)}
                </div>
              </Option>
            ))}
          </Select>
          
          {selectedPodInfo && (
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              <Space>
                <span>节点: {selectedPodInfo.node}</span>
                <span>IP: {selectedPodInfo.ip}</span>
                <span>重启: {selectedPodInfo.restarts}次</span>
              </Space>
            </div>
          )}
        </div>

        {/* 容器选择 */}
        {containers.length > 0 && (
          <div>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              容器
            </label>
            <Select
              placeholder="选择容器"
              value={selectedContainer}
              onChange={onContainerChange}
              style={{ width: '100%' }}
              disabled={disabled || isLoading}
            >
              {containers.map(container => (
                <Option key={container.name} value={container.name}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{container.name}</span>
                    <Tag color="blue" size="small">
                      {container.ready ? '就绪' : '未就绪'}
                    </Tag>
                  </div>
                </Option>
              ))}
            </Select>
          </div>
        )}

        {/* 选择摘要 */}
        {selectedNamespace && selectedPod && selectedContainer && (
          <div style={{ 
            padding: '8px 12px', 
            backgroundColor: '#f6f8fa', 
            borderRadius: '6px',
            fontSize: '12px'
          }}>
            <strong>已选择:</strong> {selectedNamespace}/{selectedPod}/{selectedContainer}
          </div>
        )}
      </Space>
    </Card>
  );
};

export default PodSelector;
