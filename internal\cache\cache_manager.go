package cache

import (
	"sync"
	"time"
)

// CacheItem 缓存项
type CacheItem struct {
	Value      interface{}
	Expiration time.Time
}

// IsExpired 检查是否过期
func (item *CacheItem) IsExpired() bool {
	return time.Now().After(item.Expiration)
}

// CacheManager 缓存管理器
type CacheManager struct {
	items map[string]*CacheItem
	mutex sync.RWMutex
}

// NewCacheManager 创建缓存管理器
func NewCacheManager() *CacheManager {
	cm := &CacheManager{
		items: make(map[string]*CacheItem),
	}
	
	// 启动清理协程
	go cm.cleanup()
	
	return cm
}

// Set 设置缓存
func (cm *CacheManager) Set(key string, value interface{}, duration time.Duration) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	cm.items[key] = &CacheItem{
		Value:      value,
		Expiration: time.Now().Add(duration),
	}
}

// Get 获取缓存
func (cm *CacheManager) Get(key string) (interface{}, bool) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	item, exists := cm.items[key]
	if !exists {
		return nil, false
	}
	
	if item.IsExpired() {
		return nil, false
	}
	
	return item.Value, true
}

// Delete 删除缓存
func (cm *CacheManager) Delete(key string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	delete(cm.items, key)
}

// Clear 清空缓存
func (cm *CacheManager) Clear() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	cm.items = make(map[string]*CacheItem)
}

// cleanup 清理过期缓存
func (cm *CacheManager) cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		cm.mutex.Lock()
		for key, item := range cm.items {
			if item.IsExpired() {
				delete(cm.items, key)
			}
		}
		cm.mutex.Unlock()
	}
}

// GetOrSet 获取缓存，如果不存在则设置
func (cm *CacheManager) GetOrSet(key string, duration time.Duration, fn func() (interface{}, error)) (interface{}, error) {
	// 先尝试获取缓存
	if value, exists := cm.Get(key); exists {
		return value, nil
	}
	
	// 缓存不存在，执行函数获取值
	value, err := fn()
	if err != nil {
		return nil, err
	}
	
	// 设置缓存
	cm.Set(key, value, duration)
	return value, nil
}
