package etcd

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"
	"go.uber.org/zap"
	"k8s-helper/pkg/common"
)

// Client etcd SDK客户端封装
type Client struct {
	client *clientv3.Client
	config *Config
	logger *zap.Logger
}

// Config etcd连接配置
type Config struct {
	Endpoints []string      // etcd端点列表
	CertFile  string        // 客户端证书文件路径
	KeyFile   string        // 客户端私钥文件路径
	CAFile    string        // CA证书文件路径
	Timeout   time.Duration // 连接超时时间
}

// BackupResult 备份结果
type BackupResult struct {
	FilePath string        // 备份文件路径
	Size     int64         // 文件大小
	Duration time.Duration // 备份耗时
	Version  string        // etcd版本信息
}

// NewClient 创建新的etcd客户端
func NewClient(cfg *Config, logger *zap.Logger) (*Client, error) {
	if cfg == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	if logger == nil {
		// 如果没有提供logger，创建一个默认的
		logger, _ = zap.NewProduction()
	}

	// 验证必要的配置
	if len(cfg.Endpoints) == 0 {
		return nil, fmt.Errorf("至少需要一个etcd端点")
	}

	if cfg.Timeout == 0 {
		cfg.Timeout = common.DefaultConnectionTimeoutSeconds * time.Second // 默认连接超时
	}

	// 配置TLS
	tlsConfig, err := configureTLS(cfg)
	if err != nil {
		return nil, fmt.Errorf("配置TLS失败: %w", err)
	}

	// 创建clientv3配置
	clientCfg := clientv3.Config{
		Endpoints:   cfg.Endpoints,
		DialTimeout: cfg.Timeout,
		TLS:         tlsConfig,
	}

	// 创建客户端
	client, err := clientv3.New(clientCfg)
	if err != nil {
		return nil, fmt.Errorf("创建etcd客户端失败: %w", err)
	}

	logger.Info("etcd SDK客户端创建成功",
		zap.Strings("endpoints", cfg.Endpoints),
		zap.Duration("timeout", cfg.Timeout))

	return &Client{
		client: client,
		config: cfg,
		logger: logger,
	}, nil
}

// NewClientForOfflineOps 创建用于离线操作（恢复、验证）的客户端
// 这些操作不需要连接到etcd集群，只需要logger
func NewClientForOfflineOps(logger *zap.Logger) *Client {
	if logger == nil {
		// 如果没有提供logger，创建一个默认的
		logger, _ = zap.NewProduction()
	}

	return &Client{
		client: nil, // 离线操作不需要连接
		config: nil, // 离线操作不需要配置
		logger: logger,
	}
}

// Backup 执行备份操作
func (c *Client) Backup(ctx context.Context, outputPath string) (*BackupResult, error) {
	startTime := time.Now()

	c.logger.Info("开始使用SDK执行etcd备份",
		zap.String("output", outputPath),
		zap.Strings("endpoints", c.config.Endpoints))

	// 创建输出文件
	file, err := os.Create(outputPath)
	if err != nil {
		return nil, fmt.Errorf("创建备份文件失败: %w", err)
	}
	defer file.Close()

	// 执行快照备份
	resp, err := c.client.Snapshot(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取快照失败: %w", err)
	}
	defer resp.Close()

	// 写入文件
	written, err := io.Copy(file, resp)
	if err != nil {
		return nil, fmt.Errorf("写入快照文件失败: %w", err)
	}

	duration := time.Since(startTime)

	result := &BackupResult{
		FilePath: outputPath,
		Size:     written,
		Duration: duration,
		Version:  "", // etcd v3.5 不直接提供版本信息
	}

	c.logger.Info("SDK备份完成",
		zap.String("file", outputPath),
		zap.Int64("size", written),
		zap.Duration("duration", duration))

	return result, nil
}

// TestConnection 测试连接
func (c *Client) TestConnection(ctx context.Context) error {
	c.logger.Info("测试etcd连接")

	// 尝试获取集群状态
	_, err := c.client.Status(ctx, c.config.Endpoints[0])
	if err != nil {
		return fmt.Errorf("连接测试失败: %w", err)
	}

	c.logger.Info("etcd连接测试成功")
	return nil
}

// Close 关闭客户端
func (c *Client) Close() error {
	if c.client != nil {
		c.logger.Info("关闭etcd SDK客户端")
		return c.client.Close()
	}
	return nil
}

// configureTLS 配置TLS证书
func configureTLS(cfg *Config) (*tls.Config, error) {
	// 如果没有提供证书文件，返回nil（使用不安全连接）
	if cfg.CertFile == "" || cfg.KeyFile == "" || cfg.CAFile == "" {
		return nil, fmt.Errorf("TLS证书配置不完整，需要cert、key和ca文件")
	}

	// 加载客户端证书和私钥
	cert, err := tls.LoadX509KeyPair(cfg.CertFile, cfg.KeyFile)
	if err != nil {
		return nil, fmt.Errorf("加载证书密钥对失败: %w", err)
	}

	// 加载CA证书
	caCert, err := os.ReadFile(cfg.CAFile)
	if err != nil {
		return nil, fmt.Errorf("读取CA证书失败: %w", err)
	}

	caCertPool := x509.NewCertPool()
	if !caCertPool.AppendCertsFromPEM(caCert) {
		return nil, fmt.Errorf("解析CA证书失败")
	}

	return &tls.Config{
		Certificates: []tls.Certificate{cert},
		RootCAs:      caCertPool,
		ServerName:   "", // 可以根据需要设置
	}, nil
}

// ParseEndpoints 解析端点字符串
func ParseEndpoints(endpoints string) []string {
	if endpoints == "" {
		return []string{"https://127.0.0.1:2379"} // 默认端点
	}

	// 分割逗号分隔的端点
	parts := strings.Split(endpoints, ",")
	result := make([]string, 0, len(parts))

	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}

	return result
}

// ValidateConfig 验证配置
func ValidateConfig(cfg *Config) error {
	if cfg == nil {
		return NewSDKError("validate", "配置不能为空", ErrInvalidConfig)
	}

	if len(cfg.Endpoints) == 0 {
		return NewSDKError("validate", "至少需要一个etcd端点", ErrInvalidConfig)
	}

	// 验证证书文件是否存在
	if cfg.CertFile != "" {
		if _, err := os.Stat(cfg.CertFile); os.IsNotExist(err) {
			return NewSDKError("validate", fmt.Sprintf("证书文件不存在: %s", cfg.CertFile), ErrCertificateInvalid)
		}
	}

	if cfg.KeyFile != "" {
		if _, err := os.Stat(cfg.KeyFile); os.IsNotExist(err) {
			return NewSDKError("validate", fmt.Sprintf("私钥文件不存在: %s", cfg.KeyFile), ErrCertificateInvalid)
		}
	}

	if cfg.CAFile != "" {
		if _, err := os.Stat(cfg.CAFile); os.IsNotExist(err) {
			return NewSDKError("validate", fmt.Sprintf("CA证书文件不存在: %s", cfg.CAFile), ErrCertificateInvalid)
		}
	}

	return nil
}

// GetClusterInfo 获取集群信息
func (c *Client) GetClusterInfo(ctx context.Context) (map[string]interface{}, error) {
	c.logger.Info("获取etcd集群信息")

	info := make(map[string]interface{})

	// 获取第一个端点的状态
	if len(c.config.Endpoints) > 0 {
		status, err := c.client.Status(ctx, c.config.Endpoints[0])
		if err != nil {
			return nil, NewSDKError("status", "获取集群状态失败", err)
		}

		info["version"] = status.Version
		info["db_size"] = status.DbSize
		info["leader"] = status.Leader
		info["raft_index"] = status.RaftIndex
		info["raft_term"] = status.RaftTerm
	}

	// 获取集群成员信息
	members, err := c.client.MemberList(ctx)
	if err != nil {
		c.logger.Warn("获取集群成员信息失败", zap.Error(err))
	} else {
		info["member_count"] = len(members.Members)
		memberInfo := make([]map[string]interface{}, 0, len(members.Members))
		for _, member := range members.Members {
			memberInfo = append(memberInfo, map[string]interface{}{
				"id":          member.ID,
				"name":        member.Name,
				"peer_urls":   member.PeerURLs,
				"client_urls": member.ClientURLs,
			})
		}
		info["members"] = memberInfo
	}

	return info, nil
}

// Restore 执行恢复操作（简化接口）
func (c *Client) Restore(ctx context.Context, snapshotPath, dataDir, name string) (*RestoreResult, error) {
	// 生成默认的集群配置
	initialCluster, initialAdvertisePeerURLs := GenerateRestoreConfig(name, dataDir)

	opts := &RestoreOptions{
		SnapshotPath:             snapshotPath,
		DataDir:                  dataDir,
		Name:                     name,
		InitialCluster:           initialCluster,
		InitialAdvertisePeerURLs: initialAdvertisePeerURLs,
		Timeout:                  common.DefaultSDKTimeoutMinutes * time.Minute,
		SkipHashCheck:            common.DefaultSkipHashCheck,
		MarkCompacted:            common.DefaultMarkCompacted,
		RevisionBump:             0,
	}

	return c.RestoreWithOptions(ctx, opts)
}

// Verify 执行验证操作（简化接口）
func (c *Client) Verify(ctx context.Context, snapshotPath string) (*VerifyResult, error) {
	opts := &VerifyOptions{
		SnapshotPath: snapshotPath,
		Timeout:      30 * time.Second,
		Detailed:     false,
	}

	return c.VerifyWithOptions(ctx, opts)
}
