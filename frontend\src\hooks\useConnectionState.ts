import { useCallback, useEffect } from 'react';
import { useConnectionStore, selectApiStatus, selectWebSockets, selectConnectionStats, selectIsOnline, selectConnectionEvents } from '@/stores';
import type { WebSocketConnection, ConnectionStatus } from '@/stores';

/**
 * 连接状态管理Hook
 * 
 * 提供API连接、WebSocket连接状态的访问和操作方法
 */
export const useConnectionState = () => {
  // 状态选择器
  const apiStatus = useConnectionStore(selectApiStatus);
  const websockets = useConnectionStore(selectWebSockets);
  const stats = useConnectionStore(selectConnectionStats);
  const isOnline = useConnectionStore(selectIsOnline);
  const events = useConnectionStore(selectConnectionEvents);

  // 操作方法
  const checkApiConnection = useConnectionStore(state => state.checkApiConnection);
  const addWebSocketConnection = useConnectionStore(state => state.addWebSocketConnection);
  const updateWebSocketConnection = useConnectionStore(state => state.updateWebSocketConnection);
  const removeWebSocketConnection = useConnectionStore(state => state.removeWebSocketConnection);
  const setWebSocketStatus = useConnectionStore(state => state.setWebSocketStatus);

  return {
    // 状态
    apiStatus,
    websockets,
    stats,
    isOnline,
    events,
    
    // 操作
    checkApiConnection,
    addWebSocketConnection,
    updateWebSocketConnection,
    removeWebSocketConnection,
    setWebSocketStatus,
  };
};

/**
 * API连接状态Hook
 */
export const useApiConnection = () => {
  const apiStatus = useConnectionStore(selectApiStatus);
  const checkApiConnection = useConnectionStore(state => state.checkApiConnection);
  const setApiStatus = useConnectionStore(state => state.setApiStatus);

  // 定期检查API连接
  useEffect(() => {
    const interval = setInterval(() => {
      checkApiConnection();
    }, 30000); // 每30秒检查一次

    // 初始检查
    checkApiConnection();

    return () => clearInterval(interval);
  }, [checkApiConnection]);

  const forceCheck = useCallback(() => {
    checkApiConnection();
  }, [checkApiConnection]);

  return {
    apiStatus,
    isOnline: apiStatus.isOnline,
    lastCheck: apiStatus.lastCheck,
    responseTime: apiStatus.responseTime,
    error: apiStatus.error,
    forceCheck,
  };
};

/**
 * WebSocket连接管理Hook
 */
export const useWebSocketManager = () => {
  const websockets = useConnectionStore(selectWebSockets);
  const addWebSocketConnection = useConnectionStore(state => state.addWebSocketConnection);
  const updateWebSocketConnection = useConnectionStore(state => state.updateWebSocketConnection);
  const removeWebSocketConnection = useConnectionStore(state => state.removeWebSocketConnection);
  const setWebSocketStatus = useConnectionStore(state => state.setWebSocketStatus);
  const incrementReconnectAttempts = useConnectionStore(state => state.incrementReconnectAttempts);
  const resetReconnectAttempts = useConnectionStore(state => state.resetReconnectAttempts);

  // 创建WebSocket连接
  const createConnection = useCallback((
    type: WebSocketConnection['type'],
    url: string,
    options?: Partial<Pick<WebSocketConnection, 'maxReconnectAttempts' | 'reconnectInterval'>>
  ) => {
    return addWebSocketConnection({
      type,
      url,
      maxReconnectAttempts: options?.maxReconnectAttempts || 5,
      reconnectInterval: options?.reconnectInterval || 3000,
    });
  }, [addWebSocketConnection]);

  // 获取连接状态
  const getConnectionStatus = useCallback((id: string): ConnectionStatus | null => {
    const connection = websockets[id];
    return connection ? connection.status : null;
  }, [websockets]);

  // 获取活跃连接数
  const getActiveConnectionCount = useCallback(() => {
    return Object.values(websockets).filter(ws => ws.status === 'connected').length;
  }, [websockets]);

  // 获取指定类型的连接
  const getConnectionsByType = useCallback((type: WebSocketConnection['type']) => {
    return Object.values(websockets).filter(ws => ws.type === type);
  }, [websockets]);

  return {
    websockets,
    createConnection,
    updateWebSocketConnection,
    removeWebSocketConnection,
    setWebSocketStatus,
    incrementReconnectAttempts,
    resetReconnectAttempts,
    getConnectionStatus,
    getActiveConnectionCount,
    getConnectionsByType,
  };
};

/**
 * 网络状态Hook
 */
export const useNetworkStatus = () => {
  const isOnline = useConnectionStore(selectIsOnline);
  const setOnlineStatus = useConnectionStore(state => state.setOnlineStatus);

  // 监听网络状态变化
  useEffect(() => {
    const handleOnline = () => setOnlineStatus(true);
    const handleOffline = () => setOnlineStatus(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [setOnlineStatus]);

  return {
    isOnline,
    isOffline: !isOnline,
  };
};

/**
 * 连接统计Hook
 */
export const useConnectionStats = () => {
  const stats = useConnectionStore(selectConnectionStats);
  const updateStats = useConnectionStore(state => state.updateStats);

  // 计算连接成功率
  const getSuccessRate = useCallback(() => {
    const { totalConnections, failedConnections } = stats;
    if (totalConnections === 0) return 100;
    return ((totalConnections - failedConnections) / totalConnections) * 100;
  }, [stats]);

  // 计算平均响应时间（格式化）
  const getFormattedResponseTime = useCallback(() => {
    const { averageResponseTime } = stats;
    if (averageResponseTime < 1000) {
      return `${Math.round(averageResponseTime)}ms`;
    } else {
      return `${(averageResponseTime / 1000).toFixed(2)}s`;
    }
  }, [stats]);

  // 计算运行时间
  const getUptime = useCallback(() => {
    const uptimeMs = Date.now() - stats.uptime;
    const seconds = Math.floor(uptimeMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}天 ${hours % 24}小时`;
    if (hours > 0) return `${hours}小时 ${minutes % 60}分钟`;
    if (minutes > 0) return `${minutes}分钟 ${seconds % 60}秒`;
    return `${seconds}秒`;
  }, [stats]);

  return {
    stats,
    successRate: getSuccessRate(),
    formattedResponseTime: getFormattedResponseTime(),
    uptime: getUptime(),
    updateStats,
  };
};

/**
 * 连接事件Hook
 */
export const useConnectionEvents = () => {
  const events = useConnectionStore(selectConnectionEvents);
  const addConnectionEvent = useConnectionStore(state => state.addConnectionEvent);
  const clearConnectionEvents = useConnectionStore(state => state.clearConnectionEvents);

  // 获取最近的事件
  const getRecentEvents = useCallback((count: number = 10) => {
    return events.slice(0, count);
  }, [events]);

  // 获取指定类型的事件
  const getEventsByType = useCallback((type: string) => {
    return events.filter(event => event.type === type);
  }, [events]);

  // 获取错误事件
  const getErrorEvents = useCallback(() => {
    return events.filter(event => event.type === 'error');
  }, [events]);

  return {
    events,
    recentEvents: getRecentEvents(),
    errorEvents: getErrorEvents(),
    addConnectionEvent,
    clearConnectionEvents,
    getRecentEvents,
    getEventsByType,
    getErrorEvents,
  };
};

/**
 * 自动重连Hook
 */
export const useAutoReconnect = () => {
  const autoReconnect = useConnectionStore(state => state.autoReconnect);
  const setAutoReconnect = useConnectionStore(state => state.setAutoReconnect);
  const websockets = useConnectionStore(selectWebSockets);
  const setWebSocketStatus = useConnectionStore(state => state.setWebSocketStatus);
  const incrementReconnectAttempts = useConnectionStore(state => state.incrementReconnectAttempts);

  // 尝试重连指定的WebSocket
  const attemptReconnect = useCallback((connectionId: string) => {
    const connection = websockets[connectionId];
    if (!connection || !autoReconnect) return;

    if (connection.reconnectAttempts >= connection.maxReconnectAttempts) {
      console.log(`WebSocket ${connectionId} 达到最大重连次数`);
      return;
    }

    setWebSocketStatus(connectionId, 'reconnecting');
    incrementReconnectAttempts(connectionId);

    // 这里应该实现实际的WebSocket重连逻辑
    setTimeout(() => {
      // 模拟重连结果
      const success = Math.random() > 0.3; // 70%成功率
      setWebSocketStatus(connectionId, success ? 'connected' : 'error', 
        success ? undefined : '重连失败');
    }, connection.reconnectInterval);
  }, [websockets, autoReconnect, setWebSocketStatus, incrementReconnectAttempts]);

  return {
    autoReconnect,
    setAutoReconnect,
    attemptReconnect,
  };
};

export default useConnectionState;
