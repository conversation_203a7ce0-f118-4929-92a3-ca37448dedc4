import React, { useState, useRef, useCallback } from 'react';
import { Card, Button, Space, Select, Tooltip, message } from 'antd';
import { 
  SaveOutlined, 
  ReloadOutlined, 
  FullscreenOutlined,
  FullscreenExitOutlined,
  CopyOutlined,
  CheckOutlined,
  SettingOutlined
} from '@ant-design/icons';
import Editor from '@monaco-editor/react';
import * as yaml from 'js-yaml';

const { Option } = Select;

// 代码编辑器属性
export interface CodeEditorProps {
  title?: string;
  value: string;
  language?: 'yaml' | 'json' | 'javascript' | 'typescript' | 'shell';
  height?: number;
  readOnly?: boolean;
  loading?: boolean;
  onChange?: (value: string) => void;
  onSave?: (value: string) => void;
  onValidate?: (value: string) => { valid: boolean; errors?: string[] };
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 代码编辑器组件
 * 
 * 功能特性：
 * - Monaco Editor集成
 * - 语法高亮和验证
 * - 全屏编辑模式
 * - 代码格式化
 * - 复制和保存功能
 */
export const CodeEditor: React.FC<CodeEditorProps> = ({
  title = '代码编辑器',
  value,
  language = 'yaml',
  height = 400,
  readOnly = false,
  loading = false,
  onChange,
  onSave,
  onValidate,
  className,
  style,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentValue, setCurrentValue] = useState(value);
  const [hasChanges, setHasChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  
  const editorRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 处理编辑器挂载
  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;
    
    // 设置编辑器选项
    editor.updateOptions({
      fontSize: 14,
      lineNumbers: 'on',
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      automaticLayout: true,
    });

    // 添加保存快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });
  };

  // 处理值变化
  const handleValueChange = useCallback((newValue: string | undefined) => {
    const val = newValue || '';
    setCurrentValue(val);
    setHasChanges(val !== value);
    
    // 验证代码
    if (onValidate) {
      const validation = onValidate(val);
      setValidationErrors(validation.errors || []);
    } else {
      // 默认验证
      validateCode(val);
    }
    
    onChange?.(val);
  }, [value, onChange, onValidate]);

  // 默认代码验证
  const validateCode = (code: string) => {
    const errors: string[] = [];
    
    if (language === 'yaml') {
      try {
        yaml.load(code);
      } catch (error) {
        errors.push(`YAML语法错误: ${(error as Error).message}`);
      }
    } else if (language === 'json') {
      try {
        JSON.parse(code);
      } catch (error) {
        errors.push(`JSON语法错误: ${(error as Error).message}`);
      }
    }
    
    setValidationErrors(errors);
  };

  // 保存代码
  const handleSave = () => {
    if (validationErrors.length > 0) {
      message.error('代码存在语法错误，无法保存');
      return;
    }
    
    onSave?.(currentValue);
    setHasChanges(false);
    message.success('保存成功');
  };

  // 重置代码
  const handleReset = () => {
    setCurrentValue(value);
    setHasChanges(false);
    setValidationErrors([]);
  };

  // 复制代码
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(currentValue);
      message.success('代码已复制到剪贴板');
    } catch (error) {
      message.error('复制失败');
    }
  };

  // 格式化代码
  const handleFormat = () => {
    if (editorRef.current) {
      editorRef.current.getAction('editor.action.formatDocument').run();
    }
  };

  // 切换全屏
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      containerRef.current?.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  };

  // 监听全屏状态变化
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 更新外部值
  React.useEffect(() => {
    if (value !== currentValue && !hasChanges) {
      setCurrentValue(value);
      setValidationErrors([]);
    }
  }, [value, currentValue, hasChanges]);

  // 渲染工具栏
  const renderToolbar = () => (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      padding: '8px 16px',
      borderBottom: '1px solid #f0f0f0'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <span style={{ fontWeight: 'bold' }}>{title}</span>
        
        {hasChanges && (
          <span style={{ color: '#faad14', fontSize: '12px' }}>
            • 未保存
          </span>
        )}
        
        {validationErrors.length > 0 && (
          <Tooltip title={validationErrors.join('; ')}>
            <span style={{ color: '#ff4d4f', fontSize: '12px' }}>
              • {validationErrors.length} 个错误
            </span>
          </Tooltip>
        )}
      </div>

      <Space>
        <Select
          size="small"
          value={theme}
          onChange={setTheme}
          style={{ width: 80 }}
        >
          <Option value="light">浅色</Option>
          <Option value="dark">深色</Option>
        </Select>

        <Tooltip title="格式化代码">
          <Button
            type="text"
            size="small"
            icon={<SettingOutlined />}
            onClick={handleFormat}
            disabled={readOnly}
          />
        </Tooltip>

        <Tooltip title="复制代码">
          <Button
            type="text"
            size="small"
            icon={<CopyOutlined />}
            onClick={handleCopy}
          />
        </Tooltip>

        <Tooltip title="重置">
          <Button
            type="text"
            size="small"
            icon={<ReloadOutlined />}
            onClick={handleReset}
            disabled={!hasChanges || readOnly}
          />
        </Tooltip>

        <Tooltip title={isFullscreen ? '退出全屏' : '全屏'}>
          <Button
            type="text"
            size="small"
            icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={toggleFullscreen}
          />
        </Tooltip>

        {!readOnly && (
          <Button
            type="primary"
            size="small"
            icon={hasChanges ? <SaveOutlined /> : <CheckOutlined />}
            onClick={handleSave}
            disabled={!hasChanges || validationErrors.length > 0}
          >
            {hasChanges ? '保存' : '已保存'}
          </Button>
        )}
      </Space>
    </div>
  );

  return (
    <div 
      ref={containerRef}
      className={className}
      style={{
        ...style,
        height: isFullscreen ? '100vh' : height + 60,
        backgroundColor: 'white',
        borderRadius: isFullscreen ? 0 : '6px',
        border: isFullscreen ? 'none' : '1px solid #d9d9d9',
      }}
    >
      {renderToolbar()}
      
      <div style={{ height: isFullscreen ? 'calc(100vh - 60px)' : height }}>
        <Editor
          height="100%"
          language={language}
          value={currentValue}
          theme={theme === 'dark' ? 'vs-dark' : 'vs'}
          loading={loading}
          options={{
            readOnly,
            fontSize: 14,
            lineNumbers: 'on',
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            wordWrap: 'on',
            automaticLayout: true,
          }}
          onChange={handleValueChange}
          onMount={handleEditorDidMount}
        />
      </div>
    </div>
  );
};

export default CodeEditor;
