{{define "dashboard"}}
<div id="dashboard" class="tab-content active">
    <div class="card">
        <div class="card-header">
            <h3>系统概览</h3>
            <button class="btn btn-primary" onclick="refreshDashboard()">
                <span class="loading" id="dashboard-loading" style="display: none;"></span>
                刷新
            </button>
        </div>
        <div class="card-body">
            <div class="metrics-grid">
                <div class="metric-card">
                    <h4>系统健康</h4>
                    <div id="health-result" class="metric-value">检查中...</div>
                    <button class="btn btn-secondary" onclick="checkHealth()">检查健康状态</button>
                </div>
                <div class="metric-card">
                    <h4>系统指标</h4>
                    <div id="system-metrics" class="metric-value">加载中...</div>
                </div>
                <div class="metric-card">
                    <h4>WebSocket连接</h4>
                    <div id="ws-connections" class="metric-value">0 个连接</div>
                </div>
                <div class="metric-card">
                    <h4>备份列表</h4>
                    <div id="backup-list" class="metric-value">加载中...</div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}
