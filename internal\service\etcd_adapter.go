package service

import (
	"strings"
	"time"

	"k8s-helper/internal/domain"
	"k8s-helper/pkg/etcd"
)

// ETCDConfigAdapter ETCD配置适配器
type ETCDConfigAdapter struct{}

// NewETCDConfigAdapter 创建配置适配器
func NewETCDConfigAdapter() *ETCDConfigAdapter {
	return &ETCDConfigAdapter{}
}

// DomainToSDK 将领域配置转换为SDK配置
func (a *ETCDConfigAdapter) DomainToSDK(domainConfig *domain.ETCDConfig) *etcd.Config {
	if domainConfig == nil {
		return nil
	}

	// 解析端点字符串为数组
	endpoints := etcd.ParseEndpoints(domainConfig.Servers)

	return &etcd.Config{
		Endpoints: endpoints,
		CertFile:  domainConfig.CertFile,
		KeyFile:   domainConfig.KeyFile,
		CAFile:    domainConfig.CaFile,
		Timeout:   30 * time.Second, // 默认超时
	}
}

// SDKToDomain 将SDK配置转换为领域配置
func (a *ETCDConfigAdapter) SDKToDomain(sdkConfig *etcd.Config) *domain.ETCDConfig {
	if sdkConfig == nil {
		return nil
	}

	// 将端点数组转换为字符串
	servers := strings.Join(sdkConfig.Endpoints, ",")

	return &domain.ETCDConfig{
		Servers:  servers,
		CaFile:   sdkConfig.CAFile,
		CertFile: sdkConfig.CertFile,
		KeyFile:  sdkConfig.KeyFile,
	}
}

// BackupResultAdapter 备份结果适配器
type BackupResultAdapter struct{}

// NewBackupResultAdapter 创建备份结果适配器
func NewBackupResultAdapter() *BackupResultAdapter {
	return &BackupResultAdapter{}
}

// SDKToDomain 将SDK备份结果转换为领域备份结果
func (a *BackupResultAdapter) SDKToDomain(sdkResult *etcd.BackupResult) *domain.BackupResult {
	if sdkResult == nil {
		return nil
	}

	return &domain.BackupResult{
		FilePath: sdkResult.FilePath,
		Size:     sdkResult.Size,
		Duration: sdkResult.Duration,
		Version:  sdkResult.Version,
	}
}

// DomainToSDK 将领域备份结果转换为SDK备份结果
func (a *BackupResultAdapter) DomainToSDK(domainResult *domain.BackupResult) *etcd.BackupResult {
	if domainResult == nil {
		return nil
	}

	return &etcd.BackupResult{
		FilePath: domainResult.FilePath,
		Size:     domainResult.Size,
		Duration: domainResult.Duration,
		Version:  domainResult.Version,
	}
}

// RestoreResultAdapter 恢复结果适配器
type RestoreResultAdapter struct{}

// NewRestoreResultAdapter 创建恢复结果适配器
func NewRestoreResultAdapter() *RestoreResultAdapter {
	return &RestoreResultAdapter{}
}

// SDKToDomain 将SDK恢复结果转换为领域恢复结果
func (a *RestoreResultAdapter) SDKToDomain(sdkResult *etcd.RestoreResult) *domain.RestoreResult {
	if sdkResult == nil {
		return nil
	}

	return &domain.RestoreResult{
		DataDir:      sdkResult.DataDir,
		Duration:     sdkResult.Duration,
		SnapshotSize: sdkResult.SnapshotSize,
	}
}

// DomainToSDK 将领域恢复结果转换为SDK恢复结果
func (a *RestoreResultAdapter) DomainToSDK(domainResult *domain.RestoreResult) *etcd.RestoreResult {
	if domainResult == nil {
		return nil
	}

	return &etcd.RestoreResult{
		DataDir:      domainResult.DataDir,
		Duration:     domainResult.Duration,
		SnapshotSize: domainResult.SnapshotSize,
		Success:      true, // 假设成功
	}
}

// VerifyResultAdapter 验证结果适配器
type VerifyResultAdapter struct{}

// NewVerifyResultAdapter 创建验证结果适配器
func NewVerifyResultAdapter() *VerifyResultAdapter {
	return &VerifyResultAdapter{}
}

// SDKToDomain 将SDK验证结果转换为领域验证结果
func (a *VerifyResultAdapter) SDKToDomain(sdkResult *etcd.VerifyResult) *domain.VerifyResult {
	if sdkResult == nil {
		return nil
	}

	// 构建详细信息映射
	details := make(map[string]interface{})
	details["fileSize"] = sdkResult.FileSize
	details["hash"] = sdkResult.Hash
	details["revision"] = sdkResult.Revision
	details["totalKeys"] = sdkResult.TotalKeys
	details["totalSize"] = sdkResult.TotalSize

	return &domain.VerifyResult{
		Valid:        sdkResult.Valid,
		Details:      details,
		Duration:     sdkResult.Duration,
		ErrorMessage: "", // SDK结果中没有错误消息字段
	}
}

// DomainToSDK 将领域验证结果转换为SDK验证结果
func (a *VerifyResultAdapter) DomainToSDK(domainResult *domain.VerifyResult) *etcd.VerifyResult {
	if domainResult == nil {
		return nil
	}

	result := &etcd.VerifyResult{
		Valid:    domainResult.Valid,
		Duration: domainResult.Duration,
	}

	// 从详细信息中提取字段
	if domainResult.Details != nil {
		if fileSize, ok := domainResult.Details["fileSize"].(int64); ok {
			result.FileSize = fileSize
		}
		if hash, ok := domainResult.Details["hash"].(uint32); ok {
			result.Hash = hash
		}
		if revision, ok := domainResult.Details["revision"].(int64); ok {
			result.Revision = revision
		}
		if totalKeys, ok := domainResult.Details["totalKeys"].(int); ok {
			result.TotalKeys = totalKeys
		}
		if totalSize, ok := domainResult.Details["totalSize"].(int64); ok {
			result.TotalSize = totalSize
		}
	}

	return result
}

// RestoreOptionsAdapter 恢复选项适配器
type RestoreOptionsAdapter struct{}

// NewRestoreOptionsAdapter 创建恢复选项适配器
func NewRestoreOptionsAdapter() *RestoreOptionsAdapter {
	return &RestoreOptionsAdapter{}
}

// DomainToSDK 将领域恢复选项转换为SDK恢复选项
func (a *RestoreOptionsAdapter) DomainToSDK(domainOpts *domain.RestoreOptions) *etcd.RestoreOptions {
	if domainOpts == nil {
		return nil
	}

	return &etcd.RestoreOptions{
		SnapshotPath:             domainOpts.SnapshotPath,
		DataDir:                  domainOpts.DataDir,
		Name:                     domainOpts.Name,
		InitialCluster:           domainOpts.InitialCluster,
		InitialAdvertisePeerURLs: domainOpts.InitialAdvertisePeerURLs,
		Timeout:                  domainOpts.Timeout,
		SkipHashCheck:            domainOpts.SkipHashCheck,
		MarkCompacted:            domainOpts.MarkCompacted,
		RevisionBump:             0, // 默认值
	}
}

// VerifyOptionsAdapter 验证选项适配器
type VerifyOptionsAdapter struct{}

// NewVerifyOptionsAdapter 创建验证选项适配器
func NewVerifyOptionsAdapter() *VerifyOptionsAdapter {
	return &VerifyOptionsAdapter{}
}

// DomainToSDK 将领域验证选项转换为SDK验证选项
func (a *VerifyOptionsAdapter) DomainToSDK(domainOpts *domain.VerifyOptions) *etcd.VerifyOptions {
	if domainOpts == nil {
		return nil
	}

	return &etcd.VerifyOptions{
		SnapshotPath: domainOpts.SnapshotPath,
		Timeout:      domainOpts.Timeout,
		Detailed:     domainOpts.DetailedVerify,
	}
}
