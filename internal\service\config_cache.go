package service

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"go.uber.org/zap"
	"gopkg.in/yaml.v3"
	"k8s-helper/internal/domain"
)

// ConfigCacheEntry 配置缓存条目
type ConfigCacheEntry struct {
	Config     interface{}
	Validation *ValidationResult
	Hash       string
	ModTime    time.Time
	CachedAt   time.Time
	AccessTime time.Time
	HitCount   int64
}

// ConfigCache 配置缓存管理器
type ConfigCache struct {
	logger        *zap.Logger
	cache         map[string]*ConfigCacheEntry
	mutex         sync.RWMutex
	maxSize       int
	ttl           time.Duration
	validator     *ConfigValidator
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
}

// NewConfigCache 创建配置缓存管理器
func NewConfigCache(logger *zap.Logger, maxSize int, ttl time.Duration) *ConfigCache {
	cache := &ConfigCache{
		logger:      logger,
		cache:       make(map[string]*ConfigCacheEntry),
		maxSize:     maxSize,
		ttl:         ttl,
		validator:   NewConfigValidator(logger),
		stopCleanup: make(chan struct{}),
	}

	// 启动定期清理
	cache.startCleanup()

	return cache
}

// GetETCDConfig 获取ETCD配置（带缓存）
func (cc *ConfigCache) GetETCDConfig(configPath string) (*domain.ETCDConfig, *ValidationResult, error) {
	// 生成缓存键
	cacheKey := fmt.Sprintf("etcd_config:%s", configPath)

	// 检查文件状态
	fileInfo, err := os.Stat(configPath)
	if err != nil {
		return nil, nil, fmt.Errorf("配置文件不存在: %w", err)
	}

	// 尝试从缓存获取
	if entry := cc.getCacheEntry(cacheKey, fileInfo.ModTime()); entry != nil {
		cc.logger.Debug("配置缓存命中",
			zap.String("path", configPath),
			zap.Int64("hitCount", entry.HitCount))

		config := entry.Config.(*domain.ETCDConfig)
		return config, entry.Validation, nil
	}

	// 缓存未命中，解析配置
	cc.logger.Debug("配置缓存未命中，重新解析", zap.String("path", configPath))

	config, err := cc.parseETCDConfig(configPath)
	if err != nil {
		return nil, nil, fmt.Errorf("解析配置失败: %w", err)
	}

	// 验证配置
	validation := cc.validator.ValidateETCDConfig(config)

	// 计算文件哈希
	hash, err := cc.calculateFileHash(configPath)
	if err != nil {
		cc.logger.Warn("计算文件哈希失败", zap.Error(err))
		hash = ""
	}

	// 存储到缓存
	cc.setCacheEntry(cacheKey, &ConfigCacheEntry{
		Config:     config,
		Validation: validation,
		Hash:       hash,
		ModTime:    fileInfo.ModTime(),
		CachedAt:   time.Now(),
		AccessTime: time.Now(),
		HitCount:   1,
	})

	return config, validation, nil
}

// GetBackupOptions 获取备份选项（带缓存）
func (cc *ConfigCache) GetBackupOptions(opts *domain.BackupOptions) (*domain.BackupOptions, *ValidationResult, error) {
	// 为选项生成缓存键
	cacheKey := cc.generateOptionsKey("backup_options", opts)

	// 尝试从缓存获取
	if entry := cc.getCacheEntryByKey(cacheKey); entry != nil {
		cc.logger.Debug("备份选项缓存命中", zap.String("key", cacheKey))

		cachedOpts := entry.Config.(*domain.BackupOptions)
		return cachedOpts, entry.Validation, nil
	}

	// 缓存未命中，验证选项
	validation := cc.validator.ValidateBackupOptions(opts)

	// 存储到缓存
	cc.setCacheEntry(cacheKey, &ConfigCacheEntry{
		Config:     opts,
		Validation: validation,
		CachedAt:   time.Now(),
		AccessTime: time.Now(),
		HitCount:   1,
	})

	return opts, validation, nil
}

// GetRestoreOptions 获取恢复选项（带缓存）
func (cc *ConfigCache) GetRestoreOptions(opts *domain.RestoreOptions) (*domain.RestoreOptions, *ValidationResult, error) {
	// 为选项生成缓存键
	cacheKey := cc.generateOptionsKey("restore_options", opts)

	// 尝试从缓存获取
	if entry := cc.getCacheEntryByKey(cacheKey); entry != nil {
		cc.logger.Debug("恢复选项缓存命中", zap.String("key", cacheKey))

		cachedOpts := entry.Config.(*domain.RestoreOptions)
		return cachedOpts, entry.Validation, nil
	}

	// 缓存未命中，验证选项
	validation := cc.validator.ValidateRestoreOptions(opts)

	// 存储到缓存
	cc.setCacheEntry(cacheKey, &ConfigCacheEntry{
		Config:     opts,
		Validation: validation,
		CachedAt:   time.Now(),
		AccessTime: time.Now(),
		HitCount:   1,
	})

	return opts, validation, nil
}

// getCacheEntry 获取缓存条目（检查文件修改时间）
func (cc *ConfigCache) getCacheEntry(key string, modTime time.Time) *ConfigCacheEntry {
	cc.mutex.RLock()
	defer cc.mutex.RUnlock()

	entry, exists := cc.cache[key]
	if !exists {
		return nil
	}

	// 检查文件是否被修改
	if !entry.ModTime.Equal(modTime) {
		cc.logger.Debug("配置文件已修改，缓存失效", zap.String("key", key))
		return nil
	}

	// 检查TTL
	if cc.ttl > 0 && time.Since(entry.CachedAt) > cc.ttl {
		cc.logger.Debug("缓存已过期", zap.String("key", key))
		return nil
	}

	// 更新访问时间和命中次数
	entry.AccessTime = time.Now()
	entry.HitCount++

	return entry
}

// getCacheEntryByKey 根据键获取缓存条目
func (cc *ConfigCache) getCacheEntryByKey(key string) *ConfigCacheEntry {
	cc.mutex.RLock()
	defer cc.mutex.RUnlock()

	entry, exists := cc.cache[key]
	if !exists {
		return nil
	}

	// 检查TTL
	if cc.ttl > 0 && time.Since(entry.CachedAt) > cc.ttl {
		return nil
	}

	// 更新访问时间和命中次数
	entry.AccessTime = time.Now()
	entry.HitCount++

	return entry
}

// setCacheEntry 设置缓存条目
func (cc *ConfigCache) setCacheEntry(key string, entry *ConfigCacheEntry) {
	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	// 检查缓存大小限制
	if len(cc.cache) >= cc.maxSize {
		cc.evictLRU()
	}

	cc.cache[key] = entry

	cc.logger.Debug("配置已缓存",
		zap.String("key", key),
		zap.Int("cacheSize", len(cc.cache)))
}

// evictLRU 驱逐最近最少使用的条目
func (cc *ConfigCache) evictLRU() {
	var oldestKey string
	var oldestTime time.Time

	for key, entry := range cc.cache {
		if oldestKey == "" || entry.AccessTime.Before(oldestTime) {
			oldestKey = key
			oldestTime = entry.AccessTime
		}
	}

	if oldestKey != "" {
		delete(cc.cache, oldestKey)
		cc.logger.Debug("驱逐LRU缓存条目", zap.String("key", oldestKey))
	}
}

// parseETCDConfig 解析ETCD配置
func (cc *ConfigCache) parseETCDConfig(configPath string) (*domain.ETCDConfig, error) {
	cc.logger.Debug("解析ETCD配置文件", zap.String("path", configPath))

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 尝试解析为YAML格式
	config, err := cc.parseYAMLConfig(data)
	if err != nil {
		cc.logger.Debug("YAML解析失败，尝试解析为JSON", zap.Error(err))
		// 如果YAML解析失败，尝试JSON格式
		config, err = cc.parseJSONConfig(data)
		if err != nil {
			return nil, fmt.Errorf("配置文件格式无效，既不是YAML也不是JSON: %w", err)
		}
	}

	// 验证配置完整性
	if err := cc.validateParsedConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	cc.logger.Debug("ETCD配置解析成功",
		zap.String("servers", config.Servers),
		zap.String("caFile", config.CaFile))

	return config, nil
}

// calculateFileHash 计算文件哈希
func (cc *ConfigCache) calculateFileHash(filePath string) (string, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}

	hash := md5.Sum(data)
	return fmt.Sprintf("%x", hash), nil
}

// generateOptionsKey 为选项生成缓存键
func (cc *ConfigCache) generateOptionsKey(prefix string, opts interface{}) string {
	if opts == nil {
		return fmt.Sprintf("%s:nil", prefix)
	}

	// 将选项序列化为JSON以生成稳定的哈希
	data, err := json.Marshal(opts)
	if err != nil {
		// 如果序列化失败，回退到指针地址
		cc.logger.Warn("选项序列化失败，使用指针地址", zap.Error(err))
		return fmt.Sprintf("%s:%p", prefix, opts)
	}

	// 计算JSON数据的MD5哈希
	hash := md5.Sum(data)
	return fmt.Sprintf("%s:%x", prefix, hash)
}

// startCleanup 启动定期清理
func (cc *ConfigCache) startCleanup() {
	cc.cleanupTicker = time.NewTicker(5 * time.Minute)

	go func() {
		for {
			select {
			case <-cc.cleanupTicker.C:
				cc.cleanup()
			case <-cc.stopCleanup:
				cc.cleanupTicker.Stop()
				return
			}
		}
	}()
}

// cleanup 清理过期缓存
func (cc *ConfigCache) cleanup() {
	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	now := time.Now()
	expiredKeys := make([]string, 0)

	for key, entry := range cc.cache {
		if cc.ttl > 0 && now.Sub(entry.CachedAt) > cc.ttl {
			expiredKeys = append(expiredKeys, key)
		}
	}

	for _, key := range expiredKeys {
		delete(cc.cache, key)
	}

	if len(expiredKeys) > 0 {
		cc.logger.Debug("清理过期缓存",
			zap.Int("expired", len(expiredKeys)),
			zap.Int("remaining", len(cc.cache)))
	}
}

// GetStats 获取缓存统计信息
func (cc *ConfigCache) GetStats() map[string]interface{} {
	cc.mutex.RLock()
	defer cc.mutex.RUnlock()

	totalHits := int64(0)
	for _, entry := range cc.cache {
		totalHits += entry.HitCount
	}

	return map[string]interface{}{
		"size":      len(cc.cache),
		"maxSize":   cc.maxSize,
		"totalHits": totalHits,
		"ttl":       cc.ttl.String(),
	}
}

// Close 关闭缓存管理器
func (cc *ConfigCache) Close() {
	close(cc.stopCleanup)

	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	cc.cache = make(map[string]*ConfigCacheEntry)
	cc.logger.Info("配置缓存已关闭")
}

// parseYAMLConfig 解析YAML格式的配置
func (cc *ConfigCache) parseYAMLConfig(data []byte) (*domain.ETCDConfig, error) {
	var yamlConfig struct {
		ETCD struct {
			Servers  string `yaml:"servers"`
			CertFile string `yaml:"cert_file"`
			KeyFile  string `yaml:"key_file"`
			CaFile   string `yaml:"ca_file"`
		} `yaml:"etcd"`
	}

	if err := yaml.Unmarshal(data, &yamlConfig); err != nil {
		return nil, fmt.Errorf("YAML解析失败: %w", err)
	}

	return &domain.ETCDConfig{
		Servers:  yamlConfig.ETCD.Servers,
		CertFile: yamlConfig.ETCD.CertFile,
		KeyFile:  yamlConfig.ETCD.KeyFile,
		CaFile:   yamlConfig.ETCD.CaFile,
	}, nil
}

// parseJSONConfig 解析JSON格式的配置
func (cc *ConfigCache) parseJSONConfig(data []byte) (*domain.ETCDConfig, error) {
	var jsonConfig struct {
		ETCD struct {
			Servers  string `json:"servers"`
			CertFile string `json:"cert_file"`
			KeyFile  string `json:"key_file"`
			CaFile   string `json:"ca_file"`
		} `json:"etcd"`
	}

	if err := json.Unmarshal(data, &jsonConfig); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %w", err)
	}

	return &domain.ETCDConfig{
		Servers:  jsonConfig.ETCD.Servers,
		CertFile: jsonConfig.ETCD.CertFile,
		KeyFile:  jsonConfig.ETCD.KeyFile,
		CaFile:   jsonConfig.ETCD.CaFile,
	}, nil
}

// validateParsedConfig 验证解析后的配置
func (cc *ConfigCache) validateParsedConfig(config *domain.ETCDConfig) error {
	if config == nil {
		return fmt.Errorf("配置为空")
	}

	if config.Servers == "" {
		return fmt.Errorf("ETCD服务器地址不能为空")
	}

	// 验证证书文件路径（如果提供）
	certFiles := []struct {
		path string
		name string
	}{
		{config.CertFile, "客户端证书"},
		{config.KeyFile, "客户端私钥"},
		{config.CaFile, "CA证书"},
	}

	for _, file := range certFiles {
		if file.path != "" {
			if _, err := os.Stat(file.path); os.IsNotExist(err) {
				return fmt.Errorf("%s文件不存在: %s", file.name, file.path)
			}
		}
	}

	return nil
}
