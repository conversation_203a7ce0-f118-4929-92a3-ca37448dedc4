// 应用常量定义

// API相关常量
// 在开发环境使用环境变量中的绝对URL，生产环境使用相对路径
export const API_BASE_URL = import.meta.env.DEV
  ? (import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1')
  : '/api/v1';
export const WS_BASE_URL = import.meta.env.DEV
  ? (import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8080/api/v1/ws')
  : '/api/v1/ws';

// 应用配置常量
export const APP_CONFIG = {
  TITLE: import.meta.env.VITE_APP_TITLE || 'K8s-Helper',
  VERSION: import.meta.env.VITE_APP_VERSION || '2.0.0',
  DEV_MODE: import.meta.env.VITE_DEV_MODE === 'true',
} as const;

// 路由路径常量
export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  ETCD: '/etcd',
  ETCD_BACKUP: '/etcd/backup',
  ETCD_RESTORE: '/etcd/restore',
  ETCD_CRONJOBS: '/etcd/cronjobs',
  CLUSTER: '/cluster',
  LOGS: '/logs',
  PORT_FORWARD: '/port-forward',
  CLEANUP: '/cleanup',
  MONITORING: '/monitoring',
  CONFIG: '/config',
} as const;

// 本地存储键名常量
export const STORAGE_KEYS = {
  THEME: 'k8s-helper-theme',
  USER_PREFERENCES: 'k8s-helper-user-preferences',
  SIDEBAR_COLLAPSED: 'k8s-helper-sidebar-collapsed',
  REFRESH_INTERVALS: 'k8s-helper-refresh-intervals',
} as const;

// 默认配置常量
export const DEFAULT_CONFIG = {
  REFRESH_INTERVAL: 30000, // 30秒
  PAGINATION_SIZE: 20,
  MAX_LOG_LINES: 1000,
  WEBSOCKET_RECONNECT_INTERVAL: 5000, // 5秒
  MAX_RECONNECT_ATTEMPTS: 10,
} as const;

// 状态常量
export const STATUS = {
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  IDLE: 'idle',
} as const;

// 主题常量
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
} as const;

// 连接状态常量
export const CONNECTION_STATUS = {
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  ERROR: 'error',
} as const;

// Pod状态常量
export const POD_STATUS = {
  RUNNING: 'Running',
  PENDING: 'Pending',
  SUCCEEDED: 'Succeeded',
  FAILED: 'Failed',
  UNKNOWN: 'Unknown',
} as const;

// 日志级别常量
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
} as const;

// 文件大小单位常量
export const FILE_SIZE_UNITS = ['B', 'KB', 'MB', 'GB', 'TB'] as const;

// 时间格式常量
export const TIME_FORMATS = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
} as const;
