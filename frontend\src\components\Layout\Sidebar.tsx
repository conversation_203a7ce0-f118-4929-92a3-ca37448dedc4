import React from 'react';
import { Layout, Menu, Button } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import { navigationConfig, filterNavigationByPermission } from '@/config/navigation';

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
  onMobileClose?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

// 转换导航配置为Ant Design Menu所需的格式
const convertToMenuItems = (items: typeof navigationConfig): any[] => {
  return items.map(item => ({
    key: item.key,
    icon: item.icon,
    label: item.label,
    children: item.children ? convertToMenuItems(item.children) : undefined,
  }));
};

// 获取过滤后的导航菜单
const getNavigationItems = () => {
  const filteredItems = filterNavigationByPermission(navigationConfig);
  return convertToMenuItems(filteredItems);
};

/**
 * 侧边栏导航组件
 * 
 * 功能特性：
 * - 可折叠的导航菜单
 * - 活跃状态高亮
 * - 响应式设计
 * - 移动端支持
 */
export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  onToggle,
  onMobileClose,
  className,
  style,
  ...restProps
}) => {
  const location = useLocation();
  const navigate = useNavigate();

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const pathname = location.pathname;

    // 查找匹配的菜单项
    const findSelectedKey = (items: typeof navigationConfig): string[] => {
      for (const item of items) {
        if (item.path === pathname) {
          return [item.key];
        }
        if (item.children) {
          const found = findSelectedKey(item.children);
          if (found.length > 0) {
            return found;
          }
        }
      }
      return [];
    };

    const selected = findSelectedKey(navigationConfig);
    return selected.length > 0 ? selected : ['/dashboard'];
  };

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const pathname = location.pathname;
    if (pathname.startsWith('/etcd')) {
      return ['etcd'];
    }
    return [];
  };

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    // 查找对应的菜单项
    const findMenuItem = (items: typeof navigationConfig): any => {
      for (const item of items) {
        if (item.path && item.key === key) {
          return item;
        }
        if (item.children) {
          const found = findMenuItem(item.children);
          if (found) return found;
        }
      }
      return null;
    };

    const menuItem = findMenuItem(navigationConfig);
    if (menuItem?.path) {
      navigate(menuItem.path);
      // 移动端点击后关闭侧边栏
      if (window.innerWidth <= 768) {
        onMobileClose?.();
      }
    }
  };

  return (
    <Sider
      className={`app-sidebar ${className || ''}`}
      style={style}
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={280}
      collapsedWidth={64}
      theme="dark"
      {...restProps}
    >
      {/* Logo区域 */}
      <div className="app-sidebar-logo">
        <div className="app-sidebar-logo-content">
          {collapsed ? (
            <span className="app-sidebar-logo-icon">K8s</span>
          ) : (
            <span className="app-sidebar-logo-text">K8s-Helper</span>
          )}
        </div>
      </div>

      {/* 折叠按钮 */}
      <div className="app-sidebar-trigger">
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={onToggle}
          className="app-sidebar-trigger-btn"
        />
      </div>

      {/* 导航菜单 */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={getSelectedKeys()}
        defaultOpenKeys={getOpenKeys()}
        items={getNavigationItems()}
        onClick={handleMenuClick}
        className="app-sidebar-menu"
      />
    </Sider>
  );
};

export default Sidebar;
