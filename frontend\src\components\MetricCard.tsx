import React from 'react';
import { Card, Statistic, Tooltip, Spin, Row, Col } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { StatusIndicator } from './StatusIndicator';

// 指标状态类型
export type MetricStatus = 'healthy' | 'warning' | 'error' | 'unknown';

// 指标卡片属性
export interface MetricCardProps {
  title: string;
  value: string | number;
  prefix?: React.ReactNode;
  suffix?: string;
  status?: MetricStatus;
  loading?: boolean;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 指标卡片组件
 * 
 * 功能特性：
 * - 显示指标数值和状态
 * - 支持加载状态
 * - 支持趋势显示
 * - 支持点击交互
 * - 支持状态指示器
 */
export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  prefix,
  suffix,
  status = 'healthy',
  loading = false,
  description,
  trend,
  onClick,
  className,
  style,
}) => {
  // 获取状态对应的颜色
  const getStatusColor = (status: MetricStatus): string => {
    const colorMap: Record<MetricStatus, string> = {
      healthy: '#52c41a',
      warning: '#faad14',
      error: '#ff4d4f',
      unknown: '#d9d9d9',
    };
    return colorMap[status];
  };

  // 格式化数值显示
  const formatValue = (val: string | number): string => {
    if (typeof val === 'number') {
      // 大数值格式化
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      }
      return val.toString();
    }
    return val;
  };

  // 渲染趋势指示器
  const renderTrend = () => {
    if (!trend) return null;

    const trendColor = trend.isPositive ? '#52c41a' : '#ff4d4f';
    const trendIcon = trend.isPositive ? '↗' : '↘';
    
    return (
      <span style={{ 
        color: trendColor, 
        fontSize: '12px', 
        marginLeft: '8px',
        fontWeight: 'bold',
      }}>
        {trendIcon} {Math.abs(trend.value)}%
      </span>
    );
  };

  // 渲染标题
  const renderTitle = () => {
    const titleElement = (
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <span>{title}</span>
        {description && (
          <Tooltip title={description}>
            <QuestionCircleOutlined style={{ color: '#8c8c8c', fontSize: '12px' }} />
          </Tooltip>
        )}
      </div>
    );

    return titleElement;
  };

  // 渲染数值
  const renderValue = () => {
    if (loading) {
      return <Spin size="small" />;
    }

    return (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>{formatValue(value)}</span>
        {suffix && <span style={{ marginLeft: '4px', fontSize: '14px', color: '#8c8c8c' }}>{suffix}</span>}
        {renderTrend()}
      </div>
    );
  };

  return (
    <Card
      className={className}
      style={{
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.3s ease',
        ...style,
      }}
      onClick={onClick}
      hoverable={!!onClick}
      bodyStyle={{ padding: '20px' }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ flex: 1 }}>
          <Statistic
            title={renderTitle()}
            value={renderValue()}
            prefix={prefix}
            valueStyle={{ 
              color: getStatusColor(status),
              fontSize: '24px',
              fontWeight: 'bold',
            }}
          />
        </div>
        
        {status !== 'healthy' && (
          <div style={{ marginLeft: '12px' }}>
            <StatusIndicator status={status} size="small" />
          </div>
        )}
      </div>
    </Card>
  );
};

// 指标卡片网格组件
export interface MetricCardGridProps {
  metrics: MetricCardProps[];
  loading?: boolean;
  gutter?: [number, number];
  responsive?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  };
}

/**
 * 指标卡片网格组件
 * 
 * 用于批量显示指标卡片，支持响应式布局
 */
export const MetricCardGrid: React.FC<MetricCardGridProps> = ({
  metrics,
  loading = false,
  gutter = [16, 16],
  responsive = { xs: 24, sm: 12, md: 8, lg: 6, xl: 6, xxl: 4 },
}) => {
  return (
    <Row gutter={gutter}>
      {metrics.map((metric, index) => (
        <Col key={index} {...responsive}>
          <MetricCard {...metric} loading={loading} />
        </Col>
      ))}
    </Row>
  );
};

export default MetricCard;
