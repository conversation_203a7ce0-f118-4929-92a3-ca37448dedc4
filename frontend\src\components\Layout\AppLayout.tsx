import React, { useState } from 'react';
import { Layout } from 'antd';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import type { ComponentProps } from '@/types';

const { Content } = Layout;

interface AppLayoutProps extends ComponentProps {
  // 使用 Outlet 而不是 children
}

/**
 * 应用主布局组件
 * 
 * 提供完整的应用布局框架，包括：
 * - 可折叠的侧边栏导航
 * - 顶部导航栏
 * - 主内容区域
 * - 响应式设计支持
 */
export const AppLayout: React.FC<AppLayoutProps> = ({
  className,
  style,
  ...restProps
}) => {
  // 侧边栏折叠状态
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测移动端
  React.useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      if (mobile) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 处理侧边栏折叠切换
  const handleSidebarToggle = () => {
    setSidebarCollapsed(prev => !prev);
  };

  // 处理移动端侧边栏关闭
  const handleMobileSidebarClose = () => {
    if (isMobile) {
      setSidebarCollapsed(true);
    }
  };

  return (
    <Layout 
      className={`app-layout ${className || ''}`}
      style={{ minHeight: '100vh', ...style }}
      {...restProps}
    >
      {/* 侧边栏 */}
      <Sidebar 
        collapsed={sidebarCollapsed}
        onToggle={handleSidebarToggle}
        onMobileClose={handleMobileSidebarClose}
      />
      
      {/* 主要内容区域 */}
      <Layout className={`app-layout-main ${sidebarCollapsed ? 'collapsed' : ''}`}>
        {/* 顶部导航栏 */}
        <Header
          sidebarCollapsed={sidebarCollapsed}
          onSidebarToggle={handleSidebarToggle}
        />

        {/* 内容区域 */}
        <Content className="app-layout-content">
          <div className="app-layout-content-inner">
            <Outlet />
          </div>
        </Content>
      </Layout>

      {/* 移动端遮罩层 */}
      {isMobile && !sidebarCollapsed && (
        <div
          className="app-layout-mobile-mask"
          onClick={handleMobileSidebarClose}
        />
      )}
    </Layout>
  );
};

export default AppLayout;
