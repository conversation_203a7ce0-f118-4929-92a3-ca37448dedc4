import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  InputNumber, 
  Switch, 
  Select, 
  Button, 
  Space, 
  Card,
  Divider,
  Tooltip,
  Alert
} from 'antd';
import { 
  SaveOutlined, 
  ReloadOutlined, 
  QuestionCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;

// 配置项类型
export type ConfigItemType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'select' 
  | 'multiselect'
  | 'textarea'
  | 'password';

// 配置项定义
export interface ConfigItem {
  key: string;
  label: string;
  type: ConfigItemType;
  value: any;
  defaultValue?: any;
  description?: string;
  required?: boolean;
  disabled?: boolean;
  options?: Array<{ label: string; value: any }>;
  min?: number;
  max?: number;
  placeholder?: string;
  validation?: {
    pattern?: RegExp;
    message?: string;
  };
  group?: string;
}

// 配置表单属性
export interface ConfigFormProps {
  title?: string;
  items: ConfigItem[];
  loading?: boolean;
  onSave?: (values: Record<string, any>) => void;
  onReset?: () => void;
  onChange?: (key: string, value: any) => void;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 配置项表单组件
 * 
 * 功能特性：
 * - 多种配置项类型支持
 * - 表单验证
 * - 分组显示
 * - 重置功能
 * - 实时保存
 */
export const ConfigForm: React.FC<ConfigFormProps> = ({
  title = '配置设置',
  items,
  loading = false,
  onSave,
  onReset,
  onChange,
  className,
  style,
}) => {
  const [form] = Form.useForm();
  const [hasChanges, setHasChanges] = useState(false);
  const [initialValues, setInitialValues] = useState<Record<string, any>>({});

  // 初始化表单值
  useEffect(() => {
    const values = items.reduce((acc, item) => {
      acc[item.key] = item.value !== undefined ? item.value : item.defaultValue;
      return acc;
    }, {} as Record<string, any>);
    
    setInitialValues(values);
    form.setFieldsValue(values);
    setHasChanges(false);
  }, [items, form]);

  // 处理表单值变化
  const handleValuesChange = (changedValues: any, allValues: any) => {
    const hasChanged = Object.keys(allValues).some(key => 
      allValues[key] !== initialValues[key]
    );
    setHasChanges(hasChanged);

    // 触发单个字段变化回调
    Object.keys(changedValues).forEach(key => {
      onChange?.(key, changedValues[key]);
    });
  };

  // 保存配置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      onSave?.(values);
      setInitialValues(values);
      setHasChanges(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 重置配置
  const handleReset = () => {
    form.setFieldsValue(initialValues);
    setHasChanges(false);
    onReset?.();
  };

  // 渲染配置项
  const renderConfigItem = (item: ConfigItem) => {
    const commonProps = {
      disabled: item.disabled || loading,
      placeholder: item.placeholder,
    };

    const rules = [
      ...(item.required ? [{ required: true, message: `请输入${item.label}` }] : []),
      ...(item.validation ? [{ 
        pattern: item.validation.pattern, 
        message: item.validation.message 
      }] : []),
    ];

    switch (item.type) {
      case 'string':
        return <Input {...commonProps} />;
      
      case 'password':
        return <Input.Password {...commonProps} />;
      
      case 'textarea':
        return <TextArea {...commonProps} rows={4} />;
      
      case 'number':
        return (
          <InputNumber
            {...commonProps}
            min={item.min}
            max={item.max}
            style={{ width: '100%' }}
          />
        );
      
      case 'boolean':
        return (
          <Switch
            disabled={item.disabled || loading}
            checkedChildren="启用"
            unCheckedChildren="禁用"
          />
        );
      
      case 'select':
        return (
          <Select {...commonProps}>
            {item.options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
      
      case 'multiselect':
        return (
          <Select {...commonProps} mode="multiple">
            {item.options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
      
      default:
        return <Input {...commonProps} />;
    }
  };

  // 按组分组配置项
  const groupedItems = items.reduce((acc, item) => {
    const group = item.group || '基本设置';
    if (!acc[group]) {
      acc[group] = [];
    }
    acc[group].push(item);
    return acc;
  }, {} as Record<string, ConfigItem[]>);

  return (
    <Card 
      title={title}
      className={className}
      style={style}
      extra={
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleReset}
            disabled={!hasChanges || loading}
          >
            重置
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={loading}
            onClick={handleSave}
            disabled={!hasChanges}
          >
            保存配置
          </Button>
        </Space>
      }
    >
      {hasChanges && (
        <Alert
          message="配置已修改"
          description="您有未保存的配置更改，请及时保存"
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        {Object.entries(groupedItems).map(([groupName, groupItems], groupIndex) => (
          <div key={groupName}>
            {groupIndex > 0 && <Divider />}
            
            <h3 style={{ marginBottom: '16px', color: '#1890ff' }}>
              {groupName}
            </h3>
            
            {groupItems.map(item => (
              <Form.Item
                key={item.key}
                name={item.key}
                label={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                    <span>{item.label}</span>
                    {item.description && (
                      <Tooltip title={item.description}>
                        <QuestionCircleOutlined style={{ color: '#8c8c8c' }} />
                      </Tooltip>
                    )}
                    {item.required && (
                      <span style={{ color: '#ff4d4f' }}>*</span>
                    )}
                  </div>
                }
                rules={[
                  ...(item.required ? [{ required: true, message: `请输入${item.label}` }] : []),
                  ...(item.validation ? [{ 
                    pattern: item.validation.pattern, 
                    message: item.validation.message 
                  }] : []),
                ]}
                valuePropName={item.type === 'boolean' ? 'checked' : 'value'}
              >
                {renderConfigItem(item)}
              </Form.Item>
            ))}
          </div>
        ))}
      </Form>
    </Card>
  );
};

export default ConfigForm;
