import { apiClient } from './api';
import type {
  PodInfo,
  PodLogsRequest,
} from '@/types/api';

/**
 * 日志管理服务类
 * 
 * 提供Pod日志相关的API操作：
 * - Pod列表查询
 * - Pod详情获取
 * - Pod日志获取
 * - 日志流管理
 */
export class LogsService {
  private readonly basePath = '/pods';

  // ============ Pod管理 ============

  /**
   * 获取所有Pod列表
   * @returns Pod列表
   */
  async listAllPods(): Promise<PodInfo[]> {
    return apiClient.get<PodInfo[]>(`${this.basePath}`);
  }

  /**
   * 获取指定命名空间的Pod列表
   * @param namespace 命名空间
   * @returns Pod列表
   */
  async listPodsInNamespace(namespace: string): Promise<PodInfo[]> {
    return apiClient.get<PodInfo[]>(`${this.basePath}/${encodeURIComponent(namespace)}`);
  }

  /**
   * 获取指定Pod的详细信息
   * @param namespace 命名空间
   * @param podName Pod名称
   * @returns Pod详细信息
   */
  async getPodInfo(namespace: string, podName: string): Promise<PodInfo> {
    return apiClient.get<PodInfo>(
      `${this.basePath}/${encodeURIComponent(namespace)}/${encodeURIComponent(podName)}`
    );
  }

  // ============ 日志管理 ============

  /**
   * 获取Pod日志
   * @param request 日志请求参数
   * @returns 日志内容
   */
  async getPodLogs(request: PodLogsRequest): Promise<string> {
    const { namespace, pod, ...params } = request;
    
    // 构建查询参数
    const queryParams = new URLSearchParams();
    
    if (params.container) queryParams.append('container', params.container);
    if (params.follow !== undefined) queryParams.append('follow', params.follow.toString());
    if (params.previous !== undefined) queryParams.append('previous', params.previous.toString());
    if (params.since_seconds !== undefined) queryParams.append('sinceSeconds', params.since_seconds.toString());
    if (params.since_time) queryParams.append('sinceTime', params.since_time);
    if (params.timestamps !== undefined) queryParams.append('timestamps', params.timestamps.toString());
    if (params.tail_lines !== undefined) queryParams.append('tailLines', params.tail_lines.toString());
    if (params.limit_bytes !== undefined) queryParams.append('limitBytes', params.limit_bytes.toString());

    const url = `${this.basePath}/${encodeURIComponent(namespace)}/${encodeURIComponent(pod)}/logs`;
    const fullUrl = queryParams.toString() ? `${url}?${queryParams.toString()}` : url;

    return apiClient.get<string>(fullUrl);
  }

  // ============ 数据处理工具方法 ============

  /**
   * 解析日志行
   * @param logContent 日志内容
   * @returns 解析后的日志行数组
   */
  parseLogLines(logContent: string): LogLine[] {
    if (!logContent) return [];

    const lines = logContent.split('\n').filter(line => line.trim());
    
    return lines.map((line, index) => {
      const logLine: LogLine = {
        id: index + 1,
        content: line,
        timestamp: null,
        level: 'INFO',
        source: '',
      };

      // 尝试解析时间戳（如果启用了timestamps）
      const timestampMatch = line.match(/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z?)\s+(.*)$/);
      if (timestampMatch) {
        logLine.timestamp = timestampMatch[1];
        logLine.content = timestampMatch[2];
      }

      // 尝试解析日志级别
      const levelMatch = logLine.content.match(/\b(DEBUG|INFO|WARN|WARNING|ERROR|FATAL|TRACE)\b/i);
      if (levelMatch) {
        logLine.level = levelMatch[1].toUpperCase() as LogLevel;
      }

      // 尝试解析来源
      const sourceMatch = logLine.content.match(/\[([^\]]+)\]/);
      if (sourceMatch) {
        logLine.source = sourceMatch[1];
      }

      return logLine;
    });
  }

  /**
   * 过滤日志行
   * @param logLines 日志行数组
   * @param filters 过滤条件
   * @returns 过滤后的日志行
   */
  filterLogLines(logLines: LogLine[], filters: LogFilters): LogLine[] {
    return logLines.filter(line => {
      // 关键词过滤
      if (filters.keyword && !line.content.toLowerCase().includes(filters.keyword.toLowerCase())) {
        return false;
      }

      // 日志级别过滤
      if (filters.levels && filters.levels.length > 0 && !filters.levels.includes(line.level)) {
        return false;
      }

      // 时间范围过滤
      if (filters.startTime && line.timestamp) {
        const lineTime = new Date(line.timestamp);
        const startTime = new Date(filters.startTime);
        if (lineTime < startTime) {
          return false;
        }
      }

      if (filters.endTime && line.timestamp) {
        const lineTime = new Date(line.timestamp);
        const endTime = new Date(filters.endTime);
        if (lineTime > endTime) {
          return false;
        }
      }

      // 来源过滤
      if (filters.source && !line.source.toLowerCase().includes(filters.source.toLowerCase())) {
        return false;
      }

      return true;
    });
  }

  /**
   * 获取日志统计信息
   * @param logLines 日志行数组
   * @returns 统计信息
   */
  getLogStats(logLines: LogLine[]): LogStats {
    const stats: LogStats = {
      total: logLines.length,
      levels: {
        DEBUG: 0,
        INFO: 0,
        WARN: 0,
        WARNING: 0,
        ERROR: 0,
        FATAL: 0,
        TRACE: 0,
      },
      timeRange: {
        start: null,
        end: null,
      },
      sources: {},
    };

    logLines.forEach(line => {
      // 统计日志级别
      stats.levels[line.level]++;

      // 统计时间范围
      if (line.timestamp) {
        const timestamp = new Date(line.timestamp);
        if (!stats.timeRange.start || timestamp < new Date(stats.timeRange.start)) {
          stats.timeRange.start = line.timestamp;
        }
        if (!stats.timeRange.end || timestamp > new Date(stats.timeRange.end)) {
          stats.timeRange.end = line.timestamp;
        }
      }

      // 统计来源
      if (line.source) {
        stats.sources[line.source] = (stats.sources[line.source] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * 格式化日志行为显示文本
   * @param logLine 日志行
   * @param options 格式化选项
   * @returns 格式化后的文本
   */
  formatLogLine(logLine: LogLine, options: LogFormatOptions = {}): string {
    const parts: string[] = [];

    // 添加时间戳
    if (options.showTimestamp && logLine.timestamp) {
      const timestamp = options.timestampFormat 
        ? this.formatTimestamp(logLine.timestamp, options.timestampFormat)
        : logLine.timestamp;
      parts.push(`[${timestamp}]`);
    }

    // 添加日志级别
    if (options.showLevel) {
      parts.push(`[${logLine.level}]`);
    }

    // 添加来源
    if (options.showSource && logLine.source) {
      parts.push(`[${logLine.source}]`);
    }

    // 添加内容
    parts.push(logLine.content);

    return parts.join(' ');
  }

  /**
   * 格式化时间戳
   * @param timestamp 时间戳
   * @param format 格式
   * @returns 格式化后的时间戳
   */
  private formatTimestamp(timestamp: string, format: 'full' | 'time' | 'relative'): string {
    const date = new Date(timestamp);
    
    switch (format) {
      case 'time':
        return date.toLocaleTimeString();
      case 'relative':
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) return `${hours}h ago`;
        if (minutes > 0) return `${minutes}m ago`;
        return `${seconds}s ago`;
      case 'full':
      default:
        return date.toLocaleString();
    }
  }

  /**
   * 获取日志级别颜色
   * @param level 日志级别
   * @returns 颜色值
   */
  getLogLevelColor(level: LogLevel): string {
    const colorMap: Record<LogLevel, string> = {
      DEBUG: '#8c8c8c',    // 灰色
      INFO: '#1890ff',     // 蓝色
      WARN: '#faad14',     // 橙色
      WARNING: '#faad14',  // 橙色
      ERROR: '#ff4d4f',    // 红色
      FATAL: '#a8071a',    // 深红色
      TRACE: '#722ed1',    // 紫色
    };
    
    return colorMap[level] || '#000000';
  }

  /**
   * 获取Pod状态颜色
   * @param status Pod状态
   * @returns 颜色值
   */
  getPodStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      'Running': '#52c41a',     // 绿色
      'Pending': '#faad14',     // 橙色
      'Succeeded': '#52c41a',   // 绿色
      'Failed': '#ff4d4f',      // 红色
      'Unknown': '#d9d9d9',     // 灰色
    };
    
    return colorMap[status] || '#d9d9d9';
  }
}

// 类型定义
export type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'WARNING' | 'ERROR' | 'FATAL' | 'TRACE';

export interface LogLine {
  id: number;
  content: string;
  timestamp: string | null;
  level: LogLevel;
  source: string;
}

export interface LogFilters {
  keyword?: string;
  levels?: LogLevel[];
  startTime?: string;
  endTime?: string;
  source?: string;
}

export interface LogStats {
  total: number;
  levels: Record<LogLevel, number>;
  timeRange: {
    start: string | null;
    end: string | null;
  };
  sources: Record<string, number>;
}

export interface LogFormatOptions {
  showTimestamp?: boolean;
  showLevel?: boolean;
  showSource?: boolean;
  timestampFormat?: 'full' | 'time' | 'relative';
}

// 创建并导出日志服务实例
export const logsService = new LogsService();

// 默认导出
export default logsService;
