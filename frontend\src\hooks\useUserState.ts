import { useCallback } from 'react';
import { useUserStore, selectUserInfo, selectIsAuthenticated, selectPreferences, selectRecentPages, selectFavorites } from '@/stores';
import type { UserInfo, UserPreferences, RecentPage } from '@/stores';

/**
 * 用户状态管理Hook
 * 
 * 提供用户信息、认证状态、偏好设置等的访问和操作方法
 */
export const useUserState = () => {
  // 状态选择器
  const userInfo = useUserStore(selectUserInfo);
  const isAuthenticated = useUserStore(selectIsAuthenticated);
  const preferences = useUserStore(selectPreferences);
  const recentPages = useUserStore(selectRecentPages);
  const favorites = useUserStore(selectFavorites);

  // 操作方法
  const setUserInfo = useUserStore(state => state.setUserInfo);
  const updateUserInfo = useUserStore(state => state.updateUserInfo);
  const login = useUserStore(state => state.login);
  const logout = useUserStore(state => state.logout);
  const updatePreferences = useUserStore(state => state.updatePreferences);
  const addRecentPage = useUserStore(state => state.addRecentPage);
  const addFavorite = useUserStore(state => state.addFavorite);
  const removeFavorite = useUserStore(state => state.removeFavorite);
  const isFavorite = useUserStore(state => state.isFavorite);
  const hasPermission = useUserStore(state => state.hasPermission);
  const hasRole = useUserStore(state => state.hasRole);

  return {
    // 状态
    userInfo,
    isAuthenticated,
    preferences,
    recentPages,
    favorites,
    
    // 操作
    setUserInfo,
    updateUserInfo,
    login,
    logout,
    updatePreferences,
    addRecentPage,
    addFavorite,
    removeFavorite,
    isFavorite,
    hasPermission,
    hasRole,
  };
};

/**
 * 用户认证Hook
 */
export const useAuth = () => {
  const isAuthenticated = useUserStore(selectIsAuthenticated);
  const userInfo = useUserStore(selectUserInfo);
  const session = useUserStore(state => state.session);
  const login = useUserStore(state => state.login);
  const logout = useUserStore(state => state.logout);
  const isTokenValid = useUserStore(state => state.isTokenValid);

  const handleLogin = useCallback(async (credentials: { username: string; password: string }) => {
    // 这里可以调用登录API
    // const response = await authService.login(credentials);
    // login(response.token, response.refreshToken, response.expiresAt);
    
    // 临时实现
    console.log('Login with:', credentials);
    login('mock-token', 'mock-refresh-token', Date.now() + 24 * 60 * 60 * 1000);
  }, [login]);

  const handleLogout = useCallback(() => {
    logout();
    // 可以在这里添加额外的清理逻辑
  }, [logout]);

  return {
    isAuthenticated,
    userInfo,
    session,
    isTokenValid: isTokenValid(),
    login: handleLogin,
    logout: handleLogout,
  };
};

/**
 * 用户偏好设置Hook
 */
export const useUserPreferences = () => {
  const preferences = useUserStore(selectPreferences);
  const updatePreferences = useUserStore(state => state.updatePreferences);
  const resetPreferences = useUserStore(state => state.resetPreferences);

  const updatePreference = useCallback(<K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => {
    updatePreferences({ [key]: value });
  }, [updatePreferences]);

  return {
    preferences,
    updatePreferences,
    updatePreference,
    resetPreferences,
  };
};

/**
 * 最近访问页面Hook
 */
export const useRecentPages = () => {
  const recentPages = useUserStore(selectRecentPages);
  const addRecentPage = useUserStore(state => state.addRecentPage);
  const removeRecentPage = useUserStore(state => state.removeRecentPage);
  const clearRecentPages = useUserStore(state => state.clearRecentPages);

  const addPage = useCallback((page: Omit<RecentPage, 'timestamp'>) => {
    addRecentPage(page);
  }, [addRecentPage]);

  return {
    recentPages,
    addPage,
    removeRecentPage,
    clearRecentPages,
  };
};

/**
 * 收藏功能Hook
 */
export const useFavorites = () => {
  const favorites = useUserStore(selectFavorites);
  const addFavorite = useUserStore(state => state.addFavorite);
  const removeFavorite = useUserStore(state => state.removeFavorite);
  const isFavorite = useUserStore(state => state.isFavorite);

  const toggleFavorite = useCallback((path: string) => {
    if (isFavorite(path)) {
      removeFavorite(path);
    } else {
      addFavorite(path);
    }
  }, [isFavorite, addFavorite, removeFavorite]);

  return {
    favorites,
    addFavorite,
    removeFavorite,
    isFavorite,
    toggleFavorite,
  };
};

/**
 * 权限检查Hook
 */
export const usePermissions = () => {
  const userInfo = useUserStore(selectUserInfo);
  const hasPermission = useUserStore(state => state.hasPermission);
  const hasRole = useUserStore(state => state.hasRole);

  const isAdmin = useCallback(() => {
    return hasRole('admin');
  }, [hasRole]);

  const canAccess = useCallback((permission: string) => {
    return hasPermission(permission);
  }, [hasPermission]);

  const canAccessMultiple = useCallback((permissions: string[]) => {
    return permissions.every(permission => hasPermission(permission));
  }, [hasPermission]);

  const canAccessAny = useCallback((permissions: string[]) => {
    return permissions.some(permission => hasPermission(permission));
  }, [hasPermission]);

  return {
    userInfo,
    permissions: userInfo?.permissions || [],
    role: userInfo?.role,
    isAdmin,
    canAccess,
    canAccessMultiple,
    canAccessAny,
    hasPermission,
    hasRole,
  };
};

/**
 * 用户操作历史Hook
 */
export const useUserActions = () => {
  const actionHistory = useUserStore(state => state.actionHistory);
  const addAction = useUserStore(state => state.addAction);
  const clearActionHistory = useUserStore(state => state.clearActionHistory);

  const recordAction = useCallback((type: string, description: string, metadata?: Record<string, any>) => {
    addAction({
      type,
      description,
      metadata,
    });
  }, [addAction]);

  // 便捷方法
  const recordPageVisit = useCallback((path: string, title: string) => {
    recordAction('page_visit', `访问页面: ${title}`, { path, title });
  }, [recordAction]);

  const recordOperation = useCallback((operation: string, target: string, result: 'success' | 'failed') => {
    recordAction('operation', `${operation}: ${target}`, { operation, target, result });
  }, [recordAction]);

  return {
    actionHistory,
    recordAction,
    recordPageVisit,
    recordOperation,
    clearActionHistory,
  };
};

export default useUserState;
