import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import {
  Table,
  Input,
  Button,
  Space,
  Dropdown,
  Tooltip,
  Card,
  Tag,
  Select,
  Checkbox
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FilterOutlined,
  SettingOutlined,
  ColumnHeightOutlined
} from '@ant-design/icons';
import type { ColumnsType, TableProps } from 'antd/es/table';
import type { MenuProps } from 'antd';
import { useTableKeyboard } from '@/hooks/useKeyboard';
import { performanceMonitor } from '@/utils/performance';

const { Option } = Select;

// 数据表格属性
export interface DataTableProps<T = any> extends Omit<TableProps<T>, 'columns'> {
  columns: ColumnsType<T>;
  data?: T[];
  loading?: boolean;
  searchable?: boolean;
  searchPlaceholder?: string;
  exportable?: boolean;
  refreshable?: boolean;
  filterable?: boolean;
  title?: string;
  extra?: React.ReactNode;
  onRefresh?: () => void;
  onExport?: (format: 'csv' | 'excel' | 'json') => void;
  onSearch?: (value: string) => void;
  className?: string;
  style?: React.CSSProperties;
  // 性能优化选项
  virtualScroll?: boolean;
  pageSize?: number;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  // 快捷键支持
  keyboardShortcuts?: boolean;
  // 列设置
  columnSettings?: boolean;
}

/**
 * 通用数据表格组件
 * 
 * 功能特性：
 * - 搜索功能
 * - 数据导出
 * - 刷新功能
 * - 列筛选
 * - 分页支持
 * - 响应式设计
 */
export const DataTable = <T extends Record<string, any>>({
  columns,
  data = [],
  loading = false,
  searchable = true,
  searchPlaceholder = '搜索...',
  exportable = true,
  refreshable = true,
  filterable = true,
  title,
  extra,
  onRefresh,
  onExport,
  onSearch,
  className,
  style,
  virtualScroll = false,
  pageSize = 10,
  showSizeChanger = true,
  showQuickJumper = true,
  keyboardShortcuts = true,
  columnSettings = true,
  ...tableProps
}: DataTableProps<T>) => {
  const [searchValue, setSearchValue] = useState('');
  const [filteredData, setFilteredData] = useState<T[]>(data);
  const [visibleColumns, setVisibleColumns] = useState<string[]>([]);
  const [tableSize, setTableSize] = useState<'small' | 'middle' | 'large'>('middle');
  const tableRef = useRef<HTMLDivElement>(null);

  // 性能监控
  useEffect(() => {
    performanceMonitor.mark('table-render-start');
    return () => {
      performanceMonitor.mark('table-render-end');
      const duration = performanceMonitor.measure(
        'table-render-duration',
        'table-render-start',
        'table-render-end'
      );
      if (duration > 100) {
        console.warn(`Table render took ${duration}ms, consider optimization`);
      }
    };
  }, [data]);

  // 快捷键支持
  useTableKeyboard({
    onRefresh: keyboardShortcuts ? onRefresh : undefined,
    onAdd: undefined,
    onDelete: undefined,
    onEdit: undefined,
  });

  // 初始化可见列
  useEffect(() => {
    if (visibleColumns.length === 0) {
      setVisibleColumns(columns.map((col: any) => col.key || col.dataIndex));
    }
  }, [columns, visibleColumns.length]);

  // 处理搜索 - 使用防抖优化
  const handleSearch = useCallback((value: string) => {
    setSearchValue(value);

    if (!value.trim()) {
      setFilteredData(data);
      onSearch?.('');
      return;
    }

    // 优化的全文搜索 - 只搜索可见列
    const searchLower = value.toLowerCase();
    const filtered = data.filter(item => {
      return visibleColumns.some(colKey => {
        const val = item[colKey];
        return val && String(val).toLowerCase().includes(searchLower);
      });
    });

    setFilteredData(filtered);
    onSearch?.(value);
  }, [data, onSearch, visibleColumns]);

  // 更新数据时重置筛选
  React.useEffect(() => {
    if (searchValue) {
      handleSearch(searchValue);
    } else {
      setFilteredData(data);
    }
  }, [data, searchValue]);

  // 导出菜单
  const exportMenuItems: MenuProps['items'] = [
    {
      key: 'csv',
      label: 'CSV格式',
      onClick: () => onExport?.('csv'),
    },
    {
      key: 'excel',
      label: 'Excel格式',
      onClick: () => onExport?.('excel'),
    },
    {
      key: 'json',
      label: 'JSON格式',
      onClick: () => onExport?.('json'),
    },
  ];

  // 列设置菜单
  const columnSettingsMenu: MenuProps['items'] = useMemo(() => {
    if (!columnSettings) return [];

    return [
      {
        key: 'columns',
        label: '显示列',
        children: columns.map((col: any) => ({
          key: col.key || col.dataIndex,
          label: (
            <Checkbox
              checked={visibleColumns.includes(col.key || col.dataIndex)}
              onChange={(e) => {
                const colKey = col.key || col.dataIndex;
                if (e.target.checked) {
                  setVisibleColumns([...visibleColumns, colKey]);
                } else {
                  setVisibleColumns(visibleColumns.filter(key => key !== colKey));
                }
              }}
            >
              {col.title}
            </Checkbox>
          ),
        })),
      },
      {
        type: 'divider',
      },
      {
        key: 'size',
        label: '表格大小',
        children: [
          {
            key: 'small',
            label: (
              <div onClick={() => setTableSize('small')}>
                紧凑 {tableSize === 'small' && '✓'}
              </div>
            ),
          },
          {
            key: 'middle',
            label: (
              <div onClick={() => setTableSize('middle')}>
                默认 {tableSize === 'middle' && '✓'}
              </div>
            ),
          },
          {
            key: 'large',
            label: (
              <div onClick={() => setTableSize('large')}>
                宽松 {tableSize === 'large' && '✓'}
              </div>
            ),
          },
        ],
      },
    ];
  }, [columns, visibleColumns, columnSettings, tableSize]);

  // 过滤可见列
  const visibleColumnsData = useMemo(() => {
    return columns.filter((col: any) =>
      visibleColumns.includes(col.key || col.dataIndex)
    );
  }, [columns, visibleColumns]);

  // 渲染工具栏
  const renderToolbar = () => {
    if (!searchable && !exportable && !refreshable && !extra) {
      return null;
    }

    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '16px',
        flexWrap: 'wrap',
        gap: '8px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {searchable && (
            <Input
              placeholder={searchPlaceholder}
              prefix={<SearchOutlined />}
              value={searchValue}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: 250 }}
              allowClear
            />
          )}
          
          {filterable && (
            <Tooltip title="筛选">
              <Button icon={<FilterOutlined />} />
            </Tooltip>
          )}
        </div>

        <Space>
          {extra}
          
          {refreshable && (
            <Tooltip title="刷新">
              <Button 
                icon={<ReloadOutlined />} 
                loading={loading}
                onClick={onRefresh}
              />
            </Tooltip>
          )}
          
          {exportable && (
            <Dropdown menu={{ items: exportMenuItems }} trigger={['click']}>
              <Button icon={<DownloadOutlined />}>
                导出
              </Button>
            </Dropdown>
          )}

          {columnSettings && (
            <Dropdown menu={{ items: columnSettingsMenu }} trigger={['click']}>
              <Tooltip title="列设置">
                <Button icon={<ColumnHeightOutlined />} />
              </Tooltip>
            </Dropdown>
          )}

          <Tooltip title="设置">
            <Button icon={<SettingOutlined />} />
          </Tooltip>
        </Space>
      </div>
    );
  };

  // 增强的分页配置
  const paginationConfig = {
    pageSize,
    showSizeChanger,
    showQuickJumper,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    pageSizeOptions: ['10', '20', '50', '100', '200'],
    ...tableProps.pagination,
  };

  // 表格内容
  const tableContent = (
    <>
      {renderToolbar()}
      <Table
        {...tableProps}
        ref={tableRef}
        columns={visibleColumnsData}
        dataSource={filteredData}
        loading={loading}
        pagination={paginationConfig}
        scroll={{
          x: 'max-content',
          ...(virtualScroll && { y: 400 })
        }}
        size={tableSize}
        className={className}
      />
    </>
  );

  // 如果有标题，包装在Card中
  if (title) {
    return (
      <Card title={title} style={style}>
        {tableContent}
      </Card>
    );
  }

  return (
    <div style={style}>
      {tableContent}
    </div>
  );
};

// 表格状态标签组件
export interface StatusTagProps {
  status: 'success' | 'warning' | 'error' | 'default' | 'processing';
  text: string;
  tooltip?: string;
}

export const StatusTag: React.FC<StatusTagProps> = ({ status, text, tooltip }) => {
  const colorMap = {
    success: 'green',
    warning: 'orange',
    error: 'red',
    default: 'default',
    processing: 'blue',
  };

  const tag = (
    <Tag color={colorMap[status]}>
      {text}
    </Tag>
  );

  return tooltip ? <Tooltip title={tooltip}>{tag}</Tooltip> : tag;
};

// 表格操作按钮组件
export interface ActionButtonsProps {
  actions: Array<{
    key: string;
    label: string;
    icon?: React.ReactNode;
    type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
    danger?: boolean;
    disabled?: boolean;
    onClick: () => void;
  }>;
  size?: 'small' | 'middle' | 'large';
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({ 
  actions, 
  size = 'small' 
}) => {
  return (
    <Space size="small">
      {actions.map(action => (
        <Button
          key={action.key}
          type={action.type || 'text'}
          size={size}
          icon={action.icon}
          danger={action.danger}
          disabled={action.disabled}
          onClick={action.onClick}
        >
          {action.label}
        </Button>
      ))}
    </Space>
  );
};

export default DataTable;
