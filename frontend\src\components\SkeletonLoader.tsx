import React from 'react';
import { Skeleton, Card, Row, Col } from 'antd';

// 骨架屏类型
export type SkeletonType = 
  | 'card' 
  | 'list' 
  | 'table' 
  | 'chart' 
  | 'form' 
  | 'dashboard'
  | 'detail';

// 骨架屏属性
export interface SkeletonLoaderProps {
  type?: SkeletonType;
  rows?: number;
  loading?: boolean;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 骨架屏加载组件
 * 
 * 功能特性：
 * - 多种骨架屏类型
 * - 自定义行数和样式
 * - 条件渲染支持
 * - 响应式设计
 */
export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  type = 'card',
  rows = 3,
  loading = true,
  children,
  className,
  style,
}) => {
  // 如果不在加载状态，直接渲染子组件
  if (!loading && children) {
    return <>{children}</>;
  }

  // 渲染不同类型的骨架屏
  const renderSkeleton = () => {
    switch (type) {
      case 'card':
        return (
          <Card className={className} style={style}>
            <Skeleton active paragraph={{ rows }} />
          </Card>
        );

      case 'list':
        return (
          <div className={className} style={style}>
            {Array.from({ length: rows }, (_, index) => (
              <div key={index} style={{ marginBottom: '16px' }}>
                <Skeleton active avatar paragraph={{ rows: 2 }} />
              </div>
            ))}
          </div>
        );

      case 'table':
        return (
          <Card className={className} style={style}>
            <Skeleton.Input active style={{ width: '200px', marginBottom: '16px' }} />
            <Skeleton active paragraph={{ rows: rows * 2 }} />
          </Card>
        );

      case 'chart':
        return (
          <Card className={className} style={style}>
            <Skeleton.Input active style={{ width: '150px', marginBottom: '16px' }} />
            <div style={{ 
              height: '300px', 
              backgroundColor: '#f5f5f5', 
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#bfbfbf'
            }}>
              图表加载中...
            </div>
          </Card>
        );

      case 'form':
        return (
          <Card className={className} style={style}>
            {Array.from({ length: rows }, (_, index) => (
              <div key={index} style={{ marginBottom: '24px' }}>
                <Skeleton.Input active style={{ width: '100px', marginBottom: '8px' }} />
                <Skeleton.Input active style={{ width: '100%' }} />
              </div>
            ))}
          </Card>
        );

      case 'dashboard':
        return (
          <div className={className} style={style}>
            <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
              {Array.from({ length: 4 }, (_, index) => (
                <Col key={index} span={6}>
                  <Card>
                    <Skeleton active paragraph={{ rows: 1 }} />
                  </Card>
                </Col>
              ))}
            </Row>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card>
                  <div style={{ 
                    height: '300px', 
                    backgroundColor: '#f5f5f5', 
                    borderRadius: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#bfbfbf'
                  }}>
                    图表加载中...
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card>
                  <Skeleton active paragraph={{ rows: 8 }} />
                </Card>
              </Col>
            </Row>
          </div>
        );

      case 'detail':
        return (
          <div className={className} style={style}>
            <Card style={{ marginBottom: '16px' }}>
              <Skeleton.Input active style={{ width: '300px', marginBottom: '16px' }} />
              <Skeleton active paragraph={{ rows: 2 }} />
            </Card>
            <Row gutter={[16, 16]}>
              <Col span={16}>
                <Card>
                  <Skeleton active paragraph={{ rows: 6 }} />
                </Card>
              </Col>
              <Col span={8}>
                <Card>
                  <Skeleton active paragraph={{ rows: 4 }} />
                </Card>
              </Col>
            </Row>
          </div>
        );

      default:
        return (
          <div className={className} style={style}>
            <Skeleton active paragraph={{ rows }} />
          </div>
        );
    }
  };

  return renderSkeleton();
};

// 预定义的骨架屏组件
export const CardSkeleton: React.FC<Omit<SkeletonLoaderProps, 'type'>> = (props) => (
  <SkeletonLoader {...props} type="card" />
);

export const ListSkeleton: React.FC<Omit<SkeletonLoaderProps, 'type'>> = (props) => (
  <SkeletonLoader {...props} type="list" />
);

export const TableSkeleton: React.FC<Omit<SkeletonLoaderProps, 'type'>> = (props) => (
  <SkeletonLoader {...props} type="table" />
);

export const ChartSkeleton: React.FC<Omit<SkeletonLoaderProps, 'type'>> = (props) => (
  <SkeletonLoader {...props} type="chart" />
);

export const FormSkeleton: React.FC<Omit<SkeletonLoaderProps, 'type'>> = (props) => (
  <SkeletonLoader {...props} type="form" />
);

export const DashboardSkeleton: React.FC<Omit<SkeletonLoaderProps, 'type'>> = (props) => (
  <SkeletonLoader {...props} type="dashboard" />
);

export const DetailSkeleton: React.FC<Omit<SkeletonLoaderProps, 'type'>> = (props) => (
  <SkeletonLoader {...props} type="detail" />
);

export default SkeletonLoader;
