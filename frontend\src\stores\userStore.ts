import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// 用户信息接口
export interface UserInfo {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  role: 'admin' | 'user' | 'viewer';
  permissions: string[];
  lastLoginAt?: string;
  createdAt?: string;
}

// 用户偏好设置接口
export interface UserPreferences {
  // 仪表板偏好
  dashboardLayout: 'grid' | 'list';
  dashboardRefreshInterval: number;
  
  // 日志偏好
  logsPageSize: number;
  logsAutoScroll: boolean;
  logsShowTimestamp: boolean;
  logsShowLevel: boolean;
  logsWrapLines: boolean;
  
  // 表格偏好
  tablePageSize: number;
  tableDensity: 'default' | 'middle' | 'small';
  
  // 图表偏好
  chartTheme: 'default' | 'dark';
  chartAnimations: boolean;
  
  // 快捷键偏好
  keyboardShortcuts: boolean;
  
  // 其他偏好
  timezone: string;
  dateFormat: string;
  numberFormat: string;
}

// 用户会话信息
export interface UserSession {
  token?: string;
  refreshToken?: string;
  expiresAt?: number;
  isAuthenticated: boolean;
  loginTime?: number;
}

// 用户状态接口
export interface UserState {
  // 用户信息
  userInfo: UserInfo | null;
  
  // 会话信息
  session: UserSession;
  
  // 用户偏好
  preferences: UserPreferences;
  
  // 最近访问的页面
  recentPages: RecentPage[];
  
  // 收藏的功能
  favorites: string[];
  
  // 用户操作历史
  actionHistory: UserAction[];
}

// 最近访问页面
export interface RecentPage {
  path: string;
  title: string;
  timestamp: number;
  icon?: string;
}

// 用户操作记录
export interface UserAction {
  id: string;
  type: string;
  description: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

// 用户操作接口
export interface UserActions {
  // 用户信息操作
  setUserInfo: (userInfo: UserInfo | null) => void;
  updateUserInfo: (updates: Partial<UserInfo>) => void;
  
  // 会话操作
  setSession: (session: Partial<UserSession>) => void;
  login: (token: string, refreshToken?: string, expiresAt?: number) => void;
  logout: () => void;
  isTokenValid: () => boolean;
  
  // 偏好设置操作
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  resetPreferences: () => void;
  
  // 最近页面操作
  addRecentPage: (page: Omit<RecentPage, 'timestamp'>) => void;
  removeRecentPage: (path: string) => void;
  clearRecentPages: () => void;
  
  // 收藏操作
  addFavorite: (path: string) => void;
  removeFavorite: (path: string) => void;
  isFavorite: (path: string) => boolean;
  
  // 操作历史
  addAction: (action: Omit<UserAction, 'id' | 'timestamp'>) => void;
  clearActionHistory: () => void;
  
  // 权限检查
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  
  // 重置操作
  resetUserState: () => void;
}

// 默认偏好设置
const defaultPreferences: UserPreferences = {
  dashboardLayout: 'grid',
  dashboardRefreshInterval: 30,
  logsPageSize: 100,
  logsAutoScroll: true,
  logsShowTimestamp: true,
  logsShowLevel: true,
  logsWrapLines: false,
  tablePageSize: 20,
  tableDensity: 'default',
  chartTheme: 'default',
  chartAnimations: true,
  keyboardShortcuts: true,
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  dateFormat: 'YYYY-MM-DD HH:mm:ss',
  numberFormat: 'en-US',
};

// 初始状态
const initialState: UserState = {
  userInfo: null,
  session: {
    isAuthenticated: false,
  },
  preferences: defaultPreferences,
  recentPages: [],
  favorites: [],
  actionHistory: [],
};

// 创建用户状态store
export const useUserStore = create<UserState & UserActions>()(
  persist(
    (set, get) => ({
      ...initialState,

      // 用户信息操作
      setUserInfo: (userInfo: UserInfo | null) => {
        set({ userInfo });
      },

      updateUserInfo: (updates: Partial<UserInfo>) => {
        set((state) => ({
          userInfo: state.userInfo ? { ...state.userInfo, ...updates } : null,
        }));
      },

      // 会话操作
      setSession: (sessionUpdates: Partial<UserSession>) => {
        set((state) => ({
          session: { ...state.session, ...sessionUpdates },
        }));
      },

      login: (token: string, refreshToken?: string, expiresAt?: number) => {
        set({
          session: {
            token,
            refreshToken,
            expiresAt,
            isAuthenticated: true,
            loginTime: Date.now(),
          },
        });
      },

      logout: () => {
        set({
          userInfo: null,
          session: {
            isAuthenticated: false,
          },
        });
      },

      isTokenValid: () => {
        const { session } = get();
        if (!session.token || !session.isAuthenticated) {
          return false;
        }
        
        if (session.expiresAt && Date.now() > session.expiresAt) {
          return false;
        }
        
        return true;
      },

      // 偏好设置操作
      updatePreferences: (newPreferences: Partial<UserPreferences>) => {
        set((state) => ({
          preferences: { ...state.preferences, ...newPreferences },
        }));
      },

      resetPreferences: () => {
        set({ preferences: defaultPreferences });
      },

      // 最近页面操作
      addRecentPage: (page: Omit<RecentPage, 'timestamp'>) => {
        set((state) => {
          const existingIndex = state.recentPages.findIndex(p => p.path === page.path);
          let newRecentPages = [...state.recentPages];
          
          const newPage: RecentPage = {
            ...page,
            timestamp: Date.now(),
          };
          
          if (existingIndex >= 0) {
            // 更新现有页面的时间戳
            newRecentPages[existingIndex] = newPage;
          } else {
            // 添加新页面
            newRecentPages.unshift(newPage);
          }
          
          // 保持最多10个最近页面
          newRecentPages = newRecentPages.slice(0, 10);
          
          return { recentPages: newRecentPages };
        });
      },

      removeRecentPage: (path: string) => {
        set((state) => ({
          recentPages: state.recentPages.filter(p => p.path !== path),
        }));
      },

      clearRecentPages: () => {
        set({ recentPages: [] });
      },

      // 收藏操作
      addFavorite: (path: string) => {
        set((state) => ({
          favorites: state.favorites.includes(path) 
            ? state.favorites 
            : [...state.favorites, path],
        }));
      },

      removeFavorite: (path: string) => {
        set((state) => ({
          favorites: state.favorites.filter(f => f !== path),
        }));
      },

      isFavorite: (path: string) => {
        return get().favorites.includes(path);
      },

      // 操作历史
      addAction: (action: Omit<UserAction, 'id' | 'timestamp'>) => {
        const newAction: UserAction = {
          ...action,
          id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
        };

        set((state) => ({
          actionHistory: [newAction, ...state.actionHistory].slice(0, 100), // 最多保留100条
        }));
      },

      clearActionHistory: () => {
        set({ actionHistory: [] });
      },

      // 权限检查
      hasPermission: (permission: string) => {
        const { userInfo } = get();
        return userInfo?.permissions.includes(permission) || false;
      },

      hasRole: (role: string) => {
        const { userInfo } = get();
        return userInfo?.role === role;
      },

      // 重置操作
      resetUserState: () => {
        set(initialState);
      },
    }),
    {
      name: 'k8s-helper-user-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        userInfo: state.userInfo,
        session: state.session,
        preferences: state.preferences,
        recentPages: state.recentPages,
        favorites: state.favorites,
        // 不持久化操作历史，因为它可能很大
      }),
    }
  )
);

// 导出状态选择器
export const selectUserInfo = (state: UserState & UserActions) => state.userInfo;
export const selectIsAuthenticated = (state: UserState & UserActions) => state.session.isAuthenticated;
export const selectPreferences = (state: UserState & UserActions) => state.preferences;
export const selectRecentPages = (state: UserState & UserActions) => state.recentPages;
export const selectFavorites = (state: UserState & UserActions) => state.favorites;
export const selectActionHistory = (state: UserState & UserActions) => state.actionHistory;

// 导出默认store
export default useUserStore;
