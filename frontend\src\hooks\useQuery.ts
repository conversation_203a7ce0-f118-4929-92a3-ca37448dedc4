import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { queryKeys, queryOptions } from '@/providers/QueryProvider';
import { 
  etcdService, 
  clusterService, 
  logsService, 
  configService, 
  monitoringService, 
  portForwardService, 
  cleanupService 
} from '@/services';
import type {
  ETCDBackupRequest,
  ETCDBackupResponse,
  ETCDRestoreRequest,
  ETCDCronJobRequest,
  ClusterInfoResponse,
  PodInfo,
  PodLogsRequest,
  PortForwardRequest,
  CleanupScanRequest,
  CleanupExecuteRequest,
} from '@/types/api';

// ============ ETCD相关查询 ============

/**
 * 获取ETCD状态
 */
export const useETCDStatus = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.etcd.status(),
    queryFn: () => etcdService.getStatus(),
    ...queryOptions.fast,
    ...options,
  });
};

/**
 * 获取ETCD备份列表
 */
export const useETCDBackups = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.etcd.backups(),
    queryFn: () => etcdService.listBackups(),
    ...queryOptions.static,
    ...options,
  });
};

/**
 * 获取ETCD CronJob列表
 */
export const useETCDCronJobs = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.etcd.cronjobs(),
    queryFn: () => etcdService.listCronJobs(),
    ...queryOptions.static,
    ...options,
  });
};

/**
 * 创建ETCD备份
 */
export const useCreateETCDBackup = (options?: UseMutationOptions<ETCDBackupResponse, Error, ETCDBackupRequest>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (request: ETCDBackupRequest) => etcdService.createBackup(request),
    onSuccess: () => {
      // 刷新备份列表
      queryClient.invalidateQueries({ queryKey: queryKeys.etcd.backups() });
    },
    ...options,
  });
};

/**
 * 恢复ETCD备份
 */
export const useETCDRestore = (options?: UseMutationOptions<any, Error, any>) => {
  return useMutation({
    mutationFn: (request: any) => etcdService.restoreBackup(request),
    ...options,
  });
};

/**
 * 创建ETCD CronJob
 */
export const useCreateETCDCronJob = (options?: UseMutationOptions<any, Error, ETCDCronJobRequest>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: ETCDCronJobRequest) => etcdService.createCronJob(request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.etcd.cronjobs() });
    },
    ...options,
  });
};

/**
 * 更新ETCD CronJob
 */
export const useUpdateETCDCronJob = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: any) => etcdService.updateCronJob(request.id, request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.etcd.cronjobs() });
    },
    ...options,
  });
};

/**
 * 删除ETCD CronJob
 */
export const useDeleteETCDCronJob = (options?: UseMutationOptions<any, Error, string>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => etcdService.deleteCronJob(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.etcd.cronjobs() });
    },
    ...options,
  });
};

// ============ 集群相关查询 ============

/**
 * 获取集群信息
 */
export const useClusterInfo = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.cluster.info(),
    queryFn: () => clusterService.getClusterInfo(),
    ...queryOptions.fast,
    ...options,
  });
};

/**
 * 获取集群健康状态
 */
export const useClusterHealth = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.cluster.health(),
    queryFn: () => clusterService.getHealthStatus(),
    ...queryOptions.realtime,
    ...options,
  });
};

/**
 * 获取集群节点列表
 */
export const useClusterNodes = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.cluster.nodes(),
    queryFn: () => clusterService.getNodes(),
    ...queryOptions.fast,
    ...options,
  });
};

/**
 * 获取集群Pod列表
 */
export const useClusterPods = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.cluster.pods(),
    queryFn: () => clusterService.getPods(),
    ...queryOptions.fast,
    ...options,
  });
};

/**
 * 获取集群服务列表
 */
export const useClusterServices = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.cluster.services(),
    queryFn: () => clusterService.getServices(),
    ...queryOptions.fast,
    ...options,
  });
};

/**
 * 获取集群指标数据
 */
export const useClusterMetrics = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.cluster.metrics(),
    queryFn: () => clusterService.getMetrics(),
    ...queryOptions.realtime,
    ...options,
  });
};

// ============ Pod和日志相关查询 ============

/**
 * 获取所有Pod列表
 */
export const usePods = (namespace?: string, options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.pods.list(namespace),
    queryFn: () => namespace ? logsService.listPodsInNamespace(namespace) : logsService.listAllPods(),
    ...queryOptions.fast,
    ...options,
  });
};

/**
 * 获取Pod详情
 */
export const usePodInfo = (namespace: string, podName: string, options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.pods.detail(namespace, podName),
    queryFn: () => logsService.getPodInfo(namespace, podName),
    enabled: !!(namespace && podName),
    ...queryOptions.fast,
    ...options,
  });
};

/**
 * 获取Pod日志
 */
export const usePodLogs = (request: PodLogsRequest, options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.pods.logs(request.namespace, request.pod, request.container),
    queryFn: () => logsService.getPodLogs(request),
    enabled: !!(request.namespace && request.pod),
    ...queryOptions.once, // 日志通常是一次性获取
    ...options,
  });
};

// ============ 监控相关查询 ============

/**
 * 获取系统指标
 */
export const useSystemMetrics = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.monitoring.metrics(),
    queryFn: () => monitoringService.getSystemMetrics(),
    ...queryOptions.realtime,
    ...options,
  });
};

/**
 * 获取系统配置
 */
export const useSystemConfig = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.system.config(),
    queryFn: () => systemService.getConfig(),
    ...queryOptions.standard,
    ...options,
  });
};

/**
 * 更新系统配置
 */
export const useUpdateSystemConfig = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (config: any) => systemService.updateConfig(config),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.system.config() });
    },
    ...options,
  });
};

// ==================== 监控告警相关 ====================

/**
 * 获取监控指标数据
 */
export const useMonitoringMetrics = (params?: { timeRange?: string }, options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.monitoring.metrics(params?.timeRange),
    queryFn: () => monitoringService.getMetrics(params),
    ...queryOptions.realtime,
    ...options,
  });
};

/**
 * 获取告警列表
 */
export const useAlerts = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.monitoring.alerts(),
    queryFn: () => monitoringService.getAlerts(),
    ...queryOptions.fast,
    ...options,
  });
};

/**
 * 获取告警规则
 */
export const useAlertRules = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.monitoring.rules(),
    queryFn: () => monitoringService.getAlertRules(),
    ...queryOptions.standard,
    ...options,
  });
};

/**
 * 确认告警
 */
export const useAcknowledgeAlert = (options?: UseMutationOptions<any, Error, string>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (alertId: string) => monitoringService.acknowledgeAlert(alertId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.monitoring.alerts() });
    },
    ...options,
  });
};

/**
 * 解决告警
 */
export const useResolveAlert = (options?: UseMutationOptions<any, Error, string>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (alertId: string) => monitoringService.resolveAlert(alertId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.monitoring.alerts() });
    },
    ...options,
  });
};

/**
 * 静默告警
 */
export const useSilenceAlert = (options?: UseMutationOptions<any, Error, { alertId: string; duration: number }>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ alertId, duration }: { alertId: string; duration: number }) =>
      monitoringService.silenceAlert(alertId, duration),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.monitoring.alerts() });
    },
    ...options,
  });
};

/**
 * 创建告警规则
 */
export const useCreateAlertRule = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (rule: any) => monitoringService.createAlertRule(rule),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.monitoring.rules() });
    },
    ...options,
  });
};

/**
 * 更新告警规则
 */
export const useUpdateAlertRule = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ ruleId, ...rule }: any) => monitoringService.updateAlertRule(ruleId, rule),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.monitoring.rules() });
    },
    ...options,
  });
};

/**
 * 删除告警规则
 */
export const useDeleteAlertRule = (options?: UseMutationOptions<any, Error, string>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ruleId: string) => monitoringService.deleteAlertRule(ruleId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.monitoring.rules() });
    },
    ...options,
  });
};

// ==================== 端口转发相关 ====================

/**
 * 获取端口转发列表
 */
export const usePortForwards = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.portForward.list(),
    queryFn: () => portForwardService.getPortForwards(),
    ...queryOptions.fast,
    ...options,
  });
};

/**
 * 创建端口转发
 */
export const useCreatePortForward = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: any) => portForwardService.createPortForward(request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.portForward.list() });
    },
    ...options,
  });
};

/**
 * 停止端口转发
 */
export const useStopPortForward = (options?: UseMutationOptions<any, Error, string>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => portForwardService.stopPortForward(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.portForward.list() });
    },
    ...options,
  });
};

/**
 * 删除端口转发
 */
export const useDeletePortForward = (options?: UseMutationOptions<any, Error, string>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => portForwardService.deletePortForward(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.portForward.list() });
    },
    ...options,
  });
};

// ==================== 资源清理相关 ====================

/**
 * 资源扫描
 */
export const useResourceScan = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.cleanup.scan(),
    queryFn: () => cleanupService.scanResources(),
    ...queryOptions.standard,
    enabled: false, // 手动触发
    ...options,
  });
};

/**
 * 获取清理规则
 */
export const useCleanupRules = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.cleanup.rules(),
    queryFn: () => cleanupService.getCleanupRules(),
    ...queryOptions.standard,
    ...options,
  });
};

/**
 * 清理资源
 */
export const useCleanupResources = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: any) => cleanupService.cleanupResources(request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.cleanup.scan() });
    },
    ...options,
  });
};

/**
 * 创建清理规则
 */
export const useCreateCleanupRule = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (rule: any) => cleanupService.createCleanupRule(rule),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.cleanup.rules() });
    },
    ...options,
  });
};

/**
 * 获取监控健康状态
 */
export const useMonitoringHealth = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.monitoring.health(),
    queryFn: () => monitoringService.getHealth(),
    ...queryOptions.realtime,
    ...options,
  });
};

/**
 * 获取性能数据
 */
export const usePerformanceData = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.monitoring.performance(),
    queryFn: () => monitoringService.getPerformance(),
    ...queryOptions.fast,
    ...options,
  });
};



/**
 * 执行资源清理
 */
export const useExecuteCleanup = (options?: UseMutationOptions<any, Error, CleanupExecuteRequest>) => {
  return useMutation({
    mutationFn: (request: CleanupExecuteRequest) => cleanupService.executeCleanup(request),
    ...options,
  });
};

// ============ 配置相关查询 ============

/**
 * 获取完整配置
 */
export const useConfig = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.config.full(),
    queryFn: () => configService.getConfig(),
    ...queryOptions.static,
    ...options,
  });
};

/**
 * 获取配置段
 */
export const useConfigSection = (section: string, options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.config.section(section),
    queryFn: () => configService.getConfigSection(section),
    enabled: !!section,
    ...queryOptions.static,
    ...options,
  });
};

/**
 * 更新配置段
 */
export const useUpdateConfigSection = (options?: UseMutationOptions<any, Error, { section: string; config: Record<string, any> }>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ section, config }) => configService.updateConfigSection(section, config),
    onSuccess: (_, { section }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.config.section(section) });
      queryClient.invalidateQueries({ queryKey: queryKeys.config.full() });
    },
    ...options,
  });
};
