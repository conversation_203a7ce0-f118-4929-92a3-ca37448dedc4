package cmd

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"k8s-helper/internal/container"
	"k8s-helper/internal/handler"
	"k8s-helper/pkg/cmdhelp"
)

var (
	monitoringPort     int
	monitoringHost     string
	monitoringTLS      bool
	monitoringCertFile string
	monitoringKeyFile  string
)

// monitoringCmd represents the monitoring command
var monitoringCmd = cmdhelp.NewCommandHelp(
	"monitoring [command]",
	"监控和告警系统",
	"启动和管理监控系统，包括指标收集、健康检查和性能监控",
).BuildCommand(nil, nil)

// monitoringStartCmd represents the start subcommand
var monitoringStartCmd = cmdhelp.NewCommandHelp(
	"start",
	"启动监控服务器",
	"启动监控服务器，提供Prometheus指标、健康检查和性能监控端点",
).AddExamples(
	"k8s-helper monitoring start",
	"k8s-helper monitoring start --port 9090",
	"k8s-helper monitoring start --host 0.0.0.0 --port 8080",
).BuildCommand(runMonitoringStart, nil)

func init() {
	monitoringCmd.AddCommand(monitoringStartCmd)
	
	monitoringStartCmd.Flags().IntVarP(&monitoringPort, "port", "p", 8080, "监控服务器端口")
	monitoringStartCmd.Flags().StringVarP(&monitoringHost, "host", "H", "localhost", "监控服务器主机")
	monitoringStartCmd.Flags().BoolVar(&monitoringTLS, "tls", false, "启用TLS")
	monitoringStartCmd.Flags().StringVar(&monitoringCertFile, "cert-file", "", "TLS证书文件")
	monitoringStartCmd.Flags().StringVar(&monitoringKeyFile, "key-file", "", "TLS密钥文件")
	
	rootCmd.AddCommand(monitoringCmd)
}

func runMonitoringStart(cmd *cobra.Command, args []string) error {
	// 初始化日志
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	// 初始化容器
	appContainer := container.NewContainer(logger)
	if err := appContainer.Initialize(); err != nil {
		return fmt.Errorf("初始化容器失败: %w", err)
	}

	// 获取服务
	metricsCollector := appContainer.GetMetricsCollector()
	healthChecker := appContainer.GetHealthChecker()
	performanceMonitor := appContainer.GetPerformanceMonitor()

	// 创建监控处理器
	monitoringHandler := handler.NewMonitoringHandler(
		logger,
		metricsCollector,
		healthChecker,
		performanceMonitor,
	)

	// 创建HTTP服务器
	mux := http.NewServeMux()
	monitoringHandler.RegisterRoutes(mux)

	server := &http.Server{
		Addr:    fmt.Sprintf("%s:%d", monitoringHost, monitoringPort),
		Handler: mux,
	}

	// 启动系统指标收集
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	
	metricsCollector.StartSystemMetricsCollection(ctx, 30*time.Second)
	healthChecker.StartPeriodicCheck(ctx)

	// 启动服务器
	serverErr := make(chan error, 1)
	go func() {
		logger.Info("启动监控服务器", 
			zap.String("addr", server.Addr))
		
		var err error
		if monitoringTLS {
			err = server.ListenAndServeTLS(monitoringCertFile, monitoringKeyFile)
		} else {
			err = server.ListenAndServe()
		}
		serverErr <- err
	}()

	// 监听系统信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号或错误
	select {
	case err := <-serverErr:
		return fmt.Errorf("服务器错误: %w", err)
	case sig := <-sigChan:
		logger.Info("收到信号，正在关闭服务器", 
			zap.String("signal", sig.String()))
		
		// 优雅关闭
		shutdownCtx, shutdownCancel := context.WithTimeout(ctx, 30*time.Second)
		defer shutdownCancel()

		if err := server.Shutdown(shutdownCtx); err != nil {
			logger.Error("服务器关闭失败", zap.Error(err))
			return err
		}

		logger.Info("服务器已关闭")
	}

	return nil
}