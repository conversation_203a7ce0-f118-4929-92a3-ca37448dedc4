package service

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"go.uber.org/zap"
)

// MetricsCollector Prometheus指标收集器
type MetricsCollector struct {
	logger *zap.Logger

	// 业务指标
	backupOperationsTotal    prometheus.Counter
	backupOperationsDuration prometheus.Histogram
	backupOperationsErrors   prometheus.Counter
	backupSizeBytes          prometheus.Histogram

	restoreOperationsTotal    prometheus.Counter
	restoreOperationsDuration prometheus.Histogram
	restoreOperationsErrors   prometheus.Counter

	verifyOperationsTotal    prometheus.Counter
	verifyOperationsDuration prometheus.Histogram
	verifyOperationsErrors   prometheus.Counter

	// 系统指标
	memoryUsageBytes prometheus.Gauge
	goroutinesCount  prometheus.Gauge
	cpuUsagePercent  prometheus.Gauge

	// 缓存指标
	cacheHitsTotal        prometheus.Counter
	cacheMissesTotal      prometheus.Counter
	cacheEntriesCount     prometheus.Gauge
	cacheMemoryUsageBytes prometheus.Gauge

	// 文件操作指标
	fileReadOperationsTotal prometheus.Counter
	fileReadDuration        prometheus.Histogram
	fileHashOperationsTotal prometheus.Counter
	fileHashDuration        prometheus.Histogram

	// 健康检查指标
	healthCheckStatus   prometheus.Gauge
	healthCheckDuration prometheus.Histogram

	// 内部状态
	registry *prometheus.Registry
	mutex    sync.RWMutex
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(logger *zap.Logger) *MetricsCollector {
	registry := prometheus.NewRegistry()

	mc := &MetricsCollector{
		logger:   logger,
		registry: registry,
	}

	mc.initMetrics()
	mc.registerMetrics()

	return mc
}

// initMetrics 初始化指标
func (mc *MetricsCollector) initMetrics() {
	// 业务指标
	mc.backupOperationsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "k8stool_backup_operations_total",
		Help: "Total number of backup operations",
	})

	mc.backupOperationsDuration = promauto.NewHistogram(prometheus.HistogramOpts{
		Name:    "k8stool_backup_operations_duration_seconds",
		Help:    "Duration of backup operations in seconds",
		Buckets: prometheus.DefBuckets,
	})

	mc.backupOperationsErrors = promauto.NewCounter(prometheus.CounterOpts{
		Name: "k8stool_backup_operations_errors_total",
		Help: "Total number of backup operation errors",
	})

	mc.backupSizeBytes = promauto.NewHistogram(prometheus.HistogramOpts{
		Name:    "k8stool_backup_size_bytes",
		Help:    "Size of backup files in bytes",
		Buckets: []float64{1024, 10240, 102400, 1048576, 10485760, 104857600, 1073741824}, // 1KB to 1GB
	})

	mc.restoreOperationsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "k8stool_restore_operations_total",
		Help: "Total number of restore operations",
	})

	mc.restoreOperationsDuration = promauto.NewHistogram(prometheus.HistogramOpts{
		Name:    "k8stool_restore_operations_duration_seconds",
		Help:    "Duration of restore operations in seconds",
		Buckets: prometheus.DefBuckets,
	})

	mc.restoreOperationsErrors = promauto.NewCounter(prometheus.CounterOpts{
		Name: "k8stool_restore_operations_errors_total",
		Help: "Total number of restore operation errors",
	})

	mc.verifyOperationsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "k8stool_verify_operations_total",
		Help: "Total number of verify operations",
	})

	mc.verifyOperationsDuration = promauto.NewHistogram(prometheus.HistogramOpts{
		Name:    "k8stool_verify_operations_duration_seconds",
		Help:    "Duration of verify operations in seconds",
		Buckets: prometheus.DefBuckets,
	})

	mc.verifyOperationsErrors = promauto.NewCounter(prometheus.CounterOpts{
		Name: "k8stool_verify_operations_errors_total",
		Help: "Total number of verify operation errors",
	})

	// 系统指标
	mc.memoryUsageBytes = promauto.NewGauge(prometheus.GaugeOpts{
		Name: "k8stool_memory_usage_bytes",
		Help: "Current memory usage in bytes",
	})

	mc.goroutinesCount = promauto.NewGauge(prometheus.GaugeOpts{
		Name: "k8stool_goroutines_count",
		Help: "Current number of goroutines",
	})

	mc.cpuUsagePercent = promauto.NewGauge(prometheus.GaugeOpts{
		Name: "k8stool_cpu_usage_percent",
		Help: "Current CPU usage percentage",
	})

	// 缓存指标
	mc.cacheHitsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "k8stool_cache_hits_total",
		Help: "Total number of cache hits",
	})

	mc.cacheMissesTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "k8stool_cache_misses_total",
		Help: "Total number of cache misses",
	})

	mc.cacheEntriesCount = promauto.NewGauge(prometheus.GaugeOpts{
		Name: "k8stool_cache_entries_count",
		Help: "Current number of cache entries",
	})

	mc.cacheMemoryUsageBytes = promauto.NewGauge(prometheus.GaugeOpts{
		Name: "k8stool_cache_memory_usage_bytes",
		Help: "Current cache memory usage in bytes",
	})

	// 文件操作指标
	mc.fileReadOperationsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "k8stool_file_read_operations_total",
		Help: "Total number of file read operations",
	})

	mc.fileReadDuration = promauto.NewHistogram(prometheus.HistogramOpts{
		Name:    "k8stool_file_read_duration_seconds",
		Help:    "Duration of file read operations in seconds",
		Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0},
	})

	mc.fileHashOperationsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "k8stool_file_hash_operations_total",
		Help: "Total number of file hash operations",
	})

	mc.fileHashDuration = promauto.NewHistogram(prometheus.HistogramOpts{
		Name:    "k8stool_file_hash_duration_seconds",
		Help:    "Duration of file hash operations in seconds",
		Buckets: []float64{0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0, 30.0},
	})

	// 健康检查指标
	mc.healthCheckStatus = promauto.NewGauge(prometheus.GaugeOpts{
		Name: "k8stool_health_check_status",
		Help: "Health check status (1 = healthy, 0 = unhealthy)",
	})

	mc.healthCheckDuration = promauto.NewHistogram(prometheus.HistogramOpts{
		Name:    "k8stool_health_check_duration_seconds",
		Help:    "Duration of health check operations in seconds",
		Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0},
	})
}

// registerMetrics 注册指标到注册表
func (mc *MetricsCollector) registerMetrics() {
	mc.registry.MustRegister(
		mc.backupOperationsTotal,
		mc.backupOperationsDuration,
		mc.backupOperationsErrors,
		mc.backupSizeBytes,
		mc.restoreOperationsTotal,
		mc.restoreOperationsDuration,
		mc.restoreOperationsErrors,
		mc.verifyOperationsTotal,
		mc.verifyOperationsDuration,
		mc.verifyOperationsErrors,
		mc.memoryUsageBytes,
		mc.goroutinesCount,
		mc.cpuUsagePercent,
		mc.cacheHitsTotal,
		mc.cacheMissesTotal,
		mc.cacheEntriesCount,
		mc.cacheMemoryUsageBytes,
		mc.fileReadOperationsTotal,
		mc.fileReadDuration,
		mc.fileHashOperationsTotal,
		mc.fileHashDuration,
		mc.healthCheckStatus,
		mc.healthCheckDuration,
	)
}

// RecordBackupOperation 记录备份操作指标
func (mc *MetricsCollector) RecordBackupOperation(duration time.Duration, success bool, size int64) {
	mc.backupOperationsTotal.Inc()
	mc.backupOperationsDuration.Observe(duration.Seconds())

	if !success {
		mc.backupOperationsErrors.Inc()
	}

	if size > 0 {
		mc.backupSizeBytes.Observe(float64(size))
	}

	mc.logger.Debug("记录备份操作指标",
		zap.Duration("duration", duration),
		zap.Bool("success", success),
		zap.Int64("size", size))
}

// RecordRestoreOperation 记录恢复操作指标
func (mc *MetricsCollector) RecordRestoreOperation(duration time.Duration, success bool) {
	mc.restoreOperationsTotal.Inc()
	mc.restoreOperationsDuration.Observe(duration.Seconds())

	if !success {
		mc.restoreOperationsErrors.Inc()
	}

	mc.logger.Debug("记录恢复操作指标",
		zap.Duration("duration", duration),
		zap.Bool("success", success))
}

// RecordVerifyOperation 记录验证操作指标
func (mc *MetricsCollector) RecordVerifyOperation(duration time.Duration, success bool) {
	mc.verifyOperationsTotal.Inc()
	mc.verifyOperationsDuration.Observe(duration.Seconds())

	if !success {
		mc.verifyOperationsErrors.Inc()
	}

	mc.logger.Debug("记录验证操作指标",
		zap.Duration("duration", duration),
		zap.Bool("success", success))
}

// RecordCacheHit 记录缓存命中
func (mc *MetricsCollector) RecordCacheHit() {
	mc.cacheHitsTotal.Inc()
}

// RecordCacheMiss 记录缓存未命中
func (mc *MetricsCollector) RecordCacheMiss() {
	mc.cacheMissesTotal.Inc()
}

// UpdateCacheStats 更新缓存统计信息
func (mc *MetricsCollector) UpdateCacheStats(entriesCount int, memoryUsage int64) {
	mc.cacheEntriesCount.Set(float64(entriesCount))
	mc.cacheMemoryUsageBytes.Set(float64(memoryUsage))
}

// RecordFileReadOperation 记录文件读取操作
func (mc *MetricsCollector) RecordFileReadOperation(duration time.Duration) {
	mc.fileReadOperationsTotal.Inc()
	mc.fileReadDuration.Observe(duration.Seconds())
}

// RecordFileHashOperation 记录文件哈希操作
func (mc *MetricsCollector) RecordFileHashOperation(duration time.Duration) {
	mc.fileHashOperationsTotal.Inc()
	mc.fileHashDuration.Observe(duration.Seconds())
}

// RecordHealthCheck 记录健康检查
func (mc *MetricsCollector) RecordHealthCheck(duration time.Duration, healthy bool) {
	mc.healthCheckDuration.Observe(duration.Seconds())

	if healthy {
		mc.healthCheckStatus.Set(1)
	} else {
		mc.healthCheckStatus.Set(0)
	}
}

// UpdateSystemMetrics 更新系统指标
func (mc *MetricsCollector) UpdateSystemMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	mc.memoryUsageBytes.Set(float64(m.Alloc))
	mc.goroutinesCount.Set(float64(runtime.NumGoroutine()))

	// CPU使用率需要更复杂的计算，这里简化处理
	// 在实际实现中可以使用第三方库如gopsutil
	mc.cpuUsagePercent.Set(0) // 占位符
}

// GetRegistry 获取Prometheus注册表
func (mc *MetricsCollector) GetRegistry() *prometheus.Registry {
	return mc.registry
}

// StartSystemMetricsCollection 启动系统指标收集
func (mc *MetricsCollector) StartSystemMetricsCollection(ctx context.Context, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	go func() {
		for {
			select {
			case <-ctx.Done():
				mc.logger.Info("停止系统指标收集")
				return
			case <-ticker.C:
				mc.UpdateSystemMetrics()
			}
		}
	}()

	mc.logger.Info("启动系统指标收集", zap.Duration("interval", interval))
}
