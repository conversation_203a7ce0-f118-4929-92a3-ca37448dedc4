import React, { useState, useEffect } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Table, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select,
  InputNumber,
  Tag,
  Tooltip,
  Popconfirm,
  Alert,
  Badge,
  message
} from 'antd';
import { 
  PlusOutlined, 
  ReloadOutlined, 
  StopOutlined,
  PlayCircleOutlined,
  DeleteOutlined,
  LinkOutlined,
  DisconnectOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { DataTable, StatusTag, ActionButtons } from '@/components/DataTable';
import {
  usePortForwards,
  useCreatePortForward,
  useStopPortForward,
  useDeletePortForward,
  useClusterPods,
  useAppState,
  useNotifications
} from '@/hooks';

const { Option } = Select;

// 端口转发状态
export type PortForwardStatus = 'running' | 'stopped' | 'error' | 'starting' | 'stopping';

// 端口转发项
export interface PortForward {
  id: string;
  name: string;
  namespace: string;
  pod: string;
  localPort: number;
  remotePort: number;
  status: PortForwardStatus;
  pid?: number;
  createdAt: string;
  lastConnected?: string;
  connectionCount: number;
  error?: string;
}

// 端口转发请求
export interface PortForwardRequest {
  name: string;
  namespace: string;
  pod: string;
  localPort: number;
  remotePort: number;
}

/**
 * 端口转发管理页面
 * 
 * 功能特性：
 * - 端口转发列表展示
 * - 创建端口转发
 * - 连接状态监控
 * - 停止和删除操作
 * - 操作日志记录
 */
const PortForwardManagement: React.FC = () => {
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [selectedNamespace, setSelectedNamespace] = useState<string>('');
  const [selectedPod, setSelectedPod] = useState<string>('');
  
  const { setPageTitle } = useAppState();
  const { showSuccess, showError, showWarning } = useNotifications();

  // 获取端口转发列表
  const { 
    data: portForwards = [], 
    isLoading, 
    refetch 
  } = usePortForwards({
    refetchInterval: 5000, // 5秒刷新状态
  });

  // 获取Pod列表
  const { 
    data: pods = [] 
  } = useClusterPods();

  // 创建端口转发
  const createMutation = useCreatePortForward({
    onSuccess: () => {
      showSuccess('端口转发创建成功', '端口转发已开始');
      setCreateModalVisible(false);
      form.resetFields();
      refetch();
    },
    onError: (error: any) => {
      showError('端口转发创建失败', error.message);
    },
  });

  // 停止端口转发
  const stopMutation = useStopPortForward({
    onSuccess: () => {
      showSuccess('端口转发已停止', '端口转发连接已断开');
      refetch();
    },
    onError: (error: any) => {
      showError('停止端口转发失败', error.message);
    },
  });

  // 删除端口转发
  const deleteMutation = useDeletePortForward({
    onSuccess: () => {
      showSuccess('端口转发已删除', '端口转发记录已删除');
      refetch();
    },
    onError: (error: any) => {
      showError('删除端口转发失败', error.message);
    },
  });

  // 设置页面标题
  useEffect(() => {
    setPageTitle('端口转发 - K8s-Helper');
  }, [setPageTitle]);

  // 获取命名空间列表
  const namespaces = Array.from(new Set(pods.map(pod => pod.namespace))).sort();

  // 根据选中的命名空间筛选Pod
  const filteredPods = selectedNamespace 
    ? pods.filter(pod => pod.namespace === selectedNamespace)
    : [];

  // 状态配置
  const statusConfig = {
    running: { color: 'green', text: '运行中', icon: <LinkOutlined /> },
    stopped: { color: 'default', text: '已停止', icon: <DisconnectOutlined /> },
    error: { color: 'red', text: '错误', icon: <DisconnectOutlined /> },
    starting: { color: 'blue', text: '启动中', icon: <PlayCircleOutlined /> },
    stopping: { color: 'orange', text: '停止中', icon: <StopOutlined /> },
  };

  // 表格列定义
  const columns: ColumnsType<PortForward> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: PortForward) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.namespace}/{record.pod}
          </div>
        </div>
      ),
    },
    {
      title: '端口映射',
      key: 'ports',
      width: 150,
      render: (_, record: PortForward) => (
        <div style={{ textAlign: 'center' }}>
          <code>{record.localPort}</code>
          <span style={{ margin: '0 8px' }}>→</span>
          <code>{record.remotePort}</code>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: PortForwardStatus, record: PortForward) => (
        <div>
          <Badge 
            status={status === 'running' ? 'processing' : status === 'error' ? 'error' : 'default'}
            text={statusConfig[status].text}
          />
          {record.error && (
            <Tooltip title={record.error}>
              <span style={{ color: '#ff4d4f', marginLeft: '4px' }}>⚠</span>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: '连接数',
      dataIndex: 'connectionCount',
      key: 'connectionCount',
      width: 80,
      render: (count: number) => (
        <Tag color={count > 0 ? 'blue' : 'default'}>
          {count}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '最后连接',
      dataIndex: 'lastConnected',
      key: 'lastConnected',
      width: 160,
      render: (time?: string) => time ? new Date(time).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record: PortForward) => (
        <ActionButtons
          actions={[
            {
              key: 'stop',
              label: record.status === 'running' ? '停止' : '启动',
              icon: record.status === 'running' ? <StopOutlined /> : <PlayCircleOutlined />,
              disabled: ['starting', 'stopping'].includes(record.status),
              onClick: () => handleTogglePortForward(record),
            },
            {
              key: 'delete',
              label: '删除',
              icon: <DeleteOutlined />,
              danger: true,
              onClick: () => handleDeletePortForward(record.id),
            },
          ]}
        />
      ),
    },
  ];

  // 处理创建端口转发
  const handleCreatePortForward = async (values: any) => {
    const request: PortForwardRequest = {
      name: values.name,
      namespace: values.namespace,
      pod: values.pod,
      localPort: values.localPort,
      remotePort: values.remotePort,
    };
    
    createMutation.mutate(request);
  };

  // 处理切换端口转发状态
  const handleTogglePortForward = (portForward: PortForward) => {
    if (portForward.status === 'running') {
      stopMutation.mutate(portForward.id);
    } else {
      // 重新启动
      createMutation.mutate({
        name: portForward.name,
        namespace: portForward.namespace,
        pod: portForward.pod,
        localPort: portForward.localPort,
        remotePort: portForward.remotePort,
      });
    }
  };

  // 处理删除端口转发
  const handleDeletePortForward = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个端口转发吗？如果正在运行，将会先停止连接。',
      onOk: () => deleteMutation.mutate(id),
    });
  };

  // 处理命名空间变化
  const handleNamespaceChange = (namespace: string) => {
    setSelectedNamespace(namespace);
    setSelectedPod('');
    form.setFieldsValue({ pod: undefined });
  };

  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f7fa' }}>
      {/* 页面头部 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '24px' 
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
            端口转发
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            管理Kubernetes Pod的端口转发连接
          </p>
        </div>
        
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            loading={isLoading}
            onClick={() => refetch()}
          >
            刷新
          </Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建端口转发
          </Button>
          <Button icon={<SettingOutlined />}>
            设置
          </Button>
        </Space>
      </div>

      {/* 运行状态提示 */}
      {portForwards.filter(pf => pf.status === 'running').length > 0 && (
        <Alert
          message={`当前有 ${portForwards.filter(pf => pf.status === 'running').length} 个端口转发正在运行`}
          description="请注意本地端口占用情况，避免端口冲突"
          type="info"
          showIcon
          closable
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* 端口转发列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={portForwards}
          loading={isLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>

      {/* 创建端口转发模态框 */}
      <Modal
        title="创建端口转发"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
          setSelectedNamespace('');
          setSelectedPod('');
        }}
        onOk={() => form.submit()}
        confirmLoading={createMutation.isPending}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreatePortForward}
        >
          <Form.Item
            name="name"
            label="转发名称"
            rules={[
              { required: true, message: '请输入转发名称' },
              { max: 50, message: '名称不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入转发名称" />
          </Form.Item>

          <Form.Item
            name="namespace"
            label="命名空间"
            rules={[{ required: true, message: '请选择命名空间' }]}
          >
            <Select
              placeholder="请选择命名空间"
              onChange={handleNamespaceChange}
              showSearch
            >
              {namespaces.map(namespace => (
                <Option key={namespace} value={namespace}>
                  {namespace}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="pod"
            label="目标Pod"
            rules={[{ required: true, message: '请选择Pod' }]}
          >
            <Select
              placeholder={selectedNamespace ? "请选择Pod" : "请先选择命名空间"}
              disabled={!selectedNamespace}
              showSearch
            >
              {filteredPods.map(pod => (
                <Option key={pod.name} value={pod.name}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>{pod.name}</span>
                    <Tag color={pod.status === 'Running' ? 'green' : 'orange'} size="small">
                      {pod.status}
                    </Tag>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="localPort"
                label="本地端口"
                rules={[
                  { required: true, message: '请输入本地端口' },
                  { type: 'number', min: 1024, max: 65535, message: '端口范围：1024-65535' }
                ]}
              >
                <InputNumber 
                  placeholder="本地端口"
                  style={{ width: '100%' }}
                  min={1024}
                  max={65535}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="remotePort"
                label="远程端口"
                rules={[
                  { required: true, message: '请输入远程端口' },
                  { type: 'number', min: 1, max: 65535, message: '端口范围：1-65535' }
                ]}
              >
                <InputNumber 
                  placeholder="远程端口"
                  style={{ width: '100%' }}
                  min={1}
                  max={65535}
                />
              </Form.Item>
            </Col>
          </Row>

          <Alert
            message="端口转发说明"
            description="端口转发将在本地创建一个监听端口，所有发送到本地端口的流量都会转发到Pod的指定端口。请确保本地端口未被占用。"
            type="info"
            showIcon
            style={{ marginTop: '16px' }}
          />
        </Form>
      </Modal>
    </div>
  );
};

export default PortForwardManagement;
