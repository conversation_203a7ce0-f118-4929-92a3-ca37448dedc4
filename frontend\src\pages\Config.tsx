import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Tabs,
  Space,
  Button,
  Alert,
  message,
  Modal
} from 'antd';
import {
  ReloadOutlined,
  SettingOutlined,
  ExportOutlined,
  ImportOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { CodeEditor } from '@/components/CodeEditor';
import { ConfigForm } from '@/components/ConfigForm';
import {
  useSystemConfig,
  useUpdateSystemConfig,
  useAppState,
  useNotifications
} from '@/hooks';
import type { ConfigItem } from '@/components/ConfigForm';
import * as yaml from 'js-yaml';

/**
 * 配置管理页面
 *
 * 功能特性：
 * - 系统配置管理
 * - 可视化配置表单
 * - YAML配置编辑
 * - 配置导入导出
 * - 配置验证
 */
const ConfigManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('form');
  const [yamlConfig, setYamlConfig] = useState('');
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importContent, setImportContent] = useState('');

  const { setPageTitle } = useAppState();
  const { showSuccess, showError, showWarning } = useNotifications();

  // 获取系统配置
  const {
    data: systemConfig,
    isLoading,
    error,
    refetch
  } = useSystemConfig();

  // 更新系统配置
  const updateConfigMutation = useUpdateSystemConfig({
    onSuccess: () => {
      showSuccess('配置保存成功', '系统配置已更新');
      refetch();
    },
    onError: (error: any) => {
      showError('配置保存失败', error.message || '保存配置时发生错误');
    },
  });

  // 设置页面标题
  useEffect(() => {
    setPageTitle('配置管理 - K8s-Helper');
  }, [setPageTitle]);

  // 更新YAML配置
  useEffect(() => {
    if (systemConfig) {
      try {
        const yamlStr = yaml.dump(systemConfig, {
          indent: 2,
          lineWidth: -1,
          noRefs: true
        });
        setYamlConfig(yamlStr);
      } catch (error) {
        console.error('YAML序列化失败:', error);
      }
    }
  }, [systemConfig]);

  // 配置项定义
  const configItems: ConfigItem[] = [
    // 基本设置
    {
      key: 'app.name',
      label: '应用名称',
      type: 'string',
      value: systemConfig?.app?.name,
      defaultValue: 'K8s-Helper',
      description: '应用显示名称',
      required: true,
      group: '基本设置',
    },
    {
      key: 'app.version',
      label: '应用版本',
      type: 'string',
      value: systemConfig?.app?.version,
      defaultValue: '1.0.0',
      description: '当前应用版本',
      disabled: true,
      group: '基本设置',
    },
    {
      key: 'app.debug',
      label: '调试模式',
      type: 'boolean',
      value: systemConfig?.app?.debug,
      defaultValue: false,
      description: '启用调试模式将显示更多日志信息',
      group: '基本设置',
    },

    // 集群设置
    {
      key: 'cluster.endpoint',
      label: 'API服务器地址',
      type: 'string',
      value: systemConfig?.cluster?.endpoint,
      defaultValue: 'https://kubernetes.default.svc',
      description: 'Kubernetes API服务器地址',
      required: true,
      group: '集群设置',
      validation: {
        pattern: /^https?:\/\/.+/,
        message: '请输入有效的URL地址',
      },
    },
    {
      key: 'cluster.timeout',
      label: '连接超时(秒)',
      type: 'number',
      value: systemConfig?.cluster?.timeout,
      defaultValue: 30,
      description: 'API请求超时时间',
      min: 5,
      max: 300,
      group: '集群设置',
    },
    {
      key: 'cluster.namespace',
      label: '默认命名空间',
      type: 'string',
      value: systemConfig?.cluster?.namespace,
      defaultValue: 'default',
      description: '默认操作的命名空间',
      group: '集群设置',
    },

    // 日志设置
    {
      key: 'logging.level',
      label: '日志级别',
      type: 'select',
      value: systemConfig?.logging?.level,
      defaultValue: 'info',
      description: '系统日志输出级别',
      options: [
        { label: 'DEBUG', value: 'debug' },
        { label: 'INFO', value: 'info' },
        { label: 'WARN', value: 'warn' },
        { label: 'ERROR', value: 'error' },
      ],
      group: '日志设置',
    },
    {
      key: 'logging.maxLines',
      label: '最大日志行数',
      type: 'number',
      value: systemConfig?.logging?.maxLines,
      defaultValue: 10000,
      description: '单个Pod日志的最大行数',
      min: 100,
      max: 100000,
      group: '日志设置',
    },

    // 监控设置
    {
      key: 'monitoring.enabled',
      label: '启用监控',
      type: 'boolean',
      value: systemConfig?.monitoring?.enabled,
      defaultValue: true,
      description: '启用系统监控功能',
      group: '监控设置',
    },
    {
      key: 'monitoring.interval',
      label: '监控间隔(秒)',
      type: 'number',
      value: systemConfig?.monitoring?.interval,
      defaultValue: 30,
      description: '监控数据收集间隔',
      min: 5,
      max: 300,
      group: '监控设置',
    },

    // 安全设置
    {
      key: 'security.tokenExpiry',
      label: 'Token过期时间(小时)',
      type: 'number',
      value: systemConfig?.security?.tokenExpiry,
      defaultValue: 24,
      description: '认证Token的过期时间',
      min: 1,
      max: 168,
      group: '安全设置',
    },
    {
      key: 'security.allowedOrigins',
      label: '允许的来源',
      type: 'multiselect',
      value: systemConfig?.security?.allowedOrigins,
      defaultValue: ['*'],
      description: 'CORS允许的来源地址',
      options: [
        { label: '所有来源 (*)', value: '*' },
        { label: 'localhost', value: 'http://localhost:3000' },
        { label: '本地网络', value: 'http://192.168.*' },
      ],
      group: '安全设置',
    },
  ];

  // 处理表单保存
  const handleFormSave = (values: Record<string, any>) => {
    // 将扁平化的配置转换为嵌套结构
    const nestedConfig = Object.keys(values).reduce((acc, key) => {
      const keys = key.split('.');
      let current = acc;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = values[key];
      return acc;
    }, {} as any);

    updateConfigMutation.mutate(nestedConfig);
  };

  // 处理YAML保存
  const handleYamlSave = (yamlContent: string) => {
    try {
      const config = yaml.load(yamlContent);
      updateConfigMutation.mutate(config);
    } catch (error) {
      showError('YAML格式错误', '请检查YAML语法是否正确');
    }
  };

  // 验证YAML
  const validateYaml = (content: string) => {
    try {
      yaml.load(content);
      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        errors: [`YAML语法错误: ${(error as Error).message}`]
      };
    }
  };

  // 导出配置
  const handleExport = () => {
    const blob = new Blob([yamlConfig], { type: 'application/yaml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `k8s-helper-config-${new Date().toISOString().slice(0, 10)}.yaml`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    showSuccess('导出成功', '配置文件已下载');
  };

  // 导入配置
  const handleImport = () => {
    setImportModalVisible(true);
    setImportContent('');
  };

  // 确认导入
  const handleImportConfirm = () => {
    try {
      const config = yaml.load(importContent);

      Modal.confirm({
        title: '确认导入配置',
        icon: <ExclamationCircleOutlined />,
        content: '导入配置将覆盖当前所有设置，此操作不可撤销。确定要继续吗？',
        onOk: () => {
          updateConfigMutation.mutate(config);
          setImportModalVisible(false);
          setImportContent('');
        },
      });
    } catch (error) {
      showError('导入失败', 'YAML格式错误，请检查文件内容');
    }
  };

  // 刷新配置
  const handleRefresh = () => {
    refetch();
    showSuccess('刷新成功', '配置数据已更新');
  };

  // 错误处理
  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="配置加载失败"
          description={error.message || '无法获取系统配置，请检查网络连接'}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={handleRefresh}>
              重试
            </Button>
          }
        />
      </div>
    );
  }

  // Tab配置
  const tabItems = [
    {
      key: 'form',
      label: '可视化配置',
      children: (
        <ConfigForm
          title="系统配置"
          items={configItems}
          loading={updateConfigMutation.isPending}
          onSave={handleFormSave}
          onReset={handleRefresh}
        />
      ),
    },
    {
      key: 'yaml',
      label: 'YAML编辑',
      children: (
        <CodeEditor
          title="配置文件 (YAML)"
          value={yamlConfig}
          language="yaml"
          height={600}
          loading={isLoading}
          onChange={setYamlConfig}
          onSave={handleYamlSave}
          onValidate={validateYaml}
        />
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f7fa' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
            配置管理
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            管理系统配置参数和设置
          </p>
        </div>

        <Space>
          <Button
            icon={<ReloadOutlined />}
            loading={isLoading}
            onClick={handleRefresh}
          >
            刷新
          </Button>
          <Button
            icon={<ImportOutlined />}
            onClick={handleImport}
          >
            导入配置
          </Button>
          <Button
            icon={<ExportOutlined />}
            onClick={handleExport}
            disabled={!systemConfig}
          >
            导出配置
          </Button>
          <Button icon={<SettingOutlined />}>
            高级设置
          </Button>
        </Space>
      </div>

      {/* 配置更新提示 */}
      {updateConfigMutation.isPending && (
        <Alert
          message="正在保存配置..."
          description="请稍候，配置更新可能需要几秒钟时间"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* Tab内容 */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        minHeight: '600px'
      }}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          style={{ padding: '0 24px' }}
          tabBarStyle={{ marginBottom: 0 }}
        />
      </div>

      {/* 导入配置模态框 */}
      <Modal
        title="导入配置"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        onOk={handleImportConfirm}
        width={800}
        okText="导入"
        cancelText="取消"
        okButtonProps={{ disabled: !importContent.trim() }}
      >
        <Alert
          message="导入说明"
          description="请粘贴有效的YAML配置内容。导入操作将覆盖当前所有配置设置。"
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
        />

        <CodeEditor
          title="配置内容"
          value={importContent}
          language="yaml"
          height={400}
          onChange={setImportContent}
          onValidate={validateYaml}
        />
      </Modal>
    </div>
  );
};

export default ConfigManagement;
