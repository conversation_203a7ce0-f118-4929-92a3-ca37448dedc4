/* 布局样式文件 */

/* 主布局样式 */
.app-layout {
  min-height: 100vh;
  position: relative;
}

.app-layout-main {
  transition: margin-left 0.3s ease;
}

.app-layout-content {
  margin: 0;
  padding: 0;
  background: #f5f7fa;
  min-height: calc(100vh - 64px);
}

.app-layout-content-inner {
  padding: 24px;
  min-height: calc(100vh - 64px - 48px);
  max-width: none; /* 移除最大宽度限制 */
  width: 100%; /* 使用全宽 */
}

/* 移动端遮罩层 */
.app-layout-mobile-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 999;
  display: none;
}

/* 侧边栏样式 */
.app-sidebar {
  position: fixed !important;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.app-sidebar .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Logo区域 */
.app-sidebar-logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #001529;
  margin-bottom: 8px;
}

.app-sidebar-logo-content {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

.app-sidebar-logo-icon {
  font-size: 20px;
}

.app-sidebar-logo-text {
  font-size: 18px;
}

/* 折叠按钮 */
.app-sidebar-trigger {
  padding: 8px 16px;
  border-bottom: 1px solid #001529;
  margin-bottom: 8px;
}

.app-sidebar-trigger-btn {
  color: #fff !important;
  width: 100%;
  border: none;
  background: transparent;
}

.app-sidebar-trigger-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* 导航菜单 */
.app-sidebar-menu {
  flex: 1;
  border-right: none;
}

.app-sidebar-menu .ant-menu-item,
.app-sidebar-menu .ant-menu-submenu-title {
  margin: 0;
  height: 48px;
  line-height: 48px;
}

.app-sidebar-menu .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.app-sidebar-menu .ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: #1890ff !important;
}

/* 顶部导航栏样式 */
.app-header {
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.app-header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.app-header-trigger {
  font-size: 16px;
  cursor: pointer;
  transition: color 0.3s;
}

.app-header-trigger:hover {
  color: #1890ff;
}

.app-header-breadcrumb {
  margin: 0;
}

.app-header-right {
  display: flex;
  align-items: center;
}

.app-header-theme-switch {
  display: flex;
  align-items: center;
}

.app-header-user {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.app-header-user:hover {
  background-color: #f5f5f5;
}

.app-header-username {
  margin-left: 8px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  /* 移动端侧边栏 */
  .app-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .app-sidebar.ant-layout-sider-collapsed {
    transform: translateX(-100%);
  }
  
  .app-sidebar:not(.ant-layout-sider-collapsed) {
    transform: translateX(0);
  }
  
  /* 移动端遮罩层显示 */
  .app-layout-mobile-mask {
    display: block;
  }
  
  /* 移动端主内容区域 */
  .app-layout-main {
    margin-left: 0 !important;
  }
  
  /* 移动端内容区域 */
  .app-layout-content-inner {
    padding: 16px;
  }
  
  /* 移动端顶部导航栏 */
  .app-header {
    padding: 0 16px !important;
  }
  
  .app-header-username {
    display: none;
  }
}

@media (min-width: 769px) {
  /* 桌面端主内容区域边距 */
  .app-layout-main {
    margin-left: 280px;
  }
  
  .app-layout-main.collapsed {
    margin-left: 64px;
  }
  
  /* 桌面端隐藏遮罩层 */
  .app-layout-mobile-mask {
    display: none !important;
  }
}

/* 动画效果 */
.app-sidebar,
.app-layout-main {
  transition: all 0.3s cubic-bezier(0.2, 0, 0, 1) 0s;
}

/* 滚动条样式 */
.app-sidebar-menu::-webkit-scrollbar {
  width: 6px;
}

.app-sidebar-menu::-webkit-scrollbar-track {
  background: #001529;
}

.app-sidebar-menu::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.app-sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: #777;
}
