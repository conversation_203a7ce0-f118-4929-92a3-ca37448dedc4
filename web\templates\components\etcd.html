{{define "etcd"}}
<div id="etcd" class="tab-content">
    <div class="card">
        <div class="card-header">
            <h3>ETCD 备份管理</h3>
            <button class="btn btn-secondary" onclick="loadBackupList()">刷新备份列表</button>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label for="backup-output" class="form-label">备份输出路径:</label>
                <input type="text" id="backup-output" class="form-control" 
                       placeholder="留空使用默认路径" value="">
            </div>
            <button class="btn btn-success" onclick="performBackup()">
                <span class="loading" id="backup-loading" style="display: none;"></span>
                创建备份
            </button>
            <div id="backup-result" class="result"></div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h3>备份文件列表</h3>
        </div>
        <div class="card-body">
            <div id="backup-files-list" class="backup-list">
                <p>加载中...</p>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h3>ETCD 恢复</h3>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label for="restore-snapshot" class="form-label">快照文件路径:</label>
                <input type="text" id="restore-snapshot" class="form-control" 
                       placeholder="./snapshot.db">
            </div>
            <div class="form-group">
                <label for="restore-data-dir" class="form-label">数据目录:</label>
                <input type="text" id="restore-data-dir" class="form-control" 
                       placeholder="/var/lib/etcd">
            </div>
            <button class="btn btn-warning" onclick="performRestore()">
                <span class="loading" id="restore-loading" style="display: none;"></span>
                恢复 ETCD
            </button>
            <div id="restore-result" class="result"></div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h3>快照验证</h3>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label for="verify-snapshot" class="form-label">快照文件路径:</label>
                <input type="text" id="verify-snapshot" class="form-control" 
                       placeholder="./snapshot.db">
            </div>
            <button class="btn btn-primary" onclick="performVerify()">
                <span class="loading" id="verify-loading" style="display: none;"></span>
                验证快照
            </button>
            <div id="verify-result" class="result"></div>
        </div>
    </div>
</div>
{{end}}
