package logger

import (
	"fmt"
	"log"
	"os"
)

// Logger 日志记录器接口
type Logger interface {
	Debug(msg string, args ...interface{})
	Info(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Error(msg string, args ...interface{})
	Fatal(msg string, args ...interface{})
}

// SimpleLogger 简单日志记录器实现
type SimpleLogger struct {
	debugLogger *log.Logger
	infoLogger  *log.Logger
	warnLogger  *log.Logger
	errorLogger *log.Logger
	fatalLogger *log.Logger
}

// NewSimpleLogger 创建简单日志记录器
func NewSimpleLogger() *SimpleLogger {
	return &SimpleLogger{
		debugLogger: log.New(os.Stdout, "[DEBUG] ", log.LstdFlags|log.Lshortfile),
		infoLogger:  log.New(os.Stdout, "[INFO] ", log.LstdFlags),
		warnLogger:  log.New(os.<PERSON>, "[WARN] ", log.LstdFlags),
		errorLogger: log.New(os.Stderr, "[ERROR] ", log.LstdFlags|log.Lshortfile),
		fatalLogger: log.New(os.Stderr, "[FATAL] ", log.LstdFlags|log.Lshortfile),
	}
}

// Debug 记录调试信息
func (l *SimpleLogger) Debug(msg string, args ...interface{}) {
	if len(args) > 0 {
		l.debugLogger.Printf(msg, args...)
	} else {
		l.debugLogger.Print(msg)
	}
}

// Info 记录信息
func (l *SimpleLogger) Info(msg string, args ...interface{}) {
	if len(args) > 0 {
		l.infoLogger.Printf(msg, args...)
	} else {
		l.infoLogger.Print(msg)
	}
}

// Warn 记录警告
func (l *SimpleLogger) Warn(msg string, args ...interface{}) {
	if len(args) > 0 {
		l.warnLogger.Printf(msg, args...)
	} else {
		l.warnLogger.Print(msg)
	}
}

// Error 记录错误
func (l *SimpleLogger) Error(msg string, args ...interface{}) {
	if len(args) > 0 {
		l.errorLogger.Printf(msg, args...)
	} else {
		l.errorLogger.Print(msg)
	}
}

// Fatal 记录致命错误并退出
func (l *SimpleLogger) Fatal(msg string, args ...interface{}) {
	if len(args) > 0 {
		l.fatalLogger.Printf(msg, args...)
	} else {
		l.fatalLogger.Print(msg)
	}
	os.Exit(1)
}

// 全局日志记录器实例
var defaultLogger Logger = NewSimpleLogger()

// SetLogger 设置全局日志记录器
func SetLogger(logger Logger) {
	defaultLogger = logger
}

// GetLogger 获取全局日志记录器
func GetLogger() Logger {
	return defaultLogger
}

// 便捷函数
func Debug(msg string, args ...interface{}) {
	defaultLogger.Debug(msg, args...)
}

func Info(msg string, args ...interface{}) {
	defaultLogger.Info(msg, args...)
}

func Warn(msg string, args ...interface{}) {
	defaultLogger.Warn(msg, args...)
}

func Error(msg string, args ...interface{}) {
	defaultLogger.Error(msg, args...)
}

func Fatal(msg string, args ...interface{}) {
	defaultLogger.Fatal(msg, args...)
}

// Printf 格式化输出（兼容标准库）
func Printf(format string, args ...interface{}) {
	fmt.Printf(format, args...)
}

// Println 输出一行（兼容标准库）
func Println(args ...interface{}) {
	fmt.Println(args...)
}
