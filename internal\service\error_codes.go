package service

import (
	"fmt"
	"time"
)

// ErrorCode 错误码类型
type ErrorCode string

// 错误码定义 - 使用分层结构：模块-类型-具体错误
const (
	// ETCD相关错误码 (E1xxx)
	ErrorCodeETCDConnectionFailed    ErrorCode = "E1001"
	ErrorCodeETCDAuthenticationFailed ErrorCode = "E1002"
	ErrorCodeETCDCertificateInvalid  ErrorCode = "E1003"
	ErrorCodeETCDTimeoutError        ErrorCode = "E1004"
	ErrorCodeETCDClusterUnavailable  ErrorCode = "E1005"
	ErrorCodeETCDVersionMismatch     ErrorCode = "E1006"
	ErrorCodeETCDPermissionDenied    ErrorCode = "E1007"

	// 备份相关错误码 (E2xxx)
	ErrorCodeBackupPathInvalid       ErrorCode = "E2001"
	ErrorCodeBackupFileExists        ErrorCode = "E2002"
	ErrorCodeBackupDiskSpaceInsufficient ErrorCode = "E2003"
	ErrorCodeBackupCorrupted         ErrorCode = "E2004"
	ErrorCodeBackupToolNotFound      ErrorCode = "E2005"
	ErrorCodeBackupSDKFailed         ErrorCode = "E2006"
	ErrorCodeBackupPermissionDenied  ErrorCode = "E2007"
	ErrorCodeBackupSizeExceeded      ErrorCode = "E2008"

	// 恢复相关错误码 (E3xxx)
	ErrorCodeRestoreSnapshotNotFound ErrorCode = "E3001"
	ErrorCodeRestoreSnapshotCorrupted ErrorCode = "E3002"
	ErrorCodeRestoreDataDirInvalid   ErrorCode = "E3003"
	ErrorCodeRestoreDataDirNotEmpty  ErrorCode = "E3004"
	ErrorCodeRestoreVersionMismatch  ErrorCode = "E3005"
	ErrorCodeRestorePermissionDenied ErrorCode = "E3006"
	ErrorCodeRestoreConfigInvalid    ErrorCode = "E3007"

	// 验证相关错误码 (E4xxx)
	ErrorCodeVerifyFileNotFound      ErrorCode = "E4001"
	ErrorCodeVerifyFileCorrupted     ErrorCode = "E4002"
	ErrorCodeVerifyHashMismatch      ErrorCode = "E4003"
	ErrorCodeVerifyFormatInvalid     ErrorCode = "E4004"
	ErrorCodeVerifyToolNotFound      ErrorCode = "E4005"

	// 配置相关错误码 (E5xxx)
	ErrorCodeConfigFileNotFound      ErrorCode = "E5001"
	ErrorCodeConfigFormatInvalid     ErrorCode = "E5002"
	ErrorCodeConfigValidationFailed  ErrorCode = "E5003"
	ErrorCodeConfigPermissionDenied  ErrorCode = "E5004"

	// 网络相关错误码 (E6xxx)
	ErrorCodeNetworkConnectionFailed ErrorCode = "E6001"
	ErrorCodeNetworkTimeoutError     ErrorCode = "E6002"
	ErrorCodeNetworkDNSResolutionFailed ErrorCode = "E6003"
	ErrorCodeNetworkProxyError       ErrorCode = "E6004"

	// 系统相关错误码 (E7xxx)
	ErrorCodeSystemDiskSpaceInsufficient ErrorCode = "E7001"
	ErrorCodeSystemMemoryInsufficient   ErrorCode = "E7002"
	ErrorCodeSystemPermissionDenied     ErrorCode = "E7003"
	ErrorCodeSystemProcessFailed        ErrorCode = "E7004"
	ErrorCodeSystemResourceLocked       ErrorCode = "E7005"

	// API相关错误码 (E8xxx)
	ErrorCodeAPIInvalidRequest       ErrorCode = "E8001"
	ErrorCodeAPIAuthenticationFailed ErrorCode = "E8002"
	ErrorCodeAPIRateLimitExceeded    ErrorCode = "E8003"
	ErrorCodeAPIServiceUnavailable   ErrorCode = "E8004"
	ErrorCodeAPIInternalError        ErrorCode = "E8005"

	// 工具相关错误码 (E9xxx)
	ErrorCodeToolNotFound            ErrorCode = "E9001"
	ErrorCodeToolVersionIncompatible ErrorCode = "E9002"
	ErrorCodeToolExecutionFailed     ErrorCode = "E9003"
	ErrorCodeToolDownloadFailed      ErrorCode = "E9004"
	ErrorCodeToolPermissionDenied    ErrorCode = "E9005"
)

// ErrorCodeInfo 错误码信息
type ErrorCodeInfo struct {
	Code        ErrorCode
	Category    string
	Severity    ErrorSeverity
	Description string
	Solutions   []string
	Recoverable bool
}

// ErrorSeverity 错误严重程度
type ErrorSeverity string

const (
	SeverityCritical ErrorSeverity = "critical" // 严重错误，系统无法继续
	SeverityHigh     ErrorSeverity = "high"     // 高级错误，功能无法使用
	SeverityMedium   ErrorSeverity = "medium"   // 中级错误，部分功能受影响
	SeverityLow      ErrorSeverity = "low"      // 低级错误，轻微影响
	SeverityInfo     ErrorSeverity = "info"     // 信息性错误，仅提示
)

// errorCodeRegistry 错误码注册表
var errorCodeRegistry = map[ErrorCode]*ErrorCodeInfo{
	// ETCD相关错误
	ErrorCodeETCDConnectionFailed: {
		Code:        ErrorCodeETCDConnectionFailed,
		Category:    "ETCD连接",
		Severity:    SeverityHigh,
		Description: "无法连接到ETCD服务器",
		Solutions: []string{
			"检查ETCD服务是否正在运行",
			"验证网络连接和防火墙设置",
			"确认ETCD端点地址和端口正确",
			"检查ETCD服务健康状态",
		},
		Recoverable: true,
	},
	ErrorCodeETCDAuthenticationFailed: {
		Code:        ErrorCodeETCDAuthenticationFailed,
		Category:    "ETCD认证",
		Severity:    SeverityHigh,
		Description: "ETCD身份验证失败",
		Solutions: []string{
			"检查客户端证书文件是否存在且有效",
			"验证证书权限和所有权",
			"确认CA证书配置正确",
			"检查证书是否已过期",
		},
		Recoverable: false,
	},
	ErrorCodeETCDTimeoutError: {
		Code:        ErrorCodeETCDTimeoutError,
		Category:    "ETCD超时",
		Severity:    SeverityMedium,
		Description: "ETCD操作超时",
		Solutions: []string{
			"增加操作超时时间",
			"检查网络延迟和带宽",
			"确认ETCD服务响应正常",
			"检查系统负载情况",
		},
		Recoverable: true,
	},

	// 备份相关错误
	ErrorCodeBackupPathInvalid: {
		Code:        ErrorCodeBackupPathInvalid,
		Category:    "备份路径",
		Severity:    SeverityMedium,
		Description: "备份文件路径无效",
		Solutions: []string{
			"检查备份目录是否存在",
			"验证路径权限",
			"确保有足够的磁盘空间",
			"使用绝对路径",
		},
		Recoverable: true,
	},
	ErrorCodeBackupDiskSpaceInsufficient: {
		Code:        ErrorCodeBackupDiskSpaceInsufficient,
		Category:    "磁盘空间",
		Severity:    SeverityHigh,
		Description: "磁盘空间不足，无法完成备份",
		Solutions: []string{
			"清理磁盘空间",
			"选择其他存储位置",
			"删除旧的备份文件",
			"压缩现有文件",
		},
		Recoverable: true,
	},
	ErrorCodeBackupToolNotFound: {
		Code:        ErrorCodeBackupToolNotFound,
		Category:    "备份工具",
		Severity:    SeverityHigh,
		Description: "找不到备份工具",
		Solutions: []string{
			"安装etcdctl或etcdutl工具",
			"检查工具是否在PATH中",
			"使用--download-tools自动下载",
			"手动指定工具路径",
		},
		Recoverable: true,
	},

	// 恢复相关错误
	ErrorCodeRestoreSnapshotNotFound: {
		Code:        ErrorCodeRestoreSnapshotNotFound,
		Category:    "快照文件",
		Severity:    SeverityHigh,
		Description: "找不到指定的快照文件",
		Solutions: []string{
			"检查快照文件路径是否正确",
			"验证文件是否存在",
			"检查文件权限",
			"使用绝对路径",
		},
		Recoverable: false,
	},
	ErrorCodeRestoreDataDirNotEmpty: {
		Code:        ErrorCodeRestoreDataDirNotEmpty,
		Category:    "数据目录",
		Severity:    SeverityMedium,
		Description: "数据目录不为空",
		Solutions: []string{
			"清空数据目录",
			"选择其他数据目录",
			"备份现有数据",
			"使用--force强制覆盖",
		},
		Recoverable: true,
	},

	// 验证相关错误
	ErrorCodeVerifyFileNotFound: {
		Code:        ErrorCodeVerifyFileNotFound,
		Category:    "文件验证",
		Severity:    SeverityMedium,
		Description: "要验证的文件不存在",
		Solutions: []string{
			"检查文件路径是否正确",
			"确认文件是否已被删除或移动",
			"检查文件权限",
		},
		Recoverable: false,
	},
	ErrorCodeVerifyFileCorrupted: {
		Code:        ErrorCodeVerifyFileCorrupted,
		Category:    "文件完整性",
		Severity:    SeverityHigh,
		Description: "文件已损坏或不完整",
		Solutions: []string{
			"重新生成备份文件",
			"从其他来源获取文件",
			"检查存储设备健康状态",
			"运行文件系统检查",
		},
		Recoverable: false,
	},

	// 系统相关错误
	ErrorCodeSystemPermissionDenied: {
		Code:        ErrorCodeSystemPermissionDenied,
		Category:    "系统权限",
		Severity:    SeverityHigh,
		Description: "权限不足，无法执行操作",
		Solutions: []string{
			"使用管理员权限运行",
			"检查文件和目录权限",
			"确认用户具有必要的权限",
			"联系系统管理员",
		},
		Recoverable: true,
	},
}

// GetErrorCodeInfo 获取错误码信息
func GetErrorCodeInfo(code ErrorCode) *ErrorCodeInfo {
	if info, exists := errorCodeRegistry[code]; exists {
		return info
	}
	return &ErrorCodeInfo{
		Code:        code,
		Category:    "未知错误",
		Severity:    SeverityMedium,
		Description: "未知的错误码",
		Solutions:   []string{"请联系技术支持"},
		Recoverable: false,
	}
}

// EnhancedServiceError 增强的服务错误
type EnhancedServiceError struct {
	*ServiceError
	ErrorCode   ErrorCode
	Severity    ErrorSeverity
	Timestamp   time.Time
	RequestID   string
	UserID      string
	Context     map[string]interface{}
}

// Error 实现error接口
func (e *EnhancedServiceError) Error() string {
	info := GetErrorCodeInfo(e.ErrorCode)
	return fmt.Sprintf("[%s:%s] %s - %s", e.ErrorCode, info.Category, e.Message, info.Description)
}

// ToJSON 转换为JSON格式
func (e *EnhancedServiceError) ToJSON() map[string]interface{} {
	info := GetErrorCodeInfo(e.ErrorCode)
	return map[string]interface{}{
		"error_code":   string(e.ErrorCode),
		"category":     info.Category,
		"severity":     string(e.Severity),
		"message":      e.Message,
		"description":  info.Description,
		"solutions":    info.Solutions,
		"recoverable":  info.Recoverable,
		"timestamp":    e.Timestamp.Format(time.RFC3339),
		"request_id":   e.RequestID,
		"user_id":      e.UserID,
		"context":      e.Context,
	}
}

// NewEnhancedServiceError 创建增强的服务错误
func NewEnhancedServiceError(code ErrorCode, operation, message string, cause error) *EnhancedServiceError {
	info := GetErrorCodeInfo(code)
	
	serviceError := &ServiceError{
		Type:        ErrorType(info.Category),
		Operation:   operation,
		Message:     message,
		Cause:       cause,
		Recoverable: info.Recoverable,
		Suggestions: info.Solutions,
	}

	return &EnhancedServiceError{
		ServiceError: serviceError,
		ErrorCode:    code,
		Severity:     info.Severity,
		Timestamp:    time.Now(),
		Context:      make(map[string]interface{}),
	}
}

// WithRequestID 添加请求ID
func (e *EnhancedServiceError) WithRequestID(requestID string) *EnhancedServiceError {
	e.RequestID = requestID
	return e
}

// WithUserID 添加用户ID
func (e *EnhancedServiceError) WithUserID(userID string) *EnhancedServiceError {
	e.UserID = userID
	return e
}

// WithContext 添加上下文信息
func (e *EnhancedServiceError) WithContext(key string, value interface{}) *EnhancedServiceError {
	e.Context[key] = value
	return e
}

// IsCritical 检查是否为严重错误
func (e *EnhancedServiceError) IsCritical() bool {
	return e.Severity == SeverityCritical
}

// IsRecoverable 检查是否可恢复
func (e *EnhancedServiceError) IsRecoverable() bool {
	info := GetErrorCodeInfo(e.ErrorCode)
	return info.Recoverable
}

// GetSolutions 获取解决方案
func (e *EnhancedServiceError) GetSolutions() []string {
	info := GetErrorCodeInfo(e.ErrorCode)
	return info.Solutions
}