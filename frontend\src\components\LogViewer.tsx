import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Space, 
  Switch, 
  Select, 
  Tooltip,
  Badge,
  Dropdown,
  message
} from 'antd';
import { 
  SearchOutlined, 
  ClearOutlined, 
  DownloadOutlined,
  SettingOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  VerticalAlignBottomOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { FixedSizeList as List } from 'react-window';
import type { MenuProps } from 'antd';
import { useRealTimeLogs } from '@/hooks';
import type { LogLine, LogLevel } from '@/services/logsService';

const { Option } = Select;

// 日志查看器属性
export interface LogViewerProps {
  namespace?: string;
  pod?: string;
  container?: string;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
}

// 日志行组件属性
interface LogRowProps {
  index: number;
  style: React.CSSProperties;
  data: {
    logs: LogLine[];
    searchKeyword: string;
    selectedLevels: LogLevel[];
  };
}

/**
 * 日志行组件
 */
const LogRow: React.FC<LogRowProps> = ({ index, style, data }) => {
  const { logs, searchKeyword, selectedLevels } = data;
  const log = logs[index];

  if (!log) return null;

  // 检查是否匹配搜索关键词
  const matchesSearch = !searchKeyword || 
    log.content.toLowerCase().includes(searchKeyword.toLowerCase());

  // 检查是否匹配日志级别
  const matchesLevel = selectedLevels.length === 0 || 
    selectedLevels.includes(log.level);

  if (!matchesSearch || !matchesLevel) {
    return <div style={style} />;
  }

  // 日志级别颜色
  const levelColors = {
    DEBUG: '#8c8c8c',
    INFO: '#1890ff',
    WARN: '#faad14',
    WARNING: '#faad14',
    ERROR: '#ff4d4f',
    FATAL: '#a8071a',
    TRACE: '#722ed1',
  };

  // 高亮搜索关键词
  const highlightText = (text: string, keyword: string) => {
    if (!keyword) return text;
    
    const regex = new RegExp(`(${keyword})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, i) => 
      regex.test(part) ? (
        <span key={i} style={{ backgroundColor: '#fff566', color: '#000' }}>
          {part}
        </span>
      ) : part
    );
  };

  return (
    <div 
      style={{
        ...style,
        display: 'flex',
        alignItems: 'flex-start',
        padding: '4px 12px',
        borderBottom: '1px solid #f0f0f0',
        fontSize: '12px',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        lineHeight: '1.4',
      }}
    >
      {/* 时间戳 */}
      <div style={{ 
        width: '140px', 
        flexShrink: 0, 
        color: '#8c8c8c',
        marginRight: '8px'
      }}>
        {log.timestamp ? new Date(log.timestamp).toLocaleTimeString() : ''}
      </div>
      
      {/* 日志级别 */}
      <div style={{ 
        width: '60px', 
        flexShrink: 0, 
        color: levelColors[log.level] || '#000',
        fontWeight: 'bold',
        marginRight: '8px'
      }}>
        {log.level}
      </div>
      
      {/* 来源 */}
      {log.source && (
        <div style={{ 
          width: '100px', 
          flexShrink: 0, 
          color: '#8c8c8c',
          marginRight: '8px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {log.source}
        </div>
      )}
      
      {/* 日志内容 */}
      <div style={{ flex: 1, wordBreak: 'break-all' }}>
        {highlightText(log.content, searchKeyword)}
      </div>
    </div>
  );
};

/**
 * 日志查看器组件
 * 
 * 功能特性：
 * - 虚拟滚动优化
 * - 实时日志流
 * - 搜索和过滤
 * - 自动滚动
 * - 日志导出
 */
export const LogViewer: React.FC<LogViewerProps> = ({
  namespace,
  pod,
  container,
  height = 600,
  className,
  style,
}) => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedLevels, setSelectedLevels] = useState<LogLevel[]>([]);
  const [autoScroll, setAutoScroll] = useState(true);
  const [showTimestamp, setShowTimestamp] = useState(true);
  const [fontSize, setFontSize] = useState(12);
  
  const listRef = useRef<List>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 实时日志Hook
  const {
    logs,
    connected,
    loading,
    paused,
    filter,
    updateFilter,
    clearLogs,
    togglePause,
    exportLogs,
    getLogStats,
  } = useRealTimeLogs({
    namespace: namespace || '',
    pod: pod || '',
    container: container,
    follow: true,
    tailLines: 1000,
    maxLines: 10000,
    autoScroll,
  });

  // 过滤后的日志
  const filteredLogs = useMemo(() => {
    return logs.filter(log => {
      const matchesSearch = !searchKeyword || 
        log.content.toLowerCase().includes(searchKeyword.toLowerCase());
      const matchesLevel = selectedLevels.length === 0 || 
        selectedLevels.includes(log.level);
      return matchesSearch && matchesLevel;
    });
  }, [logs, searchKeyword, selectedLevels]);

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && listRef.current && filteredLogs.length > 0) {
      listRef.current.scrollToItem(filteredLogs.length - 1, 'end');
    }
  }, [filteredLogs.length, autoScroll]);

  // 更新过滤器
  useEffect(() => {
    updateFilter({
      keyword: searchKeyword,
      levels: selectedLevels,
    });
  }, [searchKeyword, selectedLevels, updateFilter]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
  };

  // 处理级别过滤
  const handleLevelFilter = (levels: LogLevel[]) => {
    setSelectedLevels(levels);
  };

  // 滚动到底部
  const scrollToBottom = () => {
    if (listRef.current && filteredLogs.length > 0) {
      listRef.current.scrollToItem(filteredLogs.length - 1, 'end');
    }
  };

  // 导出菜单
  const exportMenuItems: MenuProps['items'] = [
    {
      key: 'txt',
      label: '导出为TXT',
      onClick: () => exportLogs('txt'),
    },
    {
      key: 'json',
      label: '导出为JSON',
      onClick: () => exportLogs('json'),
    },
  ];

  // 设置菜单
  const settingsMenuItems: MenuProps['items'] = [
    {
      key: 'timestamp',
      label: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>显示时间戳</span>
          <Switch 
            size="small" 
            checked={showTimestamp} 
            onChange={setShowTimestamp}
          />
        </div>
      ),
    },
    {
      key: 'fontSize',
      label: (
        <div>
          <div style={{ marginBottom: '8px' }}>字体大小</div>
          <Select
            size="small"
            value={fontSize}
            onChange={setFontSize}
            style={{ width: '100%' }}
          >
            <Option value={10}>10px</Option>
            <Option value={12}>12px</Option>
            <Option value={14}>14px</Option>
            <Option value={16}>16px</Option>
          </Select>
        </div>
      ),
    },
  ];

  // 获取日志统计
  const stats = getLogStats();

  return (
    <Card 
      className={className}
      style={style}
      bodyStyle={{ padding: 0 }}
    >
      {/* 工具栏 */}
      <div style={{ 
        padding: '12px 16px', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '8px'
      }}>
        <Space>
          {/* 连接状态 */}
          <Badge 
            status={connected ? 'processing' : 'error'} 
            text={connected ? '已连接' : '未连接'}
          />
          
          {/* 日志统计 */}
          <span style={{ fontSize: '12px', color: '#666' }}>
            总计: {stats.total} | 过滤: {filteredLogs.length}
          </span>
        </Space>

        <Space>
          {/* 搜索 */}
          <Input
            placeholder="搜索日志..."
            prefix={<SearchOutlined />}
            value={searchKeyword}
            onChange={(e) => handleSearch(e.target.value)}
            style={{ width: 200 }}
            allowClear
          />

          {/* 级别过滤 */}
          <Select
            placeholder="日志级别"
            mode="multiple"
            value={selectedLevels}
            onChange={handleLevelFilter}
            style={{ width: 150 }}
            size="small"
          >
            <Option value="DEBUG">DEBUG</Option>
            <Option value="INFO">INFO</Option>
            <Option value="WARN">WARN</Option>
            <Option value="ERROR">ERROR</Option>
            <Option value="FATAL">FATAL</Option>
          </Select>

          {/* 控制按钮 */}
          <Tooltip title={paused ? '继续' : '暂停'}>
            <Button
              type="text"
              size="small"
              icon={paused ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
              onClick={togglePause}
            />
          </Tooltip>

          <Tooltip title="滚动到底部">
            <Button
              type="text"
              size="small"
              icon={<VerticalAlignBottomOutlined />}
              onClick={scrollToBottom}
            />
          </Tooltip>

          <Tooltip title="清空日志">
            <Button
              type="text"
              size="small"
              icon={<ClearOutlined />}
              onClick={clearLogs}
            />
          </Tooltip>

          <Dropdown menu={{ items: exportMenuItems }} trigger={['click']}>
            <Button
              type="text"
              size="small"
              icon={<DownloadOutlined />}
            />
          </Dropdown>

          <Dropdown menu={{ items: settingsMenuItems }} trigger={['click']}>
            <Button
              type="text"
              size="small"
              icon={<SettingOutlined />}
            />
          </Dropdown>
        </Space>
      </div>

      {/* 自动滚动开关 */}
      <div style={{ 
        padding: '8px 16px', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Switch 
            size="small"
            checked={autoScroll}
            onChange={setAutoScroll}
          />
          <span style={{ fontSize: '12px' }}>自动滚动</span>
        </div>
        
        {namespace && pod && container && (
          <div style={{ fontSize: '12px', color: '#666' }}>
            {namespace}/{pod}/{container}
          </div>
        )}
      </div>

      {/* 日志内容 */}
      <div 
        ref={containerRef}
        style={{ 
          height: height - 120, // 减去工具栏高度
          backgroundColor: '#fafafa'
        }}
      >
        {filteredLogs.length > 0 ? (
          <List
            ref={listRef}
            height={height - 120}
            itemCount={filteredLogs.length}
            itemSize={28}
            itemData={{
              logs: filteredLogs,
              searchKeyword,
              selectedLevels,
            }}
          >
            {LogRow}
          </List>
        ) : (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            color: '#8c8c8c'
          }}>
            {loading ? '加载中...' : '暂无日志数据'}
          </div>
        )}
      </div>
    </Card>
  );
};

export default LogViewer;
