{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:integration": "vitest run src/test/integration", "test:unit": "vitest run src/**/*.test.{ts,tsx}", "test:all": "npm run test:unit && npm run test:integration", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@monaco-editor/react": "^4.7.0", "@tanstack/react-query": "^5.84.1", "@types/react-window": "^1.8.8", "antd": "^5.26.7", "axios": "^1.11.0", "dayjs": "^1.11.13", "echarts": "^6.0.0", "eventemitter3": "^5.0.1", "js-yaml": "^4.1.0", "monaco-editor": "^0.52.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "socket.io-client": "^4.8.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.15.0", "@tanstack/react-query-devtools": "^5.84.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.13.0", "jest-environment-jsdom": "^30.0.5", "jsdom": "^26.1.0", "prettier": "^3.4.2", "typescript": "~5.8.3", "typescript-eslint": "^8.15.0", "vite": "^7.0.4", "vitest": "^3.2.4"}}