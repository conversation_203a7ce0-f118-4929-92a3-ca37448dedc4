package service

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/portforward"
	"k8s.io/client-go/transport/spdy"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// PortForwardService 端口转发服务
type PortForwardService struct {
	clientset kubernetes.Interface
	config    *rest.Config
	forwards  map[string]*PortForward
	mutex     sync.RWMutex
}

// NewPortForwardService 创建端口转发服务
func NewPortForwardService(clientset kubernetes.Interface, config *rest.Config) *PortForwardService {
	return &PortForwardService{
		clientset: clientset,
		config:    config,
		forwards:  make(map[string]*PortForward),
	}
}

// PortForward 端口转发信息
type PortForward struct {
	ID          string                    `json:"id"`
	Namespace   string                    `json:"namespace"`
	Pod         string                    `json:"pod"`
	LocalPort   int                       `json:"local_port"`
	PodPort     int                       `json:"pod_port"`
	Address     string                    `json:"address"`
	Status      string                    `json:"status"`
	CreatedAt   time.Time                 `json:"created_at"`
	UpdatedAt   time.Time                 `json:"updated_at"`
	Error       string                    `json:"error,omitempty"`
	forwarder   *portforward.PortForwarder
	stopCh      chan struct{}
	readyCh     chan struct{}
}

// PortForwardRequest 端口转发请求
type PortForwardRequest struct {
	Namespace string `json:"namespace" binding:"required"`
	Pod       string `json:"pod" binding:"required"`
	LocalPort int    `json:"local_port"`
	PodPort   int    `json:"pod_port" binding:"required"`
	Address   string `json:"address,omitempty"` // 本地绑定地址，默认为 localhost
}

// PortForwardStatus 端口转发状态
const (
	StatusCreating = "creating"
	StatusRunning  = "running"
	StatusStopped  = "stopped"
	StatusError    = "error"
)

// Create 创建端口转发
func (pfs *PortForwardService) Create(req PortForwardRequest) (*PortForward, error) {
	// 验证Pod是否存在
	pod, err := pfs.clientset.CoreV1().Pods(req.Namespace).Get(context.Background(), req.Pod, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取Pod信息失败: %w", err)
	}

	if pod.Status.Phase != "Running" {
		return nil, fmt.Errorf("Pod %s 状态不是 Running，当前状态: %s", req.Pod, pod.Status.Phase)
	}

	// 如果本地端口为0，自动分配可用端口
	if req.LocalPort == 0 {
		availablePort, err := pfs.findAvailablePort()
		if err != nil {
			return nil, fmt.Errorf("查找可用端口失败: %w", err)
		}
		req.LocalPort = availablePort
	} else {
		// 检查端口是否已被占用
		if pfs.isPortInUse(req.LocalPort) {
			return nil, fmt.Errorf("端口 %d 已被占用", req.LocalPort)
		}
	}

	// 设置默认地址
	address := req.Address
	if address == "" {
		address = "localhost"
	}

	// 生成唯一ID
	id := fmt.Sprintf("%s-%s-%d-%d-%d", req.Namespace, req.Pod, req.LocalPort, req.PodPort, time.Now().Unix())

	// 创建端口转发对象
	pf := &PortForward{
		ID:        id,
		Namespace: req.Namespace,
		Pod:       req.Pod,
		LocalPort: req.LocalPort,
		PodPort:   req.PodPort,
		Address:   address,
		Status:    StatusCreating,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		stopCh:    make(chan struct{}),
		readyCh:   make(chan struct{}),
	}

	// 保存到映射中
	pfs.mutex.Lock()
	pfs.forwards[id] = pf
	pfs.mutex.Unlock()

	// 启动端口转发
	go pfs.startPortForward(pf)

	return pf, nil
}

// List 获取所有端口转发列表
func (pfs *PortForwardService) List() []*PortForward {
	pfs.mutex.RLock()
	defer pfs.mutex.RUnlock()

	var forwards []*PortForward
	for _, pf := range pfs.forwards {
		// 创建副本，避免返回内部对象
		copy := &PortForward{
			ID:        pf.ID,
			Namespace: pf.Namespace,
			Pod:       pf.Pod,
			LocalPort: pf.LocalPort,
			PodPort:   pf.PodPort,
			Address:   pf.Address,
			Status:    pf.Status,
			CreatedAt: pf.CreatedAt,
			UpdatedAt: pf.UpdatedAt,
			Error:     pf.Error,
		}
		forwards = append(forwards, copy)
	}

	return forwards
}

// Get 获取指定ID的端口转发
func (pfs *PortForwardService) Get(id string) (*PortForward, error) {
	pfs.mutex.RLock()
	defer pfs.mutex.RUnlock()

	pf, exists := pfs.forwards[id]
	if !exists {
		return nil, fmt.Errorf("端口转发 %s 不存在", id)
	}

	// 返回副本
	return &PortForward{
		ID:        pf.ID,
		Namespace: pf.Namespace,
		Pod:       pf.Pod,
		LocalPort: pf.LocalPort,
		PodPort:   pf.PodPort,
		Address:   pf.Address,
		Status:    pf.Status,
		CreatedAt: pf.CreatedAt,
		UpdatedAt: pf.UpdatedAt,
		Error:     pf.Error,
	}, nil
}

// Stop 停止端口转发
func (pfs *PortForwardService) Stop(id string) error {
	pfs.mutex.Lock()
	defer pfs.mutex.Unlock()

	pf, exists := pfs.forwards[id]
	if !exists {
		return fmt.Errorf("端口转发 %s 不存在", id)
	}

	if pf.Status == StatusStopped {
		return fmt.Errorf("端口转发 %s 已经停止", id)
	}

	// 发送停止信号
	close(pf.stopCh)
	pf.Status = StatusStopped
	pf.UpdatedAt = time.Now()

	return nil
}

// Delete 删除端口转发
func (pfs *PortForwardService) Delete(id string) error {
	pfs.mutex.Lock()
	defer pfs.mutex.Unlock()

	pf, exists := pfs.forwards[id]
	if !exists {
		return fmt.Errorf("端口转发 %s 不存在", id)
	}

	// 如果还在运行，先停止
	if pf.Status == StatusRunning {
		close(pf.stopCh)
	}

	// 从映射中删除
	delete(pfs.forwards, id)

	return nil
}

// 私有方法
func (pfs *PortForwardService) startPortForward(pf *PortForward) {
	defer func() {
		if r := recover(); r != nil {
			pfs.mutex.Lock()
			pf.Status = StatusError
			pf.Error = fmt.Sprintf("端口转发异常: %v", r)
			pf.UpdatedAt = time.Now()
			pfs.mutex.Unlock()
		}
	}()

	// 构建端口转发字符串
	portString := fmt.Sprintf("%d:%d", pf.LocalPort, pf.PodPort)

	// 创建端口转发请求
	roundTripper, upgrader, err := spdy.RoundTripperFor(pfs.config)
	if err != nil {
		pfs.updatePortForwardError(pf, fmt.Sprintf("创建 SPDY round tripper 失败: %v", err))
		return
	}

	path := fmt.Sprintf("/api/v1/namespaces/%s/pods/%s/portforward", pf.Namespace, pf.Pod)
	hostIP := strings.TrimLeft(pfs.config.Host, "htps:/")
	serverURL := url.URL{Scheme: "https", Path: path, Host: hostIP}

	dialer := spdy.NewDialer(upgrader, &http.Client{Transport: roundTripper}, http.MethodPost, &serverURL)

	// 创建端口转发器
	forwarder, err := portforward.New(dialer, []string{portString}, pf.stopCh, pf.readyCh, os.Stdout, os.Stderr)
	if err != nil {
		pfs.updatePortForwardError(pf, fmt.Sprintf("创建端口转发器失败: %v", err))
		return
	}

	pf.forwarder = forwarder

	// 启动端口转发
	go func() {
		if err := forwarder.ForwardPorts(); err != nil {
			pfs.updatePortForwardError(pf, fmt.Sprintf("端口转发错误: %v", err))
		}
	}()

	// 等待端口转发就绪
	select {
	case <-pf.readyCh:
		pfs.mutex.Lock()
		pf.Status = StatusRunning
		pf.UpdatedAt = time.Now()
		pfs.mutex.Unlock()
	case <-time.After(30 * time.Second):
		pfs.updatePortForwardError(pf, "端口转发启动超时")
		return
	}

	// 等待停止信号
	<-pf.stopCh
	pfs.mutex.Lock()
	pf.Status = StatusStopped
	pf.UpdatedAt = time.Now()
	pfs.mutex.Unlock()
}

func (pfs *PortForwardService) updatePortForwardError(pf *PortForward, errorMsg string) {
	pfs.mutex.Lock()
	defer pfs.mutex.Unlock()
	pf.Status = StatusError
	pf.Error = errorMsg
	pf.UpdatedAt = time.Now()
}

func (pfs *PortForwardService) findAvailablePort() (int, error) {
	// 从8080开始查找可用端口
	for port := 8080; port <= 9999; port++ {
		if !pfs.isPortInUse(port) {
			return port, nil
		}
	}
	return 0, fmt.Errorf("没有找到可用端口")
}

func (pfs *PortForwardService) isPortInUse(port int) bool {
	// 检查系统端口是否被占用
	conn, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		return true
	}
	conn.Close()

	// 检查是否已有端口转发使用此端口
	pfs.mutex.RLock()
	defer pfs.mutex.RUnlock()
	for _, pf := range pfs.forwards {
		if pf.LocalPort == port && (pf.Status == StatusRunning || pf.Status == StatusCreating) {
			return true
		}
	}

	return false
}
