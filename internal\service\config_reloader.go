package service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"go.uber.org/zap"
	"k8s-helper/pkg/config"
)

// ConfigReloadEvent 配置重载事件
type ConfigReloadEvent struct {
	Type      ConfigReloadEventType `json:"type"`
	Path      string                `json:"path"`
	OldConfig *config.Config        `json:"old_config,omitempty"`
	NewConfig *config.Config        `json:"new_config,omitempty"`
	Error     error                 `json:"error,omitempty"`
	Timestamp time.Time             `json:"timestamp"`
}

// ConfigReloadEventType 配置重载事件类型
type ConfigReloadEventType string

const (
	ConfigReloadEventStarted   ConfigReloadEventType = "started"
	ConfigReloadEventSuccess   ConfigReloadEventType = "success"
	ConfigReloadEventFailed    ConfigReloadEventType = "failed"
	ConfigReloadEventValidated ConfigReloadEventType = "validated"
	ConfigReloadEventApplied   ConfigReloadEventType = "applied"
)

// ConfigReloadHandler 配置重载处理器接口
type ConfigReloadHandler interface {
	OnConfigReload(oldConfig, newConfig *config.Config) error
	Name() string
}

// ConfigReloader 配置热重载管理器
type ConfigReloader struct {
	logger        *zap.Logger
	configPath    string
	currentConfig *config.Config
	watcher       *fsnotify.Watcher
	handlers      map[string]ConfigReloadHandler
	eventChan     chan ConfigReloadEvent

	// 配置
	debounceTime   time.Duration
	validateConfig bool
	backupConfig   bool
	maxBackups     int

	// 状态
	isWatching     bool
	lastReloadTime time.Time
	reloadCount    int64
	errorCount     int64

	// 控制
	ctx         context.Context
	cancel      context.CancelFunc
	mutex       sync.RWMutex
	reloadMutex sync.Mutex
}

// ConfigReloaderConfig 配置重载器配置
type ConfigReloaderConfig struct {
	DebounceTime   time.Duration `yaml:"debounce_time" json:"debounce_time"`
	ValidateConfig bool          `yaml:"validate_config" json:"validate_config"`
	BackupConfig   bool          `yaml:"backup_config" json:"backup_config"`
	MaxBackups     int           `yaml:"max_backups" json:"max_backups"`
	EventBuffer    int           `yaml:"event_buffer" json:"event_buffer"`
}

// DefaultConfigReloaderConfig 默认配置重载器配置
func DefaultConfigReloaderConfig() *ConfigReloaderConfig {
	return &ConfigReloaderConfig{
		DebounceTime:   500 * time.Millisecond,
		ValidateConfig: true,
		BackupConfig:   true,
		MaxBackups:     10,
		EventBuffer:    100,
	}
}

// NewConfigReloader 创建配置重载管理器
func NewConfigReloader(configPath string, initialConfig *config.Config, logger *zap.Logger) (*ConfigReloader, error) {
	return NewConfigReloaderWithConfig(configPath, initialConfig, DefaultConfigReloaderConfig(), logger)
}

// NewConfigReloaderWithConfig 使用指定配置创建配置重载管理器
func NewConfigReloaderWithConfig(
	configPath string,
	initialConfig *config.Config,
	reloaderConfig *ConfigReloaderConfig,
	logger *zap.Logger,
) (*ConfigReloader, error) {

	// 创建文件监听器
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return nil, fmt.Errorf("创建文件监听器失败: %w", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	cr := &ConfigReloader{
		logger:         logger,
		configPath:     configPath,
		currentConfig:  initialConfig,
		watcher:        watcher,
		handlers:       make(map[string]ConfigReloadHandler),
		eventChan:      make(chan ConfigReloadEvent, reloaderConfig.EventBuffer),
		debounceTime:   reloaderConfig.DebounceTime,
		validateConfig: reloaderConfig.ValidateConfig,
		backupConfig:   reloaderConfig.BackupConfig,
		maxBackups:     reloaderConfig.MaxBackups,
		ctx:            ctx,
		cancel:         cancel,
	}

	return cr, nil
}

// RegisterHandler 注册配置重载处理器
func (cr *ConfigReloader) RegisterHandler(handler ConfigReloadHandler) {
	cr.mutex.Lock()
	defer cr.mutex.Unlock()

	cr.handlers[handler.Name()] = handler
	cr.logger.Info("注册配置重载处理器", zap.String("handler", handler.Name()))
}

// UnregisterHandler 注销配置重载处理器
func (cr *ConfigReloader) UnregisterHandler(name string) {
	cr.mutex.Lock()
	defer cr.mutex.Unlock()

	delete(cr.handlers, name)
	cr.logger.Info("注销配置重载处理器", zap.String("handler", name))
}

// Start 启动配置热重载监听
func (cr *ConfigReloader) Start() error {
	cr.mutex.Lock()
	defer cr.mutex.Unlock()

	if cr.isWatching {
		return fmt.Errorf("配置重载器已在运行")
	}

	// 添加配置文件到监听列表
	configDir := filepath.Dir(cr.configPath)
	if err := cr.watcher.Add(configDir); err != nil {
		return fmt.Errorf("添加配置目录到监听失败: %w", err)
	}

	cr.isWatching = true

	// 启动监听协程
	go cr.watchLoop()
	go cr.eventLoop()

	cr.logger.Info("配置热重载监听已启动",
		zap.String("configPath", cr.configPath),
		zap.Duration("debounceTime", cr.debounceTime))

	return nil
}

// Stop 停止配置热重载监听
func (cr *ConfigReloader) Stop() error {
	cr.mutex.Lock()
	defer cr.mutex.Unlock()

	if !cr.isWatching {
		return nil
	}

	cr.cancel()

	if err := cr.watcher.Close(); err != nil {
		cr.logger.Error("关闭文件监听器失败", zap.Error(err))
	}

	close(cr.eventChan)
	cr.isWatching = false

	cr.logger.Info("配置热重载监听已停止")
	return nil
}

// ReloadNow 立即重载配置
func (cr *ConfigReloader) ReloadNow() error {
	cr.reloadMutex.Lock()
	defer cr.reloadMutex.Unlock()

	return cr.performReload()
}

// GetCurrentConfig 获取当前配置
func (cr *ConfigReloader) GetCurrentConfig() *config.Config {
	cr.mutex.RLock()
	defer cr.mutex.RUnlock()

	return cr.currentConfig
}

// GetStats 获取重载统计信息
func (cr *ConfigReloader) GetStats() map[string]interface{} {
	cr.mutex.RLock()
	defer cr.mutex.RUnlock()

	return map[string]interface{}{
		"is_watching":      cr.isWatching,
		"config_path":      cr.configPath,
		"last_reload_time": cr.lastReloadTime,
		"reload_count":     cr.reloadCount,
		"error_count":      cr.errorCount,
		"handlers_count":   len(cr.handlers),
		"debounce_time":    cr.debounceTime.String(),
	}
}

// GetEventChannel 获取事件通道（只读）
func (cr *ConfigReloader) GetEventChannel() <-chan ConfigReloadEvent {
	return cr.eventChan
}

// watchLoop 文件监听循环
func (cr *ConfigReloader) watchLoop() {
	debounceTimer := time.NewTimer(0)
	if !debounceTimer.Stop() {
		<-debounceTimer.C
	}

	for {
		select {
		case <-cr.ctx.Done():
			return
		case event, ok := <-cr.watcher.Events:
			if !ok {
				return
			}

			// 只处理配置文件的修改事件
			if cr.shouldProcessEvent(event) {
				cr.logger.Debug("检测到配置文件变化",
					zap.String("file", event.Name),
					zap.String("op", event.Op.String()))

				// 重置防抖定时器
				debounceTimer.Reset(cr.debounceTime)
			}

		case err, ok := <-cr.watcher.Errors:
			if !ok {
				return
			}
			cr.logger.Error("文件监听错误", zap.Error(err))

		case <-debounceTimer.C:
			// 防抖时间到，执行重载
			go func() {
				cr.reloadMutex.Lock()
				defer cr.reloadMutex.Unlock()

				if err := cr.performReload(); err != nil {
					cr.logger.Error("配置重载失败", zap.Error(err))
				}
			}()
		}
	}
}

// eventLoop 事件处理循环
func (cr *ConfigReloader) eventLoop() {
	for {
		select {
		case <-cr.ctx.Done():
			return
		case event, ok := <-cr.eventChan:
			if !ok {
				return
			}

			cr.logger.Debug("配置重载事件",
				zap.String("type", string(event.Type)),
				zap.String("path", event.Path),
				zap.Time("timestamp", event.Timestamp))
		}
	}
}

// shouldProcessEvent 判断是否应该处理事件
func (cr *ConfigReloader) shouldProcessEvent(event fsnotify.Event) bool {
	// 只处理写入和创建事件
	if event.Op&fsnotify.Write == 0 && event.Op&fsnotify.Create == 0 {
		return false
	}

	// 检查是否是配置文件
	eventPath, err := filepath.Abs(event.Name)
	if err != nil {
		return false
	}

	configPath, err := filepath.Abs(cr.configPath)
	if err != nil {
		return false
	}

	return eventPath == configPath
}

// performReload 执行配置重载
func (cr *ConfigReloader) performReload() error {
	cr.logger.Info("开始重载配置", zap.String("path", cr.configPath))

	// 发送开始事件
	cr.sendEvent(ConfigReloadEvent{
		Type:      ConfigReloadEventStarted,
		Path:      cr.configPath,
		Timestamp: time.Now(),
	})

	// 备份当前配置
	oldConfig := cr.currentConfig
	if cr.backupConfig {
		if err := cr.backupCurrentConfig(); err != nil {
			cr.logger.Warn("备份当前配置失败", zap.Error(err))
		}
	}

	// 加载新配置
	newConfig, err := config.LoadConfig(cr.configPath)
	if err != nil {
		cr.errorCount++
		cr.sendEvent(ConfigReloadEvent{
			Type:      ConfigReloadEventFailed,
			Path:      cr.configPath,
			Error:     err,
			Timestamp: time.Now(),
		})
		return fmt.Errorf("加载新配置失败: %w", err)
	}

	// 验证新配置
	if cr.validateConfig {
		if err := newConfig.Validate(); err != nil {
			cr.errorCount++
			cr.sendEvent(ConfigReloadEvent{
				Type:      ConfigReloadEventFailed,
				Path:      cr.configPath,
				Error:     err,
				Timestamp: time.Now(),
			})
			return fmt.Errorf("新配置验证失败: %w", err)
		}

		cr.sendEvent(ConfigReloadEvent{
			Type:      ConfigReloadEventValidated,
			Path:      cr.configPath,
			NewConfig: newConfig,
			Timestamp: time.Now(),
		})
	}

	// 通知所有处理器
	cr.mutex.RLock()
	handlers := make([]ConfigReloadHandler, 0, len(cr.handlers))
	for _, handler := range cr.handlers {
		handlers = append(handlers, handler)
	}
	cr.mutex.RUnlock()

	for _, handler := range handlers {
		if err := handler.OnConfigReload(oldConfig, newConfig); err != nil {
			cr.logger.Error("配置重载处理器执行失败",
				zap.String("handler", handler.Name()),
				zap.Error(err))
			// 继续执行其他处理器，不因为一个失败而中断
		}
	}

	// 更新当前配置
	cr.mutex.Lock()
	cr.currentConfig = newConfig
	cr.lastReloadTime = time.Now()
	cr.reloadCount++
	cr.mutex.Unlock()

	// 发送成功事件
	cr.sendEvent(ConfigReloadEvent{
		Type:      ConfigReloadEventSuccess,
		Path:      cr.configPath,
		OldConfig: oldConfig,
		NewConfig: newConfig,
		Timestamp: time.Now(),
	})

	cr.logger.Info("配置重载完成",
		zap.String("path", cr.configPath),
		zap.Int("handlers", len(handlers)))

	return nil
}

// sendEvent 发送事件
func (cr *ConfigReloader) sendEvent(event ConfigReloadEvent) {
	select {
	case cr.eventChan <- event:
	default:
		cr.logger.Warn("事件通道已满，丢弃事件", zap.String("type", string(event.Type)))
	}
}

// backupCurrentConfig 备份当前配置
func (cr *ConfigReloader) backupCurrentConfig() error {
	if cr.currentConfig == nil {
		return nil
	}

	backupDir := filepath.Join(filepath.Dir(cr.configPath), "backups")
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	timestamp := time.Now().Format("20060102-150405")
	backupPath := filepath.Join(backupDir, fmt.Sprintf("config-backup-%s.yaml", timestamp))

	if err := config.SaveConfig(cr.currentConfig, backupPath); err != nil {
		return fmt.Errorf("保存配置备份失败: %w", err)
	}

	// 清理旧备份
	cr.cleanupOldBackups(backupDir)

	cr.logger.Debug("配置备份完成", zap.String("backup", backupPath))
	return nil
}

// cleanupOldBackups 清理旧备份
func (cr *ConfigReloader) cleanupOldBackups(backupDir string) {
	// 这里可以实现清理逻辑，保留最近的N个备份
	// 为了简化，这里只是记录日志
	cr.logger.Debug("清理旧配置备份", zap.String("dir", backupDir))
}
