{{define "logs"}}
<div class="module-content">
    <div class="logs-container">
        <!-- Pod选择器区域 -->
        <div class="section pod-selector">
            <h3>📋 Pod 选择器</h3>
            <div class="section-content">
                <div class="selector-controls">
                    <div class="control-group">
                        <label for="namespace-select" class="form-label">命名空间:</label>
                        <select id="namespace-select" class="form-control">
                            <option value="">选择命名空间...</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label for="pod-select" class="form-label">Pod:</label>
                        <select id="pod-select" class="form-control" disabled>
                            <option value="">请先选择命名空间</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label for="container-select" class="form-label">容器:</label>
                        <select id="container-select" class="form-control" disabled>
                            <option value="">请先选择Pod</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <button id="load-logs-btn" class="btn btn-primary" disabled>
                            <span class="loading" id="logs-loading" style="display: none;"></span>
                            加载日志
                        </button>
                        <button id="test-logs-btn" class="btn btn-secondary" onclick="testLogDisplay()">
                            <i class="icon">🧪</i>
                            测试日志显示
                        </button>
                    </div>
                </div>
                
                <!-- 日志选项 -->
                <div class="log-options">
                    <div class="option-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="follow-logs" />
                            <span>实时跟踪</span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="show-timestamps" checked />
                            <span>显示时间戳</span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="show-previous" />
                            <span>显示之前容器日志</span>
                        </label>
                    </div>
                    
                    <div class="option-group">
                        <label for="tail-lines" class="form-label">显示行数:</label>
                        <select id="tail-lines" class="form-control">
                            <option value="50">最后50行</option>
                            <option value="100" selected>最后100行</option>
                            <option value="200">最后200行</option>
                            <option value="500">最后500行</option>
                            <option value="-1">全部</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 日志控制区域 -->
        <div class="section log-controls">
            <h3>🔧 日志控制</h3>
            <div class="section-content">
                <div class="controls-row">
                    <div class="search-group">
                        <input type="text" id="log-search" class="form-control" placeholder="搜索日志内容...">
                        <button id="search-btn" class="btn btn-secondary">搜索</button>
                        <button id="clear-search-btn" class="btn btn-secondary">清除</button>
                    </div>
                    
                    <div class="filter-group">
                        <select id="log-level-filter" class="form-control">
                            <option value="">所有级别</option>
                            <option value="error">错误</option>
                            <option value="warning">警告</option>
                            <option value="info">信息</option>
                            <option value="debug">调试</option>
                        </select>
                    </div>
                    
                    <div class="action-group">
                        <button id="clear-logs-btn" class="btn btn-warning">清空日志</button>
                        <button id="download-logs-btn" class="btn btn-success">下载日志</button>
                        <button id="auto-scroll-btn" class="btn btn-secondary active" data-enabled="true">
                            自动滚动
                        </button>
                    </div>
                </div>
                
                <!-- 连接状态指示器 -->
                <div class="connection-status">
                    <div id="connection-indicator" class="status-indicator disconnected">
                        <span class="status-dot"></span>
                        <span id="connection-text">未连接</span>
                    </div>
                    <div class="log-stats">
                        <span>总行数: <span id="total-lines">0</span></span>
                        <span>过滤后: <span id="filtered-lines">0</span></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 日志显示区域 -->
        <div class="section log-display">
            <h3>📄 日志内容</h3>
            <div class="section-content">
                <div class="log-viewer-container">
                    <div id="log-viewer" class="log-viewer">
                        <div class="log-placeholder">
                            <div class="placeholder-content">
                                <i class="placeholder-icon">📋</i>
                                <h4>选择Pod开始查看日志</h4>
                                <p>请在上方选择命名空间、Pod和容器，然后点击"加载日志"按钮</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 日志行模板 -->
                    <template id="log-line-template">
                        <div class="log-line" data-level="">
                            <span class="log-timestamp"></span>
                            <span class="log-level"></span>
                            <span class="log-content"></span>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        
        <!-- Pod信息面板 -->
        <div class="section pod-info-panel" id="pod-info-panel" style="display: none;">
            <h3>ℹ️ Pod 信息</h3>
            <div class="section-content">
                <div class="pod-info-grid">
                    <div class="info-item">
                        <span class="info-label">Pod名称:</span>
                        <span id="pod-name" class="info-value">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">命名空间:</span>
                        <span id="pod-namespace" class="info-value">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">状态:</span>
                        <span id="pod-status" class="info-value">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">创建时间:</span>
                        <span id="pod-created" class="info-value">--</span>
                    </div>
                </div>
                
                <div class="containers-info">
                    <h4>容器列表:</h4>
                    <div id="containers-list" class="containers-grid">
                        <!-- 容器信息将动态填充 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}
