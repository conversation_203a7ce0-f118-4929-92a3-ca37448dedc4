// API接口类型定义

// ============ 基础类型 ============

// 健康检查响应
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version?: string;
  uptime?: number;
}

// ============ ETCD相关类型 ============

// ETCD备份请求
export interface ETCDBackupRequest {
  output_path: string;
  compress?: boolean;
  description?: string;
}

// ETCD备份响应
export interface ETCDBackupResponse {
  backup_id: string;
  output_path: string;
  size: number;
  created_at: string;
  checksum: string;
  compressed: boolean;
  description?: string;
}

// ETCD备份列表项
export interface ETCDBackupItem {
  id: string;
  path: string;
  size: number;
  created_at: string;
  checksum: string;
  compressed: boolean;
  description?: string;
  valid: boolean;
}

// ETCD恢复请求
export interface ETCDRestoreRequest {
  snapshot_path: string;
  data_dir?: string;
  initial_cluster?: string;
  initial_advertise_peer_urls?: string;
  name?: string;
  skip_hash_check?: boolean;
}

// ETCD恢复响应
export interface ETCDRestoreResponse {
  restore_id: string;
  snapshot_path: string;
  data_dir: string;
  status: 'success' | 'failed' | 'in_progress';
  message: string;
  started_at: string;
  completed_at?: string;
}

// ETCD验证请求
export interface ETCDVerifyRequest {
  snapshot_path: string;
}

// ETCD验证响应
export interface ETCDVerifyResponse {
  valid: boolean;
  size: number;
  checksum: string;
  created_at?: string;
  error?: string;
}

// ETCD状态响应
export interface ETCDStatusResponse {
  cluster_id: string;
  member_id: string;
  raft_term: number;
  raft_index: number;
  raft_applied_index: number;
  errors: string[];
  db_size: number;
  db_size_in_use: number;
  leader: string;
  is_learner: boolean;
  members: ETCDMember[];
}

// ETCD成员信息
export interface ETCDMember {
  id: string;
  name: string;
  peer_urls: string[];
  client_urls: string[];
  is_leader: boolean;
  is_learner: boolean;
}

// ETCD CronJob请求
export interface ETCDCronJobRequest {
  name: string;
  schedule: string; // cron表达式
  backup_path: string;
  compress?: boolean;
  retention_days?: number;
  description?: string;
}

// ETCD CronJob响应
export interface ETCDCronJobResponse {
  name: string;
  schedule: string;
  backup_path: string;
  compress: boolean;
  retention_days: number;
  description?: string;
  status: 'active' | 'suspended';
  last_schedule_time?: string;
  next_schedule_time?: string;
  created_at: string;
  updated_at: string;
}

// ============ 集群信息相关类型 ============

// 集群信息响应
export interface ClusterInfoResponse {
  cluster_name: string;
  cluster_version: string;
  api_server_url: string;
  nodes: ClusterNode[];
  namespaces: ClusterNamespace[];
  pods: ClusterPodSummary;
  services: ClusterServiceSummary;
  deployments: ClusterDeploymentSummary;
  resource_usage: ClusterResourceUsage;
  health_status: ClusterHealthStatus;
  last_updated: string;
}

// 集群节点信息
export interface ClusterNode {
  name: string;
  status: 'Ready' | 'NotReady' | 'Unknown';
  roles: string[];
  age: string;
  version: string;
  internal_ip: string;
  external_ip?: string;
  os_image: string;
  kernel_version: string;
  container_runtime: string;
  cpu_capacity: string;
  memory_capacity: string;
  cpu_usage: number;
  memory_usage: number;
  pod_count: number;
  pod_capacity: number;
  conditions: NodeCondition[];
}

// 节点状态条件
export interface NodeCondition {
  type: string;
  status: 'True' | 'False' | 'Unknown';
  reason?: string;
  message?: string;
  last_transition_time: string;
}

// 集群命名空间
export interface ClusterNamespace {
  name: string;
  status: 'Active' | 'Terminating';
  age: string;
  pod_count: number;
  service_count: number;
  deployment_count: number;
}

// Pod摘要信息
export interface ClusterPodSummary {
  total: number;
  running: number;
  pending: number;
  succeeded: number;
  failed: number;
  unknown: number;
}

// Service摘要信息
export interface ClusterServiceSummary {
  total: number;
  cluster_ip: number;
  node_port: number;
  load_balancer: number;
  external_name: number;
}

// Deployment摘要信息
export interface ClusterDeploymentSummary {
  total: number;
  available: number;
  unavailable: number;
  up_to_date: number;
}

// 集群资源使用情况
export interface ClusterResourceUsage {
  cpu: ResourceMetric;
  memory: ResourceMetric;
  storage: ResourceMetric;
  pods: ResourceMetric;
}

// 资源指标
export interface ResourceMetric {
  used: number;
  total: number;
  percentage: number;
  unit: string;
}

// 集群健康状态
export interface ClusterHealthStatus {
  overall: 'healthy' | 'warning' | 'critical';
  components: ComponentHealth[];
  last_check: string;
}

// 组件健康状态
export interface ComponentHealth {
  name: string;
  status: 'healthy' | 'warning' | 'critical';
  message?: string;
  last_check: string;
}

// ============ Pod和日志相关类型 ============

// Pod信息
export interface PodInfo {
  name: string;
  namespace: string;
  status: 'Running' | 'Pending' | 'Succeeded' | 'Failed' | 'Unknown';
  ready: string; // "1/1"
  restarts: number;
  age: string;
  ip: string;
  node: string;
  containers: ContainerInfo[];
  labels: Record<string, string>;
  annotations: Record<string, string>;
  conditions: PodCondition[];
  events: PodEvent[];
}

// 容器信息
export interface ContainerInfo {
  name: string;
  image: string;
  ready: boolean;
  restart_count: number;
  state: ContainerState;
  last_state?: ContainerState;
  resources: ContainerResources;
}

// 容器状态
export interface ContainerState {
  waiting?: {
    reason: string;
    message?: string;
  };
  running?: {
    started_at: string;
  };
  terminated?: {
    exit_code: number;
    reason: string;
    message?: string;
    started_at: string;
    finished_at: string;
  };
}

// 容器资源
export interface ContainerResources {
  requests?: {
    cpu?: string;
    memory?: string;
  };
  limits?: {
    cpu?: string;
    memory?: string;
  };
}

// Pod状态条件
export interface PodCondition {
  type: string;
  status: 'True' | 'False' | 'Unknown';
  reason?: string;
  message?: string;
  last_transition_time: string;
}

// Pod事件
export interface PodEvent {
  type: 'Normal' | 'Warning';
  reason: string;
  message: string;
  first_timestamp: string;
  last_timestamp: string;
  count: number;
}

// Pod日志请求参数
export interface PodLogsRequest {
  namespace: string;
  pod: string;
  container?: string;
  follow?: boolean;
  previous?: boolean;
  since_seconds?: number;
  since_time?: string;
  timestamps?: boolean;
  tail_lines?: number;
  limit_bytes?: number;
}

// ============ 端口转发相关类型 ============

// 端口转发请求
export interface PortForwardRequest {
  namespace: string;
  pod: string;
  local_port: number;
  remote_port: number;
  protocol?: 'tcp' | 'udp';
  description?: string;
}

// 端口转发响应
export interface PortForwardResponse {
  id: string;
  namespace: string;
  pod: string;
  local_port: number;
  remote_port: number;
  protocol: 'tcp' | 'udp';
  status: 'active' | 'stopped' | 'error';
  description?: string;
  created_at: string;
  started_at?: string;
  stopped_at?: string;
  error_message?: string;
}

// ============ 资源清理相关类型 ============

// 清理扫描请求
export interface CleanupScanRequest {
  namespaces?: string[];
  resource_types?: string[];
  age_threshold?: string; // "7d", "1h", etc.
  dry_run?: boolean;
}

// 清理扫描响应
export interface CleanupScanResponse {
  scan_id: string;
  total_resources: number;
  cleanable_resources: CleanupResource[];
  estimated_savings: ResourceSavings;
  scan_time: string;
}

// 可清理的资源
export interface CleanupResource {
  type: string;
  namespace: string;
  name: string;
  age: string;
  size?: number;
  reason: string;
  safe_to_delete: boolean;
  dependencies: string[];
}

// 资源节省估算
export interface ResourceSavings {
  cpu: string;
  memory: string;
  storage: string;
  cost_estimate?: number;
}

// 清理执行请求
export interface CleanupExecuteRequest {
  scan_id: string;
  resource_ids: string[];
  force?: boolean;
  dry_run?: boolean;
}

// 清理执行响应
export interface CleanupExecuteResponse {
  execution_id: string;
  scan_id: string;
  total_selected: number;
  successfully_deleted: number;
  failed_deletions: number;
  results: CleanupResult[];
  execution_time: string;
}

// 清理结果
export interface CleanupResult {
  resource_id: string;
  resource_type: string;
  namespace: string;
  name: string;
  status: 'success' | 'failed' | 'skipped';
  message?: string;
  deleted_at?: string;
}
