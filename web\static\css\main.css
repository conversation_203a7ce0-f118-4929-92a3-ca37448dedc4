/* K8s-Helper Web Interface - Main Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

/* Layout Modes */
body.sidebar-layout {
    overflow-x: hidden;
}

body.traditional-layout {
    /* 保持原有布局样式 */
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* App Container for Sidebar Layout */
.app-container {
    display: flex;
    min-height: 100vh;
    background-color: #f5f7fa;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.sidebar-logo .icon {
    font-size: 1.5rem;
}

.sidebar-title {
    font-size: 1.2rem;
    font-weight: 600;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    display: none; /* 默认隐藏，移动端显示 */
}

.sidebar-toggle:hover {
    background-color: rgba(255,255,255,0.1);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    position: relative;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-left-color: #3498db;
}

.nav-link.active {
    background-color: rgba(255,255,255,0.15);
    color: white;
    border-left-color: #e74c3c;
}

.nav-icon {
    font-size: 1.2rem;
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

.nav-text {
    font-weight: 500;
    font-size: 0.95rem;
    flex: 1;
}

.nav-label {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-left: auto;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.connection-status {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #95a5a6;
}

.status-indicator.connected {
    background-color: #27ae60;
}

.status-indicator.disconnected {
    background-color: #e74c3c;
}

.status-indicator.warning {
    background-color: #f39c12;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.header .subtitle {
    opacity: 0.9;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

/* Main Wrapper for Sidebar Layout */
.main-wrapper {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

/* Top Header for Sidebar Layout */
.top-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 30px;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    color: #495057;
    transition: background-color 0.2s ease;
}

.mobile-menu-toggle:hover {
    background-color: #f8f9fa;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Main Content Area */
.main-content {
    flex: 1;
    padding: 30px;
    background-color: #f5f7fa;
}

/* Navigation Tabs */
.tabs {
    display: flex;
    background: white;
    border-radius: 8px 8px 0 0;
    margin-top: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.tab {
    flex: 1;
    padding: 15px 20px;
    text-align: center;
    cursor: pointer;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab:last-child {
    border-right: none;
}

.tab:hover {
    background: #e9ecef;
}

.tab.active {
    background: white;
    color: #667eea;
    border-bottom: 3px solid #667eea;
}

.tab .icon {
    margin-right: 8px;
    font-size: 1.1rem;
}

/* Tab Content */
.tab-content {
    display: none;
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-height: 500px;
}

.tab-content.active {
    display: block;
}

/* Cards */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.card-header {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.card-body {
    padding: 20px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-right: 10px;
    margin-bottom: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Results and Status */
.result {
    margin-top: 15px;
    padding: 15px;
    border-radius: 6px;
    display: none;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.result.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.result.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.result.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Indicators */
.status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status.healthy {
    background: #d4edda;
    color: #155724;
}

.status.unhealthy {
    background: #f8d7da;
    color: #721c24;
}

.status.unknown {
    background: #e2e3e5;
    color: #495057;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .tabs {
        flex-direction: column;
    }
    
    .tab {
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }
    
    .tab:last-child {
        border-bottom: none;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .btn {
        width: 100%;
        margin-right: 0;
        margin-bottom: 8px;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .connection-status {
        flex-direction: column;
        gap: 8px;
        margin-top: 15px;
    }

    .button-group {
        flex-direction: column;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* Dashboard Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* 统一的metric-card样式定义 */
.metric-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    text-align: center;
    overflow: hidden;
    word-wrap: break-word;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.metric-card h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.metric-value {
    font-size: 1.8rem;
    font-weight: 600;
    color: #3498db;
    margin-bottom: 15px;
    min-height: 60px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    align-items: center;
    justify-content: center;
    word-wrap: break-word;
}

/* Metric-card 相关样式 */
.metric-actions {
    margin-top: 10px;
}

.metric-actions .btn {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Connection Status */
.connection-status {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-top: 10px;
}

.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status.healthy {
    background-color: #d4edda;
    color: #155724;
}

.status.unknown {
    background-color: #fff3cd;
    color: #856404;
}

.status.unhealthy {
    background-color: #f8d7da;
    color: #721c24;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Backup List */
.backup-list {
    max-height: 400px;
    overflow-y: auto;
}

.backup-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 10px;
    background: #f8f9fa;
}

.backup-info h5 {
    margin: 0 0 5px 0;
    color: #495057;
}

.backup-info p {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
}

.backup-actions {
    display: flex;
    gap: 10px;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Logs Container */
.logs-container {
    margin-top: 20px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #f8f9fa;
    max-height: 500px;
    overflow: hidden;
}

.logs-output {
    height: 450px;
    overflow-y: auto;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    background: #1e1e1e;
    color: #d4d4d4;
}

/* 重复的log-line样式已移除，使用下方统一定义 */

.log-timestamp {
    color: #569cd6;
    margin-right: 10px;
}

.log-content {
    color: #d4d4d4;
}

/* Button Groups */
.button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

/* Icons */
.icon {
    margin-right: 8px;
    font-size: 1.1rem;
}

/* Footer */
footer {
    background: #2c3e50;
    color: white;
    padding: 20px 0;
    margin-top: 40px;
    text-align: center;
}

footer p {
    margin: 5px 0;
    opacity: 0.9;
}

/* Result Messages */
.result {
    margin-top: 15px;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid transparent;
    display: none;
}

.result.success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.result.error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.result.info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-error { color: #dc3545; }
.text-success { color: #28a745; }
.text-warning { color: #ffc107; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Sidebar States */
.sidebar.collapsed {
    transform: translateX(-280px);
}

.sidebar.collapsed + .main-wrapper {
    margin-left: 0;
}

/* Sidebar Mini State */
.sidebar.mini {
    width: 60px;
    transition: width 0.3s ease;
}

.sidebar.mini + .main-wrapper {
    margin-left: 60px;
    transition: margin-left 0.3s ease;
}

/* Hide text elements in mini state */
.sidebar.mini .sidebar-title,
.sidebar.mini .nav-text,
.sidebar.mini .nav-label,
.sidebar.mini .connection-status .status {
    opacity: 0;
    transform: translateX(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    pointer-events: none;
}

/* Adjust navigation items in mini state */
.sidebar.mini .nav-link {
    justify-content: center;
    padding: 12px 8px;
}

.sidebar.mini .nav-icon {
    margin-right: 0;
    font-size: 1.2rem;
}

/* Adjust header in mini state */
.sidebar.mini .sidebar-header {
    padding: 20px 8px;
    justify-content: center;
}

.sidebar.mini .sidebar-logo {
    justify-content: center;
}

.sidebar.mini .sidebar-toggle {
    display: none;
}

/* Adjust footer in mini state */
.sidebar.mini .sidebar-footer {
    padding: 20px 8px;
}

.sidebar.mini .connection-status {
    align-items: center;
}

.sidebar.mini .status-indicator {
    margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        padding: 20px;
    }

    .sidebar {
        width: 260px;
    }

    .main-wrapper {
        margin-left: 260px;
    }

    .main-content-area {
        margin-left: 260px;
    }

    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 15px;
    }

    .dashboard-sections {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 12px;
    }
}

@media (max-width: 768px) {
    /* Mobile Layout */
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    /* Override mini state on mobile - always use full width when open */
    .sidebar.mini {
        width: 280px;
    }

    .sidebar.mini + .main-wrapper {
        margin-left: 0;
    }

    .main-wrapper {
        margin-left: 0;
        width: 100%;
    }

    .main-content-area {
        margin-left: 0;
        width: 100%;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .sidebar-toggle {
        display: block;
    }

    .main-content {
        padding: 15px;
    }

    .module-content {
        padding: 15px;
    }

    .page-title {
        font-size: 1.3rem;
    }

    .header-content {
        padding: 12px 15px;
    }

    /* 调整导航项在移动端的显示 */
    .nav-link {
        padding: 15px 20px;
    }

    .nav-text {
        font-size: 1rem;
    }

    .nav-label {
        display: none; /* 移动端隐藏标签 */
    }

    /* 移动端模块样式调整 */
    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .metric-card {
        padding: 15px;
    }

    .metric-value {
        font-size: 1.5rem;
    }

    .dashboard-sections {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .section {
        padding: 15px;
    }

    .quick-actions {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .module-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .module-header h2 {
        font-size: 1.3rem;
    }

    .module-actions {
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 10px;
    }

    .page-title {
        font-size: 1.2rem;
    }

    .sidebar {
        width: 260px;
    }

    .nav-link {
        padding: 12px 15px;
    }

    .sidebar-header {
        padding: 15px;
    }

    .sidebar-title {
        font-size: 1.1rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Module Content Styles */
.module {
    display: none;
    animation: fadeIn 0.3s ease-in;
}

.module.active {
    display: block;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.module-header h2 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.module-actions {
    display: flex;
    gap: 10px;
}

.btn-refresh {
    background: #17a2b8;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
}

.btn-refresh:hover {
    background: #138496;
}

/* Main Interface Layout */
.main-interface {
    display: flex;
    min-height: 100vh;
}

.main-content-area {
    flex: 1;
    margin-left: 280px;
    padding: 0;
    transition: margin-left 0.3s ease;
}

/* Module Content Styles */
.module-content {
    padding: 20px;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 重复定义已移除，使用统一的.metric-card样式 */

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.metric-icon {
    font-size: 1.5rem;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.85rem;
}

/* Dashboard Sections */
.dashboard-sections {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.quick-actions .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    text-align: center;
}

.quick-actions .icon {
    font-size: 1.2rem;
}

/* Status Display */
.status-display {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 500;
    color: #6c757d;
}

.status-value {
    font-weight: 600;
    color: #2c3e50;
}

/* Placeholder Styles */
.info-placeholder,
.etcd-placeholder,
.logs-placeholder,
.port-forward-placeholder,
.cleanup-placeholder,
.monitoring-placeholder,
.config-placeholder {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.info-placeholder p,
.etcd-placeholder p,
.logs-placeholder p,
.port-forward-placeholder p,
.cleanup-placeholder p,
.monitoring-placeholder p,
.config-placeholder p {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.info-placeholder p:first-child,
.etcd-placeholder p:first-child,
.logs-placeholder p:first-child,
.port-forward-placeholder p:first-child,
.cleanup-placeholder p:first-child,
.monitoring-placeholder p:first-child,
.config-placeholder p:first-child {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Dashboard Module Specific Styles */
.dashboard-overview {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.recent-activities {
    margin-top: 20px;
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.activity-time {
    font-size: 0.85rem;
    color: #6c757d;
    min-width: 80px;
}

.activity-desc {
    flex: 1;
    margin: 0 15px;
    color: #2c3e50;
}

.activity-status {
    font-size: 0.8rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.activity-status.success {
    background: #d4edda;
    color: #155724;
}

.activity-status.warning {
    background: #fff3cd;
    color: #856404;
}

.activity-status.error {
    background: #f8d7da;
    color: #721c24;
}

/* ETCD Module Specific Styles */
.etcd-management {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.section-content {
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.95rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-text {
    display: block;
    margin-top: 5px;
    font-size: 0.85rem;
    color: #6c757d;
}

.action-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.result-display {
    margin-top: 15px;
    padding: 15px;
    border-radius: 6px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    min-height: 50px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    display: none;
}

.result-display.show {
    display: block;
}

.result-display.success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.result-display.error {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.warning-notice {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    margin-bottom: 20px;
    color: #856404;
}

.warning-notice .icon {
    font-size: 1.2rem;
}

.backup-files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    min-height: 100px;
}

.backup-file-item {
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.backup-file-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.backup-file-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.backup-file-info {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.backup-file-actions {
    display: flex;
    gap: 8px;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.status-card {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    text-align: center;
}

.status-card h4 {
    margin: 0 0 10px 0;
    font-size: 0.95rem;
    color: #6c757d;
}

.status-card .status-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.loading-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

/* Cluster Info Module Specific Styles */
.cluster-info-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Overview Cards */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.overview-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.card-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
}

.card-content h4 {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.card-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Version Information */
.version-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.version-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.version-label {
    font-weight: 500;
    color: #495057;
}

.version-value {
    font-family: 'Courier New', monospace;
    color: #2c3e50;
    font-weight: 600;
}

/* Table Styles */
.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
    flex-wrap: wrap;
}

.search-box {
    flex: 1;
    min-width: 200px;
}

.filter-controls {
    display: flex;
    gap: 10px;
}

.filter-controls .form-control {
    min-width: 120px;
}

.table-container {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.nodes-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.nodes-table th {
    background: #f8f9fa;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    white-space: nowrap;
}

.nodes-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.nodes-table tbody tr:hover {
    background: #f8f9fa;
}

.loading-cell {
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

.node-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.node-status.ready {
    background: #d4edda;
    color: #155724;
}

.node-status.not-ready {
    background: #f8d7da;
    color: #721c24;
}

.node-status.unknown {
    background: #fff3cd;
    color: #856404;
}

.node-roles {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.role-tag {
    padding: 2px 6px;
    background: #e9ecef;
    border-radius: 4px;
    font-size: 0.75rem;
    color: #495057;
}

.role-tag.master {
    background: #d1ecf1;
    color: #0c5460;
}

.role-tag.worker {
    background: #d4edda;
    color: #155724;
}

/* Namespaces Grid */
.namespaces-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.namespace-card {
    padding: 15px;
    background: white;
    border-radius: 6px;
    border-left: 4px solid #28a745;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.namespace-card.system {
    border-left-color: #6c757d;
}

.namespace-card.kube-system {
    border-left-color: #dc3545;
}

.namespace-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.namespace-info {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Charts */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    text-align: center;
}

.chart-placeholder {
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
}

.chart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.chart-label {
    font-weight: 500;
}

.chart-label.ready {
    color: #28a745;
}

.chart-label.not-ready {
    color: #dc3545;
}

.chart-label.running {
    color: #17a2b8;
}

.chart-label.total {
    color: #6c757d;
}

.chart-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Pod Logs Module Specific Styles */
.logs-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Pod Selector */
.selector-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.control-group .form-label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.control-group .form-control {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.control-group .form-control:disabled {
    background-color: #e9ecef;
    opacity: 0.6;
}

/* Log Options */
.log-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    flex-wrap: wrap;
    gap: 15px;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: #495057;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

/* Log Controls */
.controls-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.search-group {
    display: flex;
    gap: 8px;
    flex: 1;
    min-width: 300px;
}

.search-group .form-control {
    flex: 1;
}

.filter-group {
    min-width: 150px;
}

.action-group {
    display: flex;
    gap: 8px;
}

.action-group .btn {
    padding: 6px 12px;
    font-size: 0.85rem;
}

.btn.active {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

/* Connection Status */
.connection-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #dc3545;
}

.status-indicator.connected .status-dot {
    background-color: #28a745;
}

.status-indicator.connecting .status-dot {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.log-stats {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: #6c757d;
}

/* Log Viewer */
.log-viewer-container {
    position: relative;
    height: 500px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
}

.log-viewer {
    height: 100%;
    overflow-y: auto;
    background: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
}

.log-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    color: #6c757d;
}

.placeholder-content {
    text-align: center;
}

.placeholder-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.placeholder-content h4 {
    margin: 0 0 10px 0;
    color: #495057;
}

.placeholder-content p {
    margin: 0;
    font-size: 0.9rem;
}

/* Log Lines */
.log-line {
    padding: 2px 10px;
    border-bottom: 1px solid #2d2d2d;
    white-space: pre-wrap;
    word-break: break-word;
    display: flex !important;
    align-items: flex-start;
    gap: 10px;
    min-height: 20px;
    visibility: visible !important;
    opacity: 1 !important;
}

.log-line:hover {
    background-color: #2d2d2d;
}

.log-line.highlight {
    background-color: #3a3a00;
}

.log-line.hidden {
    display: none;
}

.log-timestamp {
    color: #569cd6;
    font-size: 0.8rem;
    min-width: 140px;
    flex-shrink: 0;
}

.log-level {
    min-width: 60px;
    flex-shrink: 0;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
}

.log-level.error {
    color: #f44747;
}

.log-level.warning {
    color: #ffcc02;
}

.log-level.info {
    color: #4fc1ff;
}

.log-level.debug {
    color: #b5cea8;
}

.log-content {
    flex: 1;
    word-break: break-word;
}

/* Pod Info Panel */
.pod-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
}

.info-value {
    color: #2c3e50;
    font-weight: 600;
}

.containers-info h4 {
    margin: 0 0 10px 0;
    color: #495057;
}

.containers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
}

.container-item {
    padding: 10px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #28a745;
}

.container-item.not-ready {
    border-left-color: #dc3545;
}

.container-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.container-info {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Port Forward Module Specific Styles */
.port-forward-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Create Forward Form */
.forward-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group .form-label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.form-group .form-control {
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.95rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group .form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group .form-control:disabled {
    background-color: #e9ecef;
    opacity: 0.6;
}

.form-group .form-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 2px;
}

.form-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.form-actions .btn {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* List Controls */
.list-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
    flex-wrap: wrap;
}

.search-group {
    display: flex;
    gap: 10px;
    flex: 1;
    min-width: 250px;
}

.search-group .form-control {
    flex: 1;
}

.filter-group {
    min-width: 150px;
}

/* Forwards Grid */
.forwards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    min-height: 200px;
}

.forward-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-left: 4px solid #3498db;
}

.forward-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.forward-card[data-status="running"] {
    border-left-color: #28a745;
}

.forward-card[data-status="error"] {
    border-left-color: #dc3545;
}

.forward-card[data-status="stopped"] {
    border-left-color: #6c757d;
}

.forward-card[data-status="creating"] {
    border-left-color: #ffc107;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px 10px 20px;
    border-bottom: 1px solid #e9ecef;
}

.forward-info {
    flex: 1;
}

.forward-title {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
    color: #2c3e50;
}

.forward-status {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.forward-status.running {
    background: #d4edda;
    color: #155724;
}

.forward-status.error {
    background: #f8d7da;
    color: #721c24;
}

.forward-status.stopped {
    background: #e2e3e5;
    color: #383d41;
}

.forward-status.creating {
    background: #fff3cd;
    color: #856404;
}

.card-actions {
    display: flex;
    gap: 5px;
}

.card-actions .btn {
    padding: 4px 8px;
    font-size: 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.card-body {
    padding: 15px 20px;
}

.forward-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.detail-label {
    font-weight: 500;
    color: #6c757d;
    min-width: 80px;
}

.detail-value {
    color: #2c3e50;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    flex: 1;
    text-align: right;
}

.copy-btn {
    margin-left: 8px;
    padding: 2px 6px;
    font-size: 0.7rem;
}

.error-message {
    background: #f8d7da;
    border-radius: 4px;
    padding: 8px;
    margin-top: 5px;
}

.error-message .detail-value {
    color: #721c24;
    text-align: left;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
}

.stat-content h4 {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Port Checker */
.checker-form {
    margin-bottom: 20px;
}

.input-group {
    display: flex;
    gap: 0;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.check-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
    font-size: 0.9rem;
    display: none;
}

.check-result.available {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.check-result.unavailable {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.port-suggestions h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1rem;
}

.port-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.port-tag {
    padding: 6px 12px;
    background: #e9ecef;
    border-radius: 16px;
    font-size: 0.85rem;
    color: #495057;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.port-tag:hover {
    background: #3498db;
    color: white;
}

/* Cleanup Module Specific Styles */
.cleanup-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Cleanup Form */
.cleanup-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Scan Results */
.results-summary {
    margin-bottom: 25px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.summary-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
}

.summary-card .card-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
}

.summary-card .card-content h4 {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.summary-card .card-value {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Results Details */
.results-details {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.details-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    gap: 15px;
    flex-wrap: wrap;
}

.details-controls .search-group {
    flex: 1;
    min-width: 200px;
}

.details-controls .filter-group {
    display: flex;
    gap: 10px;
}

.details-controls .filter-group .form-control {
    min-width: 120px;
}

/* Resources Table */
.resources-table-container {
    overflow-x: auto;
}

.resources-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.resources-table th {
    background: #f8f9fa;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    white-space: nowrap;
}

.resources-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.resources-table tbody tr:hover {
    background: #f8f9fa;
}

.resources-table .checkbox-label {
    margin: 0;
}

.resource-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.resource-status.evicted {
    background: #f8d7da;
    color: #721c24;
}

.resource-status.failed {
    background: #f8d7da;
    color: #721c24;
}

.resource-status.pending {
    background: #fff3cd;
    color: #856404;
}

.resource-status.completed {
    background: #d4edda;
    color: #155724;
}

.resource-type {
    padding: 2px 6px;
    background: #e9ecef;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #495057;
}

.resource-type.pod {
    background: #d1ecf1;
    color: #0c5460;
}

.resource-type.job {
    background: #d4edda;
    color: #155724;
}

/* Cleanup History */
.history-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.history-placeholder p {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.history-placeholder p:first-child {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Cleanup Suggestions */
.suggestions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.suggestion-card {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.suggestion-card:hover {
    transform: translateY(-2px);
}

.suggestion-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
    flex-shrink: 0;
}

.suggestion-content h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.suggestion-content p {
    margin: 0;
    color: #6c757d;
    line-height: 1.5;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #2c3e50;
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin-bottom: 15px;
    line-height: 1.5;
}

.confirm-details {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 4px;
    padding: 10px;
    margin-top: 15px;
}

.confirm-details ul {
    margin: 0;
    padding-left: 20px;
}

.confirm-details li {
    margin-bottom: 5px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e9ecef;
}

/* Monitoring Module Specific Styles */
.monitoring-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Monitoring Overview */
.overview-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

/* 监控模块专用的横向布局metric-card */
.metric-card.horizontal {
    display: flex;
    align-items: center;
    text-align: left;
}

.metric-card.horizontal .metric-icon {
    font-size: 2.5rem;
    margin-right: 15px;
    opacity: 0.8;
}

.metric-content {
    flex: 1;
    overflow: hidden;
}

.metric-content h4 {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.metric-trend {
    font-size: 0.8rem;
}

.trend-indicator {
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

.trend-indicator.up {
    background: #d4edda;
    color: #155724;
}

.trend-indicator.down {
    background: #f8d7da;
    color: #721c24;
}

.trend-indicator.stable {
    background: #e2e3e5;
    color: #383d41;
}

/* Kubernetes Status */
.k8s-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.k8s-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.metric-label {
    font-weight: 500;
    color: #495057;
}

.k8s-metric .metric-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Health Check */
.health-overall {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;
}

.overall-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-label {
    font-weight: 500;
    color: #495057;
}

.status-indicator {
    padding: 4px 12px;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.8rem;
}

.status-indicator.healthy {
    background: #d4edda;
    color: #155724;
}

.status-indicator.degraded {
    background: #fff3cd;
    color: #856404;
}

.status-indicator.unhealthy {
    background: #f8d7da;
    color: #721c24;
}

.uptime {
    display: flex;
    align-items: center;
    gap: 10px;
}

.uptime-label {
    font-weight: 500;
    color: #495057;
}

.uptime-value {
    font-family: 'Courier New', monospace;
    color: #2c3e50;
    font-weight: 600;
}

.health-components {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.component-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.component-name {
    font-weight: 500;
    color: #2c3e50;
    flex: 1;
}

.component-status {
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    margin-right: 10px;
}

.component-status.healthy {
    background: #d4edda;
    color: #155724;
}

.component-status.degraded {
    background: #fff3cd;
    color: #856404;
}

.component-status.unhealthy {
    background: #f8d7da;
    color: #721c24;
}

.response-time {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #6c757d;
    min-width: 60px;
    text-align: right;
}

/* Performance Charts */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    text-align: center;
    font-size: 1rem;
}

.chart-placeholder {
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-content {
    width: 100%;
}

.chart-bars {
    display: flex;
    align-items: end;
    justify-content: space-between;
    height: 100px;
    margin-bottom: 10px;
    padding: 0 10px;
}

.chart-bar {
    width: 12px;
    background: linear-gradient(to top, #3498db, #5dade2);
    border-radius: 2px 2px 0 0;
    margin: 0 1px;
    transition: all 0.3s ease;
}

.chart-bar:hover {
    background: linear-gradient(to top, #2980b9, #3498db);
}

.chart-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #6c757d;
    padding: 0 10px;
}

.network-stats,
.operations-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
}

.stat-label {
    font-weight: 500;
    color: #495057;
}

.stat-value {
    font-family: 'Courier New', monospace;
    color: #2c3e50;
    font-weight: 600;
}

/* Alerts Management */
.alerts-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
    flex-wrap: wrap;
}

.controls-left {
    display: flex;
    gap: 10px;
}

.controls-right {
    display: flex;
    gap: 10px;
}

.controls-right .form-control {
    min-width: 120px;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.alert-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-left: 4px solid #6c757d;
    transition: transform 0.2s ease;
}

.alert-item:hover {
    transform: translateX(2px);
}

.alert-item.critical {
    border-left-color: #dc3545;
}

.alert-item.warning {
    border-left-color: #ffc107;
}

.alert-item.info {
    border-left-color: #17a2b8;
}

.alert-icon {
    font-size: 1.5rem;
    margin-right: 15px;
    opacity: 0.8;
}

.alert-content {
    flex: 1;
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.alert-name {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.alert-severity {
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
}

.alert-severity.critical {
    background: #f8d7da;
    color: #721c24;
}

.alert-severity.warning {
    background: #fff3cd;
    color: #856404;
}

.alert-severity.info {
    background: #d1ecf1;
    color: #0c5460;
}

.alert-description {
    color: #6c757d;
    margin: 5px 0;
    font-size: 0.9rem;
}

.alert-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #6c757d;
}

.alert-time {
    font-family: 'Courier New', monospace;
}

.alert-tags {
    display: flex;
    gap: 5px;
}

.alert-tag {
    padding: 1px 6px;
    background: #e9ecef;
    border-radius: 8px;
    font-size: 0.7rem;
    color: #495057;
}

.alert-actions {
    display: flex;
    gap: 5px;
    margin-left: 15px;
}

.alert-action {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.alert-action.resolve {
    background: #d4edda;
    color: #155724;
}

.alert-action.resolve:hover {
    background: #c3e6cb;
}

.alert-action.delete {
    background: #f8d7da;
    color: #721c24;
}

.alert-action.delete:hover {
    background: #f5c6cb;
}

.loading-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

/* Monitoring Configuration */
.config-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.config-group {
    padding: 15px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.config-group h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 0.9rem;
}

.config-group .form-control {
    width: 100%;
}

.config-group .checkbox-label {
    margin: 0;
    font-size: 0.9rem;
}

/* Responsive Design for Monitoring */
@media (max-width: 768px) {
    .overview-metrics {
        grid-template-columns: 1fr;
    }

    .k8s-metrics {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .alerts-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .controls-left,
    .controls-right {
        justify-content: center;
    }

    .config-options {
        grid-template-columns: 1fr;
    }

    .health-overall {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .component-item {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .alert-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .alert-header {
        width: 100%;
    }

    .alert-meta {
        width: 100%;
        flex-direction: column;
        gap: 5px;
    }

    .alert-actions {
        margin-left: 0;
        align-self: stretch;
        justify-content: center;
    }
}

/* Configuration Module Specific Styles */
.config-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Configuration Overview */
.config-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.info-label {
    font-weight: 500;
    color: #495057;
}

.info-value {
    font-family: 'Courier New', monospace;
    color: #2c3e50;
    font-weight: 600;
}

.config-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Configuration Editor */
.editor-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
    flex-wrap: wrap;
}

.section-tabs {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 8px 16px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.tab-btn:hover {
    background: #f8f9fa;
    border-color: #3498db;
}

.tab-btn.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.editor-actions {
    display: flex;
    gap: 10px;
}

.config-editor-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.config-section {
    display: none;
    padding: 20px;
}

.config-section.active {
    display: block;
}

.config-section h4 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.form-control {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    margin: 0;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.custom-config-editor {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.custom-config-editor textarea {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    resize: vertical;
}

.form-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Configuration History */
.history-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
    flex-wrap: wrap;
}

.history-controls .form-control {
    min-width: 120px;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.history-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-left: 4px solid #6c757d;
    transition: transform 0.2s ease;
}

.history-item:hover {
    transform: translateX(2px);
}

.history-item.backup {
    border-left-color: #28a745;
}

.history-item.update {
    border-left-color: #ffc107;
}

.history-item.restore {
    border-left-color: #17a2b8;
}

.history-icon {
    font-size: 1.5rem;
    margin-right: 15px;
    opacity: 0.8;
}

.history-content {
    flex: 1;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.history-description {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.history-timestamp {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #6c757d;
}

.history-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #6c757d;
}

.history-section {
    padding: 2px 6px;
    background: #e9ecef;
    border-radius: 4px;
    font-size: 0.7rem;
    color: #495057;
}

.history-size {
    font-family: 'Courier New', monospace;
}

.history-actions {
    display: flex;
    gap: 5px;
    margin-left: 15px;
}

.history-action {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.history-action.restore {
    background: #d1ecf1;
    color: #0c5460;
}

.history-action.restore:hover {
    background: #bee5eb;
}

.history-action.delete {
    background: #f8d7da;
    color: #721c24;
}

.history-action.delete:hover {
    background: #f5c6cb;
}

/* Configuration Help */
.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.help-card {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.help-card:hover {
    transform: translateY(-2px);
}

.help-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
    flex-shrink: 0;
}

.help-content h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.help-content p {
    margin: 0;
    color: #6c757d;
    line-height: 1.5;
}

.loading-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

/* Responsive Design for Configuration */
@media (max-width: 768px) {
    .config-info {
        grid-template-columns: 1fr;
    }

    .editor-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .section-tabs {
        justify-content: center;
    }

    .editor-actions {
        justify-content: center;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .history-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .help-grid {
        grid-template-columns: 1fr;
    }

    .history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .history-header {
        width: 100%;
    }

    .history-meta {
        width: 100%;
        flex-direction: column;
        gap: 5px;
    }

    .history-actions {
        margin-left: 0;
        align-self: stretch;
        justify-content: center;
    }
}

/* Loading Animations and Skeleton Screens */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
    border-radius: 4px;
}

@keyframes loading-shimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-card {
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.skeleton-title {
    height: 20px;
    width: 60%;
    margin-bottom: 15px;
}

.skeleton-text {
    height: 14px;
    width: 100%;
    margin-bottom: 10px;
}

.skeleton-text.short {
    width: 70%;
}

.skeleton-text.medium {
    width: 85%;
}

.skeleton-button {
    height: 36px;
    width: 120px;
    border-radius: 4px;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.loading-content .loading-spinner {
    width: 40px;
    height: 40px;
    border-width: 4px;
    margin-bottom: 15px;
}

.loading-text {
    color: #666;
    font-size: 14px;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 4px;
    background: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-indeterminate {
    background: linear-gradient(90deg, transparent, #3498db, transparent);
    background-size: 200% 100%;
    animation: progress-indeterminate 1.5s infinite;
}

@keyframes progress-indeterminate {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 400px;
}

.toast {
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    background: #27ae60;
}

.toast.error {
    background: #e74c3c;
}

.toast.warning {
    background: #f39c12;
}

.toast.info {
    background: #3498db;
}

.toast-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.toast-close:hover {
    opacity: 1;
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255,255,255,0.3);
    animation: toast-progress 3s linear;
}

@keyframes toast-progress {
    from { width: 100%; }
    to { width: 0%; }
}

/* Error States */
.error-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.error-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.error-message {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: #333;
}

.error-details {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 20px;
}

.error-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.3;
}

.empty-title {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #333;
}

.empty-description {
    font-size: 1rem;
    margin-bottom: 25px;
    line-height: 1.5;
}

.empty-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Confirmation Dialogs */
.confirm-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.confirm-dialog.show {
    opacity: 1;
    visibility: visible;
}

.confirm-content {
    background: white;
    border-radius: 8px;
    padding: 24px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.confirm-dialog.show .confirm-content {
    transform: scale(1);
}

.confirm-icon {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 15px;
}

.confirm-title {
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 10px;
    color: #333;
}

.confirm-message {
    text-align: center;
    margin-bottom: 20px;
    color: #666;
    line-height: 1.5;
}

.confirm-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.confirm-actions .btn {
    min-width: 80px;
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
        padding: 0 15px;
    }

    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 992px) {
    .sidebar {
        width: 250px;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1000;
        background: white;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    /* Override mini state on tablet - always use full width when open */
    .sidebar.mini {
        width: 250px;
    }

    .sidebar.mini + .main-wrapper {
        margin-left: 0;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
        transition: margin-left 0.3s ease;
    }

    .header {
        padding-left: 60px;
    }

    .mobile-menu-toggle {
        display: block;
        position: fixed;
        top: 15px;
        left: 15px;
        z-index: 1001;
        background: #3498db;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        cursor: pointer;
        font-size: 16px;
    }

    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 10px 15px;
        flex-direction: column;
        gap: 10px;
    }

    .header-title {
        font-size: 1.3rem;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .module-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .module-actions {
        justify-content: center;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
    }

    .form-group {
        width: 100%;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        width: 100%;
        margin: 2px 0;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table {
        min-width: 600px;
    }

    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .toast {
        margin: 0;
    }

    .confirm-content {
        margin: 20px;
        width: auto;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 8px 10px;
    }

    .header-title {
        font-size: 1.1rem;
    }

    .module {
        padding: 10px;
    }

    .section {
        margin-bottom: 15px;
    }

    .section-content {
        padding: 10px;
    }

    .card {
        padding: 12px;
    }

    .btn {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .form-control {
        padding: 8px 10px;
        font-size: 0.9rem;
    }

    .table {
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: 6px 8px;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .header-actions,
    .module-actions,
    .btn,
    .toast-container {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }

    .module {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .section {
        break-inside: avoid;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    h1, h2, h3, h4, h5, h6 {
        color: black !important;
    }
}

/* Theme System */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;

    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;

    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);

    --sidebar-bg: #2c3e50;
    --sidebar-text: #ecf0f1;
    --sidebar-hover: #34495e;
}

[data-theme="dark"] {
    --primary-color: #3498db;
    --secondary-color: #ecf0f1;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #495057;
    --dark-color: #f8f9fa;

    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;

    --text-primary: #f8f9fa;
    --text-secondary: #adb5bd;
    --text-muted: #6c757d;

    --border-color: #495057;
    --shadow-color: rgba(0, 0, 0, 0.3);

    --sidebar-bg: #1a1a1a;
    --sidebar-text: #f8f9fa;
    --sidebar-hover: #2d2d2d;
}

body {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
}

.sidebar-nav a {
    color: var(--sidebar-text);
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
    background-color: var(--sidebar-hover);
}

.main-content {
    background-color: var(--bg-secondary);
}

.module {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    box-shadow: 0 2px 8px var(--shadow-color);
}

.section {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
}

.card {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    box-shadow: 0 2px 8px var(--shadow-color);
}

.form-control {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.table {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.table th {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

.table td {
    border-color: var(--border-color);
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    font-size: 20px;
    box-shadow: 0 4px 12px var(--shadow-color);
    transition: all 0.3s ease;
    z-index: 1000;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px var(--shadow-color);
}

.theme-toggle:active {
    transform: scale(0.95);
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --shadow-color: rgba(0, 0, 0, 0.5);
    }

    .btn {
        border-width: 2px;
    }

    .form-control {
        border-width: 2px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ========================================
   统一错误处理样式
   ======================================== */

/* 错误显示容器 */
.error-display {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border: 1px solid #f87171;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    box-shadow: 0 2px 4px rgba(248, 113, 113, 0.1);
}

.error-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.error-title {
    font-weight: 600;
    color: #dc2626;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.error-title::before {
    content: "⚠️";
    font-size: 18px;
}

.error-message {
    color: #7f1d1d;
    font-size: 14px;
    line-height: 1.5;
}

.error-suggestions {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f87171;
}

.suggestions-title {
    font-weight: 500;
    color: #dc2626;
    font-size: 14px;
    margin-bottom: 8px;
}

.error-suggestions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.error-suggestions li {
    color: #7f1d1d;
    font-size: 13px;
    padding: 4px 0;
    padding-left: 16px;
    position: relative;
}

.error-suggestions li::before {
    content: "•";
    color: #dc2626;
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* Toast 容器 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-width: 400px;
}

/* Toast 样式 */
.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: slideInRight 0.3s ease-out;
    border-left: 4px solid #dc2626;
}

.toast-error {
    border-left-color: #dc2626;
}

.toast-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.toast-title {
    font-weight: 600;
    color: #dc2626;
    font-size: 14px;
}

.toast-message {
    color: #374151;
    font-size: 13px;
    line-height: 1.4;
}

.toast-suggestions {
    color: #6b7280;
    font-size: 12px;
    margin-top: 4px;
    font-style: italic;
}

.toast-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.toast-close:hover {
    background-color: #f3f4f6;
    color: #374151;
}

/* Toast 动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .toast-container {
        left: 20px;
        right: 20px;
        max-width: none;
    }

    .error-display {
        margin: 12px 0;
        padding: 12px;
    }

    .error-title {
        font-size: 15px;
    }

    .error-message {
        font-size: 13px;
    }
}
