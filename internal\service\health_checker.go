package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
)

// HealthStatus 健康状态
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
	HealthStatusDegraded  HealthStatus = "degraded"
	HealthStatusUnknown   HealthStatus = "unknown"
)

// ComponentHealth 组件健康状态
type ComponentHealth struct {
	Name      string                 `json:"name"`
	Status    HealthStatus           `json:"status"`
	Message   string                 `json:"message,omitempty"`
	LastCheck time.Time              `json:"last_check"`
	Duration  time.Duration          `json:"duration"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Error     string                 `json:"error,omitempty"`
}

// OverallHealth 整体健康状态
type OverallHealth struct {
	Status     HealthStatus                `json:"status"`
	Timestamp  time.Time                   `json:"timestamp"`
	Duration   time.Duration               `json:"duration"`
	Version    string                      `json:"version"`
	Components map[string]*ComponentHealth `json:"components"`
	Summary    map[string]interface{}      `json:"summary"`
}

// HealthChecker 健康检查器
type HealthChecker struct {
	logger           *zap.Logger
	metricsCollector *MetricsCollector

	// 组件检查器
	checkers map[string]ComponentChecker

	// 配置
	timeout           time.Duration
	checkInterval     time.Duration
	degradedThreshold int // 失败次数阈值，超过则标记为degraded

	// 状态缓存
	lastCheck     time.Time
	lastResult    *OverallHealth
	failureCounts map[string]int
	mutex         sync.RWMutex

	// 后台检查
	ticker    *time.Ticker
	stopChan  chan struct{}
	isRunning bool
}

// ComponentChecker 组件检查器接口
type ComponentChecker interface {
	Check(ctx context.Context) *ComponentHealth
	Name() string
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(logger *zap.Logger, metricsCollector *MetricsCollector) *HealthChecker {
	return &HealthChecker{
		logger:            logger,
		metricsCollector:  metricsCollector,
		checkers:          make(map[string]ComponentChecker),
		timeout:           30 * time.Second,
		checkInterval:     30 * time.Second,
		degradedThreshold: 3,
		failureCounts:     make(map[string]int),
		stopChan:          make(chan struct{}),
	}
}

// RegisterChecker 注册组件检查器
func (hc *HealthChecker) RegisterChecker(checker ComponentChecker) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	hc.checkers[checker.Name()] = checker
	hc.failureCounts[checker.Name()] = 0

	hc.logger.Info("注册健康检查器", zap.String("component", checker.Name()))
}

// Check 执行健康检查
func (hc *HealthChecker) Check(ctx context.Context) *OverallHealth {
	start := time.Now()

	// 创建带超时的上下文
	checkCtx, cancel := context.WithTimeout(ctx, hc.timeout)
	defer cancel()

	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	components := make(map[string]*ComponentHealth)
	overallStatus := HealthStatusHealthy
	healthyCount := 0
	unhealthyCount := 0
	degradedCount := 0

	// 并发检查所有组件
	var wg sync.WaitGroup
	resultChan := make(chan *ComponentHealth, len(hc.checkers))

	for _, checker := range hc.checkers {
		wg.Add(1)
		go func(c ComponentChecker) {
			defer wg.Done()
			health := c.Check(checkCtx)
			resultChan <- health
		}(checker)
	}

	// 等待所有检查完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	for health := range resultChan {
		components[health.Name] = health

		// 更新失败计数
		if health.Status == HealthStatusUnhealthy {
			hc.failureCounts[health.Name]++
			unhealthyCount++

			// 检查是否应该标记为degraded
			if hc.failureCounts[health.Name] >= hc.degradedThreshold {
				health.Status = HealthStatusDegraded
				degradedCount++
				unhealthyCount--
			}
		} else {
			hc.failureCounts[health.Name] = 0
			if health.Status == HealthStatusHealthy {
				healthyCount++
			} else if health.Status == HealthStatusDegraded {
				degradedCount++
			}
		}
	}

	// 确定整体状态
	if unhealthyCount > 0 {
		overallStatus = HealthStatusUnhealthy
	} else if degradedCount > 0 {
		overallStatus = HealthStatusDegraded
	}

	duration := time.Since(start)

	result := &OverallHealth{
		Status:     overallStatus,
		Timestamp:  time.Now(),
		Duration:   duration,
		Version:    "1.0.0", // 可以从配置或构建信息获取
		Components: components,
		Summary: map[string]interface{}{
			"total_components": len(components),
			"healthy_count":    healthyCount,
			"unhealthy_count":  unhealthyCount,
			"degraded_count":   degradedCount,
			"check_duration":   duration.String(),
		},
	}

	// 更新缓存
	hc.lastCheck = time.Now()
	hc.lastResult = result

	// 记录指标
	if hc.metricsCollector != nil {
		hc.metricsCollector.RecordHealthCheck(duration, overallStatus == HealthStatusHealthy)
	}

	hc.logger.Debug("健康检查完成",
		zap.String("status", string(overallStatus)),
		zap.Duration("duration", duration),
		zap.Int("healthy", healthyCount),
		zap.Int("unhealthy", unhealthyCount),
		zap.Int("degraded", degradedCount))

	return result
}

// GetLastResult 获取最后一次检查结果
func (hc *HealthChecker) GetLastResult() *OverallHealth {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	return hc.lastResult
}

// IsHealthy 检查是否健康
func (hc *HealthChecker) IsHealthy() bool {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	if hc.lastResult == nil {
		return false
	}

	return hc.lastResult.Status == HealthStatusHealthy
}

// StartPeriodicCheck 启动定期健康检查
func (hc *HealthChecker) StartPeriodicCheck(ctx context.Context) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	if hc.isRunning {
		hc.logger.Warn("健康检查已在运行")
		return
	}

	hc.ticker = time.NewTicker(hc.checkInterval)
	hc.isRunning = true

	go func() {
		defer func() {
			hc.mutex.Lock()
			hc.isRunning = false
			hc.mutex.Unlock()
		}()

		// 立即执行一次检查
		hc.Check(ctx)

		for {
			select {
			case <-ctx.Done():
				hc.logger.Info("健康检查因上下文取消而停止")
				return
			case <-hc.stopChan:
				hc.logger.Info("健康检查因停止信号而停止")
				return
			case <-hc.ticker.C:
				hc.Check(ctx)
			}
		}
	}()

	hc.logger.Info("启动定期健康检查", zap.Duration("interval", hc.checkInterval))
}

// Stop 停止健康检查
func (hc *HealthChecker) Stop() {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	if !hc.isRunning {
		return
	}

	if hc.ticker != nil {
		hc.ticker.Stop()
	}

	close(hc.stopChan)
	hc.logger.Info("健康检查已停止")
}

// SetCheckInterval 设置检查间隔
func (hc *HealthChecker) SetCheckInterval(interval time.Duration) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	hc.checkInterval = interval
	hc.logger.Info("更新健康检查间隔", zap.Duration("interval", interval))
}

// SetTimeout 设置检查超时
func (hc *HealthChecker) SetTimeout(timeout time.Duration) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	hc.timeout = timeout
	hc.logger.Info("更新健康检查超时", zap.Duration("timeout", timeout))
}

// ETCDHealthChecker ETCD健康检查器
type ETCDHealthChecker struct {
	name          string
	clientManager *ClientManager
	config        *domain.ETCDConfig
	logger        *zap.Logger
}

// NewETCDHealthChecker 创建ETCD健康检查器
func NewETCDHealthChecker(clientManager *ClientManager, config *domain.ETCDConfig, logger *zap.Logger) *ETCDHealthChecker {
	return &ETCDHealthChecker{
		name:          "etcd",
		clientManager: clientManager,
		config:        config,
		logger:        logger,
	}
}

// Name 返回检查器名称
func (ehc *ETCDHealthChecker) Name() string {
	return ehc.name
}

// Check 执行ETCD健康检查
func (ehc *ETCDHealthChecker) Check(ctx context.Context) *ComponentHealth {
	start := time.Now()

	health := &ComponentHealth{
		Name:      ehc.name,
		LastCheck: start,
	}

	// 简化的ETCD健康检查
	// 在实际实现中，这里应该测试ETCD连接
	if ehc.clientManager == nil || ehc.config == nil {
		health.Status = HealthStatusUnhealthy
		health.Error = "ETCD配置或客户端管理器未初始化"
		health.Duration = time.Since(start)
		return health
	}

	// 模拟连接测试
	health.Status = HealthStatusHealthy
	health.Message = "ETCD连接正常"
	health.Details = map[string]interface{}{
		"endpoint": ehc.config.Servers,
		"timeout":  "30s",
	}

	health.Duration = time.Since(start)
	return health
}

// FileSystemHealthChecker 文件系统健康检查器
type FileSystemHealthChecker struct {
	name   string
	paths  []string
	logger *zap.Logger
}

// NewFileSystemHealthChecker 创建文件系统健康检查器
func NewFileSystemHealthChecker(paths []string, logger *zap.Logger) *FileSystemHealthChecker {
	return &FileSystemHealthChecker{
		name:   "filesystem",
		paths:  paths,
		logger: logger,
	}
}

// Name 返回检查器名称
func (fhc *FileSystemHealthChecker) Name() string {
	return fhc.name
}

// Check 执行文件系统健康检查
func (fhc *FileSystemHealthChecker) Check(ctx context.Context) *ComponentHealth {
	start := time.Now()

	health := &ComponentHealth{
		Name:      fhc.name,
		LastCheck: start,
		Status:    HealthStatusHealthy,
		Message:   "文件系统访问正常",
		Details:   make(map[string]interface{}),
	}

	for _, path := range fhc.paths {
		if err := checkPathAccess(path); err != nil {
			health.Status = HealthStatusUnhealthy
			health.Error = fmt.Sprintf("路径 %s 访问失败: %v", path, err)
			break
		}
		health.Details[path] = "accessible"
	}

	health.Duration = time.Since(start)
	return health
}

// checkPathAccess 检查路径访问权限
func checkPathAccess(path string) error {
	// 这里可以实现具体的路径访问检查逻辑
	// 例如检查目录是否存在、是否可读写等
	return nil
}
