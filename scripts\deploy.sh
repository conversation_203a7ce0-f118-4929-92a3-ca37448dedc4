#!/bin/bash

# K8s Helper 部署脚本
# 支持多环境部署：development, staging, production

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
K8s Helper 部署脚本

用法: $0 [选项] <环境>

环境:
  development    部署到开发环境
  staging        部署到预发布环境
  production     部署到生产环境

选项:
  --image-tag TAG    指定Docker镜像标签 (默认: latest)
  --skip-build       跳过构建步骤
  --skip-tests       跳过测试步骤
  --rollback         回滚到上一个版本
  --dry-run          仅显示将要执行的命令，不实际执行
  --force            强制部署，跳过确认
  -h, --help         显示帮助信息

示例:
  $0 development
  $0 production --image-tag v1.2.3
  $0 staging --skip-build --force
  $0 production --rollback

EOF
}

# 默认参数
ENVIRONMENT=""
IMAGE_TAG="latest"
SKIP_BUILD=false
SKIP_TESTS=false
ROLLBACK=false
DRY_RUN=false
FORCE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        development|staging|production)
            ENVIRONMENT="$1"
            shift
            ;;
        --image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ -z "$ENVIRONMENT" ]]; then
    log_error "请指定部署环境"
    show_help
    exit 1
fi

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

log_info "开始部署到 $ENVIRONMENT 环境"
log_info "项目根目录: $PROJECT_ROOT"
log_info "镜像标签: $IMAGE_TAG"

# 执行命令函数
execute_command() {
    local cmd="$1"
    local description="$2"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] $description"
        echo "  命令: $cmd"
        return 0
    fi
    
    log_info "$description"
    if ! eval "$cmd"; then
        log_error "$description 失败"
        exit 1
    fi
}

# 检查必要的工具
check_dependencies() {
    local tools=("docker" "docker-compose")
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool 未安装"
            exit 1
        fi
    done
}

# 加载环境配置
load_environment_config() {
    local env_file="config/environments/${ENVIRONMENT}.env"
    
    if [[ -f "$env_file" ]]; then
        log_info "加载环境配置: $env_file"
        set -a
        source "$env_file"
        set +a
    else
        log_warning "环境配置文件不存在: $env_file"
    fi
}

# 构建应用
build_application() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        log_info "跳过构建步骤"
        return 0
    fi
    
    execute_command "scripts/build.sh --production" "构建应用"
}

# 运行测试
run_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_info "跳过测试步骤"
        return 0
    fi
    
    execute_command "scripts/build.sh --skip-build" "运行测试"
}

# 构建Docker镜像
build_docker_image() {
    local image_name="k8s-helper:${IMAGE_TAG}"
    
    execute_command "docker build -f docker/Dockerfile.prod -t $image_name ." "构建Docker镜像"
    
    # 如果不是本地部署，推送到镜像仓库
    if [[ "$ENVIRONMENT" != "development" ]]; then
        execute_command "docker push $image_name" "推送Docker镜像"
    fi
}

# 部署应用
deploy_application() {
    local compose_file="docker-compose.${ENVIRONMENT}.yml"
    
    if [[ ! -f "$compose_file" ]]; then
        log_error "Docker Compose文件不存在: $compose_file"
        exit 1
    fi
    
    # 设置镜像标签环境变量
    export IMAGE_TAG="$IMAGE_TAG"
    
    if [[ "$ROLLBACK" == "true" ]]; then
        execute_command "docker-compose -f $compose_file down" "停止当前服务"
        execute_command "docker-compose -f $compose_file up -d --force-recreate" "回滚到上一个版本"
    else
        execute_command "docker-compose -f $compose_file pull" "拉取最新镜像"
        execute_command "docker-compose -f $compose_file up -d --remove-orphans" "部署应用"
    fi
}

# 健康检查
health_check() {
    local max_attempts=30
    local attempt=1
    local health_url="http://localhost:8080/health"
    
    log_info "等待应用启动..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            log_success "应用健康检查通过"
            return 0
        fi
        
        log_info "健康检查失败，重试 ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    log_error "应用健康检查失败"
    return 1
}

# 部署后验证
post_deploy_verification() {
    log_info "执行部署后验证..."
    
    # 检查容器状态
    execute_command "docker-compose -f docker-compose.${ENVIRONMENT}.yml ps" "检查容器状态"
    
    # 健康检查
    if ! health_check; then
        log_error "部署验证失败"
        exit 1
    fi
    
    # 显示日志
    log_info "最近的应用日志:"
    docker-compose -f "docker-compose.${ENVIRONMENT}.yml" logs --tail=20 k8s-helper
}

# 确认部署
confirm_deployment() {
    if [[ "$FORCE" == "true" || "$DRY_RUN" == "true" ]]; then
        return 0
    fi
    
    echo
    log_warning "即将部署到 $ENVIRONMENT 环境"
    log_warning "镜像标签: $IMAGE_TAG"
    echo
    read -p "确认继续部署? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
}

# 主函数
main() {
    log_info "K8s Helper 部署脚本启动"
    
    # 检查依赖
    check_dependencies
    
    # 加载环境配置
    load_environment_config
    
    # 确认部署
    confirm_deployment
    
    # 构建应用
    build_application
    
    # 运行测试
    run_tests
    
    # 构建Docker镜像
    build_docker_image
    
    # 部署应用
    deploy_application
    
    # 部署后验证
    if [[ "$DRY_RUN" != "true" ]]; then
        post_deploy_verification
    fi
    
    log_success "部署完成！"
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "生产环境部署完成，请进行最终验证"
    fi
}

# 执行主函数
main "$@"
