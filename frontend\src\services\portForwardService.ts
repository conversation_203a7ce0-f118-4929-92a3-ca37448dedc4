import { apiClient } from './api';
import type {
  PortForwardRequest,
  PortForwardResponse,
} from '@/types/api';

/**
 * 端口转发服务类
 * 
 * 提供端口转发相关的API操作：
 * - 端口转发管理
 * - 转发状态监控
 * - 转发历史查询
 */
export class PortForwardService {
  private readonly basePath = '/port-forward';

  // ============ 端口转发管理 ============

  /**
   * 获取端口转发列表
   * @returns 端口转发列表
   */
  async listPortForwards(): Promise<PortForwardResponse[]> {
    return apiClient.get<PortForwardResponse[]>(`${this.basePath}`);
  }

  /**
   * 创建端口转发
   * @param request 端口转发请求
   * @returns 端口转发响应
   */
  async createPortForward(request: PortForwardRequest): Promise<PortForwardResponse> {
    return apiClient.post<PortForwardResponse>(`${this.basePath}`, request);
  }

  /**
   * 获取端口转发详情
   * @param id 端口转发ID
   * @returns 端口转发详情
   */
  async getPortForward(id: string): Promise<PortForwardResponse> {
    return apiClient.get<PortForwardResponse>(`${this.basePath}/${encodeURIComponent(id)}`);
  }

  /**
   * 停止端口转发
   * @param id 端口转发ID
   * @returns 操作结果
   */
  async stopPortForward(id: string): Promise<{ success: boolean; message: string }> {
    return apiClient.delete<{ success: boolean; message: string }>(`${this.basePath}/${encodeURIComponent(id)}`);
  }

  // ============ 工具方法 ============

  /**
   * 验证端口号
   * @param port 端口号
   * @returns 是否有效
   */
  validatePort(port: number): boolean {
    return port > 0 && port <= 65535;
  }

  /**
   * 检查端口是否可用
   * @param port 端口号
   * @returns 是否可用
   */
  async checkPortAvailability(port: number): Promise<boolean> {
    // 这里可以实现端口可用性检查逻辑
    // 暂时返回简单的验证
    return this.validatePort(port);
  }

  /**
   * 获取状态颜色
   * @param status 状态
   * @returns 颜色值
   */
  getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      'active': '#52c41a',    // 绿色
      'stopped': '#d9d9d9',   // 灰色
      'error': '#ff4d4f',     // 红色
    };
    
    return colorMap[status] || '#d9d9d9';
  }

  /**
   * 格式化端口转发描述
   * @param portForward 端口转发信息
   * @returns 格式化描述
   */
  formatDescription(portForward: PortForwardResponse): string {
    return `${portForward.namespace}/${portForward.pod}:${portForward.remote_port} -> localhost:${portForward.local_port}`;
  }
}

// 创建并导出端口转发服务实例
export const portForwardService = new PortForwardService();

export default portForwardService;
