package cmdhelp

import "fmt"

// 预定义的命令帮助信息

// CleanupHelp cleanup命令的帮助信息
var CleanupHelp = NewCommandHelp(
	"cleanup <type>",
	"清理集群中的无用资源",
	`清理 Kubernetes 集群中的无用资源，包括：

• Evicted 状态的 Pod
• 已完成的 Job (Completed/Failed)
• 长时间未使用的资源`,
).AddValidArgs(map[string]string{
	"pods":         "清理 Evicted 状态的 Pod",
	"failed-pods":  "清理 Failed 状态的 Pod（非 Evicted）",
	"pending-pods": "清理长时间 Pending 的 Pod（默认1小时以上）",
	"jobs":         "清理已完成的 Job",
	"all":          "清理所有支持的资源类型",
}).AddExamples(
	"k8s-helper cleanup pods                    # 仅清理 Evicted Pod",
	"k8s-helper cleanup failed-pods             # 仅清理 Failed Pod",
	"k8s-helper cleanup pending-pods            # 仅清理长时间 Pending Pod",
	"k8s-helper cleanup jobs                    # 仅清理已完成的 Job",
	"k8s-helper cleanup all                     # 清理所有支持的资源",
	"k8s-helper cleanup pods --dry-run          # 预览将要清理的资源",
	"k8s-helper cleanup jobs --older-than 24h  # 清理24小时前的资源",
)

// CleanupErrorConfig cleanup命令的错误配置
var CleanupErrorConfig = &ErrorConfig{
	RequiredArgs:  1,
	ErrorMessage:  "需要指定清理类型",
	ShowValidArgs: true,
	ShowExamples:  true,
}

// LogsHelp logs命令的帮助信息
var LogsHelp = NewCommandHelp(
	"logs <POD_NAME>",
	"查看 Pod 日志",
	`实时查看指定 Pod 的日志输出。

支持以下功能：
• 实时流式日志查看 (类似 kubectl logs -f)
• 指定命名空间
• 指定容器 (多容器 Pod)
• 显示时间戳
• 限制日志行数`,
).AddExamples(
	"k8s-helper logs my-pod                          # 查看 Pod 日志",
	"k8s-helper logs my-pod -n kube-system          # 指定命名空间",
	"k8s-helper logs my-pod -c container-name       # 指定容器",
	"k8s-helper logs my-pod -f                      # 实时跟踪日志",
	"k8s-helper logs my-pod --tail 100              # 显示最后100行",
)

// LogsErrorConfig logs命令的错误配置
var LogsErrorConfig = &ErrorConfig{
	RequiredArgs: 1,
	ErrorMessage: "需要指定 Pod 名称",
	ShowExamples: true,
}

// PortForwardHelp port-forward命令的帮助信息
var PortForwardHelp = NewCommandHelp(
	"port-forward <POD_NAME> <[LOCAL_PORT:]REMOTE_PORT>",
	"将本地端口转发到 Pod",
	`将本地端口转发到指定 Pod 的端口。

支持以下功能：
• 转发到 Pod 的指定端口
• 自动选择本地端口或指定本地端口
• 支持多端口转发
• 优雅的信号处理

端口格式：
• 8080:80    - 本地端口 8080 转发到 Pod 端口 80
• :80        - 自动选择本地端口转发到 Pod 端口 80
• 8080       - 本地端口 8080 转发到 Pod 端口 8080`,
).AddExamples(
	"k8s-helper port-forward my-pod 8080:80          # 本地8080转发到Pod的80端口",
	"k8s-helper port-forward my-pod :80              # 自动选择本地端口转发到80",
	"k8s-helper port-forward my-pod 8080             # 本地8080转发到Pod的8080",
	"k8s-helper port-forward my-pod 8080:80 9090:90 # 多端口转发",
)

// PortForwardErrorConfig port-forward命令的错误配置
var PortForwardErrorConfig = &ErrorConfig{
	MinArgs:      2,
	ErrorMessage: "需要指定 Pod 名称和端口信息",
	ShowExamples: true,
}

// EtcdHelp etcd命令的帮助信息
var EtcdHelp = NewCommandHelp(
	"etcd [command]",
	"ETCD 数据备份与恢复工具",
	`ETCD 数据备份与恢复工具，专为使用 kubeadm 部署的集群设计。

⚠️  重要提示：
• 此功能仅适用于使用 kubeadm 工具部署的 Kubernetes 集群
• 恢复操作是高危行为，执行前请务必：
  1. 停止 kubelet 服务: sudo systemctl stop kubelet
  2. 备份现有的 ETCD 数据目录
  3. 确保集群处于维护状态

支持的子命令：
  backup   - 备份 ETCD 数据
  restore  - 恢复 ETCD 数据
  verify   - 验证 ETCD 快照文件`,
)

// EtcdBackupHelp etcd backup命令的帮助信息
var EtcdBackupHelp = NewCommandHelp(
	"backup",
	"备份 ETCD 数据",
	`自动检测 kube-apiserver 配置并备份 ETCD 数据。

此命令会：
1. 读取 /etc/kubernetes/manifests/kube-apiserver.yaml 文件
2. 解析 ETCD 相关的证书配置
3. 优先使用 Go SDK 执行快照备份
4. 如果 SDK 失败，回退到 etcdctl 工具

🔧 混合实现策略：
• 优先级1: Go SDK (go.etcd.io/etcd/client/v3)
• 优先级2: etcdutl 命令行工具
• 优先级3: etcdctl 命令行工具（向后兼容）`,
).AddExamples(
	"k8s-helper etcd backup                           # 使用默认路径备份",
	"k8s-helper etcd backup --output /tmp/backup.db  # 指定备份文件路径",
	"k8s-helper etcd backup --use-sdk=false          # 强制使用工具备份",
	"k8s-helper etcd backup --verbose                # 显示详细输出",
)

// EtcdRestoreHelp etcd restore命令的帮助信息
var EtcdRestoreHelp = NewCommandHelp(
	"restore",
	"恢复 ETCD 数据",
	`从快照文件恢复 ETCD 数据。

⚠️  危险操作警告：
此操作会完全替换现有的 ETCD 数据！执行前请务必：
1. 停止 kubelet 服务: sudo systemctl stop kubelet
2. 备份现有的 ETCD 数据目录
3. 确保所有控制平面节点都已停止

🔧 混合实现策略：
• 优先级1: Go SDK (go.etcd.io/etcd/etcdutl/v3/snapshot)
• 优先级2: etcdutl 命令行工具
• 优先级3: etcdctl 命令行工具（向后兼容）

📁 默认恢复目录：
如果不指定 --data-dir，将使用时间戳目录：
/var/lib/etcd-restore-YYYYMMDD-HHMMSS`,
).AddExamples(
	"k8s-helper etcd restore --snapshot /path/to/backup.db",
	"k8s-helper etcd restore -s backup.db --data-dir /var/lib/etcd-new",
	"k8s-helper etcd restore -s backup.db --use-sdk-restore=false",
	"k8s-helper etcd restore -s backup.db --name node1 --initial-advertise-peer-urls http://10.0.0.1:2380",
)

// EtcdRestoreErrorConfig etcd restore命令的错误配置
var EtcdRestoreErrorConfig = &ErrorConfig{
	CustomValidator: func(args []string) error {
		// restore命令不需要位置参数，所有参数都通过标志提供
		if len(args) > 0 {
			return fmt.Errorf("restore 命令不接受位置参数，请使用 --snapshot 标志指定快照文件")
		}
		return nil
	},
	ErrorMessage: "restore 命令需要通过 --snapshot 标志指定快照文件",
	ShowExamples: true,
}

// EtcdVerifyHelp etcd verify命令的帮助信息
var EtcdVerifyHelp = NewCommandHelp(
	"verify",
	"验证 ETCD 快照文件",
	`验证 ETCD 快照文件的完整性和有效性。

此命令会：
1. 检查快照文件是否存在
2. 优先使用 Go SDK 验证快照格式和完整性
3. 显示详细的快照状态信息
4. 如果 SDK 失败，回退到 etcdutl 工具

🔧 混合实现策略：
• 优先级1: Go SDK (go.etcd.io/etcd/etcdutl/v3/snapshot)
• 优先级2: etcdutl 命令行工具
• 优先级3: etcdctl 命令行工具（向后兼容）

📊 验证信息包括：
• 快照哈希值和数据版本
• 总键数和数据大小
• 快照版本信息
• 验证耗时统计`,
).AddExamples(
	"k8s-helper etcd verify --snapshot /path/to/backup.db",
	"k8s-helper etcd verify -s backup.db --detailed",
	"k8s-helper etcd verify -s backup.db --use-sdk-verify=false",
	"k8s-helper etcd verify -s backup.db --verify-timeout 60s",
)

// EtcdVerifyErrorConfig etcd verify命令的错误配置
var EtcdVerifyErrorConfig = &ErrorConfig{
	CustomValidator: func(args []string) error {
		// verify命令不需要位置参数，所有参数都通过标志提供
		if len(args) > 0 {
			return fmt.Errorf("verify 命令不接受位置参数，请使用 --snapshot 标志指定快照文件")
		}
		return nil
	},
	ErrorMessage: "verify 命令需要通过 --snapshot 标志指定快照文件",
	ShowExamples: true,
}

// InfoHelp info命令的帮助信息
var InfoHelp = NewCommandHelp(
	"info",
	"显示 Kubernetes 集群信息",
	`显示 Kubernetes 集群的详细信息，包括：

• 集群版本信息
• 节点状态和详情
• 资源使用情况
• 集群健康状态`,
).AddExamples(
	"k8s-helper info                    # 显示集群基本信息",
	"k8s-helper info --verbose          # 显示详细信息",
)

// ConfigHelp config命令的帮助信息
var ConfigHelp = NewCommandHelp(
	"config [command]",
	"配置文件管理",
	`管理 k8s-helper 的配置文件。

支持的子命令：
  init     - 生成示例配置文件
  show     - 显示当前配置
  path     - 显示配置文件路径`,
)

// CompletionHelp completion命令的帮助信息
var CompletionHelp = NewCommandHelp(
	"completion [bash|zsh|fish|powershell]",
	"生成自动补全脚本",
	`为指定的 shell 生成自动补全脚本。

支持的 shell：
• bash
• zsh  
• fish
• powershell`,
).AddExamples(
	"k8s-helper completion bash > k8s-helper-completion.bash",
	"k8s-helper completion zsh > _k8s-helper",
	"k8s-helper completion fish > k8s-helper.fish",
	"k8s-helper completion powershell > k8s-helper.ps1",
)

// EtcdCronJobHelp etcd cronjob命令的帮助信息
var EtcdCronJobHelp = NewCommandHelp(
	"cronjob [command]",
	"ETCD 备份 CronJob 管理工具",
	`管理用于 ETCD 自动备份的 Kubernetes CronJob 资源。

⚠️  重要提示：
• 此功能用于创建、查看和删除 ETCD 备份的 CronJob
• CronJob 将定期执行 ETCD 备份并保留指定数量的备份文件
• 确保集群具有足够的存储空间来保存备份文件
• 建议在非业务高峰期执行备份任务

支持的子命令：
  create  - 创建 ETCD 备份 CronJob
  list    - 列出现有的 ETCD 备份 CronJob
  delete  - 删除指定的 ETCD 备份 CronJob
  suspend - 暂停指定的 ETCD 备份 CronJob
  resume  - 恢复指定的 ETCD 备份 CronJob`,
)

// EtcdCronJobCreateHelp etcd cronjob create命令的帮助信息
var EtcdCronJobCreateHelp = NewCommandHelp(
	"create",
	"创建 ETCD 备份 CronJob",
	`创建一个 Kubernetes CronJob 来定期备份 ETCD 数据。

此命令会：
1. 创建一个配置好的 CronJob 资源
2. 设置定期执行的备份计划
3. 配置备份保留策略
4. 挂载必要的证书和配置文件

功能特性：
• 支持自定义备份调度时间（Cron 表达式）
• 支持时区设置
• 可配置备份保留数量
• 自动清理过期备份文件
• 使用项目内置的备份镜像

调度配置：
• 使用标准的 Cron 表达式格式
• 支持时区设置（如 Asia/Shanghai）
• 默认每天晚上 8 点执行备份

存储配置：
• 默认备份到 /opt/etcd-backups 目录
• 支持配置备份保留数量（默认保留 7 个）
• 自动清理超出保留数量的旧备份`,
).AddExamples(
	"k8s-helper etcd cronjob create --name etcd-backup",
	"k8s-helper etcd cronjob create --name etcd-backup --schedule '0 2 * * *'",
	"k8s-helper etcd cronjob create --name etcd-backup --schedule '0 2 * * *' --timezone Asia/Shanghai",
	"k8s-helper etcd cronjob create --name etcd-backup --backup-retain-count 10",
	"k8s-helper etcd cronjob create --name etcd-backup --namespace kube-system --image custom-backup:latest",
	"k8s-helper etcd cronjob create --name etcd-backup --verbose --force",
)

// EtcdCronJobListHelp etcd cronjob list命令的帮助信息
var EtcdCronJobListHelp = NewCommandHelp(
	"list",
	"列出 ETCD 备份 CronJob",
	`列出集群中现有的 ETCD 备份 CronJob 资源。

此命令会显示：
• CronJob 名称和命名空间
• 调度配置和时区设置
• 上次执行时间和状态
• 下次执行时间
• 备份保留配置

📊 输出信息包括：
• NAME: CronJob 名称
• NAMESPACE: 所在命名空间
• SCHEDULE: Cron 调度表达式
• TIMEZONE: 配置的时区
• LAST SCHEDULE: 上次执行时间
• ACTIVE: 当前活跃的 Job 数量
• SUSPEND: 是否暂停执行`,
).AddExamples(
	"k8s-helper etcd cronjob list",
	"k8s-helper etcd cronjob list --namespace kube-system",
	"k8s-helper etcd cronjob list --all-namespaces",
	"k8s-helper etcd cronjob list --verbose",
)

// EtcdCronJobSuspendHelp etcd cronjob suspend命令的帮助信息
var EtcdCronJobSuspendHelp = NewCommandHelp(
	"suspend <name>",
	"暂停指定的 ETCD 备份 CronJob",
	`暂停指定的 ETCD 备份 CronJob 的调度。

此命令会：
• 暂停指定的 CronJob
• CronJob 不会在暂停期间触发

⚠️  重要提示：暂停操作是临时的，可以随时恢复。

使用示例：
  k8s-helper etcd cronjob suspend --name etcd-backup`,
).AddExamples(
	"k8s-helper etcd cronjob suspend --name etcd-backup",
	"k8s-helper etcd cronjob suspend --name etcd-backup --namespace kube-system",
)

// EtcdCronJobResumeHelp etcd cronjob resume命令的帮助信息
var EtcdCronJobResumeHelp = NewCommandHelp(
	"resume <name>",
	"恢复指定的 ETCD 备份 CronJob",
	`恢复指定的 ETCD 备份 CronJob 的调度。

此命令会：
• 恢复指定的 CronJob
• CronJob 将继续按照既定的计划触发

使用示例：
  k8s-helper etcd cronjob resume --name etcd-backup`,
).AddExamples(
	"k8s-helper etcd cronjob resume --name etcd-backup",
	"k8s-helper etcd cronjob resume --name etcd-backup --namespace kube-system",
)

// EtcdCronJobDeleteHelp etcd cronjob delete命令的帮助信息
var EtcdCronJobDeleteHelp = NewCommandHelp(
	"delete",
	"删除 ETCD 备份 CronJob",
	`删除指定的 ETCD 备份 CronJob 资源。

⚠️  重要提示：
• 删除 CronJob 不会影响已创建的备份文件
• 正在运行的备份 Job 会继续执行直到完成
• 删除操作不可撤销，请谨慎操作

🗑️  删除选项：
• 支持按名称删除指定的 CronJob
• 可以强制删除（跳过确认）
• 支持删除关联的活跃 Job

📋 删除前检查：
• 显示将要删除的 CronJob 详情
• 显示关联的活跃 Job 信息
• 确认删除操作（除非使用 --force）`,
).AddExamples(
	"k8s-helper etcd cronjob delete --name etcd-backup",
	"k8s-helper etcd cronjob delete --name etcd-backup --namespace kube-system",
	"k8s-helper etcd cronjob delete --name etcd-backup --force",
	"k8s-helper etcd cronjob delete --name etcd-backup --verbose",
)

// EtcdCronJobCreateErrorConfig etcd cronjob create命令的错误配置
var EtcdCronJobCreateErrorConfig = &ErrorConfig{
	CustomValidator: func(args []string) error {
		// create命令不需要位置参数，所有参数都通过标志提供
		if len(args) > 0 {
			return fmt.Errorf("create 命令不接受位置参数，请使用 --name 标志指定 CronJob 名称")
		}
		return nil
	},
	ErrorMessage: "create 命令需要通过 --name 标志指定 CronJob 名称",
	ShowExamples: true,
}

// EtcdCronJobListErrorConfig etcd cronjob list命令的错误配置
var EtcdCronJobListErrorConfig = &ErrorConfig{
	CustomValidator: func(args []string) error {
		// list命令不需要位置参数
		if len(args) > 0 {
			return fmt.Errorf("list 命令不接受位置参数")
		}
		return nil
	},
	ErrorMessage: "list 命令不接受位置参数",
	ShowExamples: true,
}

// EtcdCronJobSuspendErrorConfig etcd cronjob suspend命令的错误配置
var EtcdCronJobSuspendErrorConfig = &ErrorConfig{
	CustomValidator: func(args []string) error {
		// suspend命令不需要位置参数，所有参数都通过标志提供
		if len(args) > 0 {
			return fmt.Errorf("suspend 命令不接受位置参数，请使用 --name 标志指定要暂停的 CronJob 名称")
		}
		return nil
	},
	ErrorMessage: "suspend 命令需要通过 --name 标志指定要暂停的 CronJob 名称",
	ShowExamples: true,
}

// EtcdCronJobResumeErrorConfig etcd cronjob resume命令的错误配置
var EtcdCronJobResumeErrorConfig = &ErrorConfig{
	CustomValidator: func(args []string) error {
		// resume命令不需要位置参数，所有参数都通过标志提供
		if len(args) > 0 {
			return fmt.Errorf("resume 命令不接受位置参数，请使用 --name 标志指定要恢复的 CronJob 名称")
		}
		return nil
	},
	ErrorMessage: "resume 命令需要通过 --name 标志指定要恢复的 CronJob 名称",
	ShowExamples: true,
}

// EtcdCronJobDeleteErrorConfig etcd cronjob delete命令的错误配置
var EtcdCronJobDeleteErrorConfig = &ErrorConfig{
	CustomValidator: func(args []string) error {
		// delete命令不需要位置参数，所有参数都通过标志提供
		if len(args) > 0 {
			return fmt.Errorf("delete 命令不接受位置参数，请使用 --name 标志指定要删除的 CronJob 名称")
		}
		return nil
	},
	ErrorMessage: "delete 命令需要通过 --name 标志指定要删除的 CronJob 名称",
	ShowExamples: true,
}
