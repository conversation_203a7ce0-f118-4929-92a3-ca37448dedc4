import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Switch,
  Select,
  Tag,
  Tooltip,
  Popconfirm,
  message
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  Check<PERSON>ircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  useETCDCronJobs,
  useCreateETCDCronJob,
  useUpdateETCDCronJob,
  useDeleteETCDCronJob,
  useAppState,
  useNotifications
} from '@/hooks';
import type { ETCDCronJobItem, ETCDCronJobRequest } from '@/types/api';

const { Option } = Select;

/**
 * ETCD CronJob管理页面
 *
 * 功能特性：
 * - CronJob列表展示
 * - 创建/编辑CronJob
 * - 启用/禁用CronJob
 * - 删除CronJob
 * - 执行历史查看
 */
const ETCDCronJobs: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingJob, setEditingJob] = useState<ETCDCronJobItem | null>(null);
  const [form] = Form.useForm();

  const { setPageTitle } = useAppState();
  const { showSuccess, showError } = useNotifications();

  // 获取CronJob列表
  const {
    data: cronJobs = [],
    isLoading,
    refetch
  } = useETCDCronJobs();

  // 创建CronJob
  const createMutation = useCreateETCDCronJob({
    onSuccess: () => {
      showSuccess('CronJob创建成功', '定时任务已创建');
      handleModalClose();
      refetch();
    },
    onError: (error: any) => {
      showError('CronJob创建失败', error.message);
    },
  });

  // 更新CronJob
  const updateMutation = useUpdateETCDCronJob({
    onSuccess: () => {
      showSuccess('CronJob更新成功', '定时任务已更新');
      handleModalClose();
      refetch();
    },
    onError: (error: any) => {
      showError('CronJob更新失败', error.message);
    },
  });

  // 删除CronJob
  const deleteMutation = useDeleteETCDCronJob({
    onSuccess: () => {
      showSuccess('CronJob删除成功', '定时任务已删除');
      refetch();
    },
    onError: (error: any) => {
      showError('CronJob删除失败', error.message);
    },
  });

  // 设置页面标题
  useEffect(() => {
    setPageTitle('ETCD CronJob - K8s-Helper');
  }, [setPageTitle]);

  // 处理模态框关闭
  const handleModalClose = () => {
    setModalVisible(false);
    setEditingJob(null);
    form.resetFields();
  };

  // 处理创建/编辑
  const handleSubmit = async (values: any) => {
    const request: ETCDCronJobRequest = {
      name: values.name,
      schedule: values.schedule,
      backup_path: values.backup_path,
      compress: values.compress || false,
      enabled: values.enabled !== false,
      description: values.description,
    };

    if (editingJob) {
      updateMutation.mutate({ id: editingJob.id, ...request });
    } else {
      createMutation.mutate(request);
    }
  };

  // 处理编辑
  const handleEdit = (job: ETCDCronJobItem) => {
    setEditingJob(job);
    form.setFieldsValue({
      name: job.name,
      schedule: job.schedule,
      backup_path: job.backup_path,
      compress: job.compress,
      enabled: job.enabled,
      description: job.description,
    });
    setModalVisible(true);
  };

  // 处理删除
  const handleDelete = (jobId: string) => {
    deleteMutation.mutate(jobId);
  };

  // 处理启用/禁用
  const handleToggleEnabled = (job: ETCDCronJobItem) => {
    updateMutation.mutate({
      id: job.id,
      enabled: !job.enabled,
    });
  };

  // 格式化时间
  const formatTime = (timeStr: string): string => {
    return new Date(timeStr).toLocaleString('zh-CN');
  };

  // 表格列定义
  const columns: ColumnsType<ETCDCronJobItem> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: ETCDCronJobItem) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          {record.description && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
              {record.description}
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Cron表达式',
      dataIndex: 'schedule',
      key: 'schedule',
      width: 150,
      render: (schedule: string) => (
        <Tooltip title="Cron表达式格式：分 时 日 月 周">
          <code style={{
            padding: '2px 6px',
            backgroundColor: '#f5f5f5',
            borderRadius: '3px',
            fontSize: '12px'
          }}>
            {schedule}
          </code>
        </Tooltip>
      ),
    },
    {
      title: '备份路径',
      dataIndex: 'backup_path',
      key: 'backup_path',
      ellipsis: true,
      render: (path: string) => (
        <Tooltip title={path}>
          <code style={{ fontSize: '12px' }}>{path}</code>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 80,
      render: (enabled: boolean) => (
        <Tag
          color={enabled ? 'green' : 'default'}
          icon={enabled ? <CheckCircleOutlined /> : <PauseCircleOutlined />}
        >
          {enabled ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '压缩',
      dataIndex: 'compress',
      key: 'compress',
      width: 80,
      render: (compress: boolean) => (
        <Tag color={compress ? 'blue' : 'default'}>
          {compress ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '下次执行',
      dataIndex: 'next_run',
      key: 'next_run',
      width: 160,
      render: (nextRun: string) => (
        <div style={{ fontSize: '12px' }}>
          <ClockCircleOutlined style={{ marginRight: '4px' }} />
          {formatTime(nextRun)}
        </div>
      ),
    },
    {
      title: '最后执行',
      dataIndex: 'last_run',
      key: 'last_run',
      width: 160,
      render: (lastRun: string, record: ETCDCronJobItem) => (
        <div style={{ fontSize: '12px' }}>
          {lastRun ? (
            <div>
              <div>{formatTime(lastRun)}</div>
              <Tag
                size="small"
                color={record.last_status === 'success' ? 'green' : 'red'}
                style={{ marginTop: '2px' }}
              >
                {record.last_status === 'success' ? '成功' : '失败'}
              </Tag>
            </div>
          ) : (
            <span style={{ color: '#999' }}>未执行</span>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record: ETCDCronJobItem) => (
        <Space size="small">
          <Tooltip title={record.enabled ? '禁用' : '启用'}>
            <Button
              type="text"
              size="small"
              icon={record.enabled ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => handleToggleEnabled(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个定时任务吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <h2 style={{ margin: 0 }}>定时任务</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            管理ETCD自动备份定时任务
          </p>
        </div>

        <Space>
          <Button
            icon={<ReloadOutlined />}
            loading={isLoading}
            onClick={() => refetch()}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setModalVisible(true)}
          >
            创建任务
          </Button>
        </Space>
      </div>

      {/* CronJob列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={cronJobs}
          loading={isLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingJob ? '编辑定时任务' : '创建定时任务'}
        open={modalVisible}
        onCancel={handleModalClose}
        onOk={() => form.submit()}
        confirmLoading={createMutation.isPending || updateMutation.isPending}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            enabled: true,
            compress: true,
          }}
        >
          <Form.Item
            name="name"
            label="任务名称"
            rules={[
              { required: true, message: '请输入任务名称' },
              { max: 50, message: '任务名称不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入任务名称" />
          </Form.Item>

          <Form.Item
            name="schedule"
            label="Cron表达式"
            rules={[
              { required: true, message: '请输入Cron表达式' },
              {
                pattern: /^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$/,
                message: '请输入有效的Cron表达式'
              }
            ]}
            extra="格式：分 时 日 月 周，例如：0 2 * * * 表示每天凌晨2点执行"
          >
            <Input placeholder="0 2 * * *" />
          </Form.Item>

          <Form.Item
            name="backup_path"
            label="备份路径"
            rules={[
              { required: true, message: '请输入备份路径' },
              {
                pattern: /^\/.*\.db$/,
                message: '路径必须以/开头，以.db结尾'
              }
            ]}
          >
            <Input placeholder="/backup/etcd-$(date +%Y%m%d-%H%M%S).db" />
          </Form.Item>

          <Form.Item
            name="description"
            label="任务描述"
          >
            <Input.TextArea
              placeholder="请输入任务描述（可选）"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="compress"
            label="压缩选项"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="启用压缩"
              unCheckedChildren="不压缩"
            />
          </Form.Item>

          <Form.Item
            name="enabled"
            label="任务状态"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ETCDCronJobs;
