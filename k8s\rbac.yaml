apiVersion: v1
kind: Namespace
metadata:
  name: k8s-helper
  labels:
    name: k8s-helper
    app: k8s-helper

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: k8s-helper
  namespace: k8s-helper
  labels:
    app: k8s-helper
automountServiceAccountToken: true

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: k8s-helper
  labels:
    app: k8s-helper
rules:
# 核心资源权限
- apiGroups: [""]
  resources:
    - pods
    - pods/log
    - pods/status
    - pods/exec
    - pods/portforward
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

- apiGroups: [""]
  resources:
    - services
    - endpoints
    - configmaps
    - secrets
    - persistentvolumes
    - persistentvolumeclaims
    - nodes
    - namespaces
    - events
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# Apps资源权限
- apiGroups: ["apps"]
  resources:
    - deployments
    - replicasets
    - statefulsets
    - daemonsets
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 扩展资源权限
- apiGroups: ["extensions"]
  resources:
    - deployments
    - replicasets
    - ingresses
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 网络资源权限
- apiGroups: ["networking.k8s.io"]
  resources:
    - ingresses
    - networkpolicies
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 自动扩缩容权限
- apiGroups: ["autoscaling"]
  resources:
    - horizontalpodautoscalers
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 批处理作业权限
- apiGroups: ["batch"]
  resources:
    - jobs
    - cronjobs
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 存储权限
- apiGroups: ["storage.k8s.io"]
  resources:
    - storageclasses
    - volumeattachments
  verbs: ["get", "list", "watch"]

# 指标权限
- apiGroups: ["metrics.k8s.io"]
  resources:
    - pods
    - nodes
  verbs: ["get", "list"]

# 自定义资源权限
- apiGroups: ["apiextensions.k8s.io"]
  resources:
    - customresourcedefinitions
  verbs: ["get", "list", "watch"]

# RBAC权限（只读）
- apiGroups: ["rbac.authorization.k8s.io"]
  resources:
    - roles
    - rolebindings
    - clusterroles
    - clusterrolebindings
  verbs: ["get", "list", "watch"]

# 策略权限
- apiGroups: ["policy"]
  resources:
    - poddisruptionbudgets
    - podsecuritypolicies
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# 证书权限
- apiGroups: ["certificates.k8s.io"]
  resources:
    - certificatesigningrequests
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: k8s-helper
  labels:
    app: k8s-helper
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: k8s-helper
subjects:
- kind: ServiceAccount
  name: k8s-helper
  namespace: k8s-helper

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: k8s-helper-namespace
  namespace: k8s-helper
  labels:
    app: k8s-helper
rules:
# 命名空间内的完全权限
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["*"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: k8s-helper-namespace
  namespace: k8s-helper
  labels:
    app: k8s-helper
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: k8s-helper-namespace
subjects:
- kind: ServiceAccount
  name: k8s-helper
  namespace: k8s-helper

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: k8s-helper-config
  namespace: k8s-helper
  labels:
    app: k8s-helper
data:
  # 应用配置
  APP_NAME: "k8s-helper"
  APP_VERSION: "1.0.0"
  APP_ENV: "production"
  
  # 服务器配置
  SERVER_HOST: "0.0.0.0"
  SERVER_PORT: "8080"
  
  # 日志配置
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  LOG_OUTPUT: "stdout"
  
  # CORS配置
  CORS_ENABLED: "true"
  
  # 缓存配置
  CACHE_ENABLED: "true"
  CACHE_TYPE: "redis"
  CACHE_TTL: "300"
  
  # 监控配置
  METRICS_ENABLED: "true"
  HEALTH_CHECK_ENABLED: "true"
  
  # 功能开关
  FEATURE_ETCD_BACKUP: "true"
  FEATURE_PORT_FORWARD: "true"
  FEATURE_LOG_STREAMING: "true"
  FEATURE_RESOURCE_CLEANUP: "true"
  FEATURE_CONFIG_MANAGEMENT: "true"
  FEATURE_MONITORING_DASHBOARD: "true"

---
apiVersion: v1
kind: Secret
metadata:
  name: k8s-helper-secrets
  namespace: k8s-helper
  labels:
    app: k8s-helper
type: Opaque
data:
  # JWT密钥 (base64编码)
  JWT_SECRET: eW91ci1qd3Qtc2VjcmV0LWtleQ==
  # Session密钥 (base64编码)
  SESSION_SECRET: eW91ci1zZXNzaW9uLXNlY3JldC1rZXk=
  # Redis连接字符串 (base64编码)
  REDIS_URL: cmVkaXM6Ly9yZWRpcy1zZXJ2aWNlOjYzNzk=

---
apiVersion: v1
kind: Secret
metadata:
  name: k8s-helper-kubeconfig
  namespace: k8s-helper
  labels:
    app: k8s-helper
type: Opaque
data:
  # Kubeconfig文件内容 (base64编码)
  # 这里应该包含实际的kubeconfig内容
  config: |
    YXBpVmVyc2lvbjogdjEKa2luZDogQ29uZmlnCmNsdXN0ZXJzOgotIGNsdXN0ZXI6CiAgICBzZXJ2
    ZXI6IGh0dHBzOi8va3ViZXJuZXRlcy5kZWZhdWx0LnN2Yy5jbHVzdGVyLmxvY2FsCiAgICBjZXJ0
    aWZpY2F0ZS1hdXRob3JpdHktZGF0YTogLS0tLS1CRUdJTi4uLgogIG5hbWU6IGRlZmF1bHQKY29u
    dGV4dHM6Ci0gY29udGV4dDoKICAgIGNsdXN0ZXI6IGRlZmF1bHQKICAgIHVzZXI6IGRlZmF1bHQK
    ICBuYW1lOiBkZWZhdWx0CmN1cnJlbnQtY29udGV4dDogZGVmYXVsdAp1c2VyczoKLSBuYW1lOiBk
    ZWZhdWx0CiAgdXNlcjoKICAgIHRva2VuOiAuLi4=
