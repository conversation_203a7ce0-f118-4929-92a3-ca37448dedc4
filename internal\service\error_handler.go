package service

import (
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"k8s-helper/pkg/etcd"
)

// ErrorType 错误类型
type ErrorType string

const (
	ErrorTypeConnection    ErrorType = "connection"
	ErrorTypeConfiguration ErrorType = "configuration"
	ErrorTypeValidation    ErrorType = "validation"
	ErrorTypeExecution     ErrorType = "execution"
	ErrorTypeTimeout       ErrorType = "timeout"
	ErrorTypePermission    ErrorType = "permission"
	ErrorTypeNotFound      ErrorType = "not_found"
	ErrorTypeInternal      ErrorType = "internal"
)

// ErrorAnalysisResult 错误分析结果
type ErrorAnalysisResult struct {
	Type        ErrorType
	Recoverable bool
	Suggestions []string
}

// ErrorAnalyzer 错误分析器接口
type ErrorAnalyzer interface {
	AnalyzeError(err error) *ErrorAnalysisResult
	CanHandle(err error) bool
}

// ErrorPattern 错误模式
type ErrorPattern struct {
	Keywords    []string
	Type        ErrorType
	Recoverable bool
	Suggestions []string
}

// StandardErrorAnalyzer 标准错误分析器
type StandardErrorAnalyzer struct {
	patterns []ErrorPattern
}

// NewStandardErrorAnalyzer 创建标准错误分析器
func NewStandardErrorAnalyzer() *StandardErrorAnalyzer {
	patterns := []ErrorPattern{
		{
			Keywords:    []string{"connection", "dial", "network"},
			Type:        ErrorTypeConnection,
			Recoverable: true,
			Suggestions: []string{
				"检查ETCD服务是否运行",
				"验证网络连接",
				"检查防火墙设置",
				"确认端点地址正确",
			},
		},
		{
			Keywords:    []string{"timeout", "deadline exceeded"},
			Type:        ErrorTypeTimeout,
			Recoverable: true,
			Suggestions: []string{
				"增加超时时间",
				"检查网络延迟",
				"确认服务响应正常",
			},
		},
		{
			Keywords:    []string{"permission", "unauthorized", "forbidden", "certificate"},
			Type:        ErrorTypePermission,
			Recoverable: false,
			Suggestions: []string{
				"检查证书文件是否存在",
				"验证证书权限",
				"确认证书未过期",
				"检查CA证书配置",
			},
		},
		{
			Keywords:    []string{"config", "invalid", "parse"},
			Type:        ErrorTypeConfiguration,
			Recoverable: false,
			Suggestions: []string{
				"检查配置文件格式",
				"验证配置参数",
				"确认文件路径正确",
			},
		},
		{
			Keywords:    []string{"no such file", "not found"},
			Type:        ErrorTypeNotFound,
			Recoverable: false,
			Suggestions: []string{
				"检查文件路径是否正确",
				"确认文件是否存在",
				"验证文件权限",
			},
		},
	}

	return &StandardErrorAnalyzer{
		patterns: patterns,
	}
}

// CanHandle 检查是否可以处理该错误
func (sea *StandardErrorAnalyzer) CanHandle(err error) bool {
	return err != nil
}

// AnalyzeError 分析错误
func (sea *StandardErrorAnalyzer) AnalyzeError(err error) *ErrorAnalysisResult {
	if err == nil {
		return &ErrorAnalysisResult{
			Type:        ErrorTypeInternal,
			Recoverable: false,
			Suggestions: nil,
		}
	}

	errMsg := strings.ToLower(err.Error())

	for _, pattern := range sea.patterns {
		for _, keyword := range pattern.Keywords {
			if strings.Contains(errMsg, keyword) {
				return &ErrorAnalysisResult{
					Type:        pattern.Type,
					Recoverable: pattern.Recoverable,
					Suggestions: pattern.Suggestions,
				}
			}
		}
	}

	// 默认为内部错误
	return &ErrorAnalysisResult{
		Type:        ErrorTypeInternal,
		Recoverable: false,
		Suggestions: []string{"请联系系统管理员"},
	}
}

// SDKErrorAnalyzer SDK错误分析器
type SDKErrorAnalyzer struct{}

// NewSDKErrorAnalyzer 创建SDK错误分析器
func NewSDKErrorAnalyzer() *SDKErrorAnalyzer {
	return &SDKErrorAnalyzer{}
}

// CanHandle 检查是否可以处理该错误
func (sea *SDKErrorAnalyzer) CanHandle(err error) bool {
	_, ok := err.(*etcd.SDKError)
	return ok
}

// AnalyzeError 分析SDK错误
func (sea *SDKErrorAnalyzer) AnalyzeError(err error) *ErrorAnalysisResult {
	sdkErr, ok := err.(*etcd.SDKError)
	if !ok {
		return &ErrorAnalysisResult{
			Type:        ErrorTypeInternal,
			Recoverable: false,
			Suggestions: []string{"请查看详细日志"},
		}
	}

	// 根据原始错误类型进行分析
	if errors.Is(sdkErr.Cause, etcd.ErrConnectionFailed) {
		return &ErrorAnalysisResult{
			Type:        ErrorTypeConnection,
			Recoverable: true,
			Suggestions: []string{
				"检查ETCD服务状态",
				"验证网络连接",
				"确认端点配置",
			},
		}
	}
	if errors.Is(sdkErr.Cause, etcd.ErrInvalidConfig) {
		return &ErrorAnalysisResult{
			Type:        ErrorTypeConfiguration,
			Recoverable: false,
			Suggestions: []string{
				"检查配置参数",
				"验证证书文件",
				"确认端点格式",
			},
		}
	}
	if errors.Is(sdkErr.Cause, etcd.ErrCertificateInvalid) {
		return &ErrorAnalysisResult{
			Type:        ErrorTypePermission,
			Recoverable: false,
			Suggestions: []string{
				"检查证书文件",
				"验证证书有效期",
				"确认CA配置",
			},
		}
	}
	if errors.Is(sdkErr.Cause, etcd.ErrSnapshotInvalid) {
		return &ErrorAnalysisResult{
			Type:        ErrorTypeValidation,
			Recoverable: false,
			Suggestions: []string{
				"检查快照文件完整性",
				"验证快照格式",
				"尝试重新生成快照",
			},
		}
	}

	// 默认情况
	return &ErrorAnalysisResult{
		Type:        ErrorTypeInternal,
		Recoverable: false,
		Suggestions: []string{"请查看详细日志"},
	}
}

// ServiceError 服务错误
type ServiceError struct {
	Type        ErrorType
	Operation   string
	Message     string
	Cause       error
	Recoverable bool
	Suggestions []string
}

// Error 实现error接口
func (e *ServiceError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%s:%s] %s: %v", e.Type, e.Operation, e.Message, e.Cause)
	}
	return fmt.Sprintf("[%s:%s] %s", e.Type, e.Operation, e.Message)
}

// Unwrap 支持errors.Unwrap
func (e *ServiceError) Unwrap() error {
	return e.Cause
}

// Is 支持errors.Is
func (e *ServiceError) Is(target error) bool {
	if se, ok := target.(*ServiceError); ok {
		return e.Type == se.Type && e.Operation == se.Operation
	}
	return false
}

// ErrorHandler 统一错误处理器
type ErrorHandler struct {
	logger    *zap.Logger
	analyzers []ErrorAnalyzer
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(logger *zap.Logger) *ErrorHandler {
	return &ErrorHandler{
		logger: logger,
		analyzers: []ErrorAnalyzer{
			NewSDKErrorAnalyzer(),      // SDK错误分析器优先
			NewStandardErrorAnalyzer(), // 标准错误分析器作为后备
		},
	}
}

// WrapError 包装错误
func (eh *ErrorHandler) WrapError(operation string, err error) *ServiceError {
	if err == nil {
		return nil
	}

	// 如果已经是ServiceError，直接返回
	if se, ok := err.(*ServiceError); ok {
		return se
	}

	// 使用分析器分析错误
	result := eh.analyzeErrorWithAnalyzers(err)

	return &ServiceError{
		Type:        result.Type,
		Operation:   operation,
		Message:     err.Error(),
		Cause:       err,
		Recoverable: result.Recoverable,
		Suggestions: result.Suggestions,
	}
}

// CreateError 创建新错误
func (eh *ErrorHandler) CreateError(errorType ErrorType, operation, message string, cause error) *ServiceError {
	result := eh.analyzeErrorWithAnalyzers(cause)

	return &ServiceError{
		Type:        errorType, // 使用指定的错误类型
		Operation:   operation,
		Message:     message,
		Cause:       cause,
		Recoverable: result.Recoverable,
		Suggestions: result.Suggestions,
	}
}

// analyzeErrorWithAnalyzers 使用分析器分析错误
func (eh *ErrorHandler) analyzeErrorWithAnalyzers(err error) *ErrorAnalysisResult {
	if err == nil {
		return &ErrorAnalysisResult{
			Type:        ErrorTypeInternal,
			Recoverable: false,
			Suggestions: nil,
		}
	}

	// 按优先级使用分析器
	for _, analyzer := range eh.analyzers {
		if analyzer.CanHandle(err) {
			return analyzer.AnalyzeError(err)
		}
	}

	// 如果没有分析器能处理，返回默认结果
	return &ErrorAnalysisResult{
		Type:        ErrorTypeInternal,
		Recoverable: false,
		Suggestions: []string{"请联系系统管理员"},
	}
}

// HandleError 处理错误（记录日志并返回用户友好的错误）
func (eh *ErrorHandler) HandleError(err error) error {
	if err == nil {
		return nil
	}

	se := eh.WrapError("unknown", err)

	// 记录日志
	eh.logError(se)

	// 返回用户友好的错误消息
	return eh.createUserFriendlyError(se)
}

// LogError 记录错误日志
func (eh *ErrorHandler) LogError(err error) {
	if err == nil {
		return
	}

	se := eh.WrapError("unknown", err)
	eh.logError(se)
}

// logError 记录错误日志
func (eh *ErrorHandler) logError(se *ServiceError) {
	fields := []zap.Field{
		zap.String("type", string(se.Type)),
		zap.String("operation", se.Operation),
		zap.Bool("recoverable", se.Recoverable),
		zap.Strings("suggestions", se.Suggestions),
	}

	if se.Cause != nil {
		fields = append(fields, zap.Error(se.Cause))
	}

	if se.Recoverable {
		eh.logger.Warn(se.Message, fields...)
	} else {
		eh.logger.Error(se.Message, fields...)
	}
}

// createUserFriendlyError 创建用户友好的错误消息
func (eh *ErrorHandler) createUserFriendlyError(se *ServiceError) error {
	message := fmt.Sprintf("操作失败: %s", se.Message)

	if len(se.Suggestions) > 0 {
		message += "\n建议解决方案:\n"
		for i, suggestion := range se.Suggestions {
			message += fmt.Sprintf("  %d. %s\n", i+1, suggestion)
		}
	}

	return errors.New(message)
}

// IsRecoverable 检查错误是否可恢复
func (eh *ErrorHandler) IsRecoverable(err error) bool {
	if se, ok := err.(*ServiceError); ok {
		return se.Recoverable
	}
	return false
}

// GetErrorType 获取错误类型
func (eh *ErrorHandler) GetErrorType(err error) ErrorType {
	if se, ok := err.(*ServiceError); ok {
		return se.Type
	}
	return ErrorTypeInternal
}

// GetSuggestions 获取错误建议
func (eh *ErrorHandler) GetSuggestions(err error) []string {
	if se, ok := err.(*ServiceError); ok {
		return se.Suggestions
	}
	return nil
}
