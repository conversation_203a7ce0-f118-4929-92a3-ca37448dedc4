package etcd

import (
	"errors"
	"fmt"
)

// 定义常见的错误类型
var (
	// ErrInvalidConfig 配置无效错误
	ErrInvalidConfig = errors.New("etcd配置无效")

	// ErrConnectionFailed 连接失败错误
	ErrConnectionFailed = errors.New("etcd连接失败")

	// ErrBackupFailed 备份失败错误
	ErrBackupFailed = errors.New("etcd备份失败")

	// ErrRestoreFailed 恢复失败错误
	ErrRestoreFailed = errors.New("etcd恢复失败")

	// ErrSnapshotInvalid 快照文件无效错误
	ErrSnapshotInvalid = errors.New("etcd快照文件无效")

	// ErrDataDirInvalid 数据目录无效错误
	ErrDataDirInvalid = errors.New("etcd数据目录无效")

	// ErrCertificateInvalid 证书无效错误
	ErrCertificateInvalid = errors.New("etcd证书无效")

	// ErrTimeout 超时错误
	ErrTimeout = errors.New("etcd操作超时")
)

// SDKError SDK特定错误类型
type SDKError struct {
	Operation string // 操作类型
	Cause     error  // 原始错误
	Message   string // 错误消息
}

// Error 实现error接口
func (e *SDKError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("etcd SDK %s 失败: %s (原因: %v)", e.Operation, e.Message, e.Cause)
	}
	return fmt.Sprintf("etcd SDK %s 失败: %s", e.Operation, e.Message)
}

// Unwrap 支持errors.Unwrap
func (e *SDKError) Unwrap() error {
	return e.Cause
}

// NewSDKError 创建新的SDK错误
func NewSDKError(operation, message string, cause error) *SDKError {
	return &SDKError{
		Operation: operation,
		Message:   message,
		Cause:     cause,
	}
}

// IsConnectionError 判断是否为连接错误
func IsConnectionError(err error) bool {
	if err == nil {
		return false
	}

	var sdkErr *SDKError
	if errors.As(err, &sdkErr) {
		return errors.Is(sdkErr.Cause, ErrConnectionFailed)
	}

	return errors.Is(err, ErrConnectionFailed)
}

// IsTimeoutError 判断是否为超时错误
func IsTimeoutError(err error) bool {
	if err == nil {
		return false
	}

	var sdkErr *SDKError
	if errors.As(err, &sdkErr) {
		return errors.Is(sdkErr.Cause, ErrTimeout)
	}

	return errors.Is(err, ErrTimeout)
}

// IsCertificateError 判断是否为证书错误
func IsCertificateError(err error) bool {
	if err == nil {
		return false
	}

	var sdkErr *SDKError
	if errors.As(err, &sdkErr) {
		return errors.Is(sdkErr.Cause, ErrCertificateInvalid)
	}

	return errors.Is(err, ErrCertificateInvalid)
}

// IsRestoreError 判断是否为恢复错误
func IsRestoreError(err error) bool {
	if err == nil {
		return false
	}

	var sdkErr *SDKError
	if errors.As(err, &sdkErr) {
		return errors.Is(sdkErr.Cause, ErrRestoreFailed)
	}

	return errors.Is(err, ErrRestoreFailed)
}

// IsSnapshotError 判断是否为快照文件错误
func IsSnapshotError(err error) bool {
	if err == nil {
		return false
	}

	var sdkErr *SDKError
	if errors.As(err, &sdkErr) {
		return errors.Is(sdkErr.Cause, ErrSnapshotInvalid)
	}

	return errors.Is(err, ErrSnapshotInvalid)
}

// IsDataDirError 判断是否为数据目录错误
func IsDataDirError(err error) bool {
	if err == nil {
		return false
	}

	var sdkErr *SDKError
	if errors.As(err, &sdkErr) {
		return errors.Is(sdkErr.Cause, ErrDataDirInvalid)
	}

	return errors.Is(err, ErrDataDirInvalid)
}
