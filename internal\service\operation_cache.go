package service

import (
	"crypto/sha256"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
)

// OperationResult 操作结果
type OperationResult struct {
	Success   bool
	Data      interface{}
	Error     error
	Duration  time.Duration
	Timestamp time.Time
}

// CacheEntry 缓存条目
type CacheEntry struct {
	Key        string
	Result     *OperationResult
	CreatedAt  time.Time
	AccessedAt time.Time
	HitCount   int64
	TTL        time.Duration
}

// OperationCache 操作结果缓存管理器
type OperationCache struct {
	logger        *zap.Logger
	cache         map[string]*CacheEntry
	mutex         sync.RWMutex
	maxSize       int
	defaultTTL    time.Duration
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
}

// NewOperationCache 创建操作缓存管理器
func NewOperationCache(logger *zap.Logger, maxSize int, defaultTTL time.Duration) *OperationCache {
	cache := &OperationCache{
		logger:      logger,
		cache:       make(map[string]*CacheEntry),
		maxSize:     maxSize,
		defaultTTL:  defaultTTL,
		stopCleanup: make(chan struct{}),
	}

	// 启动定期清理
	cache.startCleanup()

	return cache
}

// CacheBackupValidation 缓存备份验证结果
func (oc *OperationCache) CacheBackupValidation(backupPath string, result *OperationResult) {
	key := oc.generateBackupValidationKey(backupPath)
	oc.setCache(key, result, 10*time.Minute) // 备份验证结果缓存10分钟
}

// GetBackupValidation 获取备份验证结果
func (oc *OperationCache) GetBackupValidation(backupPath string) (*OperationResult, bool) {
	key := oc.generateBackupValidationKey(backupPath)
	return oc.getCache(key)
}

// CacheToolCheck 缓存工具检查结果
func (oc *OperationCache) CacheToolCheck(toolName, version string, result *OperationResult) {
	key := oc.generateToolCheckKey(toolName, version)
	oc.setCache(key, result, 30*time.Minute) // 工具检查结果缓存30分钟
}

// GetToolCheck 获取工具检查结果
func (oc *OperationCache) GetToolCheck(toolName, version string) (*OperationResult, bool) {
	key := oc.generateToolCheckKey(toolName, version)
	return oc.getCache(key)
}

// CacheClusterStatus 缓存集群状态检查结果
func (oc *OperationCache) CacheClusterStatus(clusterConfig string, result *OperationResult) {
	key := oc.generateClusterStatusKey(clusterConfig)
	oc.setCache(key, result, 5*time.Minute) // 集群状态缓存5分钟
}

// GetClusterStatus 获取集群状态检查结果
func (oc *OperationCache) GetClusterStatus(clusterConfig string) (*OperationResult, bool) {
	key := oc.generateClusterStatusKey(clusterConfig)
	return oc.getCache(key)
}

// CacheRestoreValidation 缓存恢复验证结果
func (oc *OperationCache) CacheRestoreValidation(restoreOptions *domain.RestoreOptions, result *OperationResult) {
	key := oc.generateRestoreValidationKey(restoreOptions)
	oc.setCache(key, result, 15*time.Minute) // 恢复验证结果缓存15分钟
}

// GetRestoreValidation 获取恢复验证结果
func (oc *OperationCache) GetRestoreValidation(restoreOptions *domain.RestoreOptions) (*OperationResult, bool) {
	key := oc.generateRestoreValidationKey(restoreOptions)
	return oc.getCache(key)
}

// CacheSnapshotInfo 缓存快照信息
func (oc *OperationCache) CacheSnapshotInfo(snapshotPath string, result *OperationResult) {
	key := oc.generateSnapshotInfoKey(snapshotPath)
	oc.setCache(key, result, 20*time.Minute) // 快照信息缓存20分钟
}

// GetSnapshotInfo 获取快照信息
func (oc *OperationCache) GetSnapshotInfo(snapshotPath string) (*OperationResult, bool) {
	key := oc.generateSnapshotInfoKey(snapshotPath)
	return oc.getCache(key)
}

// setCache 设置缓存
func (oc *OperationCache) setCache(key string, result *OperationResult, ttl time.Duration) {
	oc.mutex.Lock()
	defer oc.mutex.Unlock()

	// 检查缓存大小限制
	if len(oc.cache) >= oc.maxSize {
		oc.evictLRU()
	}

	entry := &CacheEntry{
		Key:        key,
		Result:     result,
		CreatedAt:  time.Now(),
		AccessedAt: time.Now(),
		HitCount:   0,
		TTL:        ttl,
	}

	oc.cache[key] = entry

	oc.logger.Debug("操作结果已缓存",
		zap.String("key", key),
		zap.Duration("ttl", ttl),
		zap.Int("cacheSize", len(oc.cache)))
}

// getCache 获取缓存
func (oc *OperationCache) getCache(key string) (*OperationResult, bool) {
	oc.mutex.RLock()
	defer oc.mutex.RUnlock()

	entry, exists := oc.cache[key]
	if !exists {
		return nil, false
	}

	// 检查TTL
	if time.Since(entry.CreatedAt) > entry.TTL {
		oc.logger.Debug("缓存已过期", zap.String("key", key))
		return nil, false
	}

	// 更新访问统计
	entry.AccessedAt = time.Now()
	entry.HitCount++

	oc.logger.Debug("缓存命中",
		zap.String("key", key),
		zap.Int64("hitCount", entry.HitCount))

	return entry.Result, true
}

// evictLRU 驱逐最近最少使用的条目
func (oc *OperationCache) evictLRU() {
	var oldestKey string
	var oldestTime time.Time

	for key, entry := range oc.cache {
		if oldestKey == "" || entry.AccessedAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = entry.AccessedAt
		}
	}

	if oldestKey != "" {
		delete(oc.cache, oldestKey)
		oc.logger.Debug("驱逐LRU缓存条目", zap.String("key", oldestKey))
	}
}

// generateBackupValidationKey 生成备份验证缓存键
func (oc *OperationCache) generateBackupValidationKey(backupPath string) string {
	hash := sha256.Sum256([]byte(backupPath))
	return fmt.Sprintf("backup_validation:%x", hash[:8])
}

// generateToolCheckKey 生成工具检查缓存键
func (oc *OperationCache) generateToolCheckKey(toolName, version string) string {
	data := fmt.Sprintf("%s:%s", toolName, version)
	hash := sha256.Sum256([]byte(data))
	return fmt.Sprintf("tool_check:%x", hash[:8])
}

// generateClusterStatusKey 生成集群状态缓存键
func (oc *OperationCache) generateClusterStatusKey(clusterConfig string) string {
	hash := sha256.Sum256([]byte(clusterConfig))
	return fmt.Sprintf("cluster_status:%x", hash[:8])
}

// generateRestoreValidationKey 生成恢复验证缓存键
func (oc *OperationCache) generateRestoreValidationKey(opts *domain.RestoreOptions) string {
	data := fmt.Sprintf("%s:%s:%s", opts.SnapshotPath, opts.DataDir, opts.Name)
	hash := sha256.Sum256([]byte(data))
	return fmt.Sprintf("restore_validation:%x", hash[:8])
}

// generateSnapshotInfoKey 生成快照信息缓存键
func (oc *OperationCache) generateSnapshotInfoKey(snapshotPath string) string {
	hash := sha256.Sum256([]byte(snapshotPath))
	return fmt.Sprintf("snapshot_info:%x", hash[:8])
}

// InvalidateByPattern 根据模式失效缓存
func (oc *OperationCache) InvalidateByPattern(pattern string) int {
	oc.mutex.Lock()
	defer oc.mutex.Unlock()

	invalidatedKeys := make([]string, 0)

	for key := range oc.cache {
		if oc.matchPattern(key, pattern) {
			invalidatedKeys = append(invalidatedKeys, key)
		}
	}

	for _, key := range invalidatedKeys {
		delete(oc.cache, key)
	}

	if len(invalidatedKeys) > 0 {
		oc.logger.Info("批量失效缓存",
			zap.String("pattern", pattern),
			zap.Int("count", len(invalidatedKeys)))
	}

	return len(invalidatedKeys)
}

// matchPattern 匹配模式
func (oc *OperationCache) matchPattern(key, pattern string) bool {
	// 简单的前缀匹配
	if len(pattern) == 0 {
		return false
	}

	if pattern[len(pattern)-1] == '*' {
		prefix := pattern[:len(pattern)-1]
		return len(key) >= len(prefix) && key[:len(prefix)] == prefix
	}

	return key == pattern
}

// startCleanup 启动定期清理
func (oc *OperationCache) startCleanup() {
	oc.cleanupTicker = time.NewTicker(5 * time.Minute)

	go func() {
		for {
			select {
			case <-oc.cleanupTicker.C:
				oc.cleanup()
			case <-oc.stopCleanup:
				oc.cleanupTicker.Stop()
				return
			}
		}
	}()
}

// cleanup 清理过期缓存
func (oc *OperationCache) cleanup() {
	oc.mutex.Lock()
	defer oc.mutex.Unlock()

	now := time.Now()
	expiredKeys := make([]string, 0)

	for key, entry := range oc.cache {
		if now.Sub(entry.CreatedAt) > entry.TTL {
			expiredKeys = append(expiredKeys, key)
		}
	}

	for _, key := range expiredKeys {
		delete(oc.cache, key)
	}

	if len(expiredKeys) > 0 {
		oc.logger.Debug("清理过期缓存",
			zap.Int("expired", len(expiredKeys)),
			zap.Int("remaining", len(oc.cache)))
	}
}

// GetStats 获取缓存统计信息
func (oc *OperationCache) GetStats() map[string]interface{} {
	oc.mutex.RLock()
	defer oc.mutex.RUnlock()

	totalHits := int64(0)
	for _, entry := range oc.cache {
		totalHits += entry.HitCount
	}

	return map[string]interface{}{
		"size":       len(oc.cache),
		"maxSize":    oc.maxSize,
		"totalHits":  totalHits,
		"defaultTTL": oc.defaultTTL.String(),
	}
}

// Close 关闭缓存管理器
func (oc *OperationCache) Close() {
	close(oc.stopCleanup)

	oc.mutex.Lock()
	defer oc.mutex.Unlock()

	oc.cache = make(map[string]*CacheEntry)
	oc.logger.Info("操作缓存已关闭")
}
