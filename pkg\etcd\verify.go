package etcd

import (
	"context"
	"fmt"
	"os"
	"time"

	"go.etcd.io/etcd/etcdutl/v3/snapshot"
	"go.uber.org/zap"
)

// VerifyOptions 验证选项
type VerifyOptions struct {
	SnapshotPath string        // 快照文件路径
	Timeout      time.Duration // 超时时间
	Detailed     bool          // 是否显示详细信息
}

// DefaultVerifyOptions 返回默认验证选项
func DefaultVerifyOptions() *VerifyOptions {
	return &VerifyOptions{
		Timeout:  30 * time.Second,
		Detailed: false,
	}
}

// VerifyResult 验证结果
type VerifyResult struct {
	SnapshotPath string        // 快照文件路径
	FileSize     int64         // 文件大小
	Hash         uint32        // 快照哈希值
	Revision     int64         // 数据版本
	TotalKeys    int           // 总键数
	TotalSize    int64         // 数据大小
	Duration     time.Duration // 验证耗时
	Valid        bool          // 是否有效
}

// VerifyWithSDK 使用SDK验证快照文件
func (c *Client) VerifyWithSDK(ctx context.Context, opts *VerifyOptions) (*VerifyResult, error) {
	if opts == nil {
		opts = DefaultVerifyOptions()
	}

	startTime := time.Now()

	c.logger.Info("开始使用SDK验证ETCD快照",
		zap.String("snapshot", opts.SnapshotPath))

	// 验证验证选项
	if err := c.validateVerifyOptions(opts); err != nil {
		return nil, NewSDKError("verify", "验证选项验证失败", err)
	}

	// 设置超时
	if opts.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, opts.Timeout)
		defer cancel()
	}

	// 获取文件信息
	fileInfo, err := os.Stat(opts.SnapshotPath)
	if err != nil {
		return nil, NewSDKError("verify", "获取快照文件信息失败", err)
	}

	// 创建快照管理器
	manager := snapshot.NewV3(c.logger.Named("snapshot"))

	c.logger.Info("执行SDK快照状态检查",
		zap.String("path", opts.SnapshotPath))

	// 获取快照状态
	status, err := manager.Status(opts.SnapshotPath)
	if err != nil {
		return nil, NewSDKError("verify", "SDK快照状态检查失败", err)
	}

	duration := time.Since(startTime)

	result := &VerifyResult{
		SnapshotPath: opts.SnapshotPath,
		FileSize:     fileInfo.Size(),
		Hash:         status.Hash,
		Revision:     status.Revision,
		TotalKeys:    status.TotalKey,
		TotalSize:    status.TotalSize,
		Duration:     duration,
		Valid:        true, // 如果没有错误，则认为是有效的
	}

	c.logger.Info("SDK快照验证完成",
		zap.String("path", result.SnapshotPath),
		zap.Duration("duration", result.Duration),
		zap.Int64("fileSize", result.FileSize),
		zap.Uint32("hash", result.Hash),
		zap.Int64("revision", result.Revision),
		zap.Int("totalKeys", result.TotalKeys))

	return result, nil
}

// VerifyWithOptions 使用选项执行验证
func (c *Client) VerifyWithOptions(ctx context.Context, opts *VerifyOptions) (*VerifyResult, error) {
	if opts == nil {
		opts = DefaultVerifyOptions()
	}

	// 验证验证选项
	if err := c.validateVerifyOptions(opts); err != nil {
		return nil, err
	}

	// 执行验证
	result, err := c.VerifyWithSDK(ctx, opts)
	if err != nil {
		return nil, err
	}

	// 验证结果检查
	if err := c.validateVerifyResult(result); err != nil {
		c.logger.Warn("验证结果检查失败", zap.Error(err))
		// 不返回错误，只记录警告
	}

	return result, nil
}

// validateVerifyOptions 验证验证选项
func (c *Client) validateVerifyOptions(opts *VerifyOptions) error {
	// 验证快照文件路径
	if opts.SnapshotPath == "" {
		return NewSDKError("validate", "快照文件路径不能为空", ErrSnapshotInvalid)
	}

	// 检查快照文件是否存在
	if _, err := os.Stat(opts.SnapshotPath); os.IsNotExist(err) {
		return NewSDKError("validate", fmt.Sprintf("快照文件不存在: %s", opts.SnapshotPath), ErrSnapshotInvalid)
	}

	return nil
}

// validateVerifyResult 验证验证结果
func (c *Client) validateVerifyResult(result *VerifyResult) error {
	// 检查基本字段
	if result.Hash == 0 {
		return NewSDKError("validate", "快照哈希值为空", ErrSnapshotInvalid)
	}

	if result.Revision <= 0 {
		return NewSDKError("validate", "快照版本号无效", ErrSnapshotInvalid)
	}

	if result.TotalKeys < 0 {
		return NewSDKError("validate", "快照键数无效", ErrSnapshotInvalid)
	}

	if result.TotalSize <= 0 {
		return NewSDKError("validate", "快照数据大小无效", ErrSnapshotInvalid)
	}

	c.logger.Info("验证结果检查通过",
		zap.String("path", result.SnapshotPath),
		zap.Uint32("hash", result.Hash),
		zap.Int64("revision", result.Revision),
		zap.Int("totalKeys", result.TotalKeys),
		zap.Int64("totalSize", result.TotalSize))

	return nil
}

// VerifyManager 验证管理器
type VerifyManager struct {
	client *Client
	logger *zap.Logger
}

// NewVerifyManager 创建验证管理器
func NewVerifyManager(client *Client) *VerifyManager {
	return &VerifyManager{
		client: client,
		logger: client.logger,
	}
}

// PerformVerify 执行验证操作
func (vm *VerifyManager) PerformVerify(ctx context.Context, snapshotPath string) (*VerifyResult, error) {
	opts := &VerifyOptions{
		SnapshotPath: snapshotPath,
		Timeout:      30 * time.Second,
		Detailed:     false,
	}

	return vm.client.VerifyWithOptions(ctx, opts)
}

// PerformDetailedVerify 执行详细验证操作
func (vm *VerifyManager) PerformDetailedVerify(ctx context.Context, snapshotPath string) (*VerifyResult, error) {
	opts := &VerifyOptions{
		SnapshotPath: snapshotPath,
		Timeout:      60 * time.Second,
		Detailed:     true,
	}

	return vm.client.VerifyWithOptions(ctx, opts)
}

// FormatVerifyResult 格式化验证结果
func FormatVerifyResult(result *VerifyResult) string {
	if result == nil {
		return "验证结果为空"
	}

	return fmt.Sprintf(`快照验证结果:
  文件路径: %s
  文件大小: %d 字节
  快照哈希: %x
  数据版本: %d
  总键数: %d
  数据大小: %d 字节
  验证耗时: %v
  验证状态: %s`,
		result.SnapshotPath,
		result.FileSize,
		result.Hash,
		result.Revision,
		result.TotalKeys,
		result.TotalSize,
		result.Duration,
		map[bool]string{true: "有效", false: "无效"}[result.Valid])
}

// CompareSnapshots 比较两个快照的状态
func CompareSnapshots(result1, result2 *VerifyResult) string {
	if result1 == nil || result2 == nil {
		return "无法比较：验证结果为空"
	}

	comparison := fmt.Sprintf(`快照比较结果:
快照1: %s
快照2: %s

哈希值: %x vs %x (%s)
版本号: %d vs %d (%s)
键数量: %d vs %d (%s)
数据大小: %d vs %d (%s)`,
		result1.SnapshotPath, result2.SnapshotPath,
		result1.Hash, result2.Hash, compareStatus(result1.Hash == result2.Hash),
		result1.Revision, result2.Revision, compareStatus(result1.Revision == result2.Revision),
		result1.TotalKeys, result2.TotalKeys, compareStatus(result1.TotalKeys == result2.TotalKeys),
		result1.TotalSize, result2.TotalSize, compareStatus(result1.TotalSize == result2.TotalSize))

	return comparison
}

// compareStatus 比较状态辅助函数
func compareStatus(same bool) string {
	if same {
		return "相同"
	}
	return "不同"
}
