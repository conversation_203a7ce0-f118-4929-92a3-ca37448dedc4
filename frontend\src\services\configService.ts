import { apiClient } from './api';

/**
 * 配置管理服务类
 * 
 * 提供系统配置相关的API操作：
 * - 配置查询和更新
 * - 配置历史管理
 * - 配置验证
 * - 配置重载
 */
export class ConfigService {
  private readonly basePath = '/config';

  // ============ 配置管理 ============

  /**
   * 获取完整配置
   * @returns 完整配置对象
   */
  async getConfig(): Promise<Record<string, any>> {
    return apiClient.get<Record<string, any>>(`${this.basePath}`);
  }

  /**
   * 获取指定配置段
   * @param section 配置段名称
   * @returns 配置段内容
   */
  async getConfigSection(section: string): Promise<Record<string, any>> {
    return apiClient.get<Record<string, any>>(`${this.basePath}/${encodeURIComponent(section)}`);
  }

  /**
   * 更新配置段
   * @param section 配置段名称
   * @param config 配置内容
   * @returns 更新结果
   */
  async updateConfigSection(section: string, config: Record<string, any>): Promise<{ success: boolean; message: string }> {
    return apiClient.put<{ success: boolean; message: string }>(
      `${this.basePath}/${encodeURIComponent(section)}`,
      config
    );
  }

  /**
   * 重载配置
   * @returns 重载结果
   */
  async reloadConfig(): Promise<{ success: boolean; message: string }> {
    return apiClient.post<{ success: boolean; message: string }>(`${this.basePath}/reload`);
  }

  /**
   * 获取配置历史
   * @returns 配置历史列表
   */
  async getConfigHistory(): Promise<ConfigHistoryItem[]> {
    return apiClient.get<ConfigHistoryItem[]>(`${this.basePath}/history`);
  }

  /**
   * 恢复配置到指定版本
   * @param id 历史记录ID
   * @returns 恢复结果
   */
  async restoreConfig(id: string): Promise<{ success: boolean; message: string }> {
    return apiClient.post<{ success: boolean; message: string }>(`${this.basePath}/restore/${encodeURIComponent(id)}`);
  }

  /**
   * 验证配置
   * @param config 配置内容
   * @returns 验证结果
   */
  async validateConfig(config: Record<string, any>): Promise<ConfigValidationResult> {
    return apiClient.post<ConfigValidationResult>(`${this.basePath}/validate`, config);
  }
}

// 配置历史项类型
export interface ConfigHistoryItem {
  id: string;
  timestamp: string;
  user: string;
  section: string;
  action: 'create' | 'update' | 'delete';
  changes: Record<string, any>;
  description?: string;
}

// 配置验证结果类型
export interface ConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// 创建并导出配置服务实例
export const configService = new ConfigService();

export default configService;
