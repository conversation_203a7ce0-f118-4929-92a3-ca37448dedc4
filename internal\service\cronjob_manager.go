package service

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
	"k8s-helper/pkg/common"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// CronJobManager CronJob管理器
type CronJobManager struct {
	logger        *zap.Logger
	clientManager *ClientManager
	tzConverter   domain.TimeZoneConverter
	errorHandler  *ErrorHandler
}

// NewCronJobManager 创建CronJob管理器
func NewCronJobManager(
	logger *zap.Logger,
	clientManager *ClientManager,
	tzConverter domain.TimeZoneConverter,
) *CronJobManager {
	return &CronJobManager{
		logger:        logger,
		clientManager: client<PERSON>anager,
		tzConverter:   tzConverter,
		errorHandler:  NewError<PERSON>andler(logger),
	}
}

// CreateBackupCronJob 创建备份CronJob
func (cm *CronJobManager) CreateBackupCronJob(ctx context.Context, opts *domain.CronJobOptions) error {
	cm.logger.Info("开始创建备份CronJob",
		zap.String("name", opts.Name),
		zap.String("namespace", opts.Namespace),
		zap.String("schedule", opts.Schedule))

	// 获取Kubernetes客户端
	k8sClient, err := cm.clientManager.GetKubernetesClient()
	if err != nil {
		return cm.errorHandler.WrapError("get_k8s_client", err)
	}

	// 检查CronJob是否已存在
	existing, err := k8sClient.BatchV1().CronJobs(opts.Namespace).Get(ctx, opts.Name, metav1.GetOptions{})
	if err == nil {
		cm.logger.Warn("CronJob已存在，将更新",
			zap.String("name", opts.Name),
			zap.String("namespace", opts.Namespace))
		return cm.updateCronJob(ctx, k8sClient, existing, opts)
	}

	// 创建新的CronJob
	cronJob := cm.buildBackupCronJob(opts)

	_, err = k8sClient.BatchV1().CronJobs(opts.Namespace).Create(ctx, cronJob, metav1.CreateOptions{})
	if err != nil {
		return cm.errorHandler.WrapError("create_cronjob", err)
	}

	cm.logger.Info("备份CronJob创建成功",
		zap.String("name", opts.Name),
		zap.String("namespace", opts.Namespace))

	return nil
}

// buildBackupCronJob 构建备份CronJob
func (cm *CronJobManager) buildBackupCronJob(opts *domain.CronJobOptions) *batchv1.CronJob {
	// 转换时区（如果需要）
	schedule := opts.Schedule
	if opts.LocalTime {
		if result, err := cm.tzConverter.ConvertCronScheduleToUTC(opts.Schedule, opts.TimeZone); err == nil {
			convertedSchedule := result.ConvertedSchedule
			schedule = convertedSchedule
			cm.logger.Info("时区转换完成",
				zap.String("original", opts.Schedule),
				zap.String("converted", schedule),
				zap.String("timezone", opts.TimeZone))
		} else {
			cm.logger.Warn("时区转换失败，使用原始调度", zap.Error(err))
		}
	}

	// 构建Pod模板
	podTemplate := cm.buildBackupPodTemplate(opts)

	// 构建CronJob
	cronJob := &batchv1.CronJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      opts.Name,
			Namespace: opts.Namespace,
			Labels: map[string]string{
				"app":        "k8s-helper",
				"component":  "etcd-backup",
				"managed-by": "k8s-helper-cronjob",
			},
			Annotations: map[string]string{
				"k8s-helper/original-schedule": opts.Schedule,
				"k8s-helper/timezone":          opts.TimeZone,
				"k8s-helper/local-time":        fmt.Sprintf("%t", opts.LocalTime),
			},
		},
		Spec: batchv1.CronJobSpec{
			Schedule:                   schedule,
			ConcurrencyPolicy:          batchv1.ForbidConcurrent,
			SuccessfulJobsHistoryLimit: &[]int32{3}[0],
			FailedJobsHistoryLimit:     &[]int32{1}[0],
			JobTemplate: batchv1.JobTemplateSpec{
				Spec: batchv1.JobSpec{
					Template: podTemplate,
				},
			},
		},
	}

	// 如果Kubernetes版本支持，添加时区字段
	if cm.supportsTimeZone() && opts.LocalTime {
		cronJob.Spec.TimeZone = &opts.TimeZone
	}

	return cronJob
}

// buildBackupPodTemplate 构建备份Pod模板
func (cm *CronJobManager) buildBackupPodTemplate(opts *domain.CronJobOptions) corev1.PodTemplateSpec {
	// 构建命令参数
	args := []string{
		"etcd", "backup",
		"--output", opts.BackupPath,
		"--servers", opts.ETCDConfig.Servers,
	}

	// 添加证书参数
	if opts.ETCDConfig.CertFile != "" {
		args = append(args, "--cert", opts.ETCDConfig.CertFile)
	}
	if opts.ETCDConfig.KeyFile != "" {
		args = append(args, "--key", opts.ETCDConfig.KeyFile)
	}
	if opts.ETCDConfig.CaFile != "" {
		args = append(args, "--ca", opts.ETCDConfig.CaFile)
	}

	// 构建卷挂载
	volumes, volumeMounts := cm.buildVolumes(opts)

	return corev1.PodTemplateSpec{
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{
				"app":       "k8s-helper",
				"component": "etcd-backup",
			},
		},
		Spec: corev1.PodSpec{
			RestartPolicy: corev1.RestartPolicyOnFailure,
			Containers: []corev1.Container{
				{
					Name:            "k8s-helper",
					Image:           opts.Image,
					ImagePullPolicy: corev1.PullIfNotPresent,
					Command:         []string{"/usr/local/bin/k8s-helper"},
					Args:            args,
					VolumeMounts:    volumeMounts,
					Env: []corev1.EnvVar{
						{
							Name:  "LOG_LEVEL",
							Value: "info",
						},
					},
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse("100m"),
							corev1.ResourceMemory: resource.MustParse("128Mi"),
						},
						Limits: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse("500m"),
							corev1.ResourceMemory: resource.MustParse("512Mi"),
						},
					},
				},
			},
			Volumes: volumes,
		},
	}
}

// buildVolumes 构建卷和卷挂载
func (cm *CronJobManager) buildVolumes(opts *domain.CronJobOptions) ([]corev1.Volume, []corev1.VolumeMount) {
	var volumes []corev1.Volume
	var volumeMounts []corev1.VolumeMount

	// 备份输出卷
	volumes = append(volumes, corev1.Volume{
		Name: "backup-storage",
		VolumeSource: corev1.VolumeSource{
			PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
				ClaimName: opts.BackupPVCName,
			},
		},
	})
	volumeMounts = append(volumeMounts, corev1.VolumeMount{
		Name:      "backup-storage",
		MountPath: "/backup",
	})

	// ETCD证书卷
	if opts.ETCDCertSecretName != "" {
		volumes = append(volumes, corev1.Volume{
			Name: "etcd-certs",
			VolumeSource: corev1.VolumeSource{
				Secret: &corev1.SecretVolumeSource{
					SecretName: opts.ETCDCertSecretName,
				},
			},
		})
		volumeMounts = append(volumeMounts, corev1.VolumeMount{
			Name:      "etcd-certs",
			MountPath: "/etc/etcd/certs",
			ReadOnly:  true,
		})
	}

	return volumes, volumeMounts
}

// updateCronJob 更新现有CronJob
func (cm *CronJobManager) updateCronJob(ctx context.Context, k8sClient kubernetes.Interface, existing *batchv1.CronJob, opts *domain.CronJobOptions) error {
	// 更新CronJob规格
	newCronJob := cm.buildBackupCronJob(opts)
	existing.Spec = newCronJob.Spec
	existing.ObjectMeta.Labels = newCronJob.ObjectMeta.Labels
	existing.ObjectMeta.Annotations = newCronJob.ObjectMeta.Annotations

	_, err := k8sClient.BatchV1().CronJobs(opts.Namespace).Update(ctx, existing, metav1.UpdateOptions{})
	if err != nil {
		return cm.errorHandler.WrapError("update_cronjob", err)
	}

	cm.logger.Info("CronJob更新成功",
		zap.String("name", opts.Name),
		zap.String("namespace", opts.Namespace))

	return nil
}

// DeleteCronJob 删除CronJob
func (cm *CronJobManager) DeleteCronJob(ctx context.Context, namespace, name string) error {
	k8sClient, err := cm.clientManager.GetKubernetesClient()
	if err != nil {
		return cm.errorHandler.WrapError("get_k8s_client", err)
	}

	err = k8sClient.BatchV1().CronJobs(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return cm.errorHandler.WrapError("delete_cronjob", err)
	}

	cm.logger.Info("CronJob删除成功",
		zap.String("name", name),
		zap.String("namespace", namespace))

	return nil
}

// ListCronJobs 列出CronJob
func (cm *CronJobManager) ListCronJobs(ctx context.Context, namespace string) (*batchv1.CronJobList, error) {
	k8sClient, err := cm.clientManager.GetKubernetesClient()
	if err != nil {
		return nil, cm.errorHandler.WrapError("get_k8s_client", err)
	}

	cronJobs, err := k8sClient.BatchV1().CronJobs(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: "managed-by=k8s-helper-cronjob",
	})
	if err != nil {
		return nil, cm.errorHandler.WrapError("list_cronjobs", err)
	}

	return cronJobs, nil
}

// supportsTimeZone 检查Kubernetes版本是否支持时区字段
func (cm *CronJobManager) supportsTimeZone() bool {
	// 获取Kubernetes客户端
	k8sClient, err := cm.clientManager.GetKubernetesClient()
	if err != nil {
		cm.logger.Warn("无法获取Kubernetes客户端，假设不支持时区字段", zap.Error(err))
		return false
	}

	// 获取服务器版本
	version, err := k8sClient.Discovery().ServerVersion()
	if err != nil {
		cm.logger.Warn("无法获取Kubernetes服务器版本，假设不支持时区字段", zap.Error(err))
		return false
	}

	// 解析版本号
	major, minor, err := cm.parseKubernetesVersion(version.Major, version.Minor)
	if err != nil {
		cm.logger.Warn("无法解析Kubernetes版本号，假设不支持时区字段",
			zap.String("major", version.Major),
			zap.String("minor", version.Minor),
			zap.Error(err))
		return false
	}

	// 检查是否支持时区字段（Kubernetes 1.24+）
	supported := (major > common.TimeZoneSupportMajor) ||
		(major == common.TimeZoneSupportMajor && minor >= common.TimeZoneSupportMinor)

	cm.logger.Debug("Kubernetes版本时区支持检查",
		zap.Int("major", major),
		zap.Int("minor", minor),
		zap.Bool("supported", supported),
		zap.String("gitVersion", version.GitVersion))

	return supported
}

// parseKubernetesVersion 解析Kubernetes版本号
func (cm *CronJobManager) parseKubernetesVersion(majorStr, minorStr string) (int, int, error) {
	// 解析主版本号
	major, err := strconv.Atoi(majorStr)
	if err != nil {
		return 0, 0, fmt.Errorf("解析主版本号失败: %w", err)
	}

	// 解析次版本号（去除可能的后缀，如 "24+"）
	minorClean := strings.TrimSuffix(minorStr, "+")
	minor, err := strconv.Atoi(minorClean)
	if err != nil {
		return 0, 0, fmt.Errorf("解析次版本号失败: %w", err)
	}

	return major, minor, nil
}

// SuspendCronJob 暂停CronJob
func (cm *CronJobManager) SuspendCronJob(ctx context.Context, namespace, name string) error {
	cm.logger.Info("暂停CronJob",
		zap.String("name", name),
		zap.String("namespace", namespace))

	// 获取Kubernetes客户端
	k8sClient, err := cm.clientManager.GetKubernetesClient()
	if err != nil {
		return cm.errorHandler.WrapError("get_k8s_client", err)
	}

	// 获取现有的CronJob
	cronJob, err := k8sClient.BatchV1().CronJobs(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return cm.errorHandler.WrapError("get_cronjob", err)
	}

	// 检查是否已经暂停
	if cronJob.Spec.Suspend != nil && *cronJob.Spec.Suspend {
		cm.logger.Info("CronJob已经处于暂停状态",
			zap.String("name", name),
			zap.String("namespace", namespace))
		return nil
	}

	// 设置暂停状态
	suspend := true
	cronJob.Spec.Suspend = &suspend

	// 更新CronJob
	_, err = k8sClient.BatchV1().CronJobs(namespace).Update(ctx, cronJob, metav1.UpdateOptions{})
	if err != nil {
		return cm.errorHandler.WrapError("suspend_cronjob", err)
	}

	cm.logger.Info("CronJob暂停成功",
		zap.String("name", name),
		zap.String("namespace", namespace))

	return nil
}

// ResumeCronJob 恢复CronJob
func (cm *CronJobManager) ResumeCronJob(ctx context.Context, namespace, name string) error {
	cm.logger.Info("恢复CronJob",
		zap.String("name", name),
		zap.String("namespace", namespace))

	// 获取Kubernetes客户端
	k8sClient, err := cm.clientManager.GetKubernetesClient()
	if err != nil {
		return cm.errorHandler.WrapError("get_k8s_client", err)
	}

	// 获取现有的CronJob
	cronJob, err := k8sClient.BatchV1().CronJobs(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return cm.errorHandler.WrapError("get_cronjob", err)
	}

	// 检查是否已经运行
	if cronJob.Spec.Suspend == nil || !*cronJob.Spec.Suspend {
		cm.logger.Info("CronJob已经处于运行状态",
			zap.String("name", name),
			zap.String("namespace", namespace))
		return nil
	}

	// 设置运行状态
	suspend := false
	cronJob.Spec.Suspend = &suspend

	// 更新CronJob
	_, err = k8sClient.BatchV1().CronJobs(namespace).Update(ctx, cronJob, metav1.UpdateOptions{})
	if err != nil {
		return cm.errorHandler.WrapError("resume_cronjob", err)
	}

	cm.logger.Info("CronJob恢复成功",
		zap.String("name", name),
		zap.String("namespace", namespace))

	return nil
}
