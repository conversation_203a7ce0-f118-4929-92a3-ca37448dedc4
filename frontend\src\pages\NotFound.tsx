import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { ROUTES } from '@/utils/constants';

/**
 * 404页面组件
 * 
 * 当用户访问不存在的路由时显示此页面
 * 
 * 功能特性：
 * - 友好的404错误提示
 * - 返回首页按钮
 * - 返回上一页按钮
 * - 响应式设计
 */
const NotFound: React.FC = () => {
  const navigate = useNavigate();

  // 返回首页
  const handleGoHome = () => {
    navigate(ROUTES.DASHBOARD);
  };

  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
      background: '#f5f7fa',
    }}>
      <Result
        status="404"
        title="404"
        subTitle="抱歉，您访问的页面不存在。"
        extra={[
          <Button 
            type="primary" 
            key="home"
            icon={<HomeOutlined />}
            onClick={handleGoHome}
          >
            返回首页
          </Button>,
          <Button 
            key="back"
            icon={<ArrowLeftOutlined />}
            onClick={handleGoBack}
          >
            返回上一页
          </Button>,
        ]}
      />
    </div>
  );
};

export default NotFound;
