package web

import (
	"fmt"
	"net/http"
	"runtime/debug"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"k8s-helper/internal/service"
)

// ErrorResponse 统一错误响应格式
type ErrorResponse struct {
	Error       string      `json:"error"`
	Code        string      `json:"code,omitempty"`
	Message     string      `json:"message,omitempty"`
	RequestID   string      `json:"request_id,omitempty"`
	Timestamp   time.Time   `json:"timestamp"`
	Path        string      `json:"path"`
	Method      string      `json:"method"`
	Recoverable bool        `json:"recoverable,omitempty"`
	Suggestions []string    `json:"suggestions,omitempty"`
	Details     interface{} `json:"details,omitempty"`
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}
		
		c.Set("request_id", requestID)
		c.<PERSON><PERSON>("X-Request-ID", requestID)
		c.Next()
	}
}

// ErrorHandlerMiddleware Web错误处理中间件
func ErrorHandlerMiddleware(logger *zap.Logger, errorHandler *service.ErrorHandler) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录panic信息
				stack := debug.Stack()
				requestID := getRequestID(c)
				
				logger.Error("Web服务发生panic",
					zap.String("request_id", requestID),
					zap.String("method", c.Request.Method),
					zap.String("path", c.Request.URL.Path),
					zap.Any("error", err),
					zap.String("stack", string(stack)))

				// 如果有错误处理器，使用它来处理错误
				if errorHandler != nil {
					if serviceErr, ok := err.(*service.ServiceError); ok {
						errorHandler.LogError(serviceErr)
						logger.Info("错误已记录到错误处理器",
							zap.String("request_id", requestID),
							zap.String("error_type", string(serviceErr.Type)),
							zap.Bool("recoverable", serviceErr.Recoverable),
							zap.Strings("suggestions", serviceErr.Suggestions))
					}
				}

				// 返回统一的错误响应
				errorResp := ErrorResponse{
					Error:     "Internal Server Error",
					Code:      "INTERNAL_ERROR",
					Message:   "服务器内部错误，请稍后重试",
					RequestID: requestID,
					Timestamp: time.Now(),
					Path:      c.Request.URL.Path,
					Method:    c.Request.Method,
				}

				c.JSON(http.StatusInternalServerError, errorResp)
				c.Abort()
			}
		}()

		c.Next()

		// 处理业务逻辑中的错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			requestID := getRequestID(c)
			
			logger.Error("Web请求处理错误",
				zap.String("request_id", requestID),
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.Error(err))

			// 如果响应还没有写入，返回错误响应
			if !c.Writer.Written() {
				errorResp := ErrorResponse{
					Error:     err.Error(),
					Code:      "REQUEST_ERROR",
					Message:   "请求处理失败",
					RequestID: requestID,
					Timestamp: time.Now(),
					Path:      c.Request.URL.Path,
					Method:    c.Request.Method,
				}

				status := http.StatusInternalServerError
				if c.Writer.Status() != 200 {
					status = c.Writer.Status()
				}

				c.JSON(status, errorResp)
			}
		}
	}
}

// AccessLogMiddleware 访问日志中间件
func AccessLogMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		requestID := getRequestID(c)
		
		// 记录请求开始
		logger.Info("Web请求开始",
			zap.String("request_id", requestID),
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("query", c.Request.URL.RawQuery),
			zap.String("user_agent", c.Request.UserAgent()),
			zap.String("remote_addr", c.ClientIP()))

		c.Next()

		// 记录请求结束
		duration := time.Since(start)
		status := c.Writer.Status()
		
		logLevel := zap.InfoLevel
		if status >= 400 {
			logLevel = zap.WarnLevel
		}
		if status >= 500 {
			logLevel = zap.ErrorLevel
		}

		logger.Log(logLevel, "Web请求完成",
			zap.String("request_id", requestID),
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.Int("status", status),
			zap.Duration("duration", duration),
			zap.Int("response_size", c.Writer.Size()))
	}
}

// AuditLogMiddleware 审计日志中间件
func AuditLogMiddleware(logger *zap.Logger, auditLogger *service.AuditLogger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只对重要操作进行审计
		if shouldAudit(c.Request.Method, c.Request.URL.Path) {
			requestID := getRequestID(c)
			
			// 记录操作前状态
			auditData := map[string]interface{}{
				"request_id":  requestID,
				"method":      c.Request.Method,
				"path":        c.Request.URL.Path,
				"query":       c.Request.URL.RawQuery,
				"user_agent":  c.Request.UserAgent(),
				"remote_addr": c.ClientIP(),
				"timestamp":   time.Now(),
			}

			c.Next()

			// 记录操作结果
			auditData["status"] = c.Writer.Status()
			auditData["duration"] = time.Since(auditData["timestamp"].(time.Time))

			if auditLogger != nil {
				ctx := c.Request.Context()
				auditLogger.LogAPIOperation(ctx,
					auditData["method"].(string),
					auditData["path"].(string),
					auditData["status"].(int),
					auditData["duration"].(time.Duration),
					auditData["remote_addr"].(string),
					auditData["user_agent"].(string),
					nil)
			} else {
				logger.Info("审计日志",
					zap.String("operation", "web_api"),
					zap.Any("data", auditData))
			}
		} else {
			c.Next()
		}
	}
}

// PerformanceLogMiddleware 性能日志中间件
func PerformanceLogMiddleware(logger *zap.Logger, performanceMonitor *service.PerformanceMonitor) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		requestID := getRequestID(c)
		operation := fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path)

		c.Next()

		duration := time.Since(start)
		status := c.Writer.Status()

		// 记录到性能监控器
		if performanceMonitor != nil {
			success := status < 400
			performanceMonitor.RecordOperation(operation, duration, success)
		}

		// 记录慢请求
		if duration > 1*time.Second {
			logger.Warn("慢请求检测",
				zap.String("request_id", requestID),
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.Duration("duration", duration),
				zap.Int("status", status))
		}
	}
}

// 辅助函数

// generateRequestID 生成请求ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// getRequestID 获取请求ID
func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return "unknown"
}

// shouldAudit 判断是否需要审计
func shouldAudit(method, path string) bool {
	// 审计所有非GET请求
	if method != "GET" {
		return true
	}
	
	// 审计敏感的GET请求
	sensitiveEndpoints := []string{
		"/api/v1/etcd/backups",
		"/api/v1/cluster/info",
		"/api/v1/monitoring",
	}
	
	for _, endpoint := range sensitiveEndpoints {
		if len(path) >= len(endpoint) && path[:len(endpoint)] == endpoint {
			return true
		}
	}
	
	return false
}

// HandleServiceError 处理服务错误
func HandleServiceError(c *gin.Context, err error, logger *zap.Logger) {
	requestID := getRequestID(c)
	
	logger.Error("服务错误",
		zap.String("request_id", requestID),
		zap.String("method", c.Request.Method),
		zap.String("path", c.Request.URL.Path),
		zap.Error(err))

	var errorResp ErrorResponse
	var status int

	// 根据错误类型设置不同的响应
	if serviceErr, ok := err.(*service.ServiceError); ok {
		switch serviceErr.Type {
		case "validation":
			status = http.StatusBadRequest
			errorResp.Code = "VALIDATION_ERROR"
			errorResp.Message = "请求参数验证失败"
		case "not_found":
			status = http.StatusNotFound
			errorResp.Code = "NOT_FOUND"
			errorResp.Message = "请求的资源不存在"
		case "permission":
			status = http.StatusForbidden
			errorResp.Code = "PERMISSION_DENIED"
			errorResp.Message = "权限不足"
		case "timeout":
			status = http.StatusRequestTimeout
			errorResp.Code = "TIMEOUT"
			errorResp.Message = "请求超时"
		case "service_unavailable":
			status = http.StatusServiceUnavailable
			errorResp.Code = "SERVICE_UNAVAILABLE"
			errorResp.Message = "服务暂时不可用"
		default:
			status = http.StatusInternalServerError
			errorResp.Code = "SERVICE_ERROR"
			errorResp.Message = "服务处理失败"
		}

		// 添加服务错误的详细信息
		errorResp.Recoverable = serviceErr.Recoverable
		errorResp.Suggestions = serviceErr.Suggestions
		if serviceErr.Operation != "" {
			errorResp.Details = map[string]interface{}{
				"operation": serviceErr.Operation,
				"cause":     serviceErr.Cause,
			}
		}
	} else {
		status = http.StatusInternalServerError
		errorResp.Code = "INTERNAL_ERROR"
		errorResp.Message = "服务器内部错误"
	}

	errorResp.Error = err.Error()
	errorResp.RequestID = requestID
	errorResp.Timestamp = time.Now()
	errorResp.Path = c.Request.URL.Path
	errorResp.Method = c.Request.Method

	c.JSON(status, errorResp)
}
