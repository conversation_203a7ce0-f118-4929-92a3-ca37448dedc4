package web

import "time"

// PageData 页面基础数据
type PageData struct {
	Title                string    `json:"title"`
	Description          string    `json:"description"`
	AppName              string    `json:"app_name"`
	Subtitle             string    `json:"subtitle"`
	Version              string    `json:"version"`
	BuildTime            string    `json:"build_time"`
	Year                 string    `json:"year"`
	ShowConnectionStatus bool      `json:"show_connection_status"`
	Tabs                 []TabData `json:"tabs"`

	// 新增：侧边栏布局相关
	UseSidebar    bool   `json:"use_sidebar"`
	PageTitle     string `json:"page_title,omitempty"`
	ShowUserInfo  bool   `json:"show_user_info,omitempty"`
	UserName      string `json:"user_name,omitempty"`

	// API文档相关
	APIVersion     string `json:"api_version,omitempty"`
	APIDescription string `json:"api_description,omitempty"`
}

// TabData 标签页数据
type TabData struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Icon   string `json:"icon"`
	Active bool   `json:"active"`
}

// DashboardData Dashboard页面数据
type DashboardData struct {
	SystemHealth    string                 `json:"system_health"`
	SystemMetrics   map[string]interface{} `json:"system_metrics"`
	WSConnections   int                    `json:"ws_connections"`
	BackupCount     int                    `json:"backup_count"`
	LastUpdateTime  time.Time              `json:"last_update_time"`
}

// ETCDData ETCD管理数据
type ETCDData struct {
	BackupFiles    []BackupFile `json:"backup_files"`
	DefaultPath    string       `json:"default_path"`
	DefaultDataDir string       `json:"default_data_dir"`
}

// BackupFile 备份文件信息
type BackupFile struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	Path      string    `json:"path"`
	Size      int64     `json:"size"`
	CreatedAt time.Time `json:"created_at"`
	Status    string    `json:"status"`
}

// ClusterData 集群信息数据
type ClusterData struct {
	Version     string                 `json:"version"`
	Nodes       int                    `json:"nodes"`
	Namespaces  int                    `json:"namespaces"`
	Pods        int                    `json:"pods"`
	Services    int                    `json:"services"`
	Details     map[string]interface{} `json:"details"`
	LastUpdated time.Time              `json:"last_updated"`
}

// LogsData 日志页面数据
type LogsData struct {
	DefaultNamespace string `json:"default_namespace"`
	DefaultPod       string `json:"default_pod"`
	DefaultContainer string `json:"default_container"`
	WSEndpoint       string `json:"ws_endpoint"`
}

// NewUIPageData 创建UI页面数据
func NewUIPageData() *PageData {
	return &PageData{
		Title:                "主界面",
		Description:          "Kubernetes 集群管理主界面",
		AppName:              "K8s-Helper",
		Subtitle:             "Kubernetes 集群管理助手",
		Version:              "1.0.0",
		BuildTime:            "2025-01-01",
		Year:                 "2025",
		ShowConnectionStatus: true,
		Tabs: []TabData{
			{ID: "dashboard", Name: "Dashboard", Icon: "📊", Active: true},
			{ID: "etcd", Name: "ETCD", Icon: "💾", Active: false},
			{ID: "info", Name: "集群信息", Icon: "🔍", Active: false},
			{ID: "logs", Name: "实时日志", Icon: "📋", Active: false},
		},
	}
}

// NewAPIDocsPageData 创建API文档页面数据
func NewAPIDocsPageData() *PageData {
	return &PageData{
		Title:          "API文档",
		Description:    "K8s-Helper RESTful API 接口文档",
		AppName:        "K8s-Helper",
		Subtitle:       "API Documentation",
		Version:        "1.0.0",
		BuildTime:      "2025-01-01",
		Year:           "2025",
		APIVersion:     "1.0.0",
		APIDescription: "K8s-Helper Kubernetes 集群管理工具 API",
	}
}

// NewIndexPageData 创建首页数据
func NewIndexPageData() *PageData {
	return &PageData{
		Title:       "首页",
		Description: "K8s-Helper Kubernetes 集群管理工具",
		AppName:     "K8s-Helper",
		Subtitle:    "Kubernetes 集群管理助手",
		Version:     "1.0.0",
		BuildTime:   "2025-01-01",
		Year:        "2025",
	}
}

// NewMainInterfaceData 创建主界面数据
func NewMainInterfaceData() *PageData {
	return &PageData{
		Title:                "主界面",
		Description:          "K8s-Helper Kubernetes 集群管理主界面",
		AppName:              "K8s-Helper",
		Subtitle:             "Kubernetes 集群管理助手",
		Version:              "1.0.0",
		BuildTime:            "2025-01-01",
		Year:                 "2025",
		UseSidebar:           true,
		PageTitle:            "Dashboard",
		ShowConnectionStatus: true,
		ShowUserInfo:         true,
		UserName:             "Admin",
	}
}
