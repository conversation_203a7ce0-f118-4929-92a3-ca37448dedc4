import { useQuery } from '@tanstack/react-query';
import { clusterService, monitoringService, etcdService } from '@/services';

/**
 * Dashboard数据类型定义
 */
export interface DashboardData {
  // 集群基本信息
  cluster: {
    nodes: number;
    readyNodes: number;
    pods: {
      running: number;
      total: number;
      failed: number;
      pending: number;
    };
    services: number;
    namespaces: number;
  };
  
  // 系统指标
  metrics: {
    cpu: number;
    memory: number;
    disk: number;
    networkIO: string;
  };
  
  // 应用指标
  application: {
    activeConnections: number;
    errorRate: string;
    requestsPerSecond: number;
    responseTime: string;
  };
  
  // ETCD状态
  etcd: {
    status: 'available' | 'unavailable';
    healthy: boolean;
  };
  
  // 最近活动（从监控数据推导）
  recentActivities: Array<{
    id: string;
    type: string;
    title: string;
    description: string;
    time: string;
    status: 'success' | 'warning' | 'error' | 'info';
  }>;
}

/**
 * 获取Dashboard所需的所有数据
 */
export const useDashboardData = () => {
  return useQuery({
    queryKey: ['dashboard', 'overview'],
    queryFn: async (): Promise<DashboardData> => {
      // 并行获取所有数据
      const [clusterInfo, metrics, etcdStatus] = await Promise.all([
        clusterService.getClusterInfo(),
        monitoringService.getSystemMetrics(),
        etcdService.getStatus().catch(() => ({ status: 'unavailable' }))
      ]);

      // 转换集群数据
      const cluster = {
        nodes: clusterInfo.resources.total_nodes,
        readyNodes: clusterInfo.resources.ready_nodes,
        pods: {
          running: clusterInfo.resources.running_pods,
          total: clusterInfo.resources.total_pods,
          failed: metrics.metrics.kubernetes.pods_failed || 0,
          pending: metrics.metrics.kubernetes.pods_pending || 0,
        },
        services: clusterInfo.resources.total_services,
        namespaces: clusterInfo.resources.total_namespaces,
      };

      // 转换系统指标
      const systemMetrics = {
        cpu: parseFloat(metrics.metrics.system.cpu_usage.replace('%', '')),
        memory: parseFloat(metrics.metrics.system.memory_usage.replace('%', '')),
        disk: parseFloat(metrics.metrics.system.disk_usage.replace('%', '')),
        networkIO: metrics.metrics.system.network_io,
      };

      // 转换应用指标
      const application = {
        activeConnections: metrics.metrics.application.active_connections,
        errorRate: metrics.metrics.application.error_rate,
        requestsPerSecond: metrics.metrics.application.requests_per_second,
        responseTime: metrics.metrics.application.response_time,
      };

      // 转换ETCD状态
      const etcd = {
        status: etcdStatus.status as 'available' | 'unavailable',
        healthy: etcdStatus.status === 'available',
      };

      // 生成最近活动（基于当前数据状态）
      const recentActivities = generateRecentActivities(cluster, systemMetrics, etcd);

      return {
        cluster,
        metrics: systemMetrics,
        application,
        etcd,
        recentActivities,
      };
    },
    refetchInterval: 30000, // 30秒自动刷新
    staleTime: 10000, // 10秒内认为数据是新鲜的
  });
};

/**
 * 基于当前系统状态生成最近活动
 */
function generateRecentActivities(
  cluster: DashboardData['cluster'],
  metrics: DashboardData['metrics'],
  etcd: DashboardData['etcd']
): DashboardData['recentActivities'] {
  const activities: DashboardData['recentActivities'] = [];
  const now = new Date();

  // ETCD状态活动
  if (etcd.healthy) {
    activities.push({
      id: 'etcd-healthy',
      type: 'etcd',
      title: 'ETCD运行正常',
      description: 'ETCD集群状态健康',
      time: formatTimeAgo(new Date(now.getTime() - 2 * 60 * 1000)), // 2分钟前
      status: 'success',
    });
  } else {
    activities.push({
      id: 'etcd-unhealthy',
      type: 'etcd',
      title: 'ETCD状态异常',
      description: 'ETCD集群连接失败',
      time: formatTimeAgo(new Date(now.getTime() - 1 * 60 * 1000)), // 1分钟前
      status: 'error',
    });
  }

  // Pod状态活动
  if (cluster.pods.running === cluster.pods.total) {
    activities.push({
      id: 'pods-healthy',
      type: 'pod',
      title: 'Pod运行正常',
      description: `所有${cluster.pods.total}个Pod运行正常`,
      time: formatTimeAgo(new Date(now.getTime() - 5 * 60 * 1000)), // 5分钟前
      status: 'success',
    });
  } else if (cluster.pods.failed > 0) {
    activities.push({
      id: 'pods-failed',
      type: 'pod',
      title: 'Pod运行异常',
      description: `${cluster.pods.failed}个Pod运行失败`,
      time: formatTimeAgo(new Date(now.getTime() - 3 * 60 * 1000)), // 3分钟前
      status: 'error',
    });
  }

  // 系统资源告警
  if (metrics.memory > 85) {
    activities.push({
      id: 'memory-warning',
      type: 'alert',
      title: '内存使用率告警',
      description: `内存使用率达到${metrics.memory.toFixed(1)}%`,
      time: formatTimeAgo(new Date(now.getTime() - 10 * 60 * 1000)), // 10分钟前
      status: 'warning',
    });
  }

  if (metrics.cpu > 80) {
    activities.push({
      id: 'cpu-warning',
      type: 'alert',
      title: 'CPU使用率告警',
      description: `CPU使用率达到${metrics.cpu.toFixed(1)}%`,
      time: formatTimeAgo(new Date(now.getTime() - 8 * 60 * 1000)), // 8分钟前
      status: 'warning',
    });
  }

  // 集群节点状态
  if (cluster.readyNodes === cluster.nodes) {
    activities.push({
      id: 'nodes-ready',
      type: 'cluster',
      title: '集群节点正常',
      description: `${cluster.nodes}个节点全部就绪`,
      time: formatTimeAgo(new Date(now.getTime() - 15 * 60 * 1000)), // 15分钟前
      status: 'info',
    });
  }

  return activities.slice(0, 4); // 只返回最近4条活动
}

/**
 * 格式化时间为"X分钟前"的形式
 */
function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  
  if (diffMins < 1) return '刚刚';
  if (diffMins < 60) return `${diffMins}分钟前`;
  
  const diffHours = Math.floor(diffMins / 60);
  if (diffHours < 24) return `${diffHours}小时前`;
  
  const diffDays = Math.floor(diffHours / 24);
  return `${diffDays}天前`;
}

/**
 * 获取集群健康状态
 */
export const useClusterHealth = () => {
  return useQuery({
    queryKey: ['cluster', 'health'],
    queryFn: async () => {
      const [clusterInfo, metrics] = await Promise.all([
        clusterService.getClusterInfo(),
        monitoringService.getSystemMetrics(),
      ]);

      const healthScore = calculateHealthScore(clusterInfo, metrics);
      
      return {
        overall: healthScore > 80 ? 'healthy' : healthScore > 60 ? 'warning' : 'critical',
        score: healthScore,
        components: [
          {
            name: 'API Server',
            status: 'healthy' as const,
            message: '运行正常',
          },
          {
            name: 'ETCD',
            status: 'healthy' as const,
            message: '集群状态健康',
          },
          {
            name: 'Controller Manager',
            status: 'healthy' as const,
            message: '控制器正常',
          },
          {
            name: 'Scheduler',
            status: 'healthy' as const,
            message: '调度器正常',
          },
        ],
      };
    },
    refetchInterval: 60000, // 1分钟刷新
  });
};

/**
 * 计算集群健康分数
 */
function calculateHealthScore(clusterInfo: any, metrics: any): number {
  let score = 100;
  
  // 节点健康度 (30%)
  const nodeHealthRatio = clusterInfo.resources.ready_nodes / clusterInfo.resources.total_nodes;
  score -= (1 - nodeHealthRatio) * 30;
  
  // Pod健康度 (25%)
  const podHealthRatio = clusterInfo.resources.running_pods / clusterInfo.resources.total_pods;
  score -= (1 - podHealthRatio) * 25;
  
  // 系统资源 (45%)
  const cpuUsage = parseFloat(metrics.metrics.system.cpu_usage.replace('%', ''));
  const memoryUsage = parseFloat(metrics.metrics.system.memory_usage.replace('%', ''));
  
  if (cpuUsage > 90) score -= 20;
  else if (cpuUsage > 80) score -= 10;
  else if (cpuUsage > 70) score -= 5;
  
  if (memoryUsage > 95) score -= 25;
  else if (memoryUsage > 85) score -= 15;
  else if (memoryUsage > 75) score -= 8;
  
  return Math.max(0, Math.min(100, score));
}
