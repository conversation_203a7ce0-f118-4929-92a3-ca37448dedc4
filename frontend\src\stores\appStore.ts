import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { subscribeWithSelector } from 'zustand/middleware';

// 主题类型
export type Theme = 'light' | 'dark' | 'auto';

// 应用状态接口
export interface AppState {
  // 主题相关
  theme: Theme;
  isDarkMode: boolean;
  
  // 布局相关
  sidebarCollapsed: boolean;
  sidebarWidth: number;
  
  // 页面相关
  pageLoading: boolean;
  pageTitle: string;
  
  // 全局加载状态
  globalLoading: boolean;
  loadingMessage: string;
  
  // 错误状态
  globalError: string | null;
  
  // 通知状态
  notifications: AppNotification[];
  
  // 应用设置
  settings: AppSettings;
}

// 通知接口
export interface AppNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: number;
  read: boolean;
}

// 应用设置接口
export interface AppSettings {
  language: 'zh-CN' | 'en-US';
  autoRefresh: boolean;
  refreshInterval: number; // 秒
  showNotifications: boolean;
  soundEnabled: boolean;
  compactMode: boolean;
  animationsEnabled: boolean;
}

// 应用操作接口
export interface AppActions {
  // 主题操作
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  setDarkMode: (isDark: boolean) => void;
  
  // 布局操作
  setSidebarCollapsed: (collapsed: boolean) => void;
  toggleSidebar: () => void;
  setSidebarWidth: (width: number) => void;
  
  // 页面操作
  setPageLoading: (loading: boolean) => void;
  setPageTitle: (title: string) => void;
  
  // 全局加载操作
  setGlobalLoading: (loading: boolean, message?: string) => void;
  
  // 错误操作
  setGlobalError: (error: string | null) => void;
  clearGlobalError: () => void;
  
  // 通知操作
  addNotification: (notification: Omit<AppNotification, 'id' | 'timestamp' | 'read'>) => void;
  removeNotification: (id: string) => void;
  markNotificationAsRead: (id: string) => void;
  clearAllNotifications: () => void;
  
  // 设置操作
  updateSettings: (settings: Partial<AppSettings>) => void;
  resetSettings: () => void;
  
  // 重置操作
  resetAppState: () => void;
}

// 默认设置
const defaultSettings: AppSettings = {
  language: 'zh-CN',
  autoRefresh: true,
  refreshInterval: 30,
  showNotifications: true,
  soundEnabled: false,
  compactMode: false,
  animationsEnabled: true,
};

// 初始状态
const initialState: AppState = {
  theme: 'light',
  isDarkMode: false,
  sidebarCollapsed: false,
  sidebarWidth: 280,
  pageLoading: false,
  pageTitle: 'K8s-Helper',
  globalLoading: false,
  loadingMessage: '',
  globalError: null,
  notifications: [],
  settings: defaultSettings,
};

// 创建应用状态store
export const useAppStore = create<AppState & AppActions>()(
  subscribeWithSelector(
    persist(
      (set, get) => ({
        ...initialState,

        // 主题操作
        setTheme: (theme: Theme) => {
          set({ theme });
          
          // 自动设置暗色模式
          if (theme === 'auto') {
            const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            set({ isDarkMode: isDark });
          } else {
            set({ isDarkMode: theme === 'dark' });
          }
        },

        toggleTheme: () => {
          const { theme } = get();
          const newTheme = theme === 'light' ? 'dark' : 'light';
          get().setTheme(newTheme);
        },

        setDarkMode: (isDark: boolean) => {
          set({ isDarkMode: isDark });
        },

        // 布局操作
        setSidebarCollapsed: (collapsed: boolean) => {
          set({ sidebarCollapsed: collapsed });
        },

        toggleSidebar: () => {
          const { sidebarCollapsed } = get();
          set({ sidebarCollapsed: !sidebarCollapsed });
        },

        setSidebarWidth: (width: number) => {
          set({ sidebarWidth: Math.max(200, Math.min(400, width)) });
        },

        // 页面操作
        setPageLoading: (loading: boolean) => {
          set({ pageLoading: loading });
        },

        setPageTitle: (title: string) => {
          set({ pageTitle: title });
          document.title = title;
        },

        // 全局加载操作
        setGlobalLoading: (loading: boolean, message = '') => {
          set({ globalLoading: loading, loadingMessage: message });
        },

        // 错误操作
        setGlobalError: (error: string | null) => {
          set({ globalError: error });
        },

        clearGlobalError: () => {
          set({ globalError: null });
        },

        // 通知操作
        addNotification: (notification) => {
          const newNotification: AppNotification = {
            ...notification,
            id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: Date.now(),
            read: false,
          };

          set((state) => ({
            notifications: [newNotification, ...state.notifications].slice(0, 50), // 最多保留50条
          }));

          // 自动移除通知（如果设置了duration）
          if (notification.duration && notification.duration > 0) {
            setTimeout(() => {
              get().removeNotification(newNotification.id);
            }, notification.duration);
          }
        },

        removeNotification: (id: string) => {
          set((state) => ({
            notifications: state.notifications.filter(n => n.id !== id),
          }));
        },

        markNotificationAsRead: (id: string) => {
          set((state) => ({
            notifications: state.notifications.map(n =>
              n.id === id ? { ...n, read: true } : n
            ),
          }));
        },

        clearAllNotifications: () => {
          set({ notifications: [] });
        },

        // 设置操作
        updateSettings: (newSettings: Partial<AppSettings>) => {
          set((state) => ({
            settings: { ...state.settings, ...newSettings },
          }));
        },

        resetSettings: () => {
          set({ settings: defaultSettings });
        },

        // 重置操作
        resetAppState: () => {
          set(initialState);
        },
      }),
      {
        name: 'k8s-helper-app-store',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          theme: state.theme,
          sidebarCollapsed: state.sidebarCollapsed,
          sidebarWidth: state.sidebarWidth,
          settings: state.settings,
        }),
      }
    )
  )
);

// 监听系统主题变化
if (typeof window !== 'undefined') {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  const handleThemeChange = (e: MediaQueryListEvent) => {
    const { theme, setDarkMode } = useAppStore.getState();
    if (theme === 'auto') {
      setDarkMode(e.matches);
    }
  };

  mediaQuery.addEventListener('change', handleThemeChange);
}

// 导出状态选择器
export const selectTheme = (state: AppState & AppActions) => state.theme;
export const selectIsDarkMode = (state: AppState & AppActions) => state.isDarkMode;
export const selectSidebarCollapsed = (state: AppState & AppActions) => state.sidebarCollapsed;
export const selectPageLoading = (state: AppState & AppActions) => state.pageLoading;
export const selectGlobalLoading = (state: AppState & AppActions) => state.globalLoading;
export const selectGlobalError = (state: AppState & AppActions) => state.globalError;
export const selectNotifications = (state: AppState & AppActions) => state.notifications;
export const selectSettings = (state: AppState & AppActions) => state.settings;

// 导出默认store
export default useAppStore;
