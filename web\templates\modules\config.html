{{define "config"}}
<div class="module-content">
    <div class="config-container">
        <!-- 配置概览区域 -->
        <div class="section config-overview">
            <h3>⚙️ 配置概览</h3>
            <div class="section-content">
                <div class="config-info">
                    <div class="info-item">
                        <span class="info-label">配置版本:</span>
                        <span id="config-version" class="info-value">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">最后修改:</span>
                        <span id="config-last-modified" class="info-value">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">配置状态:</span>
                        <span id="config-status" class="info-value status-indicator">--</span>
                    </div>
                </div>
                
                <div class="config-actions">
                    <button id="reload-config" class="btn btn-secondary">
                        <span class="loading" id="reload-loading" style="display: none;"></span>
                        🔄 重载配置
                    </button>
                    <button id="backup-config" class="btn btn-primary">
                        💾 备份配置
                    </button>
                    <button id="validate-config" class="btn btn-info">
                        ✅ 验证配置
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 配置编辑区域 -->
        <div class="section config-editor">
            <h3>📝 配置编辑</h3>
            <div class="section-content">
                <div class="editor-controls">
                    <div class="section-tabs">
                        <button class="tab-btn active" data-section="global">全局配置</button>
                        <button class="tab-btn" data-section="etcd">ETCD配置</button>
                        <button class="tab-btn" data-section="logging">日志配置</button>
                        <button class="tab-btn" data-section="cleanup">清理配置</button>
                        <button class="tab-btn" data-section="monitoring">监控配置</button>
                        <button class="tab-btn" data-section="web">Web配置</button>
                        <button class="tab-btn" data-section="custom">自定义配置</button>
                    </div>
                    
                    <div class="editor-actions">
                        <button id="save-config" class="btn btn-success" disabled>
                            <span class="loading" id="save-loading" style="display: none;"></span>
                            💾 保存配置
                        </button>
                        <button id="reset-config" class="btn btn-warning">
                            🔄 重置
                        </button>
                    </div>
                </div>
                
                <div class="config-editor-content">
                    <!-- 全局配置 -->
                    <div id="section-global" class="config-section active">
                        <h4>🌐 全局配置</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="global-kubeconfig" class="form-label">Kubeconfig路径:</label>
                                <input type="text" id="global-kubeconfig" class="form-control" placeholder="默认路径">
                            </div>
                            <div class="form-group">
                                <label for="global-namespace" class="form-label">默认命名空间:</label>
                                <input type="text" id="global-namespace" class="form-control" value="default">
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="global-verbose">
                                    <span>详细输出</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="global-force">
                                    <span>强制模式</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- ETCD配置 -->
                    <div id="section-etcd" class="config-section">
                        <h4>💾 ETCD配置</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="etcd-base-url" class="form-label">下载基础URL:</label>
                                <input type="text" id="etcd-base-url" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="etcd-version" class="form-label">版本:</label>
                                <input type="text" id="etcd-version" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="etcd-os" class="form-label">操作系统:</label>
                                <select id="etcd-os" class="form-control">
                                    <option value="linux">Linux</option>
                                    <option value="darwin">macOS</option>
                                    <option value="windows">Windows</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="etcd-data-dir" class="form-label">数据目录:</label>
                                <input type="text" id="etcd-data-dir" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="etcd-backup-dir" class="form-label">备份目录:</label>
                                <input type="text" id="etcd-backup-dir" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="etcd-api-server-manifest" class="form-label">API Server配置:</label>
                                <input type="text" id="etcd-api-server-manifest" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 日志配置 -->
                    <div id="section-logging" class="config-section">
                        <h4>📋 日志配置</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="logging-level" class="form-label">日志级别:</label>
                                <select id="logging-level" class="form-control">
                                    <option value="debug">Debug</option>
                                    <option value="info">Info</option>
                                    <option value="warn">Warn</option>
                                    <option value="error">Error</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="logging-format" class="form-label">日志格式:</label>
                                <select id="logging-format" class="form-control">
                                    <option value="text">Text</option>
                                    <option value="json">JSON</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="logging-file" class="form-label">日志文件:</label>
                                <input type="text" id="logging-file" class="form-control" placeholder="留空使用标准输出">
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="logging-color">
                                    <span>彩色输出</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 清理配置 -->
                    <div id="section-cleanup" class="config-section">
                        <h4>🧹 清理配置</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="cleanup-default-older-than" class="form-label">默认时间阈值:</label>
                                <input type="text" id="cleanup-default-older-than" class="form-control" placeholder="24h">
                            </div>
                            <div class="form-group">
                                <label for="cleanup-concurrent-workers" class="form-label">并发工作线程:</label>
                                <input type="number" id="cleanup-concurrent-workers" class="form-control" min="1" max="20">
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="cleanup-default-dry-run">
                                    <span>默认干运行</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="cleanup-auto-enabled">
                                    <span>启用自动清理</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label for="cleanup-auto-interval" class="form-label">自动清理间隔:</label>
                                <input type="text" id="cleanup-auto-interval" class="form-control" placeholder="24h">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 监控配置 -->
                    <div id="section-monitoring" class="config-section">
                        <h4>📊 监控配置</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="monitoring-port" class="form-label">监控端口:</label>
                                <input type="number" id="monitoring-port" class="form-control" min="1024" max="65535">
                            </div>
                            <div class="form-group">
                                <label for="monitoring-metrics-interval" class="form-label">指标收集间隔:</label>
                                <input type="text" id="monitoring-metrics-interval" class="form-control" placeholder="30s">
                            </div>
                            <div class="form-group">
                                <label for="monitoring-notification-url" class="form-label">通知URL:</label>
                                <input type="text" id="monitoring-notification-url" class="form-control" placeholder="Webhook URL">
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="monitoring-enabled">
                                    <span>启用监控</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="monitoring-alerts-enabled">
                                    <span>启用告警</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Web配置 -->
                    <div id="section-web" class="config-section">
                        <h4>🌐 Web配置</h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="web-host" class="form-label">监听地址:</label>
                                <input type="text" id="web-host" class="form-control" placeholder="localhost">
                            </div>
                            <div class="form-group">
                                <label for="web-port" class="form-label">监听端口:</label>
                                <input type="number" id="web-port" class="form-control" min="1024" max="65535">
                            </div>
                            <div class="form-group">
                                <label for="web-cert-file" class="form-label">证书文件:</label>
                                <input type="text" id="web-cert-file" class="form-control" placeholder="TLS证书路径">
                            </div>
                            <div class="form-group">
                                <label for="web-key-file" class="form-label">私钥文件:</label>
                                <input type="text" id="web-key-file" class="form-control" placeholder="TLS私钥路径">
                            </div>
                            <div class="form-group">
                                <label for="web-rate-limit-rps" class="form-label">速率限制(RPS):</label>
                                <input type="number" id="web-rate-limit-rps" class="form-control" min="1">
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="web-tls-enabled">
                                    <span>启用TLS</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="web-cors-enabled">
                                    <span>启用CORS</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 自定义配置 -->
                    <div id="section-custom" class="config-section">
                        <h4>🔧 自定义配置</h4>
                        <div class="custom-config-editor">
                            <textarea id="custom-config-json" class="form-control" rows="10" placeholder='{"key": "value"}'></textarea>
                            <small class="form-text">请输入有效的JSON格式配置</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 配置历史区域 -->
        <div class="section config-history">
            <h3>📚 配置历史</h3>
            <div class="section-content">
                <div class="history-controls">
                    <button id="refresh-history" class="btn btn-secondary">
                        <span class="loading" id="history-loading" style="display: none;"></span>
                        🔄 刷新历史
                    </button>
                    <select id="history-limit" class="form-control">
                        <option value="10">显示10条</option>
                        <option value="20" selected>显示20条</option>
                        <option value="50">显示50条</option>
                    </select>
                </div>
                
                <div class="history-list" id="history-list">
                    <div class="loading-placeholder">
                        <p>正在加载配置历史...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 配置帮助区域 -->
        <div class="section config-help">
            <h3>❓ 配置帮助</h3>
            <div class="section-content">
                <div class="help-grid">
                    <div class="help-card">
                        <div class="help-icon">🌐</div>
                        <div class="help-content">
                            <h4>全局配置</h4>
                            <p>设置Kubeconfig路径、默认命名空间等全局参数。</p>
                        </div>
                    </div>
                    <div class="help-card">
                        <div class="help-icon">💾</div>
                        <div class="help-content">
                            <h4>ETCD配置</h4>
                            <p>配置ETCD下载源、版本、数据目录等参数。</p>
                        </div>
                    </div>
                    <div class="help-card">
                        <div class="help-icon">📋</div>
                        <div class="help-content">
                            <h4>日志配置</h4>
                            <p>设置日志级别、格式、输出文件等日志相关配置。</p>
                        </div>
                    </div>
                    <div class="help-card">
                        <div class="help-icon">🧹</div>
                        <div class="help-content">
                            <h4>清理配置</h4>
                            <p>配置资源清理的默认参数和自动清理策略。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}
