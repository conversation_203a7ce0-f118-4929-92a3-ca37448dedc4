import React, { useEffect } from 'react';
import { Row, Col, Card, Statistic, Alert, Space, Button, Descriptions, Tag } from 'antd';
import {
  DatabaseOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { MetricCard } from '@/components/MetricCard';
import { StatusIndicator } from '@/components/StatusIndicator';
import { useETCDStatus, useAppState } from '@/hooks';

/**
 * ETCD概览页面
 *
 * 功能特性：
 * - ETCD集群状态展示
 * - 成员信息显示
 * - 性能指标监控
 * - 健康状态检查
 */
const ETCDOverview: React.FC = () => {
  const { setPageTitle } = useAppState();

  // 获取ETCD状态
  const {
    data: etcdStatus,
    isLoading,
    error,
    refetch
  } = useETCDStatus({
    refetchInterval: 30000, // 30秒刷新一次
  });

  // 设置页面标题
  useEffect(() => {
    setPageTitle('ETCD概览 - K8s-Helper');
  }, [setPageTitle]);

  // 格式化数据大小
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 渲染错误状态
  if (error) {
    return (
      <Alert
        message="ETCD状态获取失败"
        description={error.message || '无法连接到ETCD服务，请检查服务状态'}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => refetch()}>
            重试
          </Button>
        }
      />
    );
  }

  // 构建指标卡片数据
  const buildMetricCards = () => {
    if (!etcdStatus) return [];

    return [
      {
        title: '集群状态',
        value: etcdStatus.errors.length === 0 ? '健康' : '异常',
        prefix: <DatabaseOutlined />,
        status: etcdStatus.errors.length === 0 ? 'healthy' : 'error',
        description: 'ETCD集群整体健康状态',
      },
      {
        title: '集群成员',
        value: etcdStatus.members.length,
        suffix: '个',
        prefix: '👥',
        status: 'healthy',
        description: '集群中的成员节点数量',
      },
      {
        title: '数据库大小',
        value: formatBytes(etcdStatus.db_size),
        prefix: '💾',
        status: etcdStatus.db_size > 2 * 1024 * 1024 * 1024 ? 'warning' : 'healthy', // 2GB警告
        description: 'ETCD数据库当前大小',
      },
      {
        title: '使用中大小',
        value: formatBytes(etcdStatus.db_size_in_use),
        prefix: '📊',
        status: 'healthy',
        description: '实际使用的数据库空间',
      },
    ];
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <h2 style={{ margin: 0 }}>ETCD概览</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            查看ETCD集群状态和性能指标
          </p>
        </div>

        <Space>
          <Button
            icon={<ReloadOutlined />}
            loading={isLoading}
            onClick={() => refetch()}
          >
            刷新
          </Button>
          <Button icon={<SettingOutlined />}>
            设置
          </Button>
        </Space>
      </div>

      {/* 指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {buildMetricCards().map((metric, index) => (
          <Col key={index} xs={24} sm={12} md={6}>
            <MetricCard {...metric} loading={isLoading} />
          </Col>
        ))}
      </Row>

      {/* 详细信息 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="集群信息" loading={isLoading}>
            {etcdStatus && (
              <Descriptions column={1} size="small">
                <Descriptions.Item label="集群ID">
                  <code>{etcdStatus.cluster_id}</code>
                </Descriptions.Item>
                <Descriptions.Item label="成员ID">
                  <code>{etcdStatus.member_id}</code>
                </Descriptions.Item>
                <Descriptions.Item label="Raft Term">
                  {etcdStatus.raft_term}
                </Descriptions.Item>
                <Descriptions.Item label="Raft Index">
                  {etcdStatus.raft_index}
                </Descriptions.Item>
                <Descriptions.Item label="Applied Index">
                  {etcdStatus.raft_applied_index}
                </Descriptions.Item>
                <Descriptions.Item label="Leader">
                  <code>{etcdStatus.leader}</code>
                </Descriptions.Item>
                <Descriptions.Item label="是否Learner">
                  <Tag color={etcdStatus.is_learner ? 'orange' : 'green'}>
                    {etcdStatus.is_learner ? '是' : '否'}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
            )}
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="成员列表" loading={isLoading}>
            {etcdStatus?.members.map((member, index) => (
              <div
                key={member.id}
                style={{
                  padding: '12px 0',
                  borderBottom: index < etcdStatus.members.length - 1 ? '1px solid #f0f0f0' : 'none'
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <strong>{member.name}</strong>
                    <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                      ID: <code>{member.id}</code>
                    </div>
                  </div>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    {member.is_leader && (
                      <Tag color="gold" icon={<CheckCircleOutlined />}>
                        Leader
                      </Tag>
                    )}
                    {member.is_learner && (
                      <Tag color="orange">
                        Learner
                      </Tag>
                    )}
                    <StatusIndicator
                      status="healthy"
                      size="small"
                      tooltip="成员状态正常"
                    />
                  </div>
                </div>

                <div style={{ marginTop: '8px', fontSize: '12px' }}>
                  <div>
                    <strong>Peer URLs:</strong> {member.peer_urls.join(', ')}
                  </div>
                  <div style={{ marginTop: '2px' }}>
                    <strong>Client URLs:</strong> {member.client_urls.join(', ')}
                  </div>
                </div>
              </div>
            ))}
          </Card>
        </Col>
      </Row>

      {/* 错误信息 */}
      {etcdStatus?.errors && etcdStatus.errors.length > 0 && (
        <Card
          title="错误信息"
          style={{ marginTop: '16px' }}
          extra={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
        >
          {etcdStatus.errors.map((error, index) => (
            <Alert
              key={index}
              message={error}
              type="error"
              style={{ marginBottom: index < etcdStatus.errors.length - 1 ? '8px' : 0 }}
            />
          ))}
        </Card>
      )}
    </div>
  );
};

export default ETCDOverview;
