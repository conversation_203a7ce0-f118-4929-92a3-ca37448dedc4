package web

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

// WebSocketManager WebSocket连接管理器
type WebSocketManager struct {
	logger        *zap.Logger
	connections   map[string]*WebSocketConnection
	subscriptions map[string]map[string]*WebSocketConnection // module -> connID -> connection
	mutex         sync.RWMutex
	upgrader      websocket.Upgrader
}

// WebSocketConnection WebSocket连接
type WebSocketConnection struct {
	ID       string
	Conn     *websocket.Conn
	Type     string // logs, status
	LastPing time.Time
	Context  context.Context
	Cancel   context.CancelFunc
	writeMu  sync.Mutex // 写入互斥锁，防止并发写入
}

// WebSocketMessage WebSocket消息
type WebSocketMessage struct {
	Type      string      `json:"type"`
	Module    string      `json:"module"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
	ID        string      `json:"id,omitempty"`
}

// LogStreamMessage 日志流消息
type LogStreamMessage struct {
	Pod       string `json:"pod"`
	Namespace string `json:"namespace"`
	Container string `json:"container"`
	Log       string `json:"log"`
	Timestamp string `json:"timestamp"`
}

// StatusUpdateMessage 状态更新消息
type StatusUpdateMessage struct {
	Component string      `json:"component"`
	Status    string      `json:"status"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// NewWebSocketManager 创建WebSocket管理器
func NewWebSocketManager(logger *zap.Logger) *WebSocketManager {
	return &WebSocketManager{
		logger:        logger,
		connections:   make(map[string]*WebSocketConnection),
		subscriptions: make(map[string]map[string]*WebSocketConnection),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该检查Origin
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}
}

// HandleWebSocketConnection 处理WebSocket连接
func (wsm *WebSocketManager) HandleWebSocketConnection(c *gin.Context, connType string) {
	conn, err := wsm.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		wsm.logger.Error("WebSocket升级失败", zap.Error(err))
		return
	}

	// 创建连接上下文
	ctx, cancel := context.WithCancel(context.Background())
	
	// 生成连接ID
	connID := fmt.Sprintf("%s_%d", connType, time.Now().UnixNano())
	
	wsConn := &WebSocketConnection{
		ID:       connID,
		Conn:     conn,
		Type:     connType,
		LastPing: time.Now(),
		Context:  ctx,
		Cancel:   cancel,
	}

	// 注册连接
	wsm.registerConnection(wsConn)
	defer wsm.unregisterConnection(connID)

	wsm.logger.Info("WebSocket连接已建立",
		zap.String("id", connID),
		zap.String("type", connType),
		zap.String("remote", c.Request.RemoteAddr))

	// 发送欢迎消息
	welcomeMsg := WebSocketMessage{
		Type:      "welcome",
		Module:    connType,
		Data:      map[string]string{"connection_id": connID, "type": connType},
		Timestamp: time.Now(),
	}
	wsConn.SendMessage(welcomeMsg)

	// 添加到订阅列表
	wsm.addSubscription(connType, connID, wsConn)

	// 启动心跳检测
	go wsm.heartbeat(wsConn)

	// 根据连接类型启动相应的处理
	switch connType {
	case "logs":
		go wsm.handleLogStream(wsConn, c)
	case "status":
		go wsm.handleStatusStream(wsConn, c)
	case "monitoring":
		go wsm.handleMonitoringStream(wsConn, c)
	case "cleanup":
		go wsm.handleCleanupStream(wsConn, c)
	case "config":
		go wsm.handleConfigStream(wsConn, c)
	case "port-forward":
		go wsm.handlePortForwardStream(wsConn, c)
	default:
		wsm.logger.Warn("未知的WebSocket连接类型", zap.String("type", connType))
	}

	// 处理客户端消息
	wsm.handleClientMessages(wsConn)
}

// registerConnection 注册连接
func (wsm *WebSocketManager) registerConnection(conn *WebSocketConnection) {
	wsm.mutex.Lock()
	defer wsm.mutex.Unlock()
	wsm.connections[conn.ID] = conn
}

// unregisterConnection 注销连接
func (wsm *WebSocketManager) unregisterConnection(connID string) {
	wsm.mutex.Lock()
	defer wsm.mutex.Unlock()

	if conn, exists := wsm.connections[connID]; exists {
		conn.SafeClose()
		delete(wsm.connections, connID)

		// 从所有订阅中移除连接
		for module, subscribers := range wsm.subscriptions {
			delete(subscribers, connID)
			if len(subscribers) == 0 {
				delete(wsm.subscriptions, module)
			}
		}

		wsm.logger.Info("WebSocket连接已关闭", zap.String("id", connID))
	}
}

// addSubscription 添加订阅
func (wsm *WebSocketManager) addSubscription(module, connID string, conn *WebSocketConnection) {
	wsm.mutex.Lock()
	defer wsm.mutex.Unlock()

	if wsm.subscriptions[module] == nil {
		wsm.subscriptions[module] = make(map[string]*WebSocketConnection)
	}
	wsm.subscriptions[module][connID] = conn
}

// removeSubscription 移除订阅
func (wsm *WebSocketManager) removeSubscription(module, connID string) {
	wsm.mutex.Lock()
	defer wsm.mutex.Unlock()

	if subscribers, exists := wsm.subscriptions[module]; exists {
		delete(subscribers, connID)
		if len(subscribers) == 0 {
			delete(wsm.subscriptions, module)
		}
	}
}

// SendMessage 发送消息到WebSocket连接
func (conn *WebSocketConnection) SendMessage(msg WebSocketMessage) error {
	conn.writeMu.Lock()
	defer conn.writeMu.Unlock()

	// 设置写入超时
	conn.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	return conn.Conn.WriteJSON(msg)
}

// SafeClose 安全关闭WebSocket连接
func (conn *WebSocketConnection) SafeClose() error {
	conn.writeMu.Lock()
	defer conn.writeMu.Unlock()

	// 取消上下文
	if conn.Cancel != nil {
		conn.Cancel()
	}

	// 关闭连接
	return conn.Conn.Close()
}

// heartbeat 心跳检测
func (wsm *WebSocketManager) heartbeat(conn *WebSocketConnection) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 发送ping消息
			pingMsg := WebSocketMessage{
				Type:      "ping",
				Data:      nil,
				Timestamp: time.Now(),
			}
			
			if err := conn.SendMessage(pingMsg); err != nil {
				wsm.logger.Warn("发送ping消息失败", 
					zap.String("conn_id", conn.ID), 
					zap.Error(err))
				return
			}
			
		case <-conn.Context.Done():
			return
		}
	}
}

// handleClientMessages 处理客户端消息
func (wsm *WebSocketManager) handleClientMessages(conn *WebSocketConnection) {
	defer conn.Cancel()

	for {
		var msg WebSocketMessage
		err := conn.Conn.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				wsm.logger.Error("WebSocket读取错误", 
					zap.String("conn_id", conn.ID), 
					zap.Error(err))
			}
			break
		}

		// 更新最后ping时间
		conn.LastPing = time.Now()

		// 处理不同类型的消息
		switch msg.Type {
		case "pong":
			// 客户端响应ping
			wsm.logger.Debug("收到pong消息", zap.String("conn_id", conn.ID))
		case "subscribe":
			// 处理订阅请求
			wsm.handleSubscription(conn, msg)
		default:
			wsm.logger.Warn("未知消息类型", 
				zap.String("conn_id", conn.ID), 
				zap.String("type", msg.Type))
		}
	}
}

// handleSubscription 处理订阅请求
func (wsm *WebSocketManager) handleSubscription(conn *WebSocketConnection, msg WebSocketMessage) {
	wsm.logger.Info("处理订阅请求",
		zap.String("conn_id", conn.ID),
		zap.String("type", msg.Type),
		zap.Any("data", msg.Data))
	
	// 这里可以根据订阅类型启动相应的数据流
	// 例如：特定Pod的日志、特定组件的状态等
}

// handleLogStream 处理日志流
func (wsm *WebSocketManager) handleLogStream(conn *WebSocketConnection, c *gin.Context) {
	// 获取查询参数
	podName := c.Query("pod")
	namespace := c.Query("namespace")
	container := c.Query("container")
	
	if podName == "" || namespace == "" {
		errorMsg := WebSocketMessage{
			Type:      "error",
			Data:      map[string]string{"message": "pod和namespace参数是必需的"},
			Timestamp: time.Now(),
		}
		conn.SendMessage(errorMsg)
		return
	}

	wsm.logger.Info("开始日志流",
		zap.String("conn_id", conn.ID),
		zap.String("pod", podName),
		zap.String("namespace", namespace),
		zap.String("container", container))

	// 这里应该实现实际的日志流逻辑
	// 由于需要kubernetes客户端，这里提供一个模拟实现
	wsm.simulateLogStream(conn, podName, namespace, container)
}

// simulateLogStream 模拟日志流（实际实现中应该连接到Kubernetes API）
func (wsm *WebSocketManager) simulateLogStream(conn *WebSocketConnection, pod, namespace, container string) {
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	logCount := 0
	for {
		select {
		case <-ticker.C:
			logCount++
			logMsg := LogStreamMessage{
				Pod:       pod,
				Namespace: namespace,
				Container: container,
				Log:       fmt.Sprintf("模拟日志消息 #%d - %s", logCount, time.Now().Format("15:04:05")),
				Timestamp: time.Now().Format(time.RFC3339),
			}

			msg := WebSocketMessage{
				Type:      "log",
				Data:      logMsg,
				Timestamp: time.Now(),
			}

			if err := conn.SendMessage(msg); err != nil {
				wsm.logger.Error("发送日志消息失败", zap.Error(err))
				return
			}

		case <-conn.Context.Done():
			return
		}
	}
}

// handleStatusStream 处理状态流
func (wsm *WebSocketManager) handleStatusStream(conn *WebSocketConnection, c *gin.Context) {
	wsm.logger.Info("开始状态流", zap.String("conn_id", conn.ID))
	
	// 模拟状态更新
	wsm.simulateStatusStream(conn)
}

// simulateStatusStream 模拟状态流
func (wsm *WebSocketManager) simulateStatusStream(conn *WebSocketConnection) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	components := []string{"etcd", "apiserver", "scheduler", "controller-manager"}
	statuses := []string{"healthy", "warning", "error"}

	for {
		select {
		case <-ticker.C:
			// 随机选择组件和状态
			component := components[time.Now().Unix()%int64(len(components))]
			status := statuses[time.Now().Unix()%int64(len(statuses))]

			statusMsg := StatusUpdateMessage{
				Component: component,
				Status:    status,
				Data: map[string]interface{}{
					"cpu_usage":    fmt.Sprintf("%.1f%%", float64(time.Now().Unix()%100)),
					"memory_usage": fmt.Sprintf("%.1f%%", float64(time.Now().Unix()%80)),
				},
				Timestamp: time.Now(),
			}

			msg := WebSocketMessage{
				Type:      "status",
				Data:      statusMsg,
				Timestamp: time.Now(),
			}

			if err := conn.SendMessage(msg); err != nil {
				wsm.logger.Error("发送状态消息失败", zap.Error(err))
				return
			}

		case <-conn.Context.Done():
			return
		}
	}
}

// BroadcastToType 向指定类型的所有连接广播消息
func (wsm *WebSocketManager) BroadcastToType(connType string, msg WebSocketMessage) {
	wsm.mutex.RLock()
	defer wsm.mutex.RUnlock()

	for _, conn := range wsm.connections {
		if conn.Type == connType {
			if err := conn.SendMessage(msg); err != nil {
				wsm.logger.Warn("广播消息失败",
					zap.String("conn_id", conn.ID),
					zap.Error(err))
			}
		}
	}
}

// GetConnectionCount 获取连接数量
func (wsm *WebSocketManager) GetConnectionCount() int {
	wsm.mutex.RLock()
	defer wsm.mutex.RUnlock()
	return len(wsm.connections)
}

// GetConnectionsByType 按类型获取连接数量
func (wsm *WebSocketManager) GetConnectionsByType() map[string]int {
	wsm.mutex.RLock()
	defer wsm.mutex.RUnlock()

	counts := make(map[string]int)
	for _, conn := range wsm.connections {
		counts[conn.Type]++
	}
	return counts
}

// handleMonitoringStream 处理监控数据流
func (wsm *WebSocketManager) handleMonitoringStream(conn *WebSocketConnection, c *gin.Context) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 发送监控数据更新
			msg := WebSocketMessage{
				Type:      "monitoring_update",
				Module:    "monitoring",
				Data:      map[string]interface{}{"timestamp": time.Now()},
				Timestamp: time.Now(),
			}
			if err := conn.SendMessage(msg); err != nil {
				wsm.logger.Error("发送监控更新失败", zap.Error(err))
				return
			}
		case <-conn.Context.Done():
			return
		}
	}
}

// handleCleanupStream 处理清理数据流
func (wsm *WebSocketManager) handleCleanupStream(conn *WebSocketConnection, c *gin.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 发送清理状态更新
			msg := WebSocketMessage{
				Type:      "cleanup_update",
				Module:    "cleanup",
				Data:      map[string]interface{}{"timestamp": time.Now()},
				Timestamp: time.Now(),
			}
			if err := conn.SendMessage(msg); err != nil {
				wsm.logger.Error("发送清理更新失败", zap.Error(err))
				return
			}
		case <-conn.Context.Done():
			return
		}
	}
}

// handleConfigStream 处理配置数据流
func (wsm *WebSocketManager) handleConfigStream(conn *WebSocketConnection, c *gin.Context) {
	// 配置更新通常是事件驱动的，这里主要监听配置变化
	for {
		select {
		case <-conn.Context.Done():
			return
		}
	}
}

// handlePortForwardStream 处理端口转发数据流
func (wsm *WebSocketManager) handlePortForwardStream(conn *WebSocketConnection, c *gin.Context) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 发送端口转发状态更新
			msg := WebSocketMessage{
				Type:      "port_forward_update",
				Module:    "port-forward",
				Data:      map[string]interface{}{"timestamp": time.Now()},
				Timestamp: time.Now(),
			}
			if err := conn.SendMessage(msg); err != nil {
				wsm.logger.Error("发送端口转发更新失败", zap.Error(err))
				return
			}
		case <-conn.Context.Done():
			return
		}
	}
}

// BroadcastToModule 向指定模块的所有订阅者广播消息
func (wsm *WebSocketManager) BroadcastToModule(module string, msg WebSocketMessage) {
	wsm.mutex.RLock()
	defer wsm.mutex.RUnlock()

	if subscribers, exists := wsm.subscriptions[module]; exists {
		for _, conn := range subscribers {
			go func(c *WebSocketConnection) {
				if err := c.SendMessage(msg); err != nil {
					wsm.logger.Error("广播消息失败",
						zap.String("module", module),
						zap.String("conn_id", c.ID),
						zap.Error(err))
				}
			}(conn)
		}
	}
}
