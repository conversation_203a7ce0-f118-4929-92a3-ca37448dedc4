import { useState, useEffect, useRef, useCallback } from 'react';
import { useConnectionStore } from '@/stores';
import type { 
  WebSocketConfig, 
  WebSocketOptions, 
  WebSocketMessage, 
  WebSocketReadyState,
  UseWebSocketReturn,
  WebSocketConnectionState
} from '@/types/websocket';

/**
 * WebSocket自定义Hook
 * 
 * 功能特性：
 * - 自动连接管理
 * - 智能重连机制（指数退避）
 * - 消息队列和缓冲
 * - 心跳检测
 * - 状态管理集成
 */
export const useWebSocket = (
  config: WebSocketConfig,
  options: WebSocketOptions = {}
): UseWebSocketReturn => {
  // 状态管理
  const [connectionState, setConnectionState] = useState<WebSocketConnectionState>({
    readyState: 'CLOSED',
    url: config.url,
    connected: false,
    connecting: false,
    reconnecting: false,
    reconnectAttempts: 0,
    messageQueue: [],
  });

  // 引用
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const eventListenersRef = useRef<Map<string, Set<(message: WebSocketMessage) => void>>>(new Map());

  // 连接状态管理
  const { setWebSocketStatus, addConnectionEvent } = useConnectionStore();

  // 配置默认值
  const finalConfig = {
    reconnect: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5,
    heartbeatInterval: 30000,
    messageQueueSize: 100,
    timeout: 10000,
    ...config,
  };

  // 生成消息ID
  const generateMessageId = useCallback(() => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 更新连接状态
  const updateConnectionState = useCallback((updates: Partial<WebSocketConnectionState>) => {
    setConnectionState(prev => ({ ...prev, ...updates }));
  }, []);

  // 清理定时器
  const clearTimers = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);

  // 启动心跳检测
  const startHeartbeat = useCallback(() => {
    if (finalConfig.heartbeatInterval && finalConfig.heartbeatInterval > 0) {
      heartbeatIntervalRef.current = setInterval(() => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          const heartbeatMessage: WebSocketMessage = {
            id: generateMessageId(),
            type: 'heartbeat',
            action: 'ping',
            timestamp: Date.now(),
          };
          wsRef.current.send(JSON.stringify(heartbeatMessage));
        }
      }, finalConfig.heartbeatInterval);
    }
  }, [finalConfig.heartbeatInterval, generateMessageId]);

  // 停止心跳检测
  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);

  // 处理消息
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      // 处理心跳响应
      if (message.type === 'heartbeat' && message.action === 'pong') {
        return;
      }

      // 触发消息事件监听器
      const listeners = eventListenersRef.current.get(message.type);
      if (listeners) {
        listeners.forEach(listener => {
          try {
            listener(message);
          } catch (error) {
            console.error('WebSocket message listener error:', error);
          }
        });
      }

      // 调用全局消息处理器
      options.onMessage?.(message);

    } catch (error) {
      console.error('WebSocket message parse error:', error);
    }
  }, [options]);

  // 连接WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN || connectionState.connecting) {
      return;
    }

    updateConnectionState({
      connecting: true,
      reconnecting: connectionState.reconnectAttempts > 0,
      readyState: 'CONNECTING',
    });

    try {
      const ws = new WebSocket(finalConfig.url, finalConfig.protocols);
      wsRef.current = ws;

      // 连接超时处理
      const timeoutId = setTimeout(() => {
        if (ws.readyState === WebSocket.CONNECTING) {
          ws.close();
          handleError(new Error('Connection timeout'));
        }
      }, finalConfig.timeout);

      ws.onopen = (event) => {
        clearTimeout(timeoutId);
        
        updateConnectionState({
          readyState: 'OPEN',
          connected: true,
          connecting: false,
          reconnecting: false,
          reconnectAttempts: 0,
          lastConnected: Date.now(),
          lastError: undefined,
        });

        startHeartbeat();
        options.onOpen?.(event);

        addConnectionEvent({
          type: 'connect',
          message: `WebSocket连接已建立: ${finalConfig.url}`,
          details: { url: finalConfig.url },
        });
      };

      ws.onclose = (event) => {
        clearTimeout(timeoutId);
        stopHeartbeat();
        
        updateConnectionState({
          readyState: 'CLOSED',
          connected: false,
          connecting: false,
          lastDisconnected: Date.now(),
        });

        options.onClose?.(event);

        addConnectionEvent({
          type: 'disconnect',
          message: `WebSocket连接已关闭: ${event.code} ${event.reason}`,
          details: { code: event.code, reason: event.reason },
        });

        // 自动重连
        if (finalConfig.reconnect && !event.wasClean && connectionState.reconnectAttempts < finalConfig.maxReconnectAttempts) {
          scheduleReconnect();
        }
      };

      ws.onerror = (event) => {
        clearTimeout(timeoutId);
        handleError(new Error('WebSocket connection error'));
        options.onError?.(event);
      };

      ws.onmessage = handleMessage;

    } catch (error) {
      handleError(error as Error);
    }
  }, [
    connectionState.connecting,
    connectionState.reconnectAttempts,
    finalConfig,
    updateConnectionState,
    startHeartbeat,
    stopHeartbeat,
    handleMessage,
    options,
    addConnectionEvent,
  ]);

  // 处理错误
  const handleError = useCallback((error: Error) => {
    updateConnectionState({
      readyState: 'CLOSED',
      connected: false,
      connecting: false,
      lastError: error.message,
    });

    addConnectionEvent({
      type: 'error',
      message: `WebSocket连接错误: ${error.message}`,
      details: { error: error.message },
    });
  }, [updateConnectionState, addConnectionEvent]);

  // 计划重连
  const scheduleReconnect = useCallback(() => {
    if (connectionState.reconnectAttempts >= finalConfig.maxReconnectAttempts) {
      options.onReconnectFailed?.();
      return;
    }

    const delay = Math.min(
      finalConfig.reconnectInterval * Math.pow(2, connectionState.reconnectAttempts),
      30000 // 最大30秒
    );

    updateConnectionState(prev => ({
      ...prev,
      reconnectAttempts: prev.reconnectAttempts + 1,
    }));

    reconnectTimeoutRef.current = setTimeout(() => {
      options.onReconnect?.(connectionState.reconnectAttempts + 1);
      connect();
    }, delay);
  }, [
    connectionState.reconnectAttempts,
    finalConfig.maxReconnectAttempts,
    finalConfig.reconnectInterval,
    updateConnectionState,
    options,
    connect,
  ]);

  // 断开连接
  const disconnect = useCallback(() => {
    clearTimers();
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    updateConnectionState({
      readyState: 'CLOSED',
      connected: false,
      connecting: false,
      reconnecting: false,
    });
  }, [clearTimers, updateConnectionState]);

  // 重连
  const reconnect = useCallback(() => {
    disconnect();
    updateConnectionState(prev => ({ ...prev, reconnectAttempts: 0 }));
    connect();
  }, [disconnect, updateConnectionState, connect]);

  // 发送消息
  const sendMessage = useCallback((message: WebSocketMessage): boolean => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        wsRef.current.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('WebSocket send error:', error);
        return false;
      }
    } else {
      // 添加到消息队列
      updateConnectionState(prev => ({
        ...prev,
        messageQueue: [...prev.messageQueue, message].slice(-finalConfig.messageQueueSize),
      }));
      return false;
    }
  }, [finalConfig.messageQueueSize, updateConnectionState]);

  // 清空消息队列
  const clearMessageQueue = useCallback(() => {
    updateConnectionState(prev => ({ ...prev, messageQueue: [] }));
  }, [updateConnectionState]);

  // 添加事件监听器
  const addEventListener = useCallback((type: string, listener: (message: WebSocketMessage) => void) => {
    const listeners = eventListenersRef.current.get(type) || new Set();
    listeners.add(listener);
    eventListenersRef.current.set(type, listeners);
  }, []);

  // 移除事件监听器
  const removeEventListener = useCallback((type: string, listener: (message: WebSocketMessage) => void) => {
    const listeners = eventListenersRef.current.get(type);
    if (listeners) {
      listeners.delete(listener);
      if (listeners.size === 0) {
        eventListenersRef.current.delete(type);
      }
    }
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      disconnect();
      eventListenersRef.current.clear();
    };
  }, [disconnect]);

  return {
    // 连接状态
    readyState: connectionState.readyState,
    connected: connectionState.connected,
    connecting: connectionState.connecting,
    reconnecting: connectionState.reconnecting,
    
    // 连接信息
    url: connectionState.url,
    reconnectAttempts: connectionState.reconnectAttempts,
    lastError: connectionState.lastError,
    
    // 操作方法
    connect,
    disconnect,
    reconnect,
    sendMessage,
    
    // 消息队列
    messageQueue: connectionState.messageQueue,
    clearMessageQueue,
    
    // 事件处理
    addEventListener,
    removeEventListener,
  };
};

export default useWebSocket;
