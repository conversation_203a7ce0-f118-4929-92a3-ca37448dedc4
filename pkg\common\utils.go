package common

import (
	"crypto/sha256"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

// ConfirmDeletion 确认删除操作的通用函数
func ConfirmDeletion(message string) bool {
	fmt.Printf("确认要%s吗? (y/N): ", message)
	var response string
	fmt.Scanln(&response)
	return strings.ToLower(response) == "y" || strings.ToLower(response) == "yes"
}

// ConfirmAction 确认操作的通用函数
func ConfirmAction(message string) bool {
	fmt.Printf("%s (y/N): ", message)
	var response string
	fmt.Scanln(&response)
	return strings.ToLower(response) == "y" || strings.ToLower(response) == "yes"
}

// Contains 检查字符串切片是否包含指定字符串
func Contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// FormatFileSize 格式化文件大小为 MB
func FormatFileSize(bytes int64) string {
	mb := float64(bytes) / 1024 / 1024
	return fmt.Sprintf("%.2f MB", mb)
}

// PrintSuccess 打印成功消息
func PrintSuccess(message string) {
	fmt.Printf("✅ %s\n", message)
}

// PrintWarning 打印警告消息
func PrintWarning(message string) {
	fmt.Printf("⚠️  %s\n", message)
}

// PrintError 打印错误消息
func PrintError(message string) {
	fmt.Printf("❌ %s\n", message)
}

// PrintInfo 打印信息消息
func PrintInfo(message string) {
	fmt.Printf("ℹ️  %s\n", message)
}

// FileExists 检查文件是否存在
func FileExists(filename string) bool {
	info, err := os.Stat(filename)
	if os.IsNotExist(err) {
		return false
	}
	return !info.IsDir()
}

// CreateDirIfNotExists 如果目录不存在则创建
func CreateDirIfNotExists(dir string) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return os.MkdirAll(dir, 0755)
	}
	return nil
}

// GetCurrentTimestamp 获取当前时间戳字符串
func GetCurrentTimestamp(format string) string {
	return time.Now().Format(format)
}

// GenerateRestoreDir 生成带时间戳的恢复目录路径
func GenerateRestoreDir() string {
	timestamp := GetCurrentTimestamp(BackupTimeFormat)
	return fmt.Sprintf("/var/lib/etcd-restore-%s", timestamp)
}

// TruncateString 截断字符串到指定长度
func TruncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

// CalculateFileHash 计算文件的 SHA256 哈希值
func CalculateFileHash(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// SaveHashToFile 将哈希值保存到文件
func SaveHashToFile(hashValue, filePath string) error {
	hashFile := filePath + ".sha256"
	return os.WriteFile(hashFile, []byte(hashValue+"\n"), 0644)
}

// LoadHashFromFile 从文件加载哈希值
func LoadHashFromFile(filePath string) (string, error) {
	hashFile := filePath + ".sha256"
	data, err := os.ReadFile(hashFile)
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(data)), nil
}

// VerifyFileHash 验证文件的哈希值
func VerifyFileHash(filePath string) (bool, string, string, error) {
	// 计算当前文件的哈希值
	currentHash, err := CalculateFileHash(filePath)
	if err != nil {
		return false, "", "", fmt.Errorf("计算文件哈希失败: %w", err)
	}

	// 加载保存的哈希值
	savedHash, err := LoadHashFromFile(filePath)
	if err != nil {
		return false, currentHash, "", fmt.Errorf("加载保存的哈希值失败: %w", err)
	}

	// 比较哈希值
	return currentHash == savedHash, currentHash, savedHash, nil
}

// DownloadFileWithVerification 下载文件并验证校验和
func DownloadFileWithVerification(url, filePath, expectedHash string) error {
	// 创建临时文件
	tmpFile := filePath + ".tmp"
	defer os.Remove(tmpFile)

	// 下载文件
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("下载文件失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，HTTP 状态码: %d", resp.StatusCode)
	}

	// 创建临时文件
	out, err := os.Create(tmpFile)
	if err != nil {
		return fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer out.Close()

	// 复制数据
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	// 验证校验和（如果提供）
	if expectedHash != "" {
		actualHash, err := CalculateFileHash(tmpFile)
		if err != nil {
			return fmt.Errorf("计算文件哈希失败: %w", err)
		}

		if actualHash != expectedHash {
			return fmt.Errorf("文件校验和不匹配，期望: %s，实际: %s", expectedHash, actualHash)
		}
	}

	// 移动到最终位置
	if err := os.Rename(tmpFile, filePath); err != nil {
		return fmt.Errorf("移动文件失败: %w", err)
	}

	return nil
}

// SecureCreateTempFile 安全地创建临时文件
func SecureCreateTempFile(dir, pattern string) (*os.File, error) {
	file, err := os.CreateTemp(dir, pattern)
	if err != nil {
		return nil, err
	}

	// 设置安全的文件权限
	if err := file.Chmod(0600); err != nil {
		file.Close()
		os.Remove(file.Name())
		return nil, fmt.Errorf("设置文件权限失败: %w", err)
	}

	return file, nil
}

// ValidatePath 验证路径的安全性
func ValidatePath(path string) error {
	// 检查路径遍历攻击
	if strings.Contains(path, "..") {
		return fmt.Errorf("路径包含不安全的 '..' 序列: %s", path)
	}

	// 检查绝对路径
	if !strings.HasPrefix(path, "/") && !strings.HasPrefix(path, "./") {
		return fmt.Errorf("路径必须是绝对路径或相对路径: %s", path)
	}

	return nil
}
