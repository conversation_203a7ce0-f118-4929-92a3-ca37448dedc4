import { useState, useEffect, useCallback, useRef } from 'react';
import { useWebSocket } from './useWebSocket';
import { useNotifications } from './useAppState';
import type { MetricsMessage, AlertMessage, WebSocketMessage } from '@/types/websocket';

// 监控数据点
export interface MetricDataPoint {
  timestamp: number;
  value: number;
}

// 监控指标
export interface MonitoringMetrics {
  cpu: MetricDataPoint[];
  memory: MetricDataPoint[];
  disk: MetricDataPoint[];
  network: {
    sent: MetricDataPoint[];
    received: MetricDataPoint[];
  };
}

// 告警信息
export interface Alert {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  source: string;
  timestamp: number;
  acknowledged: boolean;
  metadata?: Record<string, any>;
}

// 实时监控状态
export interface RealTimeMonitoringState {
  metrics: MonitoringMetrics;
  alerts: Alert[];
  connected: boolean;
  loading: boolean;
  error: string | null;
  lastUpdate: number | null;
}

// 监控配置
export interface MonitoringConfig {
  interval?: number; // 数据更新间隔（毫秒）
  maxDataPoints?: number; // 最大数据点数量
  enableAlerts?: boolean; // 是否启用告警
  alertSeverities?: Array<'low' | 'medium' | 'high' | 'critical'>; // 关注的告警级别
}

/**
 * 实时监控Hook
 * 
 * 功能特性：
 * - WebSocket实时监控数据
 * - 指标数据缓存和图表支持
 * - 实时告警处理
 * - 数据点管理和清理
 * - 连接状态管理
 */
export const useRealTimeMonitoring = (config: MonitoringConfig = {}) => {
  // 配置默认值
  const finalConfig = {
    interval: 5000,
    maxDataPoints: 100,
    enableAlerts: true,
    alertSeverities: ['medium', 'high', 'critical'] as Array<'low' | 'medium' | 'high' | 'critical'>,
    ...config,
  };

  // 状态管理
  const [state, setState] = useState<RealTimeMonitoringState>({
    metrics: {
      cpu: [],
      memory: [],
      disk: [],
      network: {
        sent: [],
        received: [],
      },
    },
    alerts: [],
    connected: false,
    loading: true,
    error: null,
    lastUpdate: null,
  });

  // 引用
  const alertIdCounterRef = useRef(0);

  // 通知系统
  const { showError, showWarning } = useNotifications();

  // WebSocket连接
  const wsConfig = {
    url: 'ws://localhost:8080/api/v1/ws/monitoring',
    reconnect: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5,
    heartbeatInterval: 30000,
  };

  const {
    connected,
    connecting,
    reconnecting,
    sendMessage,
    addEventListener,
    removeEventListener,
  } = useWebSocket(wsConfig, {
    onOpen: () => {
      setState(prev => ({ ...prev, connected: true, loading: false, error: null }));
      
      // 发送监控订阅消息
      const subscribeMessage: WebSocketMessage = {
        id: generateMessageId(),
        type: 'monitoring_subscribe',
        timestamp: Date.now(),
        data: {
          interval: finalConfig.interval,
          enableAlerts: finalConfig.enableAlerts,
          alertSeverities: finalConfig.alertSeverities,
        },
      };
      
      sendMessage(subscribeMessage);
    },
    onClose: () => {
      setState(prev => ({ ...prev, connected: false }));
    },
    onError: () => {
      setState(prev => ({ 
        ...prev, 
        connected: false, 
        loading: false, 
        error: '监控连接失败' 
      }));
      showError('连接错误', '无法连接到监控服务');
    },
  });

  // 生成消息ID
  const generateMessageId = useCallback(() => {
    return `monitoring_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 添加数据点
  const addDataPoint = useCallback((
    data: MetricDataPoint[], 
    newPoint: MetricDataPoint, 
    maxPoints: number
  ): MetricDataPoint[] => {
    const updatedData = [...data, newPoint];
    return updatedData.slice(-maxPoints);
  }, []);

  // 处理监控指标消息
  const handleMetricsMessage = useCallback((message: MetricsMessage) => {
    const timestamp = message.timestamp;
    const metrics = message.metrics;

    setState(prev => ({
      ...prev,
      metrics: {
        cpu: addDataPoint(prev.metrics.cpu, { timestamp, value: metrics.cpu }, finalConfig.maxDataPoints),
        memory: addDataPoint(prev.metrics.memory, { timestamp, value: metrics.memory }, finalConfig.maxDataPoints),
        disk: addDataPoint(prev.metrics.disk, { timestamp, value: metrics.disk }, finalConfig.maxDataPoints),
        network: {
          sent: addDataPoint(prev.metrics.network.sent, { timestamp, value: metrics.network.bytes_sent }, finalConfig.maxDataPoints),
          received: addDataPoint(prev.metrics.network.received, { timestamp, value: metrics.network.bytes_received }, finalConfig.maxDataPoints),
        },
      },
      lastUpdate: timestamp,
    }));
  }, [addDataPoint, finalConfig.maxDataPoints]);

  // 处理告警消息
  const handleAlertMessage = useCallback((message: AlertMessage) => {
    // 检查是否关注此级别的告警
    if (!finalConfig.alertSeverities.includes(message.severity)) {
      return;
    }

    const alert: Alert = {
      id: `alert_${++alertIdCounterRef.current}`,
      severity: message.severity,
      title: message.title,
      message: message.message,
      source: message.source,
      timestamp: message.timestamp,
      acknowledged: false,
      metadata: message.metadata,
    };

    setState(prev => ({
      ...prev,
      alerts: [alert, ...prev.alerts].slice(0, 50), // 最多保留50条告警
    }));

    // 显示通知
    if (message.severity === 'critical' || message.severity === 'high') {
      showError(message.title, message.message);
    } else if (message.severity === 'medium') {
      showWarning(message.title, message.message);
    }
  }, [finalConfig.alertSeverities, showError, showWarning]);

  // 处理WebSocket消息
  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'metrics':
        handleMetricsMessage(message as MetricsMessage);
        break;
      case 'alert':
        handleAlertMessage(message as AlertMessage);
        break;
      default:
        break;
    }
  }, [handleMetricsMessage, handleAlertMessage]);

  // 确认告警
  const acknowledgeAlert = useCallback((alertId: string) => {
    setState(prev => ({
      ...prev,
      alerts: prev.alerts.map(alert =>
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      ),
    }));
  }, []);

  // 清除告警
  const clearAlert = useCallback((alertId: string) => {
    setState(prev => ({
      ...prev,
      alerts: prev.alerts.filter(alert => alert.id !== alertId),
    }));
  }, []);

  // 清除所有告警
  const clearAllAlerts = useCallback(() => {
    setState(prev => ({ ...prev, alerts: [] }));
  }, []);

  // 获取最新指标值
  const getLatestMetrics = useCallback(() => {
    const { metrics } = state;
    return {
      cpu: metrics.cpu.length > 0 ? metrics.cpu[metrics.cpu.length - 1].value : 0,
      memory: metrics.memory.length > 0 ? metrics.memory[metrics.memory.length - 1].value : 0,
      disk: metrics.disk.length > 0 ? metrics.disk[metrics.disk.length - 1].value : 0,
      network: {
        sent: metrics.network.sent.length > 0 ? metrics.network.sent[metrics.network.sent.length - 1].value : 0,
        received: metrics.network.received.length > 0 ? metrics.network.received[metrics.network.received.length - 1].value : 0,
      },
    };
  }, [state.metrics]);

  // 获取告警统计
  const getAlertStats = useCallback(() => {
    const { alerts } = state;
    return {
      total: alerts.length,
      unacknowledged: alerts.filter(alert => !alert.acknowledged).length,
      critical: alerts.filter(alert => alert.severity === 'critical').length,
      high: alerts.filter(alert => alert.severity === 'high').length,
      medium: alerts.filter(alert => alert.severity === 'medium').length,
      low: alerts.filter(alert => alert.severity === 'low').length,
    };
  }, [state.alerts]);

  // 获取指标趋势
  const getMetricTrend = useCallback((metricType: 'cpu' | 'memory' | 'disk') => {
    const data = state.metrics[metricType];
    if (data.length < 2) return 'stable';

    const recent = data.slice(-5); // 最近5个数据点
    const avg = recent.reduce((sum, point) => sum + point.value, 0) / recent.length;
    const latest = recent[recent.length - 1].value;

    if (latest > avg * 1.1) return 'increasing';
    if (latest < avg * 0.9) return 'decreasing';
    return 'stable';
  }, [state.metrics]);

  // 清空监控数据
  const clearMetrics = useCallback(() => {
    setState(prev => ({
      ...prev,
      metrics: {
        cpu: [],
        memory: [],
        disk: [],
        network: {
          sent: [],
          received: [],
        },
      },
      lastUpdate: null,
    }));
  }, []);

  // 设置事件监听器
  useEffect(() => {
    addEventListener('metrics', handleMessage);
    addEventListener('alert', handleMessage);
    
    return () => {
      removeEventListener('metrics', handleMessage);
      removeEventListener('alert', handleMessage);
    };
  }, [addEventListener, removeEventListener, handleMessage]);

  // 更新连接状态
  useEffect(() => {
    setState(prev => ({
      ...prev,
      connected,
      loading: connecting && !connected,
    }));
  }, [connected, connecting]);

  return {
    // 状态
    ...state,
    connecting,
    reconnecting,
    
    // 操作方法
    acknowledgeAlert,
    clearAlert,
    clearAllAlerts,
    clearMetrics,
    
    // 工具方法
    getLatestMetrics,
    getAlertStats,
    getMetricTrend,
    sendMessage,
  };
};

export default useRealTimeMonitoring;
