package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"

	"gopkg.in/yaml.v2"
	"k8s-helper/pkg/common"
)

// Config 表示应用程序的配置
type Config struct {
	// 全局配置
	Global GlobalConfig `yaml:"global"`

	// ETCD 配置
	ETCD ETCDConfig `yaml:"etcd"`

	// 日志配置
	Logging LoggingConfig `yaml:"logging"`

	// 清理配置
	Cleanup CleanupConfig `yaml:"cleanup"`

	// 监控配置
	Monitoring MonitoringConfig `yaml:"monitoring"`

	// API 服务配置
	API APIConfig `yaml:"api"`

	// 配置热重载
	ConfigReload ConfigReloadConfig `yaml:"config_reload"`

	// 性能优化配置
	Performance PerformanceConfig `yaml:"performance"`
}

// GlobalConfig 全局配置
type GlobalConfig struct {
	// Kubeconfig 文件路径
	Kubeconfig string `yaml:"kubeconfig"`

	// 默认命名空间
	Namespace string `yaml:"namespace"`

	// 详细输出
	Verbose bool `yaml:"verbose"`

	// 强制模式
	Force bool `yaml:"force"`
}

// ETCDConfig ETCD 相关配置
type ETCDConfig struct {
	// 下载基础 URL
	BaseURL string `yaml:"base_url"`

	// ETCD 版本
	Version string `yaml:"version"`

	// 操作系统
	OS string `yaml:"os"`

	// 默认数据目录
	DataDir string `yaml:"data_dir"`

	// 默认备份输出目录
	BackupDir string `yaml:"backup_dir"`

	// kube-apiserver 配置文件路径
	APIServerManifest string `yaml:"apiserver_manifest"`

	// SDK配置
	UseSDK          bool   `yaml:"use_sdk"`          // 是否优先使用SDK
	SDKTimeout      string `yaml:"sdk_timeout"`      // SDK超时时间
	FallbackToTool  bool   `yaml:"fallback_to_tool"` // 是否回退到工具
	BackupRetention int    `yaml:"backup_retention"` // 备份保留数量

	// 恢复配置
	UseSDKRestore   bool   `yaml:"use_sdk_restore"`   // 是否优先使用SDK进行恢复
	RestoreTimeout  string `yaml:"restore_timeout"`   // 恢复操作超时时间
	SkipHashCheck   bool   `yaml:"skip_hash_check"`   // 是否跳过哈希检查
	MarkCompacted   bool   `yaml:"mark_compacted"`    // 是否标记为压缩
	DefaultNodeName string `yaml:"default_node_name"` // 默认节点名称
	DefaultPeerURL  string `yaml:"default_peer_url"`  // 默认对等URL

	// 验证配置
	UseSDKVerify   bool   `yaml:"use_sdk_verify"`  // 是否优先使用SDK进行验证
	VerifyTimeout  string `yaml:"verify_timeout"`  // 验证操作超时时间
	DetailedVerify bool   `yaml:"detailed_verify"` // 是否显示详细验证信息
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	// 日志级别 (debug, info, warn, error)
	Level string `yaml:"level"`

	// 日志格式 (text, json)
	Format string `yaml:"format"`

	// 日志输出文件路径（空表示输出到标准输出）
	File string `yaml:"file"`

	// 是否启用颜色输出
	Color bool `yaml:"color"`
}

// CleanupConfig 清理配置
type CleanupConfig struct {
	// 默认清理时间阈值
	DefaultOlderThan string `yaml:"default_older_than"`

	// 是否默认启用干运行模式
	DefaultDryRun bool `yaml:"default_dry_run"`

	// 并发清理的 goroutine 数量
	ConcurrentWorkers int `yaml:"concurrent_workers"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled             bool          `yaml:"enabled"`
	Port                int           `yaml:"port"`
	Host                string        `yaml:"host"`
	MetricsInterval     string        `yaml:"metrics_interval"`
	HealthCheckInterval string        `yaml:"health_check_interval"`
	EnablePprof         bool          `yaml:"enable_pprof"`
}

// APIConfig API 服务配置
type APIConfig struct {
	Enabled         bool   `yaml:"enabled"`
	Port            int    `yaml:"port"`
	Host            string `yaml:"host"`
	ReadTimeout     string `yaml:"read_timeout"`
	WriteTimeout    string `yaml:"write_timeout"`
	IdleTimeout     string `yaml:"idle_timeout"`
	EnableCORS      bool   `yaml:"enable_cors"`
	EnableAuth      bool   `yaml:"enable_auth"`
	EnableRateLimit bool   `yaml:"enable_rate_limit"`
	DefaultVersion  string `yaml:"default_version"`
}

// ConfigReloadConfig 配置重载配置
type ConfigReloadConfig struct {
	Enabled      bool   `yaml:"enabled"`
	DebounceTime string `yaml:"debounce_time"`
	Validate     bool   `yaml:"validate_config"`
	Backup       bool   `yaml:"backup_config"`
	MaxBackups   int    `yaml:"max_backups"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	Cache      CacheConfig      `yaml:"cache"`
	FileCache  FileCacheConfig  `yaml:"file_cache"`
	AsyncHash  AsyncHashConfig  `yaml:"async_hasher"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Enabled         bool   `yaml:"enabled"`
	MaxSize         int    `yaml:"max_size"`
	TTL             string `yaml:"ttl"`
	CleanupInterval string `yaml:"cleanup_interval"`
}

// FileCacheConfig 文件缓存配置
type FileCacheConfig struct {
	Enabled     bool   `yaml:"enabled"`
	MaxSize     int    `yaml:"max_size"`
	MaxFileSize string `yaml:"max_file_size"`
}

// AsyncHashConfig 异步哈希配置
type AsyncHashConfig struct {
	Enabled    bool `yaml:"enabled"`
	WorkerCnt  int  `yaml:"worker_count"`
	QueueSize  int  `yaml:"queue_size"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Global: GlobalConfig{
			Kubeconfig: "",
			Namespace:  common.DefaultNamespace,
			Verbose:    false,
			Force:      false,
		},
		ETCD: ETCDConfig{
			BaseURL:           common.DefaultEtcdBaseURL,
			Version:           common.DefaultEtcdVersion,
			OS:                common.DefaultEtcdOS,
			DataDir:           common.DefaultEtcdDataDir,
			BackupDir:         common.DefaultEtcdBackupDir,
			APIServerManifest: common.DefaultAPIServerManifest,
			UseSDK:            true,
			SDKTimeout:        "5m",
			FallbackToTool:    true,
			BackupRetention:   10,
			UseSDKRestore:     true,
			RestoreTimeout:    "5m",
			SkipHashCheck:     false,
			MarkCompacted:     false,
			DefaultNodeName:   "default",
			DefaultPeerURL:    "http://localhost:2380",
			UseSDKVerify:      true,
			VerifyTimeout:     "30s",
			DetailedVerify:    false,
		},
		Logging: LoggingConfig{
			Level:  common.DefaultLogLevel,
			Format: common.DefaultLogFormat,
			File:   "",
			Color:  common.DefaultColorOutput,
		},
		Cleanup: CleanupConfig{
			DefaultOlderThan:  common.DefaultCleanupOlderThan,
			DefaultDryRun:     common.DefaultDryRun,
			ConcurrentWorkers: common.DefaultConcurrentWorkers,
		},
		Monitoring: MonitoringConfig{
			Enabled:             true,
			Port:                8080,
			Host:                "0.0.0.0",
			MetricsInterval:     "30s",
			HealthCheckInterval: "30s",
			EnablePprof:         false,
		},
		API: APIConfig{
			Enabled:         false,
			Port:            8081,
			Host:            "0.0.0.0",
			ReadTimeout:     "30s",
			WriteTimeout:    "30s",
			IdleTimeout:     "60s",
			EnableCORS:      true,
			EnableAuth:      false,
			EnableRateLimit: false,
			DefaultVersion:  "v1",
		},
		ConfigReload: ConfigReloadConfig{
			Enabled:      true,
			DebounceTime: "500ms",
			Validate:     true,
			Backup:       true,
			MaxBackups:   10,
		},
		Performance: PerformanceConfig{
			Cache: CacheConfig{
				Enabled:         true,
				MaxSize:         1000,
				TTL:             "1h",
				CleanupInterval: "10m",
			},
			FileCache: FileCacheConfig{
				Enabled:     true,
				MaxSize:     100,
				MaxFileSize: "10MB",
			},
			AsyncHash: AsyncHashConfig{
				Enabled:   true,
				WorkerCnt: 4,
				QueueSize: 1000,
			},
		},
	}
}

// LoadConfig 从文件加载配置
func LoadConfig(configPath string) (*Config, error) {
	config := DefaultConfig()

	// 如果没有指定配置文件路径，尝试默认位置
	if configPath == "" {
		configPath = GetDefaultConfigPath()
	}

	// 检查配置文件是否存在
	if !common.FileExists(configPath) {
		// 配置文件不存在，返回默认配置
		return config, nil
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析 YAML
	if err := yaml.Unmarshal(data, config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return config, nil
}

// SaveConfig 保存配置到文件
func SaveConfig(config *Config, configPath string) error {
	if configPath == "" {
		configPath = GetDefaultConfigPath()
	}

	// 确保配置目录存在
	configDir := filepath.Dir(configPath)
	if err := common.CreateDirIfNotExists(configDir); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 序列化为 YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

// GetDefaultConfigPath 获取默认配置文件路径
func GetDefaultConfigPath() string {
	// 优先使用环境变量
	if configPath := os.Getenv("K8S_HELPER_CONFIG"); configPath != "" {
		return configPath
	}

	// 使用用户主目录下的配置文件
	home, err := os.UserHomeDir()
	if err != nil {
		return ".k8s-helper.yaml"
	}

	return filepath.Join(home, ".k8s-helper.yaml")
}

// Validate 验证配置的有效性
func (c *Config) Validate() error {
	// 验证日志级别
	validLogLevels := []string{"debug", "info", "warn", "error"}
	if !common.Contains(validLogLevels, c.Logging.Level) {
		return fmt.Errorf("无效的日志级别: %s，支持的级别: %v", c.Logging.Level, validLogLevels)
	}

	// 验证日志格式
	validLogFormats := []string{"text", "json"}
	if !common.Contains(validLogFormats, c.Logging.Format) {
		return fmt.Errorf("无效的日志格式: %s，支持的格式: %v", c.Logging.Format, validLogFormats)
	}

	// 验证清理工作线程数
	if c.Cleanup.ConcurrentWorkers < 1 || c.Cleanup.ConcurrentWorkers > 50 {
		return fmt.Errorf("清理工作线程数必须在 1-50 之间，当前值: %d", c.Cleanup.ConcurrentWorkers)
	}

	// 验证监控端口
	if c.Monitoring.Port < 1 || c.Monitoring.Port > 65535 {
		return fmt.Errorf("监控端口必须在 1-65535 之间，当前值: %d", c.Monitoring.Port)
	}

	// 验证API端口
	if c.API.Port < 1 || c.API.Port > 65535 {
		return fmt.Errorf("API端口必须在 1-65535 之间，当前值: %d", c.API.Port)
	}

	// 验证缓存配置
	if c.Performance.Cache.MaxSize < 0 {
		return fmt.Errorf("缓存最大条目数不能为负数，当前值: %d", c.Performance.Cache.MaxSize)
	}

	if c.Performance.FileCache.MaxSize < 0 {
		return fmt.Errorf("文件缓存最大数量不能为负数，当前值: %d", c.Performance.FileCache.MaxSize)
	}

	return nil
}

// MergeFromEnv 从环境变量合并配置
func (c *Config) MergeFromEnv() {
	// 全局配置
	if kubeconfig := os.Getenv("KUBECONFIG"); kubeconfig != "" {
		c.Global.Kubeconfig = kubeconfig
	}
	if namespace := os.Getenv("K8S_HELPER_NAMESPACE"); namespace != "" {
		c.Global.Namespace = namespace
	}
	if verbose := os.Getenv("K8S_HELPER_VERBOSE"); verbose == "true" {
		c.Global.Verbose = true
	}
	if force := os.Getenv("K8S_HELPER_FORCE"); force == "true" {
		c.Global.Force = true
	}

	// ETCD 配置
	if baseURL := os.Getenv("K8S_HELPER_ETCD_BASE_URL"); baseURL != "" {
		c.ETCD.BaseURL = baseURL
	}
	if version := os.Getenv("K8S_HELPER_ETCD_VERSION"); version != "" {
		c.ETCD.Version = version
	}
	if dataDir := os.Getenv("K8S_HELPER_ETCD_DATA_DIR"); dataDir != "" {
		c.ETCD.DataDir = dataDir
	}

	// 日志配置
	if level := os.Getenv("K8S_HELPER_LOG_LEVEL"); level != "" {
		c.Logging.Level = level
	}
	if format := os.Getenv("K8S_HELPER_LOG_FORMAT"); format != "" {
		c.Logging.Format = format
	}
	if file := os.Getenv("K8S_HELPER_LOG_FILE"); file != "" {
		c.Logging.File = file
	}

	// 监控配置
	if port := os.Getenv("K8S_HELPER_MONITORING_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			c.Monitoring.Port = p
		}
	}
	if host := os.Getenv("K8S_HELPER_MONITORING_HOST"); host != "" {
		c.Monitoring.Host = host
	}

	// API配置
	if port := os.Getenv("K8S_HELPER_API_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			c.API.Port = p
		}
	}
	if host := os.Getenv("K8S_HELPER_API_HOST"); host != "" {
		c.API.Host = host
	}
}

// GenerateExampleConfig 生成示例配置文件
func GenerateExampleConfig(configPath string) error {

	// 添加注释的配置内容
	configContent := `# K8s-Helper 配置文件
# 配置优先级：命令行参数 > 环境变量 > 配置文件 > 默认值

# 全局配置
global:
  # Kubeconfig 文件路径（空表示使用默认路径）
  kubeconfig: ""
  
  # 默认命名空间
  namespace: "default"
  
  # 是否启用详细输出
  verbose: false
  
  # 是否启用强制模式（跳过确认）
  force: false

# ETCD 配置
etcd:
  # ETCD 工具下载基础 URL
  base_url: "https://wutong-paas.obs.cn-east-3.myhuaweicloud.com/kubeadm-ansible/etcd"
  
  # ETCD 版本
  version: "v3.5.15"
  
  # 操作系统
  os: "linux"
  
  # 默认数据目录
  data_dir: "/var/lib/etcd"
  
  # 默认备份输出目录
  backup_dir: "./backups"
  
  # kube-apiserver 配置文件路径
  apiserver_manifest: "/etc/kubernetes/manifests/kube-apiserver.yaml"

  # 是否优先使用Go SDK进行备份
  use_sdk: true

  # SDK操作超时时间
  sdk_timeout: "5m"

  # SDK失败时是否回退到etcdctl工具
  fallback_to_tool: true

  # 备份文件保留数量（0表示不限制）
  backup_retention: 10

  # 是否优先使用Go SDK进行恢复
  use_sdk_restore: true

  # 恢复操作超时时间
  restore_timeout: "5m"

  # 是否跳过哈希检查（仅在快照文件损坏时使用）
  skip_hash_check: false

  # 是否标记为压缩（高级选项）
  mark_compacted: false

  # 默认节点名称
  default_node_name: "default"

  # 默认对等URL
  default_peer_url: "http://localhost:2380"

  # 是否优先使用Go SDK进行验证
  use_sdk_verify: true

  # 验证操作超时时间
  verify_timeout: "30s"

  # 是否显示详细验证信息
  detailed_verify: false

# 日志配置
logging:
  # 日志级别：debug, info, warn, error
  level: "info"
  
  # 日志格式：text, json
  format: "text"
  
  # 日志文件路径（空表示输出到标准输出）
  file: ""
  
  # 是否启用颜色输出
  color: true

# 清理配置
cleanup:
  # 默认清理时间阈值
  default_older_than: "1h"
  
  # 是否默认启用干运行模式
  default_dry_run: false
  
  # 并发清理的 goroutine 数量
  concurrent_workers: 5

# 监控配置
monitoring:
  enabled: true
  port: 8080
  host: "0.0.0.0"
  metrics_interval: "30s"     # 指标收集间隔
  health_check_interval: "30s" # 健康检查间隔
  enable_pprof: false         # 是否启用 pprof

# API 服务配置
api:
  enabled: false
  port: 8081
  host: "0.0.0.0"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  enable_cors: true
  enable_auth: false
  enable_rate_limit: false
  default_version: "v1"

# 配置热重载
config_reload:
  enabled: true
  debounce_time: "500ms"      # 防抖时间
  validate_config: true       # 重载前验证配置
  backup_config: true         # 备份当前配置
  max_backups: 10            # 最大备份数量

# 性能优化配置
performance:
  cache:
    enabled: true
    max_size: 1000            # 最大缓存条目数
    ttl: "1h"                # 缓存过期时间
    cleanup_interval: "10m"   # 清理间隔

  file_cache:
    enabled: true
    max_size: 100             # 最大文件缓存数
    max_file_size: "10MB"     # 最大单文件缓存大小

  async_hasher:
    enabled: true
    worker_count: 4           # 异步哈希工作线程数
    queue_size: 1000         # 队列大小
`

	// 确保配置目录存在
	configDir := filepath.Dir(configPath)
	if err := common.CreateDirIfNotExists(configDir); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 写入配置文件
	if err := os.WriteFile(configPath, []byte(configContent), 0644); err != nil {
		return fmt.Errorf("写入示例配置文件失败: %w", err)
	}

	return nil
}
