{{define "etcd"}}
<div class="module-content">
    <div class="etcd-management">
        <!-- ETCD 备份区域 -->
        <div class="section backup-section">
            <h3>💾 ETCD 备份</h3>
            <div class="section-content">
                <div class="form-group">
                    <label for="backup-output" class="form-label">备份输出路径:</label>
                    <input type="text" id="backup-output" class="form-control" 
                           placeholder="留空使用默认路径 (./etcd-backup-YYYYMMDD-HHMMSS.db)" value="">
                    <small class="form-text">默认路径会自动生成时间戳</small>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="performBackup()">
                        <span class="loading" id="backup-loading" style="display: none;"></span>
                        <i class="icon">💾</i> 创建备份
                    </button>
                    <button class="btn btn-secondary" onclick="loadBackupList()">
                        <i class="icon">🔄</i> 刷新列表
                    </button>
                </div>
                
                <div id="backup-result" class="result-display"></div>
            </div>
        </div>
        
        <!-- 备份文件列表区域 -->
        <div class="section backup-list-section">
            <h3>📋 备份文件列表</h3>
            <div class="section-content">
                <div id="backup-files-list" class="backup-files-grid">
                    <div class="loading-placeholder">
                        <p>正在加载备份文件列表...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- ETCD 恢复区域 -->
        <div class="section restore-section">
            <h3>🔄 ETCD 恢复</h3>
            <div class="section-content">
                <div class="warning-notice">
                    <i class="icon">⚠️</i>
                    <strong>警告:</strong> ETCD恢复操作会覆盖现有数据，请确保在维护窗口期间执行。
                </div>
                
                <div class="form-group">
                    <label for="restore-snapshot" class="form-label">快照文件路径:</label>
                    <input type="text" id="restore-snapshot" class="form-control" 
                           placeholder="./snapshot.db">
                </div>
                
                <div class="form-group">
                    <label for="restore-data-dir" class="form-label">数据目录:</label>
                    <input type="text" id="restore-data-dir" class="form-control" 
                           placeholder="/var/lib/etcd">
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-warning" onclick="performRestore()">
                        <span class="loading" id="restore-loading" style="display: none;"></span>
                        <i class="icon">🔄</i> 恢复 ETCD
                    </button>
                </div>
                
                <div id="restore-result" class="result-display"></div>
            </div>
        </div>
        
        <!-- 快照验证区域 -->
        <div class="section verify-section">
            <h3>✅ 快照验证</h3>
            <div class="section-content">
                <div class="form-group">
                    <label for="verify-snapshot" class="form-label">快照文件路径:</label>
                    <input type="text" id="verify-snapshot" class="form-control" 
                           placeholder="./snapshot.db">
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="performVerify()">
                        <span class="loading" id="verify-loading" style="display: none;"></span>
                        <i class="icon">✅</i> 验证快照
                    </button>
                </div>
                
                <div id="verify-result" class="result-display"></div>
            </div>
        </div>
        
        <!-- ETCD 状态监控区域 -->
        <div class="section status-section">
            <h3>📊 ETCD 状态</h3>
            <div class="section-content">
                <div class="status-grid">
                    <div class="status-card">
                        <h4>连接状态</h4>
                        <div id="etcd-connection-status" class="status-value">检查中...</div>
                    </div>
                    <div class="status-card">
                        <h4>数据库大小</h4>
                        <div id="etcd-db-size" class="status-value">--</div>
                    </div>
                    <div class="status-card">
                        <h4>最后备份</h4>
                        <div id="etcd-last-backup" class="status-value">--</div>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-secondary" onclick="checkETCDStatus()">
                        <i class="icon">🔍</i> 检查状态
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}
