package service

import (
	"sync"
	"time"

	"go.uber.org/zap"
)

// GenericCacheEntry 通用缓存条目
type GenericCacheEntry[V any] struct {
	Value      V
	CreatedAt  time.Time
	AccessedAt time.Time
	ExpiresAt  time.Time
	HitCount   int64
	Metadata   map[string]interface{}
}

// IsExpired 检查条目是否过期
func (e *GenericCacheEntry[V]) IsExpired() bool {
	return !e.ExpiresAt.IsZero() && time.Now().After(e.ExpiresAt)
}

// Touch 更新访问时间和命中次数
func (e *GenericCacheEntry[V]) Touch() {
	e.AccessedAt = time.Now()
	e.HitCount++
}

// Cache 通用缓存接口
type Cache[K comparable, V any] interface {
	// Get 获取缓存值
	Get(key K) (V, bool)

	// Set 设置缓存值
	Set(key K, value V, ttl time.Duration)

	// SetWithMetadata 设置缓存值和元数据
	SetWithMetadata(key K, value V, ttl time.Duration, metadata map[string]interface{})

	// Delete 删除缓存值
	Delete(key K) bool

	// Clear 清空所有缓存
	Clear()

	// Size 获取缓存大小
	Size() int

	// Keys 获取所有键
	Keys() []K

	// GetStats 获取统计信息
	GetStats() CacheStats

	// Close 关闭缓存
	Close() error
}

// CacheStats 缓存统计信息
type CacheStats struct {
	Size      int           `json:"size"`
	MaxSize   int           `json:"max_size"`
	TotalHits int64         `json:"total_hits"`
	TTL       time.Duration `json:"ttl"`
	HitRate   float64       `json:"hit_rate"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	MaxSize         int           // 最大缓存条目数
	DefaultTTL      time.Duration // 默认TTL
	CleanupInterval time.Duration // 清理间隔
	EnableStats     bool          // 是否启用统计
}

// DefaultCacheConfig 默认缓存配置
func DefaultCacheConfig() *CacheConfig {
	return &CacheConfig{
		MaxSize:         1000,
		DefaultTTL:      30 * time.Minute,
		CleanupInterval: 5 * time.Minute,
		EnableStats:     true,
	}
}

// GenericCache 通用缓存实现
type GenericCache[K comparable, V any] struct {
	config        *CacheConfig
	logger        *zap.Logger
	cache         map[K]*GenericCacheEntry[V]
	mutex         sync.RWMutex
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
	totalRequests int64
	totalHits     int64
}

// NewGenericCache 创建通用缓存
func NewGenericCache[K comparable, V any](config *CacheConfig, logger *zap.Logger) *GenericCache[K, V] {
	if config == nil {
		config = DefaultCacheConfig()
	}

	cache := &GenericCache[K, V]{
		config:      config,
		logger:      logger,
		cache:       make(map[K]*GenericCacheEntry[V]),
		stopCleanup: make(chan struct{}),
	}

	// 启动定期清理
	if config.CleanupInterval > 0 {
		cache.startCleanup()
	}

	return cache
}

// Get 获取缓存值
func (gc *GenericCache[K, V]) Get(key K) (V, bool) {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()

	var zero V
	if gc.config.EnableStats {
		gc.totalRequests++
	}

	entry, exists := gc.cache[key]
	if !exists {
		return zero, false
	}

	// 检查是否过期
	if entry.IsExpired() {
		gc.logger.Debug("缓存条目已过期", zap.Any("key", key))
		return zero, false
	}

	// 更新访问统计
	entry.Touch()
	if gc.config.EnableStats {
		gc.totalHits++
	}

	gc.logger.Debug("缓存命中",
		zap.Any("key", key),
		zap.Int64("hitCount", entry.HitCount))

	return entry.Value, true
}

// Set 设置缓存值
func (gc *GenericCache[K, V]) Set(key K, value V, ttl time.Duration) {
	gc.SetWithMetadata(key, value, ttl, nil)
}

// SetWithMetadata 设置缓存值和元数据
func (gc *GenericCache[K, V]) SetWithMetadata(key K, value V, ttl time.Duration, metadata map[string]interface{}) {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()

	// 检查缓存大小限制
	if len(gc.cache) >= gc.config.MaxSize {
		gc.evictLRU()
	}

	now := time.Now()
	var expiresAt time.Time
	if ttl > 0 {
		expiresAt = now.Add(ttl)
	} else if gc.config.DefaultTTL > 0 {
		expiresAt = now.Add(gc.config.DefaultTTL)
	}

	entry := &GenericCacheEntry[V]{
		Value:      value,
		CreatedAt:  now,
		AccessedAt: now,
		ExpiresAt:  expiresAt,
		HitCount:   0,
		Metadata:   metadata,
	}

	gc.cache[key] = entry

	gc.logger.Debug("缓存已设置",
		zap.Any("key", key),
		zap.Time("expiresAt", expiresAt),
		zap.Int("cacheSize", len(gc.cache)))
}

// Delete 删除缓存值
func (gc *GenericCache[K, V]) Delete(key K) bool {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()

	_, exists := gc.cache[key]
	if exists {
		delete(gc.cache, key)
		gc.logger.Debug("缓存条目已删除", zap.Any("key", key))
	}

	return exists
}

// Clear 清空所有缓存
func (gc *GenericCache[K, V]) Clear() {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()

	size := len(gc.cache)
	gc.cache = make(map[K]*GenericCacheEntry[V])
	gc.totalRequests = 0
	gc.totalHits = 0

	gc.logger.Info("缓存已清空", zap.Int("clearedEntries", size))
}

// Size 获取缓存大小
func (gc *GenericCache[K, V]) Size() int {
	gc.mutex.RLock()
	defer gc.mutex.RUnlock()
	return len(gc.cache)
}

// Keys 获取所有键
func (gc *GenericCache[K, V]) Keys() []K {
	gc.mutex.RLock()
	defer gc.mutex.RUnlock()

	keys := make([]K, 0, len(gc.cache))
	for key := range gc.cache {
		keys = append(keys, key)
	}
	return keys
}

// GetStats 获取统计信息
func (gc *GenericCache[K, V]) GetStats() CacheStats {
	gc.mutex.RLock()
	defer gc.mutex.RUnlock()

	var hitRate float64
	if gc.totalRequests > 0 {
		hitRate = float64(gc.totalHits) / float64(gc.totalRequests)
	}

	return CacheStats{
		Size:      len(gc.cache),
		MaxSize:   gc.config.MaxSize,
		TotalHits: gc.totalHits,
		TTL:       gc.config.DefaultTTL,
		HitRate:   hitRate,
	}
}

// Close 关闭缓存
func (gc *GenericCache[K, V]) Close() error {
	if gc.stopCleanup != nil {
		close(gc.stopCleanup)
	}

	gc.Clear()
	gc.logger.Info("通用缓存已关闭")
	return nil
}

// evictLRU 驱逐最近最少使用的条目
func (gc *GenericCache[K, V]) evictLRU() {
	var oldestKey K
	var oldestEntry *GenericCacheEntry[V]
	var found bool

	for key, entry := range gc.cache {
		if !found {
			oldestKey = key
			oldestEntry = entry
			found = true
		} else {
			// 首先比较访问时间，如果相同则比较创建时间
			shouldEvict := entry.AccessedAt.Before(oldestEntry.AccessedAt) ||
				(entry.AccessedAt.Equal(oldestEntry.AccessedAt) && entry.CreatedAt.Before(oldestEntry.CreatedAt))

			if shouldEvict {
				oldestKey = key
				oldestEntry = entry
			}
		}
	}

	if found {
		delete(gc.cache, oldestKey)
		gc.logger.Debug("驱逐LRU缓存条目",
			zap.Any("key", oldestKey),
			zap.Time("accessedAt", oldestEntry.AccessedAt),
			zap.Time("createdAt", oldestEntry.CreatedAt))
	}
}

// startCleanup 启动定期清理
func (gc *GenericCache[K, V]) startCleanup() {
	gc.cleanupTicker = time.NewTicker(gc.config.CleanupInterval)

	go func() {
		for {
			select {
			case <-gc.cleanupTicker.C:
				gc.cleanup()
			case <-gc.stopCleanup:
				gc.cleanupTicker.Stop()
				return
			}
		}
	}()
}

// cleanup 清理过期缓存
func (gc *GenericCache[K, V]) cleanup() {
	gc.mutex.Lock()
	defer gc.mutex.Unlock()

	expiredKeys := make([]K, 0)

	for key, entry := range gc.cache {
		if entry.IsExpired() {
			expiredKeys = append(expiredKeys, key)
		}
	}

	for _, key := range expiredKeys {
		delete(gc.cache, key)
	}

	if len(expiredKeys) > 0 {
		gc.logger.Debug("清理过期缓存",
			zap.Int("expired", len(expiredKeys)),
			zap.Int("remaining", len(gc.cache)))
	}
}

// CacheManager 缓存管理器
type CacheManager struct {
	logger *zap.Logger
	caches map[string]interface{}
	mutex  sync.RWMutex
}

// NewCacheManager 创建缓存管理器
func NewCacheManager(logger *zap.Logger) *CacheManager {
	return &CacheManager{
		logger: logger,
		caches: make(map[string]interface{}),
	}
}

// GetOrCreateStringCache 获取或创建字符串缓存
func (cm *CacheManager) GetOrCreateStringCache(name string, config *CacheConfig) Cache[string, string] {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cache, exists := cm.caches[name]; exists {
		if typedCache, ok := cache.(Cache[string, string]); ok {
			return typedCache
		}
	}

	cache := NewGenericCache[string, string](config, cm.logger.With(zap.String("cache", name)))
	cm.caches[name] = cache

	cm.logger.Info("创建新缓存", zap.String("name", name))
	return cache
}

// GetOrCreateIntCache 获取或创建整数缓存
func (cm *CacheManager) GetOrCreateIntCache(name string, config *CacheConfig) Cache[string, int] {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cache, exists := cm.caches[name]; exists {
		if typedCache, ok := cache.(Cache[string, int]); ok {
			return typedCache
		}
	}

	cache := NewGenericCache[string, int](config, cm.logger.With(zap.String("cache", name)))
	cm.caches[name] = cache

	cm.logger.Info("创建新缓存", zap.String("name", name))
	return cache
}

// GetOrCreateInterfaceCache 获取或创建interface{}缓存
func (cm *CacheManager) GetOrCreateInterfaceCache(name string, config *CacheConfig) Cache[string, interface{}] {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cache, exists := cm.caches[name]; exists {
		if typedCache, ok := cache.(Cache[string, interface{}]); ok {
			return typedCache
		}
	}

	cache := NewGenericCache[string, interface{}](config, cm.logger.With(zap.String("cache", name)))
	cm.caches[name] = cache

	cm.logger.Info("创建新缓存", zap.String("name", name))
	return cache
}

// CloseAll 关闭所有缓存
func (cm *CacheManager) CloseAll() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	for name, cache := range cm.caches {
		if closer, ok := cache.(interface{ Close() error }); ok {
			if err := closer.Close(); err != nil {
				cm.logger.Error("关闭缓存失败", zap.String("name", name), zap.Error(err))
			}
		}
	}

	cm.caches = make(map[string]interface{})
	cm.logger.Info("所有缓存已关闭")
	return nil
}

// GetAllStats 获取所有缓存的统计信息
func (cm *CacheManager) GetAllStats() map[string]interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	stats := make(map[string]interface{})
	for name, cache := range cm.caches {
		if statsProvider, ok := cache.(interface{ GetStats() CacheStats }); ok {
			stats[name] = statsProvider.GetStats()
		}
	}

	return stats
}
