package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// CleanupService 资源清理服务
type CleanupService struct {
	clientset kubernetes.Interface
}

// NewCleanupService 创建资源清理服务
func NewCleanupService(clientset kubernetes.Interface) *CleanupService {
	return &CleanupService{
		clientset: clientset,
	}
}

// CleanupRequest 清理请求
type CleanupRequest struct {
	Type        string            `json:"type" binding:"required"`        // pods, jobs, failed-pods, pending-pods, all
	Namespaces  []string          `json:"namespaces"`                     // 要清理的命名空间列表
	DryRun      bool              `json:"dry_run"`                        // 预览模式
	OlderThan   string            `json:"older_than,omitempty"`           // 时间过滤器 (1h, 24h, 7d)
	Filters     map[string]string `json:"filters,omitempty"`              // 额外过滤条件
	Force       bool              `json:"force"`                          // 强制删除，跳过确认
}

// CleanupResult 清理结果
type CleanupResult struct {
	Resources       []CleanupResource `json:"resources"`
	Stats           CleanupStats      `json:"stats"`
	ProcessedNS     []string          `json:"processed_namespaces"`
	Errors          []string          `json:"errors"`
	DryRun          bool              `json:"dry_run"`
	ExecutionTime   time.Duration     `json:"execution_time"`
	Timestamp       time.Time         `json:"timestamp"`
}

// CleanupResource 待清理的资源
type CleanupResource struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Type        string            `json:"type"`        // Pod, Job
	Status      string            `json:"status"`      // Evicted, Failed, Pending, Completed
	Reason      string            `json:"reason,omitempty"`
	CreatedAt   time.Time         `json:"created_at"`
	Age         string            `json:"age"`
	Labels      map[string]string `json:"labels,omitempty"`
	OwnerRef    string            `json:"owner_ref,omitempty"`
}

// CleanupStats 清理统计信息
type CleanupStats struct {
	EvictedPods      int `json:"evicted_pods"`
	FailedPods       int `json:"failed_pods"`
	PendingPods      int `json:"pending_pods"`
	CompletedJobs    int `json:"completed_jobs"`
	FailedJobs       int `json:"failed_jobs"`
	TotalScanned     int `json:"total_scanned"`
	TotalCleaned     int `json:"total_cleaned"`
	ErrorCount       int `json:"error_count"`
}

// Execute 执行清理操作
func (cs *CleanupService) Execute(req CleanupRequest) (*CleanupResult, error) {
	startTime := time.Now()
	
	result := &CleanupResult{
		Resources:     []CleanupResource{},
		Stats:         CleanupStats{},
		ProcessedNS:   []string{},
		Errors:        []string{},
		DryRun:        req.DryRun,
		Timestamp:     startTime,
	}

	// 解析时间过滤器
	var olderThanDuration time.Duration
	var err error
	if req.OlderThan != "" {
		olderThanDuration, err = time.ParseDuration(req.OlderThan)
		if err != nil {
			return nil, fmt.Errorf("无效的时间格式 '%s': %w", req.OlderThan, err)
		}
	}

	// 获取要清理的命名空间列表
	namespaces := req.Namespaces
	if len(namespaces) == 0 {
		namespaces = []string{"default"}
	}

	ctx := context.Background()

	// 处理每个命名空间
	for _, ns := range namespaces {
		result.ProcessedNS = append(result.ProcessedNS, ns)

		switch req.Type {
		case "pods":
			if err := cs.processEvictedPods(ctx, ns, olderThanDuration, req.DryRun, result); err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("处理命名空间 %s 的 Evicted Pod 失败: %v", ns, err))
			}
		case "failed-pods":
			if err := cs.processFailedPods(ctx, ns, olderThanDuration, req.DryRun, result); err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("处理命名空间 %s 的 Failed Pod 失败: %v", ns, err))
			}
		case "pending-pods":
			if err := cs.processPendingPods(ctx, ns, olderThanDuration, req.DryRun, result); err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("处理命名空间 %s 的 Pending Pod 失败: %v", ns, err))
			}
		case "jobs":
			if err := cs.processCompletedJobs(ctx, ns, olderThanDuration, req.DryRun, result); err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("处理命名空间 %s 的 Job 失败: %v", ns, err))
			}
		case "all":
			// 处理所有类型的资源
			if err := cs.processEvictedPods(ctx, ns, olderThanDuration, req.DryRun, result); err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("处理命名空间 %s 的 Evicted Pod 失败: %v", ns, err))
			}
			if err := cs.processFailedPods(ctx, ns, olderThanDuration, req.DryRun, result); err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("处理命名空间 %s 的 Failed Pod 失败: %v", ns, err))
			}
			if err := cs.processPendingPods(ctx, ns, olderThanDuration, req.DryRun, result); err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("处理命名空间 %s 的 Pending Pod 失败: %v", ns, err))
			}
			if err := cs.processCompletedJobs(ctx, ns, olderThanDuration, req.DryRun, result); err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("处理命名空间 %s 的 Job 失败: %v", ns, err))
			}
		default:
			return nil, fmt.Errorf("不支持的清理类型: %s", req.Type)
		}
	}

	result.ExecutionTime = time.Since(startTime)
	result.Stats.ErrorCount = len(result.Errors)
	result.Stats.TotalCleaned = result.Stats.EvictedPods + result.Stats.FailedPods + 
		result.Stats.PendingPods + result.Stats.CompletedJobs + result.Stats.FailedJobs

	return result, nil
}

// GetNamespaces 获取所有命名空间列表
func (cs *CleanupService) GetNamespaces(ctx context.Context) ([]string, error) {
	namespaces, err := cs.clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	var names []string
	for _, ns := range namespaces.Items {
		names = append(names, ns.Name)
	}

	return names, nil
}

// processEvictedPods 处理被驱逐的 Pod
func (cs *CleanupService) processEvictedPods(ctx context.Context, namespace string, 
	olderThan time.Duration, dryRun bool, result *CleanupResult) error {

	pods, err := cs.clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}

	for _, pod := range pods.Items {
		result.Stats.TotalScanned++
		
		if pod.Status.Phase == v1.PodFailed && pod.Status.Reason == "Evicted" {
			// 检查时间过滤器
			if olderThan > 0 && time.Since(pod.CreationTimestamp.Time) < olderThan {
				continue
			}

			resource := CleanupResource{
				Name:      pod.Name,
				Namespace: namespace,
				Type:      "Pod",
				Status:    "Evicted",
				Reason:    pod.Status.Reason,
				CreatedAt: pod.CreationTimestamp.Time,
				Age:       formatAge(pod.CreationTimestamp.Time),
				Labels:    pod.Labels,
				OwnerRef:  getOwnerReference(&pod),
			}

			result.Resources = append(result.Resources, resource)

			if !dryRun {
				err := cs.clientset.CoreV1().Pods(namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{})
				if err != nil {
					result.Errors = append(result.Errors, fmt.Sprintf("删除 Pod %s/%s 失败: %v", namespace, pod.Name, err))
				} else {
					result.Stats.EvictedPods++
				}
			}
		}
	}

	return nil
}

// processFailedPods 处理失败状态的 Pod（非 Evicted）
func (cs *CleanupService) processFailedPods(ctx context.Context, namespace string,
	olderThan time.Duration, dryRun bool, result *CleanupResult) error {

	pods, err := cs.clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}

	for _, pod := range pods.Items {
		result.Stats.TotalScanned++
		
		if pod.Status.Phase == v1.PodFailed && pod.Status.Reason != "Evicted" {
			// 检查时间过滤器
			if olderThan > 0 && time.Since(pod.CreationTimestamp.Time) < olderThan {
				continue
			}

			resource := CleanupResource{
				Name:      pod.Name,
				Namespace: namespace,
				Type:      "Pod",
				Status:    "Failed",
				Reason:    pod.Status.Reason,
				CreatedAt: pod.CreationTimestamp.Time,
				Age:       formatAge(pod.CreationTimestamp.Time),
				Labels:    pod.Labels,
				OwnerRef:  getOwnerReference(&pod),
			}

			result.Resources = append(result.Resources, resource)

			if !dryRun {
				err := cs.clientset.CoreV1().Pods(namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{})
				if err != nil {
					result.Errors = append(result.Errors, fmt.Sprintf("删除 Failed Pod %s/%s 失败: %v", namespace, pod.Name, err))
				} else {
					result.Stats.FailedPods++
				}
			}
		}
	}

	return nil
}

// processPendingPods 处理长时间处于 Pending 状态的 Pod
func (cs *CleanupService) processPendingPods(ctx context.Context, namespace string,
	olderThan time.Duration, dryRun bool, result *CleanupResult) error {

	pods, err := cs.clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}

	// 如果没有指定时间过滤器，默认为 1 小时
	if olderThan == 0 {
		olderThan = 1 * time.Hour
	}

	for _, pod := range pods.Items {
		result.Stats.TotalScanned++

		if pod.Status.Phase == v1.PodPending {
			// 检查是否长时间处于 Pending 状态
			if time.Since(pod.CreationTimestamp.Time) >= olderThan {
				// 检查是否有调度问题
				hasSchedulingIssues := false
				reason := "Unknown"
				for _, condition := range pod.Status.Conditions {
					if condition.Type == v1.PodScheduled && condition.Status == v1.ConditionFalse {
						if strings.Contains(condition.Reason, "Unschedulable") {
							hasSchedulingIssues = true
							reason = condition.Reason
							break
						}
					}
				}

				if hasSchedulingIssues {
					resource := CleanupResource{
						Name:      pod.Name,
						Namespace: namespace,
						Type:      "Pod",
						Status:    "Pending",
						Reason:    reason,
						CreatedAt: pod.CreationTimestamp.Time,
						Age:       formatAge(pod.CreationTimestamp.Time),
						Labels:    pod.Labels,
						OwnerRef:  getOwnerReference(&pod),
					}

					result.Resources = append(result.Resources, resource)

					if !dryRun {
						err := cs.clientset.CoreV1().Pods(namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{})
						if err != nil {
							result.Errors = append(result.Errors, fmt.Sprintf("删除 Pending Pod %s/%s 失败: %v", namespace, pod.Name, err))
						} else {
							result.Stats.PendingPods++
						}
					}
				}
			}
		}
	}

	return nil
}

// processCompletedJobs 处理已完成的 Job
func (cs *CleanupService) processCompletedJobs(ctx context.Context, namespace string,
	olderThan time.Duration, dryRun bool, result *CleanupResult) error {

	jobs, err := cs.clientset.BatchV1().Jobs(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}

	for _, job := range jobs.Items {
		result.Stats.TotalScanned++

		// 检查时间过滤器
		if olderThan > 0 && time.Since(job.CreationTimestamp.Time) < olderThan {
			continue
		}

		// 检查 Job 状态
		for _, condition := range job.Status.Conditions {
			if condition.Type == batchv1.JobComplete && condition.Status == v1.ConditionTrue {
				resource := CleanupResource{
					Name:      job.Name,
					Namespace: namespace,
					Type:      "Job",
					Status:    "Completed",
					Reason:    "JobComplete",
					CreatedAt: job.CreationTimestamp.Time,
					Age:       formatAge(job.CreationTimestamp.Time),
					Labels:    job.Labels,
					OwnerRef:  getJobOwnerReference(&job),
				}

				result.Resources = append(result.Resources, resource)

				if !dryRun {
					err := cs.clientset.BatchV1().Jobs(namespace).Delete(ctx, job.Name, metav1.DeleteOptions{
						PropagationPolicy: &[]metav1.DeletionPropagation{metav1.DeletePropagationForeground}[0],
					})
					if err != nil {
						result.Errors = append(result.Errors, fmt.Sprintf("删除 Job %s/%s 失败: %v", namespace, job.Name, err))
					} else {
						result.Stats.CompletedJobs++
					}
				}
				break
			} else if condition.Type == batchv1.JobFailed && condition.Status == v1.ConditionTrue {
				resource := CleanupResource{
					Name:      job.Name,
					Namespace: namespace,
					Type:      "Job",
					Status:    "Failed",
					Reason:    "JobFailed",
					CreatedAt: job.CreationTimestamp.Time,
					Age:       formatAge(job.CreationTimestamp.Time),
					Labels:    job.Labels,
					OwnerRef:  getJobOwnerReference(&job),
				}

				result.Resources = append(result.Resources, resource)

				if !dryRun {
					err := cs.clientset.BatchV1().Jobs(namespace).Delete(ctx, job.Name, metav1.DeleteOptions{
						PropagationPolicy: &[]metav1.DeletionPropagation{metav1.DeletePropagationForeground}[0],
					})
					if err != nil {
						result.Errors = append(result.Errors, fmt.Sprintf("删除 Failed Job %s/%s 失败: %v", namespace, job.Name, err))
					} else {
						result.Stats.FailedJobs++
					}
				}
				break
			}
		}
	}

	return nil
}

// 辅助函数
func formatAge(createdAt time.Time) string {
	duration := time.Since(createdAt)
	if duration < time.Minute {
		return fmt.Sprintf("%ds", int(duration.Seconds()))
	} else if duration < time.Hour {
		return fmt.Sprintf("%dm", int(duration.Minutes()))
	} else if duration < 24*time.Hour {
		return fmt.Sprintf("%dh", int(duration.Hours()))
	} else {
		return fmt.Sprintf("%dd", int(duration.Hours()/24))
	}
}

func getOwnerReference(pod *v1.Pod) string {
	if len(pod.OwnerReferences) > 0 {
		owner := pod.OwnerReferences[0]
		return fmt.Sprintf("%s/%s", owner.Kind, owner.Name)
	}
	return ""
}

func getJobOwnerReference(job *batchv1.Job) string {
	if len(job.OwnerReferences) > 0 {
		owner := job.OwnerReferences[0]
		return fmt.Sprintf("%s/%s", owner.Kind, owner.Name)
	}
	return ""
}
