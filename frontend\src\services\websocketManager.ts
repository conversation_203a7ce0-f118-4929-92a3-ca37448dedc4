import { EventEmitter } from 'eventemitter3';
import type {
  WebSocketManager as IWebSocketManager,
  WebSocketType,
  WebSocketConfig,
  WebSocketOptions,
  WebSocketMessage,
  WebSocketConnectionState,
  WebSocketReadyState
} from '@/types/websocket';

// 连接信息接口
interface WebSocketConnectionInfo {
  id: string;
  type: WebSocketType;
  config: WebSocketConfig;
  options: WebSocketOptions;
  ws: WebSocket | null;
  state: WebSocketConnectionState;
  reconnectTimeout?: NodeJS.Timeout;
  heartbeatInterval?: NodeJS.Timeout;
}

/**
 * WebSocket连接管理器
 * 
 * 功能特性：
 * - 多连接管理
 * - 连接池和资源清理
 * - 消息路由和广播
 * - 事件系统
 * - 自动重连管理
 */
export class WebSocketManager extends EventEmitter implements IWebSocketManager {
  private connections: Map<string, WebSocketConnectionInfo> = new Map();
  private typeMapping: Map<WebSocketType, Set<string>> = new Map();
  private messageHandlers: Map<string, (message: WebSocketMessage) => void> = new Map();

  constructor() {
    super();
    // eventemitter3 不支持 setMaxListeners 方法，移除此调用
  }



  /**
   * 创建WebSocket连接
   */
  createConnection(type: WebSocketType, config: WebSocketConfig, options: WebSocketOptions = {}): string {
    const id = this.generateConnectionId(type);
    
    const connectionInfo: WebSocketConnectionInfo = {
      id,
      type,
      config: {
        reconnect: true,
        reconnectInterval: 3000,
        maxReconnectAttempts: 5,
        heartbeatInterval: 30000,
        messageQueueSize: 100,
        timeout: 10000,
        ...config,
      },
      options,
      ws: null,
      state: {
        readyState: 'CLOSED',
        url: config.url,
        connected: false,
        connecting: false,
        reconnecting: false,
        reconnectAttempts: 0,
        messageQueue: [],
      },
    };

    this.connections.set(id, connectionInfo);
    
    // 更新类型映射
    if (!this.typeMapping.has(type)) {
      this.typeMapping.set(type, new Set());
    }
    this.typeMapping.get(type)!.add(id);

    // 自动连接
    this.connectSingle(id);

    this.emit('connection_created', { id, type, config });
    
    return id;
  }

  /**
   * 获取连接状态
   */
  getConnection(id: string): WebSocketConnectionState | null {
    const connection = this.connections.get(id);
    return connection ? connection.state : null;
  }

  /**
   * 移除连接
   */
  removeConnection(id: string): void {
    const connection = this.connections.get(id);
    if (!connection) return;

    // 断开连接
    this.disconnectSingle(id);

    // 清理类型映射
    const typeConnections = this.typeMapping.get(connection.type);
    if (typeConnections) {
      typeConnections.delete(id);
      if (typeConnections.size === 0) {
        this.typeMapping.delete(connection.type);
      }
    }

    // 移除连接
    this.connections.delete(id);

    this.emit('connection_removed', { id, type: connection.type });
  }

  /**
   * 连接所有WebSocket
   */
  connectAll(): void {
    for (const [id] of this.connections) {
      this.connectSingle(id);
    }
  }

  /**
   * 断开所有WebSocket连接
   */
  disconnectAll(): void {
    for (const [id] of this.connections) {
      this.disconnectSingle(id);
    }
  }

  /**
   * 广播消息到所有连接
   */
  broadcast(message: WebSocketMessage, excludeTypes: WebSocketType[] = []): void {
    for (const [id, connection] of this.connections) {
      if (!excludeTypes.includes(connection.type)) {
        this.sendToConnection(id, message);
      }
    }
  }

  /**
   * 发送消息到指定连接
   */
  sendToConnection(id: string, message: WebSocketMessage): boolean {
    const connection = this.connections.get(id);
    if (!connection || !connection.ws || connection.ws.readyState !== WebSocket.OPEN) {
      // 添加到消息队列
      if (connection) {
        connection.state.messageQueue.push(message);
        if (connection.state.messageQueue.length > connection.config.messageQueueSize!) {
          connection.state.messageQueue.shift();
        }
      }
      return false;
    }

    try {
      connection.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error(`WebSocket send error for connection ${id}:`, error);
      return false;
    }
  }

  /**
   * 获取活跃连接
   */
  getActiveConnections(): string[] {
    const activeConnections: string[] = [];
    for (const [id, connection] of this.connections) {
      if (connection.state.connected) {
        activeConnections.push(id);
      }
    }
    return activeConnections;
  }

  /**
   * 根据类型获取连接
   */
  getConnectionsByType(type: WebSocketType): string[] {
    const typeConnections = this.typeMapping.get(type);
    return typeConnections ? Array.from(typeConnections) : [];
  }

  /**
   * 检查连接是否已连接
   */
  isConnected(id: string): boolean {
    const connection = this.connections.get(id);
    return connection ? connection.state.connected : false;
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.disconnectAll();
    this.connections.clear();
    this.typeMapping.clear();
    this.messageHandlers.clear();
    this.removeAllListeners();
  }

  // 私有方法

  /**
   * 生成连接ID
   */
  private generateConnectionId(type: WebSocketType): string {
    return `ws_${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 连接单个WebSocket
   */
  private connectSingle(id: string): void {
    const connection = this.connections.get(id);
    if (!connection || connection.state.connecting) return;

    this.updateConnectionState(id, {
      connecting: true,
      reconnecting: connection.state.reconnectAttempts > 0,
      readyState: 'CONNECTING',
    });

    try {
      const ws = new WebSocket(connection.config.url, connection.config.protocols);
      connection.ws = ws;

      // 连接超时处理
      const timeoutId = setTimeout(() => {
        if (ws.readyState === WebSocket.CONNECTING) {
          ws.close();
          this.handleConnectionError(id, new Error('Connection timeout'));
        }
      }, connection.config.timeout);

      ws.onopen = (event) => {
        clearTimeout(timeoutId);
        
        this.updateConnectionState(id, {
          readyState: 'OPEN',
          connected: true,
          connecting: false,
          reconnecting: false,
          reconnectAttempts: 0,
          lastConnected: Date.now(),
          lastError: undefined,
        });

        this.startHeartbeat(id);
        this.processMessageQueue(id);
        
        connection.options.onOpen?.(event);
        this.emit('connection_opened', { id, type: connection.type });
      };

      ws.onclose = (event) => {
        clearTimeout(timeoutId);
        this.stopHeartbeat(id);
        
        this.updateConnectionState(id, {
          readyState: 'CLOSED',
          connected: false,
          connecting: false,
          lastDisconnected: Date.now(),
        });

        connection.options.onClose?.(event);
        this.emit('connection_closed', { id, type: connection.type, code: event.code, reason: event.reason });

        // 自动重连
        if (connection.config.reconnect && !event.wasClean && 
            connection.state.reconnectAttempts < connection.config.maxReconnectAttempts!) {
          this.scheduleReconnect(id);
        }
      };

      ws.onerror = (event) => {
        clearTimeout(timeoutId);
        this.handleConnectionError(id, new Error('WebSocket connection error'));
        connection.options.onError?.(event);
      };

      ws.onmessage = (event) => {
        this.handleMessage(id, event);
      };

    } catch (error) {
      this.handleConnectionError(id, error as Error);
    }
  }

  /**
   * 断开单个WebSocket连接
   */
  private disconnectSingle(id: string): void {
    const connection = this.connections.get(id);
    if (!connection) return;

    this.clearReconnectTimeout(id);
    this.stopHeartbeat(id);

    if (connection.ws) {
      connection.ws.close(1000, 'Manual disconnect');
      connection.ws = null;
    }

    this.updateConnectionState(id, {
      readyState: 'CLOSED',
      connected: false,
      connecting: false,
      reconnecting: false,
    });
  }

  /**
   * 更新连接状态
   */
  private updateConnectionState(id: string, updates: Partial<WebSocketConnectionState>): void {
    const connection = this.connections.get(id);
    if (connection) {
      connection.state = { ...connection.state, ...updates };
      this.emit('connection_state_changed', { id, state: connection.state });
    }
  }

  /**
   * 处理连接错误
   */
  private handleConnectionError(id: string, error: Error): void {
    this.updateConnectionState(id, {
      readyState: 'CLOSED',
      connected: false,
      connecting: false,
      lastError: error.message,
    });

    this.emit('connection_error', { id, error: error.message });
  }

  /**
   * 计划重连
   */
  private scheduleReconnect(id: string): void {
    const connection = this.connections.get(id);
    if (!connection) return;

    if (connection.state.reconnectAttempts >= connection.config.maxReconnectAttempts!) {
      connection.options.onReconnectFailed?.();
      this.emit('reconnect_failed', { id, type: connection.type });
      return;
    }

    const delay = Math.min(
      connection.config.reconnectInterval! * Math.pow(2, connection.state.reconnectAttempts),
      30000 // 最大30秒
    );

    this.updateConnectionState(id, {
      reconnectAttempts: connection.state.reconnectAttempts + 1,
    });

    connection.reconnectTimeout = setTimeout(() => {
      connection.options.onReconnect?.(connection.state.reconnectAttempts);
      this.emit('reconnecting', { id, type: connection.type, attempt: connection.state.reconnectAttempts });
      this.connectSingle(id);
    }, delay);
  }

  /**
   * 清除重连定时器
   */
  private clearReconnectTimeout(id: string): void {
    const connection = this.connections.get(id);
    if (connection?.reconnectTimeout) {
      clearTimeout(connection.reconnectTimeout);
      connection.reconnectTimeout = undefined;
    }
  }

  /**
   * 启动心跳检测
   */
  private startHeartbeat(id: string): void {
    const connection = this.connections.get(id);
    if (!connection || !connection.config.heartbeatInterval) return;

    connection.heartbeatInterval = setInterval(() => {
      if (connection.ws?.readyState === WebSocket.OPEN) {
        const heartbeatMessage: WebSocketMessage = {
          id: this.generateMessageId(),
          type: 'heartbeat',
          action: 'ping',
          timestamp: Date.now(),
        };
        this.sendToConnection(id, heartbeatMessage);
      }
    }, connection.config.heartbeatInterval);
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(id: string): void {
    const connection = this.connections.get(id);
    if (connection?.heartbeatInterval) {
      clearInterval(connection.heartbeatInterval);
      connection.heartbeatInterval = undefined;
    }
  }

  /**
   * 处理消息
   */
  private handleMessage(id: string, event: MessageEvent): void {
    const connection = this.connections.get(id);
    if (!connection) return;

    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      // 处理心跳响应
      if (message.type === 'heartbeat' && message.action === 'pong') {
        return;
      }

      // 调用连接特定的消息处理器
      connection.options.onMessage?.(message);

      // 触发全局消息事件
      this.emit('message', { id, type: connection.type, message });
      this.emit(`message:${message.type}`, { id, type: connection.type, message });

    } catch (error) {
      console.error(`WebSocket message parse error for connection ${id}:`, error);
      this.emit('message_error', { id, error: error.message });
    }
  }

  /**
   * 处理消息队列
   */
  private processMessageQueue(id: string): void {
    const connection = this.connections.get(id);
    if (!connection || !connection.state.connected) return;

    const queue = connection.state.messageQueue;
    while (queue.length > 0) {
      const message = queue.shift()!;
      if (!this.sendToConnection(id, message)) {
        // 如果发送失败，重新加入队列
        queue.unshift(message);
        break;
      }
    }
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 创建全局WebSocket管理器实例
export const websocketManager = new WebSocketManager();

// 导出默认实例
export default websocketManager;
