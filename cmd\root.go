package cmd

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

var (
	// kubeconfig 文件路径
	kubeconfig string
	// 详细输出模式
	verbose bool
	// 强制模式，跳过确认
	force bool
)

// rootCmd 代表没有调用子命令时的基础命令
var rootCmd = &cobra.Command{
	Use:   "k8s-helper",
	Short: "Kubernetes 集群管理助手工具",
	Long: `k8s-helper 是一个功能强大的 Kubernetes 集群管理助手工具。

它提供了以下核心功能：
• 获取集群信息和节点状态
• ETCD 数据备份与恢复
• Pod 日志实时查看
• 端口转发管理
• 资源清理工具
• CronJob 任务管理

使用示例：
  k8s-helper info                         # 显示集群信息
  k8s-helper etcd backup                  # 备份 ETCD 数据
  k8s-helper etcd cronjob create          # 创建 ETCD 备份 CronJob
  k8s-helper logs <pod-name>              # 查看 Pod 日志
  k8s-helper port-forward <pod> 8080      # 端口转发`,
	Version: "1.0.0",
}

// Execute 添加所有子命令到根命令并设置标志
// 这个函数被 main.main() 调用，只调用一次
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	cobra.OnInitialize(initConfig)

	// 全局标志
	rootCmd.PersistentFlags().StringVar(&kubeconfig, "kubeconfig", "",
		"kubeconfig 文件路径 (默认为 $HOME/.kube/config)")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false,
		"启用详细输出模式")
	rootCmd.PersistentFlags().BoolVar(&force, "force", false,
		"强制模式，跳过确认提示")

	// 添加子命令
	rootCmd.AddCommand(infoCmd)
	rootCmd.AddCommand(etcdCmd)
	rootCmd.AddCommand(logsCmd)
	rootCmd.AddCommand(portForwardCmd)
	rootCmd.AddCommand(cleanupCmd)
	rootCmd.AddCommand(configCmd)
	rootCmd.AddCommand(webCmd)
	rootCmd.AddCommand(completionCmd)
}

// initConfig 读取配置文件和环境变量
func initConfig() {
	if kubeconfig == "" {
		// 如果没有指定 kubeconfig，使用默认路径
		home, err := os.UserHomeDir()
		if err != nil {
			fmt.Fprintf(os.Stderr, "无法获取用户主目录: %v\n", err)
			return
		}
		kubeconfig = fmt.Sprintf("%s/.kube/config", home)
	}

	if verbose {
		fmt.Printf("使用 kubeconfig: %s\n", kubeconfig)
	}
}
