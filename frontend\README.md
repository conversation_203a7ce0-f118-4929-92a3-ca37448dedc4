# K8s-Helper Frontend

基于 React 18 + TypeScript + Vite 的现代化 Kubernetes 集群管理前端应用。

## 🚀 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Ant Design
- **状态管理**: Zustand + TanStack Query
- **路由**: React Router v6
- **网络请求**: Axios
- **实时通信**: Socket.io
- **测试**: Vitest + Testing Library
- **代码规范**: ESLint + Prettier

## 📁 项目结构

```
src/
├── components/          # 通用组件
│   ├── Layout/         # 布局组件
│   ├── Common/         # 通用UI组件
│   └── Charts/         # 图表组件
├── pages/              # 页面组件
│   ├── Dashboard/      # 仪表板
│   ├── ETCD/          # ETCD管理
│   ├── ClusterInfo/   # 集群信息
│   ├── Logs/          # 日志管理
│   ├── PortForward/   # 端口转发
│   ├── Cleanup/       # 资源清理
│   ├── Monitoring/    # 监控告警
│   └── Config/        # 配置管理
├── hooks/              # 自定义Hooks
├── services/           # API服务层
├── stores/             # 状态管理
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── styles/             # 样式文件
├── config/             # 配置文件
├── providers/          # Context Providers
└── test/               # 测试配置
```

## 🛠️ 开发指南

### 环境要求

- Node.js >= 18
- npm >= 9

### 安装依赖

```bash
npm install
```

### 开发命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 运行测试
npm run test

# 代码检查
npm run lint

# 代码格式化
npm run lint:fix
```

## 📝 开发规范

### 命名规范

- **组件**: PascalCase (如: `UserProfile`)
- **文件**: PascalCase for components, camelCase for others
- **变量/函数**: camelCase (如: `getUserInfo`)
- **常量**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)
- **类型/接口**: PascalCase (如: `UserInfo`)

### 路径别名

项目配置了路径别名，可以使用 `@` 前缀导入：

```typescript
import { Button } from '@/components';
import { useAppStore } from '@/stores';
import { formatTime } from '@/utils';
```
