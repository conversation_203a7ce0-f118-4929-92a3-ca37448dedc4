{{define "port-forward"}}
<div class="module-content">
    <div class="port-forward-container">
        <!-- 创建端口转发区域 -->
        <div class="section create-forward">
            <h3>🔗 创建端口转发</h3>
            <div class="section-content">
                <form id="create-forward-form" class="forward-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="pf-namespace" class="form-label">命名空间:</label>
                            <select id="pf-namespace" class="form-control" required>
                                <option value="">选择命名空间...</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="pf-pod" class="form-label">Pod:</label>
                            <select id="pf-pod" class="form-control" required disabled>
                                <option value="">请先选择命名空间</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="pf-local-port" class="form-label">本地端口:</label>
                            <input type="number" id="pf-local-port" class="form-control" 
                                   placeholder="留空自动分配" min="1" max="65535">
                            <small class="form-text">留空将自动分配可用端口</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="pf-pod-port" class="form-label">Pod端口:</label>
                            <input type="number" id="pf-pod-port" class="form-control" 
                                   placeholder="目标端口" min="1" max="65535" required>
                            <small class="form-text">Pod内服务监听的端口</small>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <span class="loading" id="create-loading" style="display: none;"></span>
                            <i class="icon">🔗</i> 创建转发
                        </button>
                        <button type="reset" class="btn btn-secondary">重置</button>
                    </div>
                </form>
                
                <div id="create-result" class="result-display"></div>
            </div>
        </div>
        
        <!-- 端口转发列表区域 -->
        <div class="section forwards-list">
            <h3>📋 活跃的端口转发</h3>
            <div class="section-content">
                <div class="list-controls">
                    <div class="search-group">
                        <input type="text" id="forward-search" class="form-control" placeholder="搜索端口转发...">
                        <button id="refresh-forwards" class="btn btn-secondary">
                            <span class="loading" id="refresh-loading" style="display: none;"></span>
                            🔄 刷新
                        </button>
                    </div>
                    
                    <div class="filter-group">
                        <select id="status-filter" class="form-control">
                            <option value="">所有状态</option>
                            <option value="running">运行中</option>
                            <option value="creating">创建中</option>
                            <option value="stopped">已停止</option>
                            <option value="error">错误</option>
                        </select>
                    </div>
                </div>
                
                <div class="forwards-grid" id="forwards-grid">
                    <div class="loading-placeholder">
                        <p>正在加载端口转发列表...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 端口转发统计区域 -->
        <div class="section forward-stats">
            <h3>📊 转发统计</h3>
            <div class="section-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">🔗</div>
                        <div class="stat-content">
                            <h4>总转发数</h4>
                            <div id="total-forwards" class="stat-value">0</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <h4>运行中</h4>
                            <div id="running-forwards" class="stat-value">0</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">⚠️</div>
                        <div class="stat-content">
                            <h4>错误状态</h4>
                            <div id="error-forwards" class="stat-value">0</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🔌</div>
                        <div class="stat-content">
                            <h4>占用端口</h4>
                            <div id="used-ports" class="stat-value">0</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 端口检查工具区域 -->
        <div class="section port-checker">
            <h3>🔍 端口检查工具</h3>
            <div class="section-content">
                <div class="checker-form">
                    <div class="form-group">
                        <label for="check-port" class="form-label">检查端口:</label>
                        <div class="input-group">
                            <input type="number" id="check-port" class="form-control" 
                                   placeholder="端口号" min="1" max="65535">
                            <button id="check-port-btn" class="btn btn-secondary">检查</button>
                        </div>
                    </div>
                    
                    <div id="port-check-result" class="check-result"></div>
                </div>
                
                <div class="port-suggestions">
                    <h4>常用端口建议:</h4>
                    <div class="port-tags">
                        <span class="port-tag" data-port="8080">8080 (HTTP)</span>
                        <span class="port-tag" data-port="8443">8443 (HTTPS)</span>
                        <span class="port-tag" data-port="3000">3000 (Node.js)</span>
                        <span class="port-tag" data-port="5000">5000 (Flask)</span>
                        <span class="port-tag" data-port="8000">8000 (Django)</span>
                        <span class="port-tag" data-port="9000">9000 (通用)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 端口转发卡片模板 -->
    <template id="forward-card-template">
        <div class="forward-card" data-id="" data-status="">
            <div class="card-header">
                <div class="forward-info">
                    <h4 class="forward-title"></h4>
                    <span class="forward-status"></span>
                </div>
                <div class="card-actions">
                    <button class="btn btn-sm btn-secondary test-btn" title="测试连接">🔗</button>
                    <button class="btn btn-sm btn-warning stop-btn" title="停止转发">⏹️</button>
                    <button class="btn btn-sm btn-danger delete-btn" title="删除转发">🗑️</button>
                </div>
            </div>
            
            <div class="card-body">
                <div class="forward-details">
                    <div class="detail-item">
                        <span class="detail-label">本地地址:</span>
                        <span class="detail-value local-address"></span>
                        <button class="btn btn-xs btn-secondary copy-btn" title="复制地址">📋</button>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">目标:</span>
                        <span class="detail-value target"></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">创建时间:</span>
                        <span class="detail-value created-time"></span>
                    </div>
                    <div class="detail-item error-message" style="display: none;">
                        <span class="detail-label">错误信息:</span>
                        <span class="detail-value error-text"></span>
                    </div>
                </div>
            </div>
        </div>
    </template>
</div>
{{end}}
