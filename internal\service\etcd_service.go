package service

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
	"k8s-helper/pkg/common"
	"k8s-helper/pkg/etcd"
)

// etcdService ETCD服务实现
type etcdService struct {
	logger             *zap.Logger
	toolManager        domain.ToolManager
	configParser       domain.ConfigParser
	tzConverter        domain.TimeZoneConverter
	clientManager      *ClientManager
	cronJobManager     *CronJobManager
	configAdapter      *ETCDConfigAdapter
	backupAdapter      *BackupResultAdapter
	restoreAdapter     *RestoreResultAdapter
	verifyAdapter      *VerifyResultAdapter
	restoreOptsAdapter *RestoreOptionsAdapter
	verifyOptsAdapter  *VerifyOptionsAdapter
	errorHandler       *ErrorHandler
}

// NewETCDService 创建ETCD服务
func NewETCDService(
	logger *zap.Logger,
	toolManager domain.ToolManager,
	configParser domain.ConfigParser,
	tzConverter domain.TimeZoneConverter,
	clientManager *ClientManager,
) domain.ETCDService {
	cronJobManager := NewCronJobManager(logger, clientManager, tzConverter)

	return &etcdService{
		logger:             logger,
		toolManager:        toolManager,
		configParser:       configParser,
		tzConverter:        tzConverter,
		clientManager:      clientManager,
		cronJobManager:     cronJobManager,
		configAdapter:      NewETCDConfigAdapter(),
		backupAdapter:      NewBackupResultAdapter(),
		restoreAdapter:     NewRestoreResultAdapter(),
		verifyAdapter:      NewVerifyResultAdapter(),
		restoreOptsAdapter: NewRestoreOptionsAdapter(),
		verifyOptsAdapter:  NewVerifyOptionsAdapter(),
		errorHandler:       NewErrorHandler(logger),
	}
}

// Backup 执行备份操作
func (s *etcdService) Backup(ctx context.Context, opts *domain.BackupOptions) (*domain.BackupResult, error) {
	s.logger.Info("开始ETCD备份操作", zap.String("output", opts.OutputPath))

	// 生成备份文件名（如果未指定）
	if opts.OutputPath == "" {
		if err := common.CreateDirIfNotExists(common.DefaultEtcdBackupDir); err != nil {
			return nil, fmt.Errorf("创建备份目录失败: %w", err)
		}
		timestamp := common.GetCurrentTimestamp(common.BackupTimeFormat)
		opts.OutputPath = filepath.Join(common.DefaultEtcdBackupDir, fmt.Sprintf("etcd-backup-%s.db", timestamp))
	}

	var result *domain.BackupResult
	var err error

	// 优先尝试SDK备份
	if opts.UseSDK {
		s.logger.Info("尝试使用SDK进行备份")
		result, err = s.performSDKBackup(ctx, opts)

		if err != nil && !opts.FallbackToTool {
			return nil, fmt.Errorf("SDK备份失败: %w", err)
		}

		if err == nil {
			s.logger.Info("SDK备份成功完成", zap.String("file", result.FilePath))
			return result, nil
		}

		s.logger.Warn("SDK备份失败，回退到工具备份", zap.Error(err))
	}

	// 使用工具备份
	if !opts.UseSDK || (err != nil && opts.FallbackToTool) {
		// 使用自定义下载配置确保工具可用
		if err := s.toolManager.EnsureToolsWithConfig(ctx, opts.DownloadBaseURL, opts.DownloadVersion, opts.DownloadOS); err != nil {
			return nil, fmt.Errorf("确保工具可用失败: %w", err)
		}

		result, err = s.performToolBackup(ctx, opts)
		if err != nil {
			return nil, fmt.Errorf("工具备份失败: %w", err)
		}

		s.logger.Info("工具备份成功完成", zap.String("file", result.FilePath))
	}

	return result, nil
}

// Restore 执行恢复操作
func (s *etcdService) Restore(ctx context.Context, opts *domain.RestoreOptions) (*domain.RestoreResult, error) {
	s.logger.Info("开始ETCD恢复操作",
		zap.String("snapshot", opts.SnapshotPath),
		zap.String("dataDir", opts.DataDir))

	// 检查快照文件是否存在
	if _, err := os.Stat(opts.SnapshotPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("快照文件不存在: %s", opts.SnapshotPath)
	}

	// 生成默认恢复目录（如果未指定）
	if opts.DataDir == "" {
		opts.DataDir = common.GenerateRestoreDir()
		s.logger.Info("使用默认恢复目录", zap.String("dataDir", opts.DataDir))
	}

	var result *domain.RestoreResult
	var err error

	// 优先尝试SDK恢复
	if opts.UseSDK {
		s.logger.Info("尝试使用SDK进行恢复")
		result, err = s.performSDKRestore(ctx, opts)

		if err != nil && !opts.FallbackToTool {
			return nil, fmt.Errorf("SDK恢复失败: %w", err)
		}

		if err == nil {
			s.logger.Info("SDK恢复成功完成", zap.String("dataDir", result.DataDir))
			return result, nil
		}

		s.logger.Warn("SDK恢复失败，回退到工具恢复", zap.Error(err))
	}

	// 使用工具恢复
	if !opts.UseSDK || (err != nil && opts.FallbackToTool) {
		if err := s.toolManager.EnsureTools(ctx); err != nil {
			return nil, fmt.Errorf("确保工具可用失败: %w", err)
		}

		result, err = s.performToolRestore(ctx, opts)
		if err != nil {
			return nil, fmt.Errorf("工具恢复失败: %w", err)
		}

		s.logger.Info("工具恢复成功完成", zap.String("dataDir", result.DataDir))
	}

	return result, nil
}

// Verify 验证快照文件
func (s *etcdService) Verify(ctx context.Context, opts *domain.VerifyOptions) (*domain.VerifyResult, error) {
	s.logger.Info("开始验证ETCD快照", zap.String("snapshot", opts.SnapshotPath))

	// 检查快照文件是否存在
	if _, err := os.Stat(opts.SnapshotPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("快照文件不存在: %s", opts.SnapshotPath)
	}

	var result *domain.VerifyResult
	var err error

	// 优先尝试SDK验证
	if opts.UseSDK {
		s.logger.Info("尝试使用SDK进行验证")
		result, err = s.performSDKVerify(ctx, opts)

		if err != nil && !opts.FallbackToTool {
			return nil, fmt.Errorf("SDK验证失败: %w", err)
		}

		if err == nil {
			s.logger.Info("SDK验证成功完成", zap.Bool("valid", result.Valid))
			return result, nil
		}

		s.logger.Warn("SDK验证失败，回退到工具验证", zap.Error(err))
	}

	// 使用工具验证
	if !opts.UseSDK || (err != nil && opts.FallbackToTool) {
		if err := s.toolManager.EnsureTools(ctx); err != nil {
			return nil, fmt.Errorf("确保工具可用失败: %w", err)
		}

		result, err = s.performToolVerify(ctx, opts)
		if err != nil {
			return nil, fmt.Errorf("工具验证失败: %w", err)
		}

		s.logger.Info("工具验证成功完成", zap.Bool("valid", result.Valid))
	}

	return result, nil
}

// CreateCronJob 创建备份CronJob
func (s *etcdService) CreateCronJob(ctx context.Context, opts *domain.CronJobOptions) error {
	s.logger.Info("创建ETCD备份CronJob",
		zap.String("name", opts.Name),
		zap.String("namespace", opts.Namespace))

	// 验证CronJob选项
	if err := s.validateCronJobOptions(opts); err != nil {
		return s.errorHandler.WrapError("validate_cronjob_options", err)
	}

	// 使用CronJob管理器创建CronJob
	if err := s.cronJobManager.CreateBackupCronJob(ctx, opts); err != nil {
		return s.errorHandler.WrapError("create_backup_cronjob", err)
	}

	s.logger.Info("ETCD备份CronJob创建成功",
		zap.String("name", opts.Name),
		zap.String("namespace", opts.Namespace))

	return nil
}

// ListCronJobs 列出CronJob
func (s *etcdService) ListCronJobs(ctx context.Context, namespace string) ([]domain.CronJobInfo, error) {
	s.logger.Info("列出ETCD备份CronJob", zap.String("namespace", namespace))

	// 使用CronJob管理器列出CronJob
	cronJobs, err := s.cronJobManager.ListCronJobs(ctx, namespace)
	if err != nil {
		return nil, s.errorHandler.WrapError("list_cronjobs", err)
	}

	// 转换为领域模型
	var result []domain.CronJobInfo
	for _, cronJob := range cronJobs.Items {
		info := domain.CronJobInfo{
			Name:      cronJob.Name,
			Namespace: cronJob.Namespace,
			Schedule:  cronJob.Spec.Schedule,
			Suspend:   cronJob.Spec.Suspend != nil && *cronJob.Spec.Suspend,
		}

		// 获取原始调度和时区信息
		if originalSchedule, exists := cronJob.Annotations["k8s-helper/original-schedule"]; exists {
			info.OriginalSchedule = originalSchedule
		}
		if timezone, exists := cronJob.Annotations["k8s-helper/timezone"]; exists {
			info.TimeZone = timezone
		}

		result = append(result, info)
	}

	return result, nil
}

// DeleteCronJob 删除CronJob
func (s *etcdService) DeleteCronJob(ctx context.Context, namespace, name string) error {
	s.logger.Info("删除ETCD备份CronJob",
		zap.String("namespace", namespace),
		zap.String("name", name))

	// 使用CronJob管理器删除CronJob
	if err := s.cronJobManager.DeleteCronJob(ctx, namespace, name); err != nil {
		return s.errorHandler.WrapError("delete_cronjob", err)
	}

	return nil
}

// SuspendCronJob 暂停CronJob
func (s *etcdService) SuspendCronJob(ctx context.Context, namespace, name string) error {
	s.logger.Info("暂停ETCD备份CronJob",
		zap.String("namespace", namespace),
		zap.String("name", name))

	// 使用CronJob管理器暂停CronJob
	if err := s.cronJobManager.SuspendCronJob(ctx, namespace, name); err != nil {
		return s.errorHandler.WrapError("suspend_cronjob", err)
	}

	s.logger.Info("ETCD备份CronJob暂停成功",
		zap.String("namespace", namespace),
		zap.String("name", name))

	return nil
}

// ResumeCronJob 恢复CronJob
func (s *etcdService) ResumeCronJob(ctx context.Context, namespace, name string) error {
	s.logger.Info("恢复ETCD备份CronJob",
		zap.String("namespace", namespace),
		zap.String("name", name))

	// 使用CronJob管理器恢复CronJob
	if err := s.cronJobManager.ResumeCronJob(ctx, namespace, name); err != nil {
		return s.errorHandler.WrapError("resume_cronjob", err)
	}

	s.logger.Info("ETCD备份CronJob恢复成功",
		zap.String("namespace", namespace),
		zap.String("name", name))

	return nil
}

// performSDKBackup 使用SDK执行备份
func (s *etcdService) performSDKBackup(ctx context.Context, opts *domain.BackupOptions) (*domain.BackupResult, error) {
	// 创建SDK客户端
	logger, err := s.createZapLogger()
	if err != nil {
		return nil, fmt.Errorf("创建logger失败: %w", err)
	}

	// 转换配置
	sdkConfig := s.configAdapter.DomainToSDK(opts.ETCDConfig)
	client, err := etcd.NewClient(sdkConfig, logger)
	if err != nil {
		return nil, fmt.Errorf("创建ETCD客户端失败: %w", err)
	}
	defer client.Close()

	// 测试连接
	testCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	if err := client.TestConnection(testCtx); err != nil {
		return nil, fmt.Errorf("SDK连接测试失败: %w", err)
	}

	// 执行备份
	backupCtx, backupCancel := context.WithTimeout(ctx, opts.Timeout)
	defer backupCancel()

	sdkResult, err := client.Backup(backupCtx, opts.OutputPath)
	if err != nil {
		return nil, err
	}

	// 转换结果
	return s.backupAdapter.SDKToDomain(sdkResult), nil
}

// performSDKRestore 使用SDK执行恢复
func (s *etcdService) performSDKRestore(ctx context.Context, opts *domain.RestoreOptions) (*domain.RestoreResult, error) {
	// 创建SDK客户端（用于离线操作）
	logger, err := s.createZapLogger()
	if err != nil {
		return nil, fmt.Errorf("创建logger失败: %w", err)
	}

	client := etcd.NewClientForOfflineOps(logger)

	// 转换恢复选项
	restoreOpts := s.restoreOptsAdapter.DomainToSDK(opts)

	sdkResult, err := client.RestoreWithOptions(ctx, restoreOpts)
	if err != nil {
		return nil, err
	}

	// 转换结果
	return s.restoreAdapter.SDKToDomain(sdkResult), nil
}

// performSDKVerify 使用SDK执行验证
func (s *etcdService) performSDKVerify(ctx context.Context, opts *domain.VerifyOptions) (*domain.VerifyResult, error) {
	// 创建SDK客户端（用于离线操作）
	logger, err := s.createZapLogger()
	if err != nil {
		return nil, fmt.Errorf("创建logger失败: %w", err)
	}

	client := etcd.NewClientForOfflineOps(logger)

	// 转换验证选项
	verifyOpts := s.verifyOptsAdapter.DomainToSDK(opts)

	sdkResult, err := client.VerifyWithOptions(ctx, verifyOpts)
	if err != nil {
		return nil, err
	}

	// 转换结果
	return s.verifyAdapter.SDKToDomain(sdkResult), nil
}

// performToolBackup 使用工具执行备份
func (s *etcdService) performToolBackup(ctx context.Context, opts *domain.BackupOptions) (*domain.BackupResult, error) {
	toolPath, err := s.toolManager.GetBackupRestoreTool()
	if err != nil {
		return nil, fmt.Errorf("获取备份工具失败: %w", err)
	}

	// 根据工具类型构建不同的命令参数
	args, err := s.buildBackupCommand(toolPath, opts)
	if err != nil {
		return nil, fmt.Errorf("构建备份命令失败: %w", err)
	}

	s.logger.Info("执行工具备份命令",
		zap.String("tool", filepath.Base(toolPath)),
		zap.Strings("args", args))

	// 执行备份命令
	startTime := time.Now()
	if err := s.executeCommand(ctx, toolPath, args, opts.Timeout); err != nil {
		return nil, fmt.Errorf("执行备份命令失败: %w", err)
	}
	duration := time.Since(startTime)

	// 获取文件信息
	fileInfo, err := os.Stat(opts.OutputPath)
	if err != nil {
		return nil, fmt.Errorf("获取备份文件信息失败: %w", err)
	}

	return &domain.BackupResult{
		FilePath: opts.OutputPath,
		Size:     fileInfo.Size(),
		Duration: duration,
	}, nil
}

// buildBackupCommand 根据工具类型构建备份命令参数
func (s *etcdService) buildBackupCommand(toolPath string, opts *domain.BackupOptions) ([]string, error) {
	toolName := filepath.Base(toolPath)

	switch toolName {
	case "etcdutl":
		// etcdutl 使用新的命令格式
		return s.buildEtcdutlBackupCommand(opts), nil
	case "etcdctl":
		// etcdctl 使用传统的命令格式
		return s.buildEtcdctlBackupCommand(opts), nil
	default:
		return nil, fmt.Errorf("不支持的ETCD工具: %s", toolName)
	}
}

// buildEtcdutlBackupCommand 构建etcdutl备份命令
func (s *etcdService) buildEtcdutlBackupCommand(opts *domain.BackupOptions) []string {
	args := []string{"snapshot", "save", opts.OutputPath}

	// etcdutl 使用 --endpoints 参数
	if opts.ETCDConfig.Servers != "" {
		args = append(args, "--endpoints", opts.ETCDConfig.Servers)
	}

	// 添加证书参数
	if opts.ETCDConfig.CaFile != "" {
		args = append(args, "--cacert", opts.ETCDConfig.CaFile)
	}
	if opts.ETCDConfig.CertFile != "" {
		args = append(args, "--cert", opts.ETCDConfig.CertFile)
	}
	if opts.ETCDConfig.KeyFile != "" {
		args = append(args, "--key", opts.ETCDConfig.KeyFile)
	}

	return args
}

// buildEtcdctlBackupCommand 构建etcdctl备份命令
func (s *etcdService) buildEtcdctlBackupCommand(opts *domain.BackupOptions) []string {
	args := []string{"snapshot", "save", opts.OutputPath}

	// etcdctl 使用 --endpoints 参数（格式相同）
	if opts.ETCDConfig.Servers != "" {
		args = append(args, "--endpoints", opts.ETCDConfig.Servers)
	}

	// 添加证书参数
	if opts.ETCDConfig.CaFile != "" {
		args = append(args, "--cacert", opts.ETCDConfig.CaFile)
	}
	if opts.ETCDConfig.CertFile != "" {
		args = append(args, "--cert", opts.ETCDConfig.CertFile)
	}
	if opts.ETCDConfig.KeyFile != "" {
		args = append(args, "--key", opts.ETCDConfig.KeyFile)
	}

	return args
}

// performToolRestore 使用工具执行恢复
func (s *etcdService) performToolRestore(ctx context.Context, opts *domain.RestoreOptions) (*domain.RestoreResult, error) {
	toolPath, err := s.toolManager.GetBackupRestoreTool()
	if err != nil {
		return nil, fmt.Errorf("获取恢复工具失败: %w", err)
	}

	// 构建命令参数
	args := []string{
		"snapshot", "restore", opts.SnapshotPath,
		"--data-dir=" + opts.DataDir,
	}

	if opts.Name != "" {
		args = append(args, "--name="+opts.Name)
	}
	if opts.InitialCluster != "" {
		args = append(args, "--initial-cluster="+opts.InitialCluster)
	}
	if opts.InitialAdvertisePeerURLs != "" {
		args = append(args, "--initial-advertise-peer-urls="+opts.InitialAdvertisePeerURLs)
	}
	if opts.SkipHashCheck {
		args = append(args, "--skip-hash-check")
	}
	if opts.MarkCompacted {
		args = append(args, "--mark-compacted")
	}

	// 执行恢复命令
	startTime := time.Now()
	if err := s.executeCommand(ctx, toolPath, args, opts.Timeout); err != nil {
		return nil, fmt.Errorf("执行恢复命令失败: %w", err)
	}
	duration := time.Since(startTime)

	// 获取快照文件信息
	fileInfo, err := os.Stat(opts.SnapshotPath)
	if err != nil {
		return nil, fmt.Errorf("获取快照文件信息失败: %w", err)
	}

	return &domain.RestoreResult{
		DataDir:      opts.DataDir,
		Duration:     duration,
		SnapshotSize: fileInfo.Size(),
	}, nil
}

// performToolVerify 使用工具执行验证
func (s *etcdService) performToolVerify(ctx context.Context, opts *domain.VerifyOptions) (*domain.VerifyResult, error) {
	toolPath, err := s.toolManager.GetVerifyTool()
	if err != nil {
		return nil, fmt.Errorf("获取验证工具失败: %w", err)
	}

	// 构建命令参数
	args := []string{
		"snapshot", "status", opts.SnapshotPath,
	}

	if opts.DetailedVerify {
		args = append(args, "--write-out=json")
	}

	// 执行验证命令
	startTime := time.Now()
	if err := s.executeCommand(ctx, toolPath, args, opts.Timeout); err != nil {
		return &domain.VerifyResult{
			Valid:        false,
			Duration:     time.Since(startTime),
			ErrorMessage: err.Error(),
		}, nil
	}
	duration := time.Since(startTime)

	return &domain.VerifyResult{
		Valid:    true,
		Duration: duration,
		Details:  make(map[string]interface{}),
	}, nil
}

// executeCommand 执行命令
func (s *etcdService) executeCommand(ctx context.Context, command string, args []string, timeout time.Duration) error {
	// 创建带超时的上下文
	cmdCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	s.logger.Debug("执行命令",
		zap.String("command", command),
		zap.Strings("args", args),
		zap.Duration("timeout", timeout))

	// 使用os/exec执行命令
	cmd := exec.CommandContext(cmdCtx, command, args...)

	// 设置输出缓冲区
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// 执行命令
	err := cmd.Run()

	// 记录输出
	if stdout.Len() > 0 {
		s.logger.Debug("命令标准输出", zap.String("stdout", stdout.String()))
	}
	if stderr.Len() > 0 {
		s.logger.Debug("命令错误输出", zap.String("stderr", stderr.String()))
	}

	if err != nil {
		return fmt.Errorf("命令执行失败: %w, stderr: %s", err, stderr.String())
	}

	s.logger.Info("命令执行成功",
		zap.String("command", command),
		zap.Duration("duration", time.Since(time.Now().Add(-timeout))))

	return nil
}

// validateCronJobOptions 验证CronJob选项
func (s *etcdService) validateCronJobOptions(opts *domain.CronJobOptions) error {
	if opts == nil {
		return fmt.Errorf("CronJob选项不能为空")
	}

	if opts.Name == "" {
		return fmt.Errorf("CronJob名称不能为空")
	}

	if opts.Namespace == "" {
		return fmt.Errorf("命名空间不能为空")
	}

	if opts.Schedule == "" {
		return fmt.Errorf("调度表达式不能为空")
	}

	if opts.BackupPath == "" {
		return fmt.Errorf("备份路径不能为空")
	}

	if opts.ETCDConfig == nil {
		return fmt.Errorf("ETCD配置不能为空")
	}

	return nil
}

// createZapLogger 创建zap logger
func (s *etcdService) createZapLogger() (*zap.Logger, error) {
	// 这里可以根据配置选择不同的logger配置
	return zap.NewProduction()
}
