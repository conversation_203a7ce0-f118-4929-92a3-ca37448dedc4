import { useState, useEffect, useCallback, useRef } from 'react';
import { useWebSocket } from './useWebSocket';
import { useNotifications } from './useAppState';
import type { LogMessage, WebSocketMessage } from '@/types/websocket';
import type { LogLine, LogLevel } from '@/services/logsService';

// 实时日志配置
export interface RealTimeLogsConfig {
  namespace: string;
  pod: string;
  container?: string;
  follow?: boolean;
  tailLines?: number;
  maxLines?: number;
  autoScroll?: boolean;
}

// 实时日志状态
export interface RealTimeLogsState {
  logs: LogLine[];
  connected: boolean;
  loading: boolean;
  error: string | null;
  totalLines: number;
  filteredLines: number;
}

// 日志过滤器
export interface LogFilter {
  keyword?: string;
  levels?: LogLevel[];
  startTime?: Date;
  endTime?: Date;
}

/**
 * 实时日志Hook
 * 
 * 功能特性：
 * - WebSocket实时日志流
 * - 日志缓冲和分页
 * - 日志过滤和搜索
 * - 自动滚动控制
 * - 错误处理和重连
 */
export const useRealTimeLogs = (config: RealTimeLogsConfig) => {
  // 状态管理
  const [state, setState] = useState<RealTimeLogsState>({
    logs: [],
    connected: false,
    loading: true,
    error: null,
    totalLines: 0,
    filteredLines: 0,
  });

  const [filter, setFilter] = useState<LogFilter>({});
  const [paused, setPaused] = useState(false);

  // 引用
  const logBufferRef = useRef<LogLine[]>([]);
  const logIdCounterRef = useRef(0);
  const maxLines = config.maxLines || 1000;

  // 通知系统
  const { showError } = useNotifications();

  // WebSocket连接
  const wsConfig = {
    url: `ws://localhost:8080/api/v1/ws/logs/${config.namespace}/${config.pod}${config.container ? `/${config.container}` : ''}`,
    reconnect: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5,
  };

  const {
    connected,
    connecting,
    reconnecting,
    sendMessage,
    addEventListener,
    removeEventListener,
  } = useWebSocket(wsConfig, {
    onOpen: () => {
      setState(prev => ({ ...prev, connected: true, loading: false, error: null }));
      
      // 发送日志订阅消息
      const subscribeMessage: WebSocketMessage = {
        id: generateMessageId(),
        type: 'log_subscribe',
        timestamp: Date.now(),
        data: {
          namespace: config.namespace,
          pod: config.pod,
          container: config.container,
          follow: config.follow !== false,
          tailLines: config.tailLines || 100,
        },
      };
      
      sendMessage(subscribeMessage);
    },
    onClose: () => {
      setState(prev => ({ ...prev, connected: false }));
    },
    onError: () => {
      setState(prev => ({ 
        ...prev, 
        connected: false, 
        loading: false, 
        error: '日志连接失败' 
      }));
      showError('连接错误', '无法连接到日志服务');
    },
  });

  // 生成消息ID
  const generateMessageId = useCallback(() => {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 解析日志消息
  const parseLogMessage = useCallback((message: LogMessage): LogLine => {
    const logId = ++logIdCounterRef.current;
    
    // 解析日志级别
    let level: LogLevel = 'INFO';
    const levelMatch = message.content.match(/\b(DEBUG|INFO|WARN|WARNING|ERROR|FATAL|TRACE)\b/i);
    if (levelMatch) {
      level = levelMatch[1].toUpperCase() as LogLevel;
    }

    // 解析时间戳
    let timestamp: string | null = null;
    const timestampMatch = message.content.match(/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z?)\s+(.*)$/);
    let content = message.content;
    if (timestampMatch) {
      timestamp = timestampMatch[1];
      content = timestampMatch[2];
    }

    // 解析来源
    let source = '';
    const sourceMatch = content.match(/\[([^\]]+)\]/);
    if (sourceMatch) {
      source = sourceMatch[1];
    }

    return {
      id: logId,
      content,
      timestamp: timestamp || new Date(message.timestamp).toISOString(),
      level,
      source,
    };
  }, []);

  // 添加日志行
  const addLogLine = useCallback((logLine: LogLine) => {
    if (paused) return;

    logBufferRef.current.push(logLine);
    
    // 限制缓冲区大小
    if (logBufferRef.current.length > maxLines) {
      logBufferRef.current = logBufferRef.current.slice(-maxLines);
    }

    // 应用过滤器
    const filteredLogs = applyFilter(logBufferRef.current, filter);
    
    setState(prev => ({
      ...prev,
      logs: filteredLogs,
      totalLines: logBufferRef.current.length,
      filteredLines: filteredLogs.length,
    }));
  }, [paused, maxLines, filter]);

  // 应用日志过滤器
  const applyFilter = useCallback((logs: LogLine[], currentFilter: LogFilter): LogLine[] => {
    return logs.filter(log => {
      // 关键词过滤
      if (currentFilter.keyword && !log.content.toLowerCase().includes(currentFilter.keyword.toLowerCase())) {
        return false;
      }

      // 日志级别过滤
      if (currentFilter.levels && currentFilter.levels.length > 0 && !currentFilter.levels.includes(log.level)) {
        return false;
      }

      // 时间范围过滤
      if (currentFilter.startTime && log.timestamp) {
        const logTime = new Date(log.timestamp);
        if (logTime < currentFilter.startTime) {
          return false;
        }
      }

      if (currentFilter.endTime && log.timestamp) {
        const logTime = new Date(log.timestamp);
        if (logTime > currentFilter.endTime) {
          return false;
        }
      }

      return true;
    });
  }, []);

  // 处理日志消息
  const handleLogMessage = useCallback((message: WebSocketMessage) => {
    if (message.type === 'log') {
      const logMessage = message as LogMessage;
      const logLine = parseLogMessage(logMessage);
      addLogLine(logLine);
    }
  }, [parseLogMessage, addLogLine]);

  // 设置过滤器
  const updateFilter = useCallback((newFilter: Partial<LogFilter>) => {
    const updatedFilter = { ...filter, ...newFilter };
    setFilter(updatedFilter);
    
    // 重新应用过滤器
    const filteredLogs = applyFilter(logBufferRef.current, updatedFilter);
    setState(prev => ({
      ...prev,
      logs: filteredLogs,
      filteredLines: filteredLogs.length,
    }));
  }, [filter, applyFilter]);

  // 清空日志
  const clearLogs = useCallback(() => {
    logBufferRef.current = [];
    setState(prev => ({
      ...prev,
      logs: [],
      totalLines: 0,
      filteredLines: 0,
    }));
  }, []);

  // 暂停/恢复日志流
  const togglePause = useCallback(() => {
    setPaused(prev => !prev);
  }, []);

  // 导出日志
  const exportLogs = useCallback((format: 'txt' | 'json' = 'txt') => {
    const logs = state.logs;
    let content: string;
    
    if (format === 'json') {
      content = JSON.stringify(logs, null, 2);
    } else {
      content = logs.map(log => {
        const timestamp = log.timestamp ? `[${log.timestamp}] ` : '';
        const level = `[${log.level}] `;
        const source = log.source ? `[${log.source}] ` : '';
        return `${timestamp}${level}${source}${log.content}`;
      }).join('\n');
    }

    // 创建下载链接
    const blob = new Blob([content], { type: format === 'json' ? 'application/json' : 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `logs_${config.namespace}_${config.pod}_${new Date().toISOString().slice(0, 19)}.${format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [state.logs, config.namespace, config.pod]);

  // 获取日志统计
  const getLogStats = useCallback(() => {
    const logs = logBufferRef.current;
    const stats = {
      total: logs.length,
      levels: {
        DEBUG: 0,
        INFO: 0,
        WARN: 0,
        WARNING: 0,
        ERROR: 0,
        FATAL: 0,
        TRACE: 0,
      } as Record<LogLevel, number>,
    };

    logs.forEach(log => {
      stats.levels[log.level]++;
    });

    return stats;
  }, []);

  // 设置事件监听器
  useEffect(() => {
    addEventListener('log', handleLogMessage);
    return () => {
      removeEventListener('log', handleLogMessage);
    };
  }, [addEventListener, removeEventListener, handleLogMessage]);

  // 更新连接状态
  useEffect(() => {
    setState(prev => ({
      ...prev,
      connected,
      loading: connecting && !connected,
    }));
  }, [connected, connecting]);

  return {
    // 状态
    ...state,
    connecting,
    reconnecting,
    paused,
    filter,
    
    // 操作方法
    updateFilter,
    clearLogs,
    togglePause,
    exportLogs,
    getLogStats,
    
    // 工具方法
    sendMessage,
  };
};

export default useRealTimeLogs;
