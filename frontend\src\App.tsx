import { RouterProvider } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { QueryProvider } from '@/providers/QueryProvider';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { router } from '@/router';
import { lightTheme } from '@/config/theme';
import '@/styles/layout.css';
import './App.css';

function App() {
  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={zhCN}
        theme={lightTheme}
      >
        <QueryProvider>
          <RouterProvider router={router} />
        </QueryProvider>
      </ConfigProvider>
    </ErrorBoundary>
  );
}

export default App;
