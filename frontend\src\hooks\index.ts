// 自定义Hooks导出文件
// 这里将导出所有自定义Hooks

// 性能优化相关Hooks
export {
  useVirtualList,
  useFixedSizeList,
  useDynamicSizeList
} from './useVirtualList';
export {
  useKeyboard,
  useGlobalKeyboard,
  useTableKeyboard,
  useFormKeyboard
} from './useKeyboard';

// 应用状态相关Hooks
export {
  default as useAppState,
  useTheme,
  useSidebar,
  useGlobalLoading,
  useGlobalError,
  useNotifications,
  useAppSettings,
} from './useAppState';

// Dashboard相关Hooks
export { useDashboardData } from './useDashboard';

// 数据查询相关Hooks
export {
  // ETCD相关
  useETCDStatus,
  useETCDBackups,
  useETCDCronJobs,
  useCreateETCDBackup,
  useETCDRestore,
  useCreateETCDCronJob,
  useUpdateETCDCronJob,
  useDeleteETCDCronJob,

  // 集群相关
  useClusterInfo,
  useClusterHealth,
  useClusterNodes,
  useClusterPods,
  useClusterServices,
  useClusterMetrics,

  // Pod和日志相关
  usePods,
  usePodInfo,
  usePodLogs,

  // 监控相关
  useSystemMetrics,
  useMonitoringHealth,
  usePerformanceData,
  useMonitoringMetrics,
  useAlerts,
  useAlertRules,
  useAcknowledgeAlert,
  useResolveAlert,
  useSilenceAlert,
  useCreateAlertRule,
  useUpdateAlertRule,
  useDeleteAlertRule,

  // 系统配置相关
  useSystemConfig,
  useUpdateSystemConfig,

  // 资源清理相关
  useResourceScan,
  useCleanupRules,
  useCleanupResources,
  useCreateCleanupRule,

  // 端口转发相关
  usePortForwards,
  useCreatePortForward,
  useStopPortForward,
  useDeletePortForward,

  // 清理相关
  useCleanupNamespaces,
  useScanCleanupResources,
  useExecuteCleanup,

  // 配置相关
  useConfig,
  useConfigSection,
  useUpdateConfigSection,
} from './useQuery';

// 路由相关Hooks
export { default as useRouteGuard } from './useRouteGuard';

// 用户状态相关Hooks
export {
  default as useUserState,
  useAuth,
  useUserPreferences,
  useRecentPages,
  useFavorites,
  usePermissions,
  useUserActions,
} from './useUserState';

// 连接状态相关Hooks
export {
  default as useConnectionState,
  useApiConnection,
  useWebSocketManager,
  useNetworkStatus,
  useConnectionStats,
  useConnectionEvents,
  useAutoReconnect,
} from './useConnectionState';

// WebSocket相关Hooks
export { default as useWebSocket } from './useWebSocket';
export { default as useRealTimeLogs } from './useRealTimeLogs';
export { default as useRealTimeMonitoring } from './useRealTimeMonitoring';

// UI交互相关Hooks（待实现）
// export { default as useModal } from './useModal';
// export { default as useConfirm } from './useConfirm';
// export { default as useKeyboard } from './useKeyboard';
// export { default as useDebounce } from './useDebounce';
// export { default as useThrottle } from './useThrottle';

// 表单相关Hooks（待实现）
// export { default as useForm } from './useForm';
// export { default as useValidation } from './useValidation';
