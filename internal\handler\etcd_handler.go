package handler

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"k8s-helper/internal/container"
	"k8s-helper/internal/domain"
	"k8s-helper/pkg/common"
)

// ETCDHandler ETCD命令处理器
type ETCDHandler struct {
	container *container.Container
	logger    *zap.Logger
}

// NewETCDHandler 创建ETCD处理器
func NewETCDHandler(container *container.Container) *ETCDHandler {
	return &ETCDHandler{
		container: container,
		logger:    container.GetLogger(),
	}
}

// HandleBackup 处理备份命令
func (h *ETCDHandler) HandleBackup(cmd *cobra.Command, args []string) error {
	h.logger.Info("开始处理ETCD备份命令")

	// 获取全局标志
	verbose, _ := cmd.Flags().GetBool("verbose")

	// 获取命令行参数
	backupOutput, _ := cmd.Flags().GetString("output")
	apiserverManifest, _ := cmd.Flags().GetString("apiserver-manifest")
	useSDK, _ := cmd.Flags().GetBool("use-sdk")
	fallbackToTool, _ := cmd.Flags().GetBool("fallback-to-tool")
	sdkTimeout, _ := cmd.Flags().GetDuration("sdk-timeout")

	// 获取ETCD工具下载配置参数
	baseURL, _ := cmd.Flags().GetString("base-url")
	etcdVersion, _ := cmd.Flags().GetString("etcd-version")
	etcdOS, _ := cmd.Flags().GetString("etcd-os")

	if verbose {
		common.PrintInfo(fmt.Sprintf("备份配置: 输出=%s, 使用SDK=%v, 回退工具=%v", backupOutput, useSDK, fallbackToTool))
		common.PrintInfo(fmt.Sprintf("下载配置: URL=%s, 版本=%s, 系统=%s", baseURL, etcdVersion, etcdOS))
	}

	// 解析API Server配置
	configParser := h.container.GetConfigParser()
	etcdConfig, err := configParser.ParseAPIServerConfig(apiserverManifest)
	if err != nil {
		return fmt.Errorf("解析kube-apiserver配置失败: %w", err)
	}

	// 创建备份选项
	opts := &domain.BackupOptions{
		OutputPath:      backupOutput,
		UseSDK:          useSDK,
		FallbackToTool:  fallbackToTool,
		Timeout:         sdkTimeout,
		ETCDConfig:      etcdConfig,
		DownloadBaseURL: baseURL,
		DownloadVersion: etcdVersion,
		DownloadOS:      etcdOS,
	}

	// 执行备份
	etcdService := h.container.GetETCDService()
	result, err := etcdService.Backup(context.Background(), opts)
	if err != nil {
		return fmt.Errorf("备份失败: %w", err)
	}

	// 显示结果
	h.printBackupResult(result)

	// 验证备份文件完整性
	if err := h.verifyBackupIntegrity(result.FilePath); err != nil {
		common.PrintWarning(fmt.Sprintf("备份文件完整性验证失败: %v", err))
	} else if verbose {
		common.PrintSuccess("备份文件完整性验证通过")
	}

	if verbose {
		common.PrintSuccess("ETCD备份操作完成")
	}

	return nil
}

// HandleRestore 处理恢复命令
func (h *ETCDHandler) HandleRestore(cmd *cobra.Command, args []string) error {
	h.logger.Info("开始处理ETCD恢复命令")

	// 获取全局标志
	verbose, _ := cmd.Flags().GetBool("verbose")

	// 获取命令行参数
	snapshotFile, _ := cmd.Flags().GetString("snapshot")
	etcdDataDir, _ := cmd.Flags().GetString("data-dir")
	useSDKRestore, _ := cmd.Flags().GetBool("use-sdk-restore")
	fallbackToTool, _ := cmd.Flags().GetBool("fallback-to-tool")
	restoreTimeout, _ := cmd.Flags().GetDuration("restore-timeout")
	skipHashCheck, _ := cmd.Flags().GetBool("skip-hash-check")
	markCompacted, _ := cmd.Flags().GetBool("mark-compacted")
	nodeName, _ := cmd.Flags().GetString("name")
	peerURL, _ := cmd.Flags().GetString("initial-advertise-peer-urls")

	if verbose {
		common.PrintInfo(fmt.Sprintf("恢复配置: 快照=%s, 数据目录=%s, 使用SDK=%v", snapshotFile, etcdDataDir, useSDKRestore))
		common.PrintInfo(fmt.Sprintf("高级选项: 跳过哈希=%v, 标记压缩=%v", skipHashCheck, markCompacted))
	}

	// 显示警告信息
	fmt.Println("⚠️  准备执行 ETCD 数据恢复...")
	fmt.Println("这是一个危险操作，将完全替换现有的 ETCD 数据！")

	// 创建恢复选项
	opts := &domain.RestoreOptions{
		SnapshotPath:             snapshotFile,
		DataDir:                  etcdDataDir,
		Name:                     nodeName,
		InitialAdvertisePeerURLs: peerURL,
		UseSDK:                   useSDKRestore,
		FallbackToTool:           fallbackToTool,
		Timeout:                  restoreTimeout,
		SkipHashCheck:            skipHashCheck,
		MarkCompacted:            markCompacted,
	}

	// 执行恢复
	etcdService := h.container.GetETCDService()
	result, err := etcdService.Restore(context.Background(), opts)
	if err != nil {
		return fmt.Errorf("恢复失败: %w", err)
	}

	// 显示结果
	h.printRestoreResult(result)
	h.printRestoreReminder()

	if verbose {
		common.PrintSuccess("ETCD恢复操作完成")
	}

	return nil
}

// HandleVerify 处理验证命令
func (h *ETCDHandler) HandleVerify(cmd *cobra.Command, args []string) error {
	h.logger.Info("开始处理ETCD验证命令")

	// 获取全局标志
	verbose, _ := cmd.Flags().GetBool("verbose")

	// 获取命令行参数
	snapshotFile, _ := cmd.Flags().GetString("snapshot")
	useSDKVerify, _ := cmd.Flags().GetBool("use-sdk-verify")
	fallbackToTool, _ := cmd.Flags().GetBool("fallback-to-tool")
	verifyTimeout, _ := cmd.Flags().GetDuration("verify-timeout")
	detailedVerify, _ := cmd.Flags().GetBool("detailed-verify")

	if verbose {
		common.PrintInfo(fmt.Sprintf("验证配置: 快照=%s, 使用SDK=%v, 详细模式=%v", snapshotFile, useSDKVerify, detailedVerify))
	}

	// 创建验证选项
	opts := &domain.VerifyOptions{
		SnapshotPath:   snapshotFile,
		UseSDK:         useSDKVerify,
		FallbackToTool: fallbackToTool,
		Timeout:        verifyTimeout,
		DetailedVerify: detailedVerify,
	}

	// 执行验证
	etcdService := h.container.GetETCDService()
	result, err := etcdService.Verify(context.Background(), opts)
	if err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}

	// 显示结果
	h.printVerifyResult(result)

	if verbose {
		common.PrintSuccess("ETCD验证操作完成")
	}

	return nil
}

// HandleCronJobCreate 处理CronJob创建命令
func (h *ETCDHandler) HandleCronJobCreate(cmd *cobra.Command, args []string) error {
	h.logger.Info("开始处理ETCD CronJob创建命令")

	// 获取全局标志
	verbose, _ := cmd.Flags().GetBool("verbose")

	// 获取命令行参数
	cronjobName, _ := cmd.Flags().GetString("name")
	cronjobNamespace, _ := cmd.Flags().GetString("namespace")
	cronjobSchedule, _ := cmd.Flags().GetString("schedule")
	cronjobTimezone, _ := cmd.Flags().GetString("timezone")
	cronjobImage, _ := cmd.Flags().GetString("image")
	backupRetain, _ := cmd.Flags().GetInt("backup-retain")
	apiserverManifest, _ := cmd.Flags().GetString("apiserver-manifest")

	if verbose {
		common.PrintInfo(fmt.Sprintf("CronJob配置: 名称=%s, 命名空间=%s, 调度=%s", cronjobName, cronjobNamespace, cronjobSchedule))
		common.PrintInfo(fmt.Sprintf("备份配置: 镜像=%s, 保留=%d天, 时区=%s", cronjobImage, backupRetain, cronjobTimezone))
	}

	// 解析API Server配置
	configParser := h.container.GetConfigParser()
	etcdConfig, err := configParser.ParseAPIServerConfig(apiserverManifest)
	if err != nil {
		return fmt.Errorf("解析kube-apiserver配置失败: %w", err)
	}

	// 创建CronJob选项
	opts := &domain.CronJobOptions{
		Name:         cronjobName,
		Namespace:    cronjobNamespace,
		Schedule:     cronjobSchedule,
		TimeZone:     cronjobTimezone,
		Image:        cronjobImage,
		BackupRetain: backupRetain,
		ETCDConfig:   etcdConfig,
	}

	// 执行创建
	etcdService := h.container.GetETCDService()
	err = etcdService.CreateCronJob(context.Background(), opts)
	if err != nil {
		return fmt.Errorf("创建CronJob失败: %w", err)
	}

	common.PrintSuccess(fmt.Sprintf("ETCD备份CronJob '%s' 创建成功", cronjobName))

	if verbose {
		common.PrintInfo("CronJob将按照指定的调度自动执行ETCD备份")
	}

	return nil
}

// HandleCronJobList 处理CronJob列表命令
func (h *ETCDHandler) HandleCronJobList(cmd *cobra.Command, args []string) error {
	h.logger.Info("开始处理ETCD CronJob列表命令")

	// 获取全局标志
	verbose, _ := cmd.Flags().GetBool("verbose")

	// 获取命令行参数
	namespace, _ := cmd.Flags().GetString("namespace")
	allNamespaces, _ := cmd.Flags().GetBool("all-namespaces")

	// 如果指定了all-namespaces，则使用空字符串表示所有命名空间
	if allNamespaces {
		namespace = ""
	}

	// 执行列表查询
	etcdService := h.container.GetETCDService()
	cronJobs, err := etcdService.ListCronJobs(context.Background(), namespace)
	if err != nil {
		return fmt.Errorf("列出CronJob失败: %w", err)
	}

	// 显示结果
	h.printCronJobList(cronJobs)

	if verbose {
		common.PrintSuccess(fmt.Sprintf("找到 %d 个ETCD备份CronJob", len(cronJobs)))
	}

	return nil
}

// HandleCronJobSuspend 处理CronJob暂停命令
func (h *ETCDHandler) HandleCronJobSuspend(cmd *cobra.Command, args []string) error {
	h.logger.Info("开始处理ETCD CronJob暂停命令")

	// 获取命令行参数
	cronjobName, _ := cmd.Flags().GetString("name")
	cronjobNamespace, _ := cmd.Flags().GetString("namespace")

	// 执行暂停
	etcdService := h.container.GetETCDService()
	err := etcdService.SuspendCronJob(context.Background(), cronjobNamespace, cronjobName)
	if err != nil {
		return fmt.Errorf("暂停CronJob失败: %w", err)
	}

	common.PrintSuccess(fmt.Sprintf("ETCD备份CronJob '%s' 暂停成功", cronjobName))
	return nil
}

// HandleCronJobResume 处理CronJob恢复命令
func (h *ETCDHandler) HandleCronJobResume(cmd *cobra.Command, args []string) error {
	h.logger.Info("开始处理ETCD CronJob恢复命令")

	// 获取命令行参数
	cronjobName, _ := cmd.Flags().GetString("name")
	cronjobNamespace, _ := cmd.Flags().GetString("namespace")

	// 执行恢复
	etcdService := h.container.GetETCDService()
	err := etcdService.ResumeCronJob(context.Background(), cronjobNamespace, cronjobName)
	if err != nil {
		return fmt.Errorf("恢复CronJob失败: %w", err)
	}

	common.PrintSuccess(fmt.Sprintf("ETCD备份CronJob '%s' 恢复成功", cronjobName))
	return nil
}

// HandleCronJobDelete 处理CronJob删除命令
func (h *ETCDHandler) HandleCronJobDelete(cmd *cobra.Command, args []string) error {
	h.logger.Info("开始处理ETCD CronJob删除命令")

	// 获取命令行参数
	cronjobName, _ := cmd.Flags().GetString("name")
	cronjobNamespace, _ := cmd.Flags().GetString("namespace")
	force, _ := cmd.Flags().GetBool("force")

	// 如果不是强制模式，显示确认信息
	if !force {
		fmt.Printf("⚠️  即将删除 CronJob '%s' (命名空间: %s)\n", cronjobName, cronjobNamespace)
		fmt.Print("确认删除? (y/N): ")
		var response string
		fmt.Scanln(&response)
		if response != "y" && response != "Y" {
			fmt.Println("操作已取消")
			return nil
		}
	}

	// 执行删除
	etcdService := h.container.GetETCDService()
	err := etcdService.DeleteCronJob(context.Background(), cronjobNamespace, cronjobName)
	if err != nil {
		return fmt.Errorf("删除CronJob失败: %w", err)
	}

	common.PrintSuccess(fmt.Sprintf("ETCD备份CronJob '%s' 删除成功", cronjobName))
	return nil
}

// printCronJobList 打印CronJob列表
func (h *ETCDHandler) printCronJobList(cronJobs []domain.CronJobInfo) {
	if len(cronJobs) == 0 {
		fmt.Println("未找到ETCD备份CronJob")
		return
	}

	fmt.Printf("%-20s %-15s %-20s %-10s %-10s\n", "NAME", "NAMESPACE", "SCHEDULE", "TIMEZONE", "SUSPEND")
	fmt.Println(strings.Repeat("-", 80))

	for _, cronJob := range cronJobs {
		suspend := "No"
		if cronJob.Suspend {
			suspend = "Yes"
		}

		timezone := cronJob.TimeZone
		if timezone == "" {
			timezone = "UTC"
		}

		schedule := cronJob.Schedule
		if cronJob.OriginalSchedule != "" {
			schedule = cronJob.OriginalSchedule
		}

		fmt.Printf("%-20s %-15s %-20s %-10s %-10s\n",
			cronJob.Name,
			cronJob.Namespace,
			schedule,
			timezone,
			suspend)
	}
}

// printBackupResult 打印备份结果
func (h *ETCDHandler) printBackupResult(result *domain.BackupResult) {
	common.PrintSuccess("ETCD备份成功完成！")
	fmt.Printf("备份文件: %s\n", result.FilePath)
	fmt.Printf("文件大小: %s\n", common.FormatFileSize(result.Size))
	fmt.Printf("备份耗时: %v\n", result.Duration)
	if result.Version != "" {
		fmt.Printf("ETCD版本: %s\n", result.Version)
	}
}

// printRestoreResult 打印恢复结果
func (h *ETCDHandler) printRestoreResult(result *domain.RestoreResult) {
	common.PrintSuccess("ETCD恢复成功完成！")
	fmt.Printf("数据目录: %s\n", result.DataDir)
	fmt.Printf("恢复耗时: %v\n", result.Duration)
	fmt.Printf("快照大小: %s\n", common.FormatFileSize(result.SnapshotSize))
}

// printVerifyResult 打印验证结果
func (h *ETCDHandler) printVerifyResult(result *domain.VerifyResult) {
	if result.Valid {
		common.PrintSuccess("ETCD快照文件验证通过！")
	} else {
		common.PrintError("ETCD快照文件验证失败！")
		if result.ErrorMessage != "" {
			fmt.Printf("错误信息: %s\n", result.ErrorMessage)
		}
	}

	fmt.Printf("验证耗时: %v\n", result.Duration)

	if result.Details != nil && len(result.Details) > 0 {
		fmt.Println("详细信息:")
		for key, value := range result.Details {
			fmt.Printf("  %s: %v\n", key, value)
		}
	}
}

// printRestoreReminder 打印恢复后的提醒信息
func (h *ETCDHandler) printRestoreReminder() {
	fmt.Println("\n⚠️  重要提醒:")
	fmt.Println("1. 请确保 kubelet 服务已停止")
	fmt.Println("2. 请将恢复的数据目录移动到正确的 ETCD 数据目录")
	fmt.Println("3. 请确保 ETCD 配置文件中的数据目录路径正确")
	fmt.Println("4. 重启 ETCD 服务和 kubelet 服务")
	fmt.Println("5. 验证集群状态是否正常")
}

// verifyBackupIntegrity 验证备份文件完整性
func (h *ETCDHandler) verifyBackupIntegrity(filePath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("备份文件不存在: %s", filePath)
	}

	// 使用验证服务验证文件
	opts := &domain.VerifyOptions{
		SnapshotPath:   filePath,
		UseSDK:         true,
		FallbackToTool: true,
		Timeout:        30 * time.Second,
		DetailedVerify: false,
	}

	etcdService := h.container.GetETCDService()
	result, err := etcdService.Verify(context.Background(), opts)
	if err != nil {
		return err
	}

	if !result.Valid {
		return fmt.Errorf("备份文件验证失败: %s", result.ErrorMessage)
	}

	return nil
}
