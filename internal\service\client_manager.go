package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
	"k8s-helper/pkg/etcd"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

// ClientInfo 客户端信息
type ClientInfo struct {
	Client    *etcd.Client
	Config    *domain.ETCDConfig
	CreatedAt time.Time
	LastUsed  time.Time
	UseCount  int64
	IsHealthy bool
	LastCheck time.Time
}

// ClientManager 统一客户端管理器
type ClientManager struct {
	logger        *zap.Logger
	configAdapter *ETCDConfigAdapter

	// 客户端缓存
	etcdClients map[string]*ClientInfo
	k8sClient   kubernetes.Interface

	// 连接池管理
	mutex               sync.RWMutex
	maxClients          int
	clientTTL           time.Duration
	healthCheckInterval time.Duration
	healthChecker       *time.Ticker
	stopHealthCheck     chan struct{}
}

// NewClientManager 创建客户端管理器
func NewClientManager(logger *zap.Logger) *ClientManager {
	cm := &ClientManager{
		logger:              logger,
		configAdapter:       NewETCDConfigAdapter(),
		etcdClients:         make(map[string]*ClientInfo),
		maxClients:          10,               // 最大客户端数量
		clientTTL:           30 * time.Minute, // 客户端生存时间
		healthCheckInterval: 5 * time.Minute,  // 健康检查间隔
		stopHealthCheck:     make(chan struct{}),
	}

	// 启动健康检查
	cm.startHealthCheck()

	return cm
}

// GetETCDClient 获取ETCD客户端（带连接池）
func (cm *ClientManager) GetETCDClient(config *domain.ETCDConfig) (*etcd.Client, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 生成客户端键
	clientKey := cm.generateClientKey(config)

	// 检查是否已存在客户端
	if clientInfo, exists := cm.etcdClients[clientKey]; exists {
		// 更新使用统计
		clientInfo.LastUsed = time.Now()
		clientInfo.UseCount++

		// 测试连接是否有效
		if cm.isClientHealthy(clientInfo) {
			cm.logger.Debug("复用现有ETCD客户端",
				zap.String("key", clientKey),
				zap.Int64("useCount", clientInfo.UseCount))
			return clientInfo.Client, nil
		}

		// 连接无效，移除客户端
		cm.logger.Warn("ETCD客户端连接无效，重新创建", zap.String("key", clientKey))
		cm.closeClient(clientInfo)
		delete(cm.etcdClients, clientKey)
	}

	// 检查客户端数量限制
	if len(cm.etcdClients) >= cm.maxClients {
		cm.evictOldestClient()
	}

	// 创建新客户端
	sdkConfig := cm.configAdapter.DomainToSDK(config)
	client, err := etcd.NewClient(sdkConfig, cm.logger)
	if err != nil {
		return nil, fmt.Errorf("创建ETCD客户端失败: %w", err)
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := client.TestConnection(ctx); err != nil {
		client.Close()
		return nil, fmt.Errorf("ETCD连接测试失败: %w", err)
	}

	// 创建客户端信息
	clientInfo := &ClientInfo{
		Client:    client,
		Config:    config,
		CreatedAt: time.Now(),
		LastUsed:  time.Now(),
		UseCount:  1,
		IsHealthy: true,
		LastCheck: time.Now(),
	}

	// 缓存客户端
	cm.etcdClients[clientKey] = clientInfo
	cm.logger.Info("创建并缓存新的ETCD客户端",
		zap.String("key", clientKey),
		zap.Int("totalClients", len(cm.etcdClients)))

	return client, nil
}

// isClientHealthy 检查客户端是否健康
func (cm *ClientManager) isClientHealthy(clientInfo *ClientInfo) bool {
	// 如果最近检查过且健康，直接返回
	if time.Since(clientInfo.LastCheck) < cm.healthCheckInterval && clientInfo.IsHealthy {
		return true
	}

	// 执行健康检查
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := clientInfo.Client.TestConnection(ctx)
	clientInfo.LastCheck = time.Now()
	clientInfo.IsHealthy = (err == nil)

	return clientInfo.IsHealthy
}

// closeClient 关闭客户端
func (cm *ClientManager) closeClient(clientInfo *ClientInfo) {
	if clientInfo.Client != nil {
		if err := clientInfo.Client.Close(); err != nil {
			cm.logger.Warn("关闭ETCD客户端失败", zap.Error(err))
		}
	}
}

// evictOldestClient 驱逐最旧的客户端
func (cm *ClientManager) evictOldestClient() {
	var oldestKey string
	var oldestTime time.Time

	for key, clientInfo := range cm.etcdClients {
		if oldestKey == "" || clientInfo.LastUsed.Before(oldestTime) {
			oldestKey = key
			oldestTime = clientInfo.LastUsed
		}
	}

	if oldestKey != "" {
		clientInfo := cm.etcdClients[oldestKey]
		cm.closeClient(clientInfo)
		delete(cm.etcdClients, oldestKey)
		cm.logger.Debug("驱逐最旧的ETCD客户端", zap.String("key", oldestKey))
	}
}

// GetETCDOfflineClient 获取离线ETCD客户端
func (cm *ClientManager) GetETCDOfflineClient() *etcd.Client {
	return etcd.NewClientForOfflineOps(cm.logger)
}

// GetKubernetesClient 获取Kubernetes客户端
func (cm *ClientManager) GetKubernetesClient() (kubernetes.Interface, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 如果已存在客户端，直接返回
	if cm.k8sClient != nil {
		return cm.k8sClient, nil
	}

	// 创建Kubernetes配置
	config, err := cm.createKubernetesConfig()
	if err != nil {
		return nil, fmt.Errorf("创建Kubernetes配置失败: %w", err)
	}

	// 创建客户端
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建Kubernetes客户端失败: %w", err)
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err = clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{Limit: 1})
	if err != nil {
		return nil, fmt.Errorf("Kubernetes连接测试失败: %w", err)
	}

	cm.k8sClient = clientset
	cm.logger.Info("创建Kubernetes客户端成功")

	return cm.k8sClient, nil
}

// ValidateETCDConnection 验证ETCD连接
func (cm *ClientManager) ValidateETCDConnection(config *domain.ETCDConfig) error {
	client, err := cm.GetETCDClient(config)
	if err != nil {
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	return client.TestConnection(ctx)
}

// ValidateKubernetesConnection 验证Kubernetes连接
func (cm *ClientManager) ValidateKubernetesConnection() error {
	_, err := cm.GetKubernetesClient()
	return err
}

// Close 关闭所有客户端连接
func (cm *ClientManager) Close() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.logger.Info("关闭所有客户端连接")

	// 关闭所有ETCD客户端
	for key, clientInfo := range cm.etcdClients {
		cm.closeClient(clientInfo)
		cm.logger.Debug("关闭ETCD客户端", zap.String("key", key))
	}
	cm.etcdClients = make(map[string]*ClientInfo)

	// Kubernetes客户端不需要显式关闭
	cm.k8sClient = nil

	// 停止健康检查
	close(cm.stopHealthCheck)

	cm.logger.Info("客户端管理器已关闭")
	return nil
}

// generateClientKey 生成客户端键
func (cm *ClientManager) generateClientKey(config *domain.ETCDConfig) string {
	return fmt.Sprintf("%s|%s|%s|%s",
		config.Servers,
		config.CaFile,
		config.CertFile,
		config.KeyFile)
}

// createKubernetesConfig 创建Kubernetes配置
func (cm *ClientManager) createKubernetesConfig() (*rest.Config, error) {
	// 优先使用集群内配置
	if config, err := rest.InClusterConfig(); err == nil {
		cm.logger.Info("使用集群内Kubernetes配置")
		return config, nil
	}

	// 使用kubeconfig文件
	kubeconfig := clientcmd.NewDefaultClientConfigLoadingRules().GetDefaultFilename()
	config, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
	if err != nil {
		return nil, fmt.Errorf("构建kubeconfig失败: %w", err)
	}

	cm.logger.Info("使用kubeconfig文件", zap.String("path", kubeconfig))
	return config, nil
}

// cleanupExpiredClients 清理过期客户端
func (cm *ClientManager) cleanupExpiredClients() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 如果客户端数量超过限制，清理最旧的客户端
	if len(cm.etcdClients) > cm.maxClients {
		// 简单实现：清理一半客户端
		count := 0
		for key, client := range cm.etcdClients {
			if count >= len(cm.etcdClients)/2 {
				break
			}
			cm.closeClient(client)
			delete(cm.etcdClients, key)
			count++
		}
		cm.logger.Info("清理过期ETCD客户端", zap.Int("cleaned", count))
	}
}

// GetConnectionStats 获取连接统计信息
func (cm *ClientManager) GetConnectionStats() map[string]interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	return map[string]interface{}{
		"etcd_clients_count": len(cm.etcdClients),
		"max_clients":        cm.maxClients,
		"client_ttl":         cm.clientTTL,
		"has_k8s_client":     cm.k8sClient != nil,
	}
}

// startHealthCheck 启动健康检查
func (cm *ClientManager) startHealthCheck() {
	cm.healthChecker = time.NewTicker(cm.healthCheckInterval)

	go func() {
		for {
			select {
			case <-cm.healthChecker.C:
				cm.performHealthCheck()
			case <-cm.stopHealthCheck:
				cm.healthChecker.Stop()
				return
			}
		}
	}()
}

// performHealthCheck 执行健康检查
func (cm *ClientManager) performHealthCheck() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	unhealthyKeys := make([]string, 0)

	for key, clientInfo := range cm.etcdClients {
		// 检查是否过期
		if time.Since(clientInfo.CreatedAt) > cm.clientTTL {
			unhealthyKeys = append(unhealthyKeys, key)
			continue
		}

		// 检查健康状态
		if !cm.isClientHealthy(clientInfo) {
			unhealthyKeys = append(unhealthyKeys, key)
		}
	}

	// 清理不健康的客户端
	for _, key := range unhealthyKeys {
		clientInfo := cm.etcdClients[key]
		cm.closeClient(clientInfo)
		delete(cm.etcdClients, key)
		cm.logger.Debug("清理不健康的ETCD客户端", zap.String("key", key))
	}

	if len(unhealthyKeys) > 0 {
		cm.logger.Info("健康检查完成",
			zap.Int("cleaned", len(unhealthyKeys)),
			zap.Int("remaining", len(cm.etcdClients)))
	}
}
