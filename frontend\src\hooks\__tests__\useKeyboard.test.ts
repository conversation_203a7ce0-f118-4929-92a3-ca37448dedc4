import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useKeyboard, useGlobalKeyboard, useTableKeyboard, useFormKeyboard } from '../useKeyboard';

describe('useKeyboard', () => {
  beforeEach(() => {
    // 清理事件监听器
    document.removeEventListener('keydown', vi.fn());
    document.removeEventListener('keyup', vi.fn());
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确注册快捷键', () => {
    const mockAction = vi.fn();
    const shortcuts = [
      {
        key: 's',
        ctrl: true,
        action: mockAction,
        description: '保存',
      },
    ];

    renderHook(() => useKeyboard(shortcuts));

    // 模拟按键事件
    const event = new KeyboardEvent('keydown', {
      key: 's',
      ctrlKey: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockAction).toHaveBeenCalledTimes(1);
  });

  it('应该忽略在输入框中的按键', () => {
    const mockAction = vi.fn();
    const shortcuts = [
      {
        key: 's',
        ctrl: true,
        action: mockAction,
        description: '保存',
      },
    ];

    renderHook(() => useKeyboard(shortcuts));

    // 创建一个输入框并设置为事件目标
    const input = document.createElement('input');
    document.body.appendChild(input);

    const event = new KeyboardEvent('keydown', {
      key: 's',
      ctrlKey: true,
    });

    // 模拟事件目标为输入框
    Object.defineProperty(event, 'target', {
      value: input,
      enumerable: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockAction).not.toHaveBeenCalled();

    // 清理
    document.body.removeChild(input);
  });

  it('应该支持组合键', () => {
    const mockAction = vi.fn();
    const shortcuts = [
      {
        key: 'k',
        ctrl: true,
        shift: true,
        action: mockAction,
        description: '组合键测试',
      },
    ];

    renderHook(() => useKeyboard(shortcuts));

    // 测试正确的组合键
    const correctEvent = new KeyboardEvent('keydown', {
      key: 'k',
      ctrlKey: true,
      shiftKey: true,
    });

    act(() => {
      document.dispatchEvent(correctEvent);
    });

    expect(mockAction).toHaveBeenCalledTimes(1);

    // 测试错误的组合键
    const incorrectEvent = new KeyboardEvent('keydown', {
      key: 'k',
      ctrlKey: true,
      // 缺少 shiftKey
    });

    act(() => {
      document.dispatchEvent(incorrectEvent);
    });

    // 应该只被调用一次（之前的正确组合键）
    expect(mockAction).toHaveBeenCalledTimes(1);
  });

  it('应该支持禁用快捷键', () => {
    const mockAction = vi.fn();
    const shortcuts = [
      {
        key: 's',
        ctrl: true,
        action: mockAction,
        description: '保存',
        disabled: true,
      },
    ];

    renderHook(() => useKeyboard(shortcuts));

    const event = new KeyboardEvent('keydown', {
      key: 's',
      ctrlKey: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockAction).not.toHaveBeenCalled();
  });

  it('应该返回快捷键帮助信息', () => {
    const shortcuts = [
      {
        key: 's',
        ctrl: true,
        action: vi.fn(),
        description: '保存文件',
      },
      {
        key: 'o',
        ctrl: true,
        action: vi.fn(),
        description: '打开文件',
      },
      {
        key: 'h',
        action: vi.fn(),
        // 没有描述的快捷键不应该出现在帮助中
      },
    ];

    const { result } = renderHook(() => useKeyboard(shortcuts));

    const help = result.current.getShortcutHelp();
    
    expect(help).toHaveLength(2);
    expect(help[0]).toEqual({
      shortcut: 'ctrl+s',
      description: '保存文件',
    });
    expect(help[1]).toEqual({
      shortcut: 'ctrl+o',
      description: '打开文件',
    });
  });

  it('应该检测快捷键冲突', () => {
    const shortcuts = [
      {
        key: 's',
        ctrl: true,
        action: vi.fn(),
        description: '保存',
      },
      {
        key: 's',
        ctrl: true,
        action: vi.fn(),
        description: '另一个保存',
      },
    ];

    const { result } = renderHook(() => useKeyboard(shortcuts));

    const conflicts = result.current.checkConflicts();
    
    expect(conflicts).toHaveLength(1);
    expect(conflicts[0]).toContain('Conflict: ctrl+s');
  });
});

describe('useGlobalKeyboard', () => {
  it('应该注册全局快捷键', () => {
    const { result } = renderHook(() => useGlobalKeyboard());

    const help = result.current.getShortcutHelp();
    
    expect(help.length).toBeGreaterThan(0);
    expect(help.some(item => item.description === '聚焦搜索框')).toBe(true);
    expect(help.some(item => item.description === '打开命令面板')).toBe(true);
  });

  it('应该支持搜索框聚焦', () => {
    // 创建一个搜索输入框
    const searchInput = document.createElement('input');
    searchInput.placeholder = '搜索内容';
    searchInput.focus = vi.fn();
    document.body.appendChild(searchInput);

    renderHook(() => useGlobalKeyboard());

    const event = new KeyboardEvent('keydown', {
      key: '/',
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(searchInput.focus).toHaveBeenCalledTimes(1);

    // 清理
    document.body.removeChild(searchInput);
  });
});

describe('useTableKeyboard', () => {
  it('应该注册表格快捷键', () => {
    const mockOnRefresh = vi.fn();
    const mockOnAdd = vi.fn();
    const mockOnDelete = vi.fn();

    const { result } = renderHook(() => 
      useTableKeyboard({
        onRefresh: mockOnRefresh,
        onAdd: mockOnAdd,
        onDelete: mockOnDelete,
      })
    );

    const help = result.current.getShortcutHelp();
    
    expect(help.some(item => item.description === '刷新表格')).toBe(true);
    expect(help.some(item => item.description === '新增记录')).toBe(true);
    expect(help.some(item => item.description === '删除选中项')).toBe(true);
  });

  it('应该正确处理F5刷新', () => {
    const mockOnRefresh = vi.fn();

    renderHook(() => 
      useTableKeyboard({
        onRefresh: mockOnRefresh,
      })
    );

    const event = new KeyboardEvent('keydown', {
      key: 'F5',
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockOnRefresh).toHaveBeenCalledTimes(1);
  });
});

describe('useFormKeyboard', () => {
  it('应该注册表单快捷键', () => {
    const mockOnSave = vi.fn();
    const mockOnCancel = vi.fn();
    const mockOnReset = vi.fn();

    const { result } = renderHook(() => 
      useFormKeyboard({
        onSave: mockOnSave,
        onCancel: mockOnCancel,
        onReset: mockOnReset,
      })
    );

    const help = result.current.getShortcutHelp();
    
    expect(help.some(item => item.description === '保存表单')).toBe(true);
    expect(help.some(item => item.description === '取消操作')).toBe(true);
    expect(help.some(item => item.description === '重置表单')).toBe(true);
  });

  it('应该正确处理Ctrl+S保存', () => {
    const mockOnSave = vi.fn();

    renderHook(() => 
      useFormKeyboard({
        onSave: mockOnSave,
      })
    );

    const event = new KeyboardEvent('keydown', {
      key: 's',
      ctrlKey: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockOnSave).toHaveBeenCalledTimes(1);
  });

  it('应该正确处理Escape取消', () => {
    const mockOnCancel = vi.fn();

    renderHook(() => 
      useFormKeyboard({
        onCancel: mockOnCancel,
      })
    );

    const event = new KeyboardEvent('keydown', {
      key: 'Escape',
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });
});
