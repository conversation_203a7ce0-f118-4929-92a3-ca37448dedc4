import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Dashboard from '@/pages/Dashboard';
import { renderWithProviders, mockKubernetesData, mockMonitoringData } from '@/test/utils';

// 模拟API服务
vi.mock('@/services/api', () => ({
  clusterService: {
    getClusterInfo: vi.fn(() => Promise.resolve({
      name: 'test-cluster',
      version: 'v1.28.0',
      nodes: 3,
      pods: 10,
      services: 5,
    })),
    getNodes: vi.fn(() => Promise.resolve(mockKubernetesData.nodes)),
    getPods: vi.fn(() => Promise.resolve(mockKubernetesData.pods)),
    getServices: vi.fn(() => Promise.resolve(mockKubernetesData.services)),
  },
  monitoringService: {
    getMetrics: vi.fn(() => Promise.resolve(mockMonitoringData)),
  },
}));

describe('Dashboard Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确加载和显示Dashboard', async () => {
    renderWithProviders(<Dashboard />);

    // 检查页面标题
    expect(screen.getByText('仪表板')).toBeInTheDocument();

    // 等待数据加载完成
    await waitFor(() => {
      expect(screen.getByText('test-cluster')).toBeInTheDocument();
    });

    // 检查集群信息卡片
    expect(screen.getByText('集群信息')).toBeInTheDocument();
    expect(screen.getByText('v1.28.0')).toBeInTheDocument();

    // 检查统计卡片
    expect(screen.getByText('节点数量')).toBeInTheDocument();
    expect(screen.getByText('Pod数量')).toBeInTheDocument();
    expect(screen.getByText('服务数量')).toBeInTheDocument();
  });

  it('应该显示监控图表', async () => {
    renderWithProviders(<Dashboard />);

    // 等待监控数据加载
    await waitFor(() => {
      expect(screen.getByText('CPU使用率')).toBeInTheDocument();
      expect(screen.getByText('内存使用率')).toBeInTheDocument();
    });

    // 检查图表容器
    const chartContainers = document.querySelectorAll('.ant-card');
    expect(chartContainers.length).toBeGreaterThan(0);
  });

  it('应该支持刷新功能', async () => {
    const user = userEvent.setup();
    
    renderWithProviders(<Dashboard />);

    // 等待初始加载完成
    await waitFor(() => {
      expect(screen.getByText('test-cluster')).toBeInTheDocument();
    });

    // 查找并点击刷新按钮
    const refreshButton = screen.getByRole('button', { name: /刷新/i });
    await user.click(refreshButton);

    // 验证API被再次调用
    await waitFor(() => {
      expect(vi.mocked(require('@/services/api').clusterService.getClusterInfo)).toHaveBeenCalledTimes(2);
    });
  });

  it('应该处理加载错误', async () => {
    // 模拟API错误
    vi.mocked(require('@/services/api').clusterService.getClusterInfo).mockRejectedValueOnce(
      new Error('Network error')
    );

    renderWithProviders(<Dashboard />);

    // 等待错误状态显示
    await waitFor(() => {
      expect(screen.getByText(/加载失败/i)).toBeInTheDocument();
    });
  });

  it('应该显示加载状态', () => {
    // 模拟长时间加载
    vi.mocked(require('@/services/api').clusterService.getClusterInfo).mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 5000))
    );

    renderWithProviders(<Dashboard />);

    // 检查骨架屏或加载指示器
    const loadingElements = document.querySelectorAll('.ant-skeleton, .ant-spin');
    expect(loadingElements.length).toBeGreaterThan(0);
  });

  it('应该响应窗口大小变化', async () => {
    renderWithProviders(<Dashboard />);

    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.getByText('仪表板')).toBeInTheDocument();
    });

    // 模拟窗口大小变化
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    });

    // 触发resize事件
    window.dispatchEvent(new Event('resize'));

    // 验证响应式布局
    const containers = document.querySelectorAll('.ant-col');
    expect(containers.length).toBeGreaterThan(0);
  });

  it('应该支持快捷键操作', async () => {
    renderWithProviders(<Dashboard />);

    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.getByText('仪表板')).toBeInTheDocument();
    });

    // 模拟F5刷新快捷键
    const refreshSpy = vi.spyOn(window.location, 'reload').mockImplementation(() => {});
    
    // 触发F5按键
    const event = new KeyboardEvent('keydown', {
      key: 'F5',
      code: 'F5',
    });
    document.dispatchEvent(event);

    // 验证刷新被调用
    expect(refreshSpy).toHaveBeenCalled();
    
    refreshSpy.mockRestore();
  });

  it('应该正确处理数据更新', async () => {
    const { rerender } = renderWithProviders(<Dashboard />);

    // 等待初始数据加载
    await waitFor(() => {
      expect(screen.getByText('test-cluster')).toBeInTheDocument();
    });

    // 更新模拟数据
    vi.mocked(require('@/services/api').clusterService.getClusterInfo).mockResolvedValueOnce({
      name: 'updated-cluster',
      version: 'v1.29.0',
      nodes: 5,
      pods: 20,
      services: 10,
    });

    // 重新渲染组件
    rerender(<Dashboard />);

    // 等待数据更新
    await waitFor(() => {
      expect(screen.getByText('updated-cluster')).toBeInTheDocument();
      expect(screen.getByText('v1.29.0')).toBeInTheDocument();
    });
  });

  it('应该支持时间范围切换', async () => {
    const user = userEvent.setup();
    
    renderWithProviders(<Dashboard />);

    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.getByText('CPU使用率')).toBeInTheDocument();
    });

    // 查找时间范围选择器
    const timeRangeSelectors = screen.getAllByRole('combobox');
    if (timeRangeSelectors.length > 0) {
      await user.click(timeRangeSelectors[0]);
      
      // 等待下拉选项出现
      await waitFor(() => {
        const options = screen.getAllByText(/小时|天/);
        expect(options.length).toBeGreaterThan(0);
      });
    }
  });

  it('应该正确显示实时数据', async () => {
    vi.useFakeTimers();
    
    renderWithProviders(<Dashboard />);

    // 等待初始加载
    await waitFor(() => {
      expect(screen.getByText('test-cluster')).toBeInTheDocument();
    });

    // 模拟实时数据更新
    vi.advanceTimersByTime(30000); // 30秒后

    // 验证数据被重新获取
    await waitFor(() => {
      expect(vi.mocked(require('@/services/api').monitoringService.getMetrics)).toHaveBeenCalledTimes(2);
    });

    vi.useRealTimers();
  });
});
