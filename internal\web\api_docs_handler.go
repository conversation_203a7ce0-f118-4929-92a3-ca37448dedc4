package web

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// APIDocumentation API文档结构
type APIDocumentation struct {
	Title       string      `json:"title"`
	Version     string      `json:"version"`
	Description string      `json:"description"`
	BaseURL     string      `json:"base_url"`
	Endpoints   []APIEndpoint `json:"endpoints"`
	GeneratedAt time.Time   `json:"generated_at"`
}

// APIEndpoint API端点信息
type APIEndpoint struct {
	Path        string            `json:"path"`
	Method      string            `json:"method"`
	Summary     string            `json:"summary"`
	Description string            `json:"description"`
	Parameters  []APIParameter    `json:"parameters,omitempty"`
	Responses   map[string]APIResponse `json:"responses"`
	Tags        []string          `json:"tags"`
}

// APIParameter API参数信息
type APIParameter struct {
	Name        string `json:"name"`
	In          string `json:"in"` // query, path, header, body
	Type        string `json:"type"`
	Required    bool   `json:"required"`
	Description string `json:"description"`
	Example     string `json:"example,omitempty"`
}

// APIResponse API响应信息
type APIResponse struct {
	Description string      `json:"description"`
	Schema      interface{} `json:"schema,omitempty"`
	Example     interface{} `json:"example,omitempty"`
}

// handleAPIDocs 处理API文档请求
func (s *HTTPServer) handleAPIDocs(c *gin.Context) {
	docs := s.generateAPIDocumentation()
	c.JSON(http.StatusOK, docs)
}

// handleAPIDocsUI 处理API文档UI请求
func (s *HTTPServer) handleAPIDocsUI(c *gin.Context) {
	html := `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K8s-Helper API Documentation</title>
    <link rel="stylesheet" href="/static/css/main.css">
    <style>
        .api-docs {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .endpoint {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .endpoint-header {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .method {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.8rem;
            text-transform: uppercase;
        }
        .method.get { background: #d4edda; color: #155724; }
        .method.post { background: #d1ecf1; color: #0c5460; }
        .method.put { background: #fff3cd; color: #856404; }
        .method.delete { background: #f8d7da; color: #721c24; }
        .endpoint-body {
            padding: 20px;
        }
        .test-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
        }
        .param-input {
            margin-bottom: 10px;
        }
        .param-input label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
        }
        .response-area {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>K8s-Helper API Documentation</h1>
            <p class="subtitle">RESTful API 接口文档</p>
        </div>
    </div>
    
    <div class="api-docs">
        <div class="card">
            <div class="card-header">
                <h2>API 概览</h2>
            </div>
            <div class="card-body">
                <p><strong>基础URL:</strong> <code>/api/v1</code></p>
                <p><strong>版本:</strong> 1.0.0</p>
                <p><strong>描述:</strong> K8s-Helper Kubernetes 集群管理工具 API</p>
                <button class="btn btn-primary" onclick="loadAPIDocumentation()">加载API文档</button>
            </div>
        </div>
        
        <div id="api-endpoints"></div>
    </div>
    
    <script src="/static/js/main.js"></script>
    <script>
        function loadAPIDocumentation() {
            fetch('/api/docs')
                .then(response => response.json())
                .then(data => {
                    renderAPIEndpoints(data.endpoints);
                })
                .catch(error => {
                    console.error('加载API文档失败:', error);
                });
        }
        
        function renderAPIEndpoints(endpoints) {
            const container = document.getElementById('api-endpoints');
            container.innerHTML = '';
            
            endpoints.forEach(endpoint => {
                const endpointDiv = document.createElement('div');
                endpointDiv.className = 'endpoint';
                endpointDiv.innerHTML = 
                    '<div class="endpoint-header">' +
                        '<span class="method ' + endpoint.method.toLowerCase() + '">' + endpoint.method + '</span>' +
                        '<span class="path"><code>' + endpoint.path + '</code></span>' +
                        '<span class="summary">' + endpoint.summary + '</span>' +
                    '</div>' +
                    '<div class="endpoint-body">' +
                        '<p>' + endpoint.description + '</p>' +
                        renderParameters(endpoint.parameters || []) +
                        renderTestForm(endpoint) +
                    '</div>';
                
                container.appendChild(endpointDiv);
            });
        }
        
        function renderParameters(parameters) {
            if (parameters.length === 0) return '';
            
            let html = '<h4>参数</h4><ul>';
            parameters.forEach(param => {
                html += '<li><strong>' + param.name + '</strong> (' + param.type + ')';
                if (param.required) html += ' <em>必需</em>';
                html += ' - ' + param.description;
                if (param.example) html += ' <code>示例: ' + param.example + '</code>';
                html += '</li>';
            });
            html += '</ul>';
            return html;
        }
        
        function renderTestForm(endpoint) {
            const formId = 'form-' + endpoint.method.toLowerCase() + '-' + endpoint.path.replace(/[^a-zA-Z0-9]/g, '');
            let html = '<div class="test-form">';
            html += '<h4>测试接口</h4>';
            html += '<form id="' + formId + '">';
            
            if (endpoint.parameters) {
                endpoint.parameters.forEach(param => {
                    if (param.in === 'query' || param.in === 'path') {
                        html += '<div class="param-input">';
                        html += '<label>' + param.name + (param.required ? ' *' : '') + '</label>';
                        html += '<input type="text" name="' + param.name + '" class="form-control" placeholder="' + (param.example || '') + '">';
                        html += '</div>';
                    }
                });
            }
            
            html += '<button type="button" class="btn btn-primary" onclick="testEndpoint(\'' + endpoint.method + '\', \'' + endpoint.path + '\', \'' + formId + '\')">测试</button>';
            html += '</form>';
            html += '<div id="response-' + formId + '" class="response-area" style="display: none;"></div>';
            html += '</div>';
            
            return html;
        }
        
        function testEndpoint(method, path, formId) {
            const form = document.getElementById(formId);
            const formData = new FormData(form);
            const params = new URLSearchParams();
            
            for (let [key, value] of formData.entries()) {
                if (value) params.append(key, value);
            }
            
            let url = '/api/v1' + path;
            if (method === 'GET' && params.toString()) {
                url += '?' + params.toString();
            }
            
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (method !== 'GET' && params.toString()) {
                const body = {};
                for (let [key, value] of params.entries()) {
                    body[key] = value;
                }
                options.body = JSON.stringify(body);
            }
            
            const responseDiv = document.getElementById('response-' + formId);
            responseDiv.style.display = 'block';
            responseDiv.textContent = '请求中...';
            
            fetch(url, options)
                .then(response => {
                    return response.json().then(data => ({
                        status: response.status,
                        data: data
                    }));
                })
                .then(result => {
                    responseDiv.innerHTML = 
                        '<strong>状态码:</strong> ' + result.status + '<br>' +
                        '<strong>响应:</strong><br>' +
                        '<pre>' + JSON.stringify(result.data, null, 2) + '</pre>';
                })
                .catch(error => {
                    responseDiv.innerHTML = '<strong>错误:</strong> ' + error.message;
                });
        }
        
        // 页面加载时自动加载API文档
        document.addEventListener('DOMContentLoaded', function() {
            loadAPIDocumentation();
        });
    </script>
</body>
</html>`
	
	c.Data(http.StatusOK, "text/html; charset=utf-8", []byte(html))
}

// generateAPIDocumentation 生成API文档
func (s *HTTPServer) generateAPIDocumentation() *APIDocumentation {
	return &APIDocumentation{
		Title:       "K8s-Helper API",
		Version:     "1.0.0",
		Description: "Kubernetes 集群管理工具 RESTful API",
		BaseURL:     "/api/v1",
		GeneratedAt: time.Now(),
		Endpoints: []APIEndpoint{
			// 健康检查相关
			{
				Path:        "/health",
				Method:      "GET",
				Summary:     "健康检查",
				Description: "检查服务健康状态",
				Tags:        []string{"健康检查"},
				Responses: map[string]APIResponse{
					"200": {
						Description: "健康检查成功",
						Example: map[string]interface{}{
							"status":    "healthy",
							"timestamp": "2025-01-01T12:00:00Z",
						},
					},
				},
			},
			{
				Path:        "/health/live",
				Method:      "GET",
				Summary:     "存活检查",
				Description: "检查服务是否存活",
				Tags:        []string{"健康检查"},
				Responses: map[string]APIResponse{
					"200": {
						Description: "服务存活",
						Example:     map[string]interface{}{"status": "ok"},
					},
				},
			},
			{
				Path:        "/health/ready",
				Method:      "GET",
				Summary:     "就绪检查",
				Description: "检查服务是否就绪",
				Tags:        []string{"健康检查"},
				Responses: map[string]APIResponse{
					"200": {
						Description: "服务就绪",
						Example:     map[string]interface{}{"status": "ready"},
					},
				},
			},
			// ETCD相关
			{
				Path:        "/etcd/backup",
				Method:      "POST",
				Summary:     "创建ETCD备份",
				Description: "创建ETCD数据库备份",
				Tags:        []string{"ETCD"},
				Parameters: []APIParameter{
					{
						Name:        "output_path",
						In:          "body",
						Type:        "string",
						Required:    false,
						Description: "备份文件输出路径",
						Example:     "./backup.db",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "备份创建成功",
						Example: map[string]interface{}{
							"path": "./backup.db",
							"size": 1024000,
						},
					},
				},
			},
			{
				Path:        "/etcd/backups",
				Method:      "GET",
				Summary:     "获取备份列表",
				Description: "获取ETCD备份文件列表，支持分页和过滤",
				Tags:        []string{"ETCD"},
				Parameters: []APIParameter{
					{
						Name:        "page",
						In:          "query",
						Type:        "integer",
						Required:    false,
						Description: "页码",
						Example:     "1",
					},
					{
						Name:        "page_size",
						In:          "query",
						Type:        "integer",
						Required:    false,
						Description: "每页大小",
						Example:     "10",
					},
					{
						Name:        "sort_by",
						In:          "query",
						Type:        "string",
						Required:    false,
						Description: "排序字段 (created_at, size, name)",
						Example:     "created_at",
					},
					{
						Name:        "sort_order",
						In:          "query",
						Type:        "string",
						Required:    false,
						Description: "排序顺序 (asc, desc)",
						Example:     "desc",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "备份列表获取成功",
						Example: map[string]interface{}{
							"backups": []interface{}{
								map[string]interface{}{
									"id":         "backup-001",
									"name":       "backup-001.db",
									"path":       "./backups/backup-001.db",
									"size":       1024000,
									"created_at": "2025-01-01T12:00:00Z",
									"status":     "completed",
								},
							},
							"pagination": map[string]interface{}{
								"page":        1,
								"page_size":   10,
								"total":       1,
								"total_pages": 1,
							},
						},
					},
				},
			},
			{
				Path:        "/etcd/restore",
				Method:      "POST",
				Summary:     "恢复ETCD备份",
				Description: "从备份文件恢复ETCD数据",
				Tags:        []string{"ETCD"},
				Parameters: []APIParameter{
					{
						Name:        "snapshot_path",
						In:          "body",
						Type:        "string",
						Required:    true,
						Description: "快照文件路径",
						Example:     "./backup.db",
					},
					{
						Name:        "data_dir",
						In:          "body",
						Type:        "string",
						Required:    false,
						Description: "数据目录路径",
						Example:     "/var/lib/etcd",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "恢复成功",
						Example: map[string]interface{}{
							"data_dir": "/var/lib/etcd",
							"duration": "2.5s",
						},
					},
				},
			},
			{
				Path:        "/etcd/verify",
				Method:      "POST",
				Summary:     "验证ETCD快照",
				Description: "验证ETCD快照文件的完整性",
				Tags:        []string{"ETCD"},
				Parameters: []APIParameter{
					{
						Name:        "snapshot_path",
						In:          "body",
						Type:        "string",
						Required:    true,
						Description: "快照文件路径",
						Example:     "./backup.db",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "验证成功",
						Example: map[string]interface{}{
							"valid":    true,
							"size":     1024000,
							"checksum": "abc123",
						},
					},
				},
			},
			// ETCD CronJob管理相关
			{
				Path:        "/etcd/cronjob",
				Method:      "POST",
				Summary:     "创建ETCD CronJob",
				Description: "创建ETCD备份的定时任务",
				Tags:        []string{"ETCD", "CronJob"},
				Parameters: []APIParameter{
					{
						Name:        "name",
						In:          "body",
						Type:        "string",
						Required:    true,
						Description: "CronJob名称",
						Example:     "etcd-backup",
					},
					{
						Name:        "schedule",
						In:          "body",
						Type:        "string",
						Required:    true,
						Description: "Cron调度表达式",
						Example:     "0 2 * * *",
					},
					{
						Name:        "namespace",
						In:          "body",
						Type:        "string",
						Required:    false,
						Description: "命名空间",
						Example:     "default",
					},
					{
						Name:        "backup_path",
						In:          "body",
						Type:        "string",
						Required:    false,
						Description: "备份路径",
						Example:     "/backup",
					},
				},
				Responses: map[string]APIResponse{
					"201": {
						Description: "CronJob创建成功",
						Example: map[string]interface{}{
							"message":   "CronJob created successfully",
							"name":      "etcd-backup",
							"namespace": "default",
							"schedule":  "0 2 * * *",
						},
					},
				},
			},
			{
				Path:        "/etcd/cronjobs",
				Method:      "GET",
				Summary:     "列出ETCD CronJobs",
				Description: "获取ETCD备份CronJob列表",
				Tags:        []string{"ETCD", "CronJob"},
				Parameters: []APIParameter{
					{
						Name:        "namespace",
						In:          "query",
						Type:        "string",
						Required:    false,
						Description: "命名空间",
						Example:     "default",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "CronJob列表获取成功",
						Example: map[string]interface{}{
							"cronjobs": []interface{}{
								map[string]interface{}{
									"name":      "etcd-backup",
									"namespace": "default",
									"schedule":  "0 2 * * *",
									"suspend":   false,
								},
							},
							"count": 1,
						},
					},
				},
			},
			{
				Path:        "/etcd/cronjob/{name}/suspend",
				Method:      "PUT",
				Summary:     "暂停ETCD CronJob",
				Description: "暂停指定的ETCD备份CronJob",
				Tags:        []string{"ETCD", "CronJob"},
				Parameters: []APIParameter{
					{
						Name:        "name",
						In:          "path",
						Type:        "string",
						Required:    true,
						Description: "CronJob名称",
						Example:     "etcd-backup",
					},
					{
						Name:        "namespace",
						In:          "query",
						Type:        "string",
						Required:    false,
						Description: "命名空间",
						Example:     "default",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "CronJob暂停成功",
						Example: map[string]interface{}{
							"message": "CronJob suspended successfully",
							"name":    "etcd-backup",
						},
					},
				},
			},
			{
				Path:        "/etcd/cronjob/{name}/resume",
				Method:      "PUT",
				Summary:     "恢复ETCD CronJob",
				Description: "恢复指定的ETCD备份CronJob",
				Tags:        []string{"ETCD", "CronJob"},
				Parameters: []APIParameter{
					{
						Name:        "name",
						In:          "path",
						Type:        "string",
						Required:    true,
						Description: "CronJob名称",
						Example:     "etcd-backup",
					},
					{
						Name:        "namespace",
						In:          "query",
						Type:        "string",
						Required:    false,
						Description: "命名空间",
						Example:     "default",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "CronJob恢复成功",
						Example: map[string]interface{}{
							"message": "CronJob resumed successfully",
							"name":    "etcd-backup",
						},
					},
				},
			},
			{
				Path:        "/etcd/cronjob/{name}",
				Method:      "DELETE",
				Summary:     "删除ETCD CronJob",
				Description: "删除指定的ETCD备份CronJob",
				Tags:        []string{"ETCD", "CronJob"},
				Parameters: []APIParameter{
					{
						Name:        "name",
						In:          "path",
						Type:        "string",
						Required:    true,
						Description: "CronJob名称",
						Example:     "etcd-backup",
					},
					{
						Name:        "namespace",
						In:          "query",
						Type:        "string",
						Required:    false,
						Description: "命名空间",
						Example:     "default",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "CronJob删除成功",
						Example: map[string]interface{}{
							"message": "CronJob deleted successfully",
							"name":    "etcd-backup",
						},
					},
				},
			},
			// 集群信息相关
			{
				Path:        "/cluster/info",
				Method:      "GET",
				Summary:     "获取集群信息",
				Description: "获取Kubernetes集群基本信息",
				Tags:        []string{"集群"},
				Responses: map[string]APIResponse{
					"200": {
						Description: "集群信息获取成功",
						Example: map[string]interface{}{
							"version":    "v1.28.0",
							"nodes":      3,
							"namespaces": 10,
						},
					},
				},
			},
			// Pod日志相关
			{
				Path:        "/pods/{namespace}/{pod}/logs",
				Method:      "GET",
				Summary:     "获取Pod日志",
				Description: "获取指定Pod的日志",
				Tags:        []string{"Pod"},
				Parameters: []APIParameter{
					{
						Name:        "namespace",
						In:          "path",
						Type:        "string",
						Required:    true,
						Description: "命名空间",
						Example:     "default",
					},
					{
						Name:        "pod",
						In:          "path",
						Type:        "string",
						Required:    true,
						Description: "Pod名称",
						Example:     "my-pod",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "日志获取成功",
						Example: map[string]interface{}{
							"logs":      "Pod log content...",
							"pod":       "my-pod",
							"namespace": "default",
						},
					},
				},
			},
			// 监控相关
			{
				Path:        "/monitoring/performance",
				Method:      "GET",
				Summary:     "获取性能指标",
				Description: "获取系统性能监控指标",
				Tags:        []string{"监控"},
				Parameters: []APIParameter{
					{
						Name:        "page",
						In:          "query",
						Type:        "integer",
						Required:    false,
						Description: "页码",
						Example:     "1",
					},
					{
						Name:        "page_size",
						In:          "query",
						Type:        "integer",
						Required:    false,
						Description: "每页大小",
						Example:     "10",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "性能指标获取成功",
						Example: map[string]interface{}{
							"performance": map[string]interface{}{
								"operations":     []interface{}{},
								"system_metrics": map[string]interface{}{},
							},
						},
					},
				},
			},
			{
				Path:        "/monitoring/operations",
				Method:      "GET",
				Summary:     "获取操作指标",
				Description: "获取系统操作统计指标",
				Tags:        []string{"监控"},
				Parameters: []APIParameter{
					{
						Name:        "type",
						In:          "query",
						Type:        "string",
						Required:    false,
						Description: "操作类型过滤",
						Example:     "backup",
					},
				},
				Responses: map[string]APIResponse{
					"200": {
						Description: "操作指标获取成功",
						Example: map[string]interface{}{
							"operations": map[string]interface{}{},
							"summary":    map[string]interface{}{},
						},
					},
				},
			},
			{
				Path:        "/monitoring/system",
				Method:      "GET",
				Summary:     "获取系统指标",
				Description: "获取系统资源使用指标",
				Tags:        []string{"监控"},
				Responses: map[string]APIResponse{
					"200": {
						Description: "系统指标获取成功",
						Example: map[string]interface{}{
							"system": map[string]interface{}{
								"memory_alloc": 1024000,
								"goroutines":   100,
							},
							"health": map[string]interface{}{
								"status": "healthy",
							},
						},
					},
				},
			},
			// WebSocket相关
			{
				Path:        "/ws/logs",
				Method:      "GET",
				Summary:     "WebSocket日志流",
				Description: "建立WebSocket连接获取实时日志流",
				Tags:        []string{"WebSocket"},
				Parameters: []APIParameter{
					{
						Name:        "pod",
						In:          "query",
						Type:        "string",
						Required:    true,
						Description: "Pod名称",
						Example:     "my-pod",
					},
					{
						Name:        "namespace",
						In:          "query",
						Type:        "string",
						Required:    true,
						Description: "命名空间",
						Example:     "default",
					},
					{
						Name:        "container",
						In:          "query",
						Type:        "string",
						Required:    false,
						Description: "容器名称",
						Example:     "main",
					},
				},
				Responses: map[string]APIResponse{
					"101": {
						Description: "WebSocket连接建立成功",
					},
				},
			},
			{
				Path:        "/ws/status",
				Method:      "GET",
				Summary:     "WebSocket状态流",
				Description: "建立WebSocket连接获取实时状态更新",
				Tags:        []string{"WebSocket"},
				Responses: map[string]APIResponse{
					"101": {
						Description: "WebSocket连接建立成功",
					},
				},
			},
		},
	}
}
