import React, { useState, useEffect } from 'react';
import {
  Card,
  Steps,
  Button,
  Upload,
  Form,
  Input,
  Alert,
  Progress,
  Result,
  Space,
  Modal,
  Descriptions,
  Tag
} from 'antd';
import {
  UploadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FileOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import {
  useETCDRestore,
  useAppState,
  useNotifications
} from '@/hooks';

const { Step } = Steps;

// 恢复步骤
enum RestoreStep {
  SELECT_FILE = 0,
  VALIDATE_FILE = 1,
  CONFIRM_RESTORE = 2,
  RESTORE_PROGRESS = 3,
  RESTORE_COMPLETE = 4,
}

/**
 * ETCD恢复管理页面
 *
 * 功能特性：
 * - 分步骤恢复流程
 * - 备份文件验证
 * - 恢复进度显示
 * - 恢复确认对话框
 * - 错误处理和回滚
 */
const ETCDRestore: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<RestoreStep>(RestoreStep.SELECT_FILE);
  const [selectedFile, setSelectedFile] = useState<UploadFile | null>(null);
  const [restoreForm] = Form.useForm();
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);

  const { setPageTitle } = useAppState();
  const { showSuccess, showError, showWarning } = useNotifications();

  // 恢复操作
  const restoreMutation = useETCDRestore({
    onSuccess: () => {
      showSuccess('恢复完成', 'ETCD数据已成功恢复');
      setCurrentStep(RestoreStep.RESTORE_COMPLETE);
    },
    onError: (error: any) => {
      showError('恢复失败', error.message || '恢复过程中发生错误');
      setCurrentStep(RestoreStep.SELECT_FILE);
    },
  });

  // 设置页面标题
  useEffect(() => {
    setPageTitle('ETCD恢复 - K8s-Helper');
  }, [setPageTitle]);

  // 文件上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.db,.snap',
    beforeUpload: (file) => {
      // 验证文件类型和大小
      const isValidType = file.name.endsWith('.db') || file.name.endsWith('.snap');
      if (!isValidType) {
        showError('文件类型错误', '请选择.db或.snap格式的备份文件');
        return false;
      }

      const isLt2G = file.size / 1024 / 1024 / 1024 < 2;
      if (!isLt2G) {
        showError('文件过大', '备份文件大小不能超过2GB');
        return false;
      }

      setSelectedFile(file);
      setCurrentStep(RestoreStep.VALIDATE_FILE);

      // 模拟文件验证
      setTimeout(() => {
        setCurrentStep(RestoreStep.CONFIRM_RESTORE);
      }, 2000);

      return false; // 阻止自动上传
    },
    onRemove: () => {
      setSelectedFile(null);
      setCurrentStep(RestoreStep.SELECT_FILE);
    },
  };

  // 处理恢复确认
  const handleConfirmRestore = () => {
    setConfirmModalVisible(true);
  };

  // 执行恢复
  const executeRestore = async (values: any) => {
    setConfirmModalVisible(false);
    setCurrentStep(RestoreStep.RESTORE_PROGRESS);

    // 模拟恢复过程
    const restoreData = {
      file: selectedFile,
      data_dir: values.data_dir,
      name: values.name,
      initial_cluster: values.initial_cluster,
      initial_advertise_peer_urls: values.initial_advertise_peer_urls,
    };

    restoreMutation.mutate(restoreData);
  };

  // 重置流程
  const resetRestore = () => {
    setCurrentStep(RestoreStep.SELECT_FILE);
    setSelectedFile(null);
    restoreForm.resetFields();
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case RestoreStep.SELECT_FILE:
        return (
          <Card title="选择备份文件">
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Upload.Dragger {...uploadProps}>
                <p className="ant-upload-drag-icon">
                  <FileOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持.db和.snap格式的ETCD备份文件，文件大小不超过2GB
                </p>
              </Upload.Dragger>
            </div>
          </Card>
        );

      case RestoreStep.VALIDATE_FILE:
        return (
          <Card title="验证备份文件">
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Progress type="circle" percent={75} />
              <p style={{ marginTop: '16px' }}>正在验证备份文件...</p>
              {selectedFile && (
                <div style={{ marginTop: '16px' }}>
                  <p><strong>文件名:</strong> {selectedFile.name}</p>
                  <p><strong>文件大小:</strong> {formatFileSize(selectedFile.size || 0)}</p>
                </div>
              )}
            </div>
          </Card>
        );

      case RestoreStep.CONFIRM_RESTORE:
        return (
          <Card title="确认恢复信息">
            {selectedFile && (
              <div>
                <Alert
                  message="重要提醒"
                  description="恢复操作将覆盖当前ETCD数据，请确保已做好数据备份。恢复过程中ETCD服务将暂时不可用。"
                  type="warning"
                  showIcon
                  style={{ marginBottom: '24px' }}
                />

                <Descriptions title="备份文件信息" bordered column={2}>
                  <Descriptions.Item label="文件名">{selectedFile.name}</Descriptions.Item>
                  <Descriptions.Item label="文件大小">
                    {formatFileSize(selectedFile.size || 0)}
                  </Descriptions.Item>
                  <Descriptions.Item label="文件类型">
                    <Tag color="blue">
                      {selectedFile.name.endsWith('.db') ? 'Database' : 'Snapshot'}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="验证状态">
                    <Tag color="green" icon={<CheckCircleOutlined />}>
                      验证通过
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>

                <div style={{ textAlign: 'center', marginTop: '24px' }}>
                  <Space>
                    <Button onClick={resetRestore}>
                      重新选择
                    </Button>
                    <Button type="primary" onClick={handleConfirmRestore}>
                      确认恢复
                    </Button>
                  </Space>
                </div>
              </div>
            )}
          </Card>
        );

      case RestoreStep.RESTORE_PROGRESS:
        return (
          <Card title="恢复进度">
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Progress
                type="circle"
                percent={restoreMutation.isPending ? 50 : 100}
                status={restoreMutation.isPending ? 'active' : 'success'}
              />
              <p style={{ marginTop: '16px' }}>
                {restoreMutation.isPending ? '正在恢复数据...' : '恢复完成'}
              </p>
              <div style={{ marginTop: '16px', fontSize: '14px', color: '#666' }}>
                <p>• 停止ETCD服务</p>
                <p>• 清理数据目录</p>
                <p>• 恢复备份数据</p>
                <p>• 重启ETCD服务</p>
              </div>
            </div>
          </Card>
        );

      case RestoreStep.RESTORE_COMPLETE:
        return (
          <Result
            status="success"
            title="恢复完成"
            subTitle="ETCD数据已成功恢复，服务正在重新启动"
            extra={[
              <Button type="primary" key="dashboard" onClick={() => window.location.href = '/dashboard'}>
                返回仪表板
              </Button>,
              <Button key="restart" onClick={resetRestore}>
                重新恢复
              </Button>,
            ]}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <h2 style={{ margin: 0 }}>恢复管理</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            从备份文件恢复ETCD数据
          </p>
        </div>

        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={resetRestore}
            disabled={currentStep === RestoreStep.RESTORE_PROGRESS}
          >
            重置
          </Button>
        </Space>
      </div>

      {/* 步骤指示器 */}
      <Card style={{ marginBottom: '24px' }}>
        <Steps current={currentStep} size="small">
          <Step title="选择文件" description="选择备份文件" />
          <Step title="验证文件" description="验证文件完整性" />
          <Step title="确认恢复" description="确认恢复信息" />
          <Step title="恢复进度" description="执行恢复操作" />
          <Step title="恢复完成" description="恢复操作完成" />
        </Steps>
      </Card>

      {/* 步骤内容 */}
      {renderStepContent()}

      {/* 确认恢复模态框 */}
      <Modal
        title="确认恢复操作"
        open={confirmModalVisible}
        onCancel={() => setConfirmModalVisible(false)}
        onOk={() => restoreForm.submit()}
        confirmLoading={restoreMutation.isPending}
        width={600}
        okText="确认恢复"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <Alert
          message="危险操作警告"
          description="此操作将完全覆盖当前ETCD数据，且无法撤销。请确保您已经充分了解此操作的后果。"
          type="error"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Form
          form={restoreForm}
          layout="vertical"
          onFinish={executeRestore}
          initialValues={{
            data_dir: '/var/lib/etcd',
            name: 'default',
            initial_cluster: 'default=http://localhost:2380',
            initial_advertise_peer_urls: 'http://localhost:2380',
          }}
        >
          <Form.Item
            name="data_dir"
            label="数据目录"
            rules={[{ required: true, message: '请输入数据目录路径' }]}
          >
            <Input placeholder="/var/lib/etcd" />
          </Form.Item>

          <Form.Item
            name="name"
            label="节点名称"
            rules={[{ required: true, message: '请输入节点名称' }]}
          >
            <Input placeholder="default" />
          </Form.Item>

          <Form.Item
            name="initial_cluster"
            label="初始集群配置"
            rules={[{ required: true, message: '请输入初始集群配置' }]}
          >
            <Input placeholder="default=http://localhost:2380" />
          </Form.Item>

          <Form.Item
            name="initial_advertise_peer_urls"
            label="初始通告地址"
            rules={[{ required: true, message: '请输入初始通告地址' }]}
          >
            <Input placeholder="http://localhost:2380" />
          </Form.Item>
        </Form>

        <div style={{
          padding: '12px',
          backgroundColor: '#fff2e8',
          borderRadius: '6px',
          marginTop: '16px'
        }}>
          <h4 style={{ margin: '0 0 8px 0', color: '#d46b08' }}>恢复过程说明：</h4>
          <ul style={{ margin: 0, paddingLeft: '20px', color: '#d46b08' }}>
            <li>ETCD服务将被停止</li>
            <li>现有数据目录将被清空</li>
            <li>从备份文件恢复数据</li>
            <li>重新启动ETCD服务</li>
            <li>整个过程可能需要几分钟时间</li>
          </ul>
        </div>
      </Modal>
    </div>
  );
};

export default ETCDRestore;
