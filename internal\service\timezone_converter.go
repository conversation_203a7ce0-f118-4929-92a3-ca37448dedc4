package service

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
)

// timeZoneConverter 时区转换器实现
type timeZoneConverter struct {
	logger *zap.Logger
}

// NewTimeZoneConverter 创建时区转换器
func NewTimeZoneConverter(logger *zap.Logger) domain.TimeZoneConverter {
	return &timeZoneConverter{
		logger: logger,
	}
}

// ConvertCronScheduleToUTC 转换Cron表达式到UTC
func (tc *timeZoneConverter) ConvertCronScheduleToUTC(schedule, timezone string) (*domain.ConversionResult, error) {
	tc.logger.Debug("开始转换Cron表达式",
		zap.String("schedule", schedule),
		zap.String("timezone", timezone))

	result := &domain.ConversionResult{
		OriginalSchedule:  schedule,
		OriginalTimezone:  timezone,
		ConvertedTimezone: "UTC",
		ConversionApplied: false,
	}

	// 如果已经是UTC或为空，不需要转换
	if timezone == "" || timezone == "UTC" {
		result.ConvertedSchedule = schedule
		return result, nil
	}

	// 解析Cron表达式
	cronParts := strings.Fields(schedule)
	if len(cronParts) != 5 {
		return nil, fmt.Errorf("无效的Cron表达式格式: %s", schedule)
	}

	// 解析时区
	location, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, fmt.Errorf("无效的时区: %s", timezone)
	}

	// 解析小时部分
	hourPart := cronParts[1]
	if hourPart == "*" {
		// 如果小时是通配符，不需要转换
		result.ConvertedSchedule = schedule
		return result, nil
	}

	// 处理小时范围或列表
	convertedHours, converted, err := tc.convertHourPart(hourPart, location)
	if err != nil {
		return nil, fmt.Errorf("转换小时部分失败: %w", err)
	}

	if converted {
		// 构建新的Cron表达式
		newCronParts := make([]string, len(cronParts))
		copy(newCronParts, cronParts)
		newCronParts[1] = convertedHours

		result.ConvertedSchedule = strings.Join(newCronParts, " ")
		result.ConversionApplied = true
	} else {
		result.ConvertedSchedule = schedule
	}

	tc.logger.Debug("Cron表达式转换完成",
		zap.String("original", schedule),
		zap.String("converted", result.ConvertedSchedule),
		zap.Bool("applied", result.ConversionApplied))

	return result, nil
}

// SupportsTimeZone 检查Kubernetes版本是否支持时区
func (tc *timeZoneConverter) SupportsTimeZone(version string) bool {
	// 解析版本号
	major, minor, err := tc.parseVersion(version)
	if err != nil {
		tc.logger.Warn("无法解析Kubernetes版本", zap.String("version", version), zap.Error(err))
		return false
	}

	// Kubernetes 1.24+ 支持 timeZone 字段
	if major > 1 || (major == 1 && minor >= 24) {
		return true
	}

	return false
}

// convertHourPart 转换小时部分
func (tc *timeZoneConverter) convertHourPart(hourPart string, location *time.Location) (string, bool, error) {
	// 处理单个小时
	if !strings.Contains(hourPart, ",") && !strings.Contains(hourPart, "-") && !strings.Contains(hourPart, "/") {
		hour, err := strconv.Atoi(hourPart)
		if err != nil {
			return hourPart, false, nil // 不是数字，可能是表达式，不转换
		}

		convertedHour := tc.convertSingleHour(hour, location)
		return strconv.Itoa(convertedHour), true, nil
	}

	// 处理小时列表（如：8,12,18）
	if strings.Contains(hourPart, ",") {
		hours := strings.Split(hourPart, ",")
		var convertedHours []string

		for _, h := range hours {
			hour, err := strconv.Atoi(strings.TrimSpace(h))
			if err != nil {
				return hourPart, false, nil // 包含非数字，不转换
			}

			convertedHour := tc.convertSingleHour(hour, location)
			convertedHours = append(convertedHours, strconv.Itoa(convertedHour))
		}

		return strings.Join(convertedHours, ","), true, nil
	}

	// 处理小时范围（如：8-18）
	if strings.Contains(hourPart, "-") && !strings.Contains(hourPart, "/") {
		rangeParts := strings.Split(hourPart, "-")
		if len(rangeParts) != 2 {
			return hourPart, false, nil
		}

		startHour, err1 := strconv.Atoi(strings.TrimSpace(rangeParts[0]))
		endHour, err2 := strconv.Atoi(strings.TrimSpace(rangeParts[1]))
		if err1 != nil || err2 != nil {
			return hourPart, false, nil
		}

		convertedStart := tc.convertSingleHour(startHour, location)
		convertedEnd := tc.convertSingleHour(endHour, location)

		return fmt.Sprintf("%d-%d", convertedStart, convertedEnd), true, nil
	}

	// 其他复杂表达式暂不支持转换
	return hourPart, false, nil
}

// convertSingleHour 转换单个小时
func (tc *timeZoneConverter) convertSingleHour(hour int, location *time.Location) int {
	// 创建一个示例时间（使用今天的日期）
	now := time.Now()
	localTime := time.Date(now.Year(), now.Month(), now.Day(), hour, 0, 0, 0, location)

	// 转换为UTC
	utcTime := localTime.UTC()

	return utcTime.Hour()
}

// parseVersion 解析版本号
func (tc *timeZoneConverter) parseVersion(version string) (int, int, error) {
	// 移除 'v' 前缀
	version = strings.TrimPrefix(version, "v")

	// 分割版本号
	parts := strings.Split(version, ".")
	if len(parts) < 2 {
		return 0, 0, fmt.Errorf("无效的版本格式: %s", version)
	}

	major, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, 0, fmt.Errorf("无效的主版本号: %s", parts[0])
	}

	// 处理次版本号中可能的后缀（如 "24+"）
	minorStr := strings.TrimSuffix(parts[1], "+")
	minor, err := strconv.Atoi(minorStr)
	if err != nil {
		return 0, 0, fmt.Errorf("无效的次版本号: %s", parts[1])
	}

	return major, minor, nil
}
