{{define "cluster-info"}}
<div class="module-content">
    <div class="cluster-info-container">
        <!-- 集群概览卡片 -->
        <div class="section cluster-overview">
            <h3>🔍 集群概览</h3>
            <div class="section-content">
                <div class="overview-grid">
                    <div class="overview-card">
                        <div class="card-icon">🏗️</div>
                        <div class="card-content">
                            <h4>Kubernetes版本</h4>
                            <div id="k8s-version" class="card-value">加载中...</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">🖥️</div>
                        <div class="card-content">
                            <h4>节点总数</h4>
                            <div id="total-nodes" class="card-value">0</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">✅</div>
                        <div class="card-content">
                            <h4>就绪节点</h4>
                            <div id="ready-nodes" class="card-value">0</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">📦</div>
                        <div class="card-content">
                            <h4>命名空间</h4>
                            <div id="total-namespaces" class="card-value">0</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">🚀</div>
                        <div class="card-content">
                            <h4>运行中Pod</h4>
                            <div id="running-pods" class="card-value">0</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">🌐</div>
                        <div class="card-content">
                            <h4>服务总数</h4>
                            <div id="total-services" class="card-value">0</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服务端版本信息 -->
        <div class="section server-version">
            <h3>🏗️ Kubernetes 服务端信息</h3>
            <div class="section-content">
                <div class="version-grid">
                    <div class="version-item">
                        <span class="version-label">Git版本:</span>
                        <span id="git-version" class="version-value">--</span>
                    </div>
                    <div class="version-item">
                        <span class="version-label">主版本:</span>
                        <span id="major-version" class="version-value">--</span>
                    </div>
                    <div class="version-item">
                        <span class="version-label">次版本:</span>
                        <span id="minor-version" class="version-value">--</span>
                    </div>
                    <div class="version-item">
                        <span class="version-label">平台:</span>
                        <span id="platform" class="version-value">--</span>
                    </div>
                    <div class="version-item">
                        <span class="version-label">构建日期:</span>
                        <span id="build-date" class="version-value">--</span>
                    </div>
                    <div class="version-item">
                        <span class="version-label">Go版本:</span>
                        <span id="go-version" class="version-value">--</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 节点信息表格 -->
        <div class="section nodes-info">
            <h3>🖥️ 集群节点信息</h3>
            <div class="section-content">
                <div class="table-controls">
                    <div class="search-box">
                        <input type="text" id="node-search" class="form-control" placeholder="搜索节点...">
                    </div>
                    <div class="filter-controls">
                        <select id="status-filter" class="form-control">
                            <option value="">所有状态</option>
                            <option value="Ready">就绪</option>
                            <option value="NotReady">未就绪</option>
                        </select>
                        <select id="role-filter" class="form-control">
                            <option value="">所有角色</option>
                            <option value="master">Master</option>
                            <option value="control-plane">Control Plane</option>
                            <option value="worker">Worker</option>
                        </select>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="nodes-table">
                        <thead>
                            <tr>
                                <th>节点名称</th>
                                <th>状态</th>
                                <th>角色</th>
                                <th>内部IP</th>
                                <th>版本</th>
                                <th>操作系统</th>
                                <th>架构</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="nodes-table-body">
                            <tr>
                                <td colspan="9" class="loading-cell">正在加载节点信息...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 命名空间信息 -->
        <div class="section namespaces-info">
            <h3>📦 命名空间信息</h3>
            <div class="section-content">
                <div class="namespaces-grid" id="namespaces-grid">
                    <div class="loading-placeholder">
                        <p>正在加载命名空间信息...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 资源统计图表 -->
        <div class="section resource-charts">
            <h3>📊 资源统计</h3>
            <div class="section-content">
                <div class="charts-grid">
                    <div class="chart-container">
                        <h4>节点状态分布</h4>
                        <div id="nodes-status-chart" class="chart-placeholder">
                            <div class="chart-content">
                                <div class="chart-item">
                                    <span class="chart-label ready">就绪节点</span>
                                    <span id="chart-ready-nodes" class="chart-value">0</span>
                                </div>
                                <div class="chart-item">
                                    <span class="chart-label not-ready">未就绪节点</span>
                                    <span id="chart-not-ready-nodes" class="chart-value">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <h4>Pod状态分布</h4>
                        <div id="pods-status-chart" class="chart-placeholder">
                            <div class="chart-content">
                                <div class="chart-item">
                                    <span class="chart-label running">运行中</span>
                                    <span id="chart-running-pods" class="chart-value">0</span>
                                </div>
                                <div class="chart-item">
                                    <span class="chart-label total">总数</span>
                                    <span id="chart-total-pods" class="chart-value">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}
