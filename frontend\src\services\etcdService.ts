import { apiClient } from './api';
import type {
  ETCDBackupRequest,
  ETCDBackupResponse,
  ETCDBackupItem,
  ETCDRestoreRequest,
  ETCDRestoreResponse,
  ETCDVerifyRequest,
  ETCDVerifyResponse,
  ETCDStatusResponse,
  ETCDCronJobRequest,
  ETCDCronJobResponse,
} from '@/types/api';

/**
 * ETCD管理服务类
 * 
 * 提供ETCD相关的所有API操作：
 * - 备份管理（创建、列表、验证）
 * - 恢复管理
 * - 状态查询
 * - CronJob管理
 */
export class ETCDService {
  private readonly basePath = '/etcd';

  // ============ 备份管理 ============

  /**
   * 创建ETCD备份
   * @param request 备份请求参数
   * @returns 备份响应信息
   */
  async createBackup(request: ETCDBackupRequest): Promise<ETCDBackupResponse> {
    return apiClient.post<ETCDBackupResponse>(`${this.basePath}/backup`, request);
  }

  /**
   * 获取备份列表
   * @returns 备份列表
   */
  async listBackups(): Promise<ETCDBackupItem[]> {
    return apiClient.get<ETCDBackupItem[]>(`${this.basePath}/backups`);
  }

  /**
   * 验证备份文件
   * @param request 验证请求参数
   * @returns 验证结果
   */
  async verifyBackup(request: ETCDVerifyRequest): Promise<ETCDVerifyResponse> {
    return apiClient.post<ETCDVerifyResponse>(`${this.basePath}/verify`, request);
  }

  // ============ 恢复管理 ============

  /**
   * 恢复ETCD数据
   * @param request 恢复请求参数
   * @returns 恢复响应信息
   */
  async restoreBackup(request: ETCDRestoreRequest): Promise<ETCDRestoreResponse> {
    return apiClient.post<ETCDRestoreResponse>(`${this.basePath}/restore`, request);
  }

  // ============ 状态查询 ============

  /**
   * 获取ETCD状态
   * @returns ETCD状态信息
   */
  async getStatus(): Promise<ETCDStatusResponse> {
    return apiClient.get<ETCDStatusResponse>(`${this.basePath}/status`);
  }

  // ============ CronJob管理 ============

  /**
   * 创建定时备份任务
   * @param request CronJob请求参数
   * @returns CronJob响应信息
   */
  async createCronJob(request: ETCDCronJobRequest): Promise<ETCDCronJobResponse> {
    return apiClient.post<ETCDCronJobResponse>(`${this.basePath}/cronjob`, request);
  }

  /**
   * 获取CronJob列表
   * @returns CronJob列表
   */
  async listCronJobs(): Promise<ETCDCronJobResponse[]> {
    return apiClient.get<ETCDCronJobResponse[]>(`${this.basePath}/cronjobs`);
  }

  /**
   * 暂停CronJob
   * @param name CronJob名称
   * @returns 操作结果
   */
  async suspendCronJob(name: string): Promise<{ success: boolean; message: string }> {
    return apiClient.put<{ success: boolean; message: string }>(
      `${this.basePath}/cronjob/${encodeURIComponent(name)}/suspend`
    );
  }

  /**
   * 恢复CronJob
   * @param name CronJob名称
   * @returns 操作结果
   */
  async resumeCronJob(name: string): Promise<{ success: boolean; message: string }> {
    return apiClient.put<{ success: boolean; message: string }>(
      `${this.basePath}/cronjob/${encodeURIComponent(name)}/resume`
    );
  }

  /**
   * 删除CronJob
   * @param name CronJob名称
   * @returns 操作结果
   */
  async deleteCronJob(name: string): Promise<{ success: boolean; message: string }> {
    return apiClient.delete<{ success: boolean; message: string }>(
      `${this.basePath}/cronjob/${encodeURIComponent(name)}`
    );
  }

  // ============ 工具方法 ============

  /**
   * 格式化备份大小
   * @param bytes 字节数
   * @returns 格式化后的大小字符串
   */
  formatBackupSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 验证Cron表达式
   * @param cronExpression Cron表达式
   * @returns 是否有效
   */
  validateCronExpression(cronExpression: string): boolean {
    // 简单的Cron表达式验证
    const cronRegex = /^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$/;
    return cronRegex.test(cronExpression);
  }

  /**
   * 解析Cron表达式为人类可读的描述
   * @param cronExpression Cron表达式
   * @returns 描述字符串
   */
  parseCronExpression(cronExpression: string): string {
    const parts = cronExpression.split(' ');
    if (parts.length !== 5) {
      return '无效的Cron表达式';
    }

    const [minute, hour, day, month, weekday] = parts;

    // 简单的解析逻辑
    if (cronExpression === '0 0 * * *') {
      return '每天午夜执行';
    } else if (cronExpression === '0 0 * * 0') {
      return '每周日午夜执行';
    } else if (cronExpression === '0 0 1 * *') {
      return '每月1号午夜执行';
    } else if (cronExpression.startsWith('0 */')) {
      const hours = hour.split('/')[1];
      return `每${hours}小时执行一次`;
    } else if (cronExpression.startsWith('*/')) {
      const minutes = minute.split('/')[1];
      return `每${minutes}分钟执行一次`;
    }

    return `自定义: ${cronExpression}`;
  }

  /**
   * 获取常用的Cron表达式模板
   * @returns Cron表达式模板列表
   */
  getCronTemplates(): Array<{ label: string; value: string; description: string }> {
    return [
      {
        label: '每小时',
        value: '0 * * * *',
        description: '每小时的第0分钟执行',
      },
      {
        label: '每天午夜',
        value: '0 0 * * *',
        description: '每天凌晨0点执行',
      },
      {
        label: '每天早上6点',
        value: '0 6 * * *',
        description: '每天早上6点执行',
      },
      {
        label: '每周日午夜',
        value: '0 0 * * 0',
        description: '每周日凌晨0点执行',
      },
      {
        label: '每月1号',
        value: '0 0 1 * *',
        description: '每月1号凌晨0点执行',
      },
      {
        label: '每6小时',
        value: '0 */6 * * *',
        description: '每6小时执行一次',
      },
      {
        label: '每12小时',
        value: '0 */12 * * *',
        description: '每12小时执行一次',
      },
      {
        label: '工作日早上9点',
        value: '0 9 * * 1-5',
        description: '周一到周五早上9点执行',
      },
    ];
  }
}

// 创建并导出ETCD服务实例
export const etcdService = new ETCDService();

// 默认导出
export default etcdService;
