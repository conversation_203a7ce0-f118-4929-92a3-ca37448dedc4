# API配置
VITE_API_BASE_URL=http://localhost:8080
VITE_API_TIMEOUT=10000

# WebSocket配置
VITE_WS_BASE_URL=ws://localhost:8080

# 应用配置
VITE_APP_TITLE=Kubernetes Helper
VITE_APP_VERSION=1.0.0

# 开发配置
VITE_DEV_MODE=true
VITE_DEBUG_MODE=false

# 后端集成配置
VITE_BACKEND_INTEGRATION=true
VITE_STATIC_ASSETS_PATH=/assets
VITE_PUBLIC_PATH=/

# 性能配置
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=true

# 功能开关
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_I18N=false
VITE_ENABLE_PWA=false
