import React, { useRef, useEffect, useMemo } from 'react';
import { Card, Empty, Spin, Select, Space, Button, Tooltip } from 'antd';
import { ReloadOutlined, FullscreenOutlined, DownloadOutlined } from '@ant-design/icons';
import * as echarts from 'echarts';

const { Option } = Select;

// 监控数据点
export interface MonitoringDataPoint {
  timestamp: number;
  value: number;
  label?: string;
}

// 监控图表属性
export interface MonitoringChartProps {
  title: string;
  data: MonitoringDataPoint[];
  loading?: boolean;
  height?: number;
  type?: 'line' | 'area' | 'bar' | 'gauge' | 'pie';
  color?: string | string[];
  unit?: string;
  max?: number;
  min?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  timeRange?: '1h' | '6h' | '24h' | '7d' | '30d';
  onTimeRangeChange?: (range: string) => void;
  onRefresh?: () => void;
  onFullscreen?: () => void;
  onExport?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 监控图表组件
 * 
 * 功能特性：
 * - 支持多种图表类型
 * - 实时数据更新
 * - 时间范围选择
 * - 图表操作功能
 * - 响应式设计
 */
export const MonitoringChart: React.FC<MonitoringChartProps> = ({
  title,
  data,
  loading = false,
  height = 300,
  type = 'line',
  color = '#1890ff',
  unit = '',
  max,
  min,
  showGrid = true,
  showLegend = false,
  timeRange = '1h',
  onTimeRangeChange,
  onRefresh,
  onFullscreen,
  onExport,
  className,
  style,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current) return;

    chartInstance.current = echarts.init(chartRef.current);
    
    return () => {
      chartInstance.current?.dispose();
    };
  }, []);

  // 图表配置
  const chartOption = useMemo(() => {
    if (!data.length) return null;

    const baseOption: echarts.EChartsOption = {
      title: {
        show: false,
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const point = params[0];
          const time = new Date(point.data[0]).toLocaleString();
          return `${time}<br/>${title}: ${point.data[1]}${unit}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true,
        show: showGrid,
      },
      legend: {
        show: showLegend,
        top: 0,
      },
    };

    // 根据图表类型配置
    switch (type) {
      case 'gauge':
        return {
          ...baseOption,
          series: [
            {
              name: title,
              type: 'gauge',
              data: [{ value: data[data.length - 1]?.value || 0, name: title }],
              detail: {
                formatter: `{value}${unit}`,
              },
              axisLine: {
                lineStyle: {
                  color: [
                    [0.3, '#67e0e3'],
                    [0.7, '#37a2da'],
                    [1, '#fd666d']
                  ]
                }
              },
            },
          ],
        };

      case 'pie':
        return {
          ...baseOption,
          series: [
            {
              name: title,
              type: 'pie',
              radius: '50%',
              data: data.map((point, index) => ({
                value: point.value,
                name: point.label || `数据${index + 1}`,
              })),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            },
          ],
        };

      default:
        return {
          ...baseOption,
          xAxis: {
            type: 'time',
            boundaryGap: false,
            axisLine: {
              lineStyle: {
                color: '#e8e8e8',
              },
            },
            axisLabel: {
              color: '#666',
              formatter: (value: number) => {
                return new Date(value).toLocaleTimeString('zh-CN', {
                  hour: '2-digit',
                  minute: '2-digit',
                });
              },
            },
            splitLine: {
              show: showGrid,
              lineStyle: {
                color: '#f0f0f0',
              },
            },
          },
          yAxis: {
            type: 'value',
            max: max,
            min: min,
            axisLine: {
              lineStyle: {
                color: '#e8e8e8',
              },
            },
            axisLabel: {
              color: '#666',
              formatter: `{value}${unit}`,
            },
            splitLine: {
              show: showGrid,
              lineStyle: {
                color: '#f0f0f0',
              },
            },
          },
          series: [
            {
              name: title,
              type: type === 'area' ? 'line' : type,
              data: data.map(point => [point.timestamp, point.value]),
              smooth: true,
              symbol: 'none',
              lineStyle: {
                color: Array.isArray(color) ? color[0] : color,
                width: 2,
              },
              areaStyle: type === 'area' ? {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: (Array.isArray(color) ? color[0] : color) + '40',
                    },
                    {
                      offset: 1,
                      color: (Array.isArray(color) ? color[0] : color) + '10',
                    },
                  ],
                },
              } : undefined,
              itemStyle: {
                color: Array.isArray(color) ? color[0] : color,
              },
            },
          ],
        };
    }
  }, [data, type, color, unit, max, min, showGrid, showLegend, title]);

  // 更新图表
  useEffect(() => {
    if (!chartInstance.current || loading || !chartOption) return;

    chartInstance.current.setOption(chartOption, true);
  }, [chartOption, loading]);

  // 响应式处理
  useEffect(() => {
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 渲染工具栏
  const renderToolbar = () => (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      marginBottom: '16px'
    }}>
      <h3 style={{ margin: 0 }}>{title}</h3>
      
      <Space>
        {onTimeRangeChange && (
          <Select
            size="small"
            value={timeRange}
            onChange={onTimeRangeChange}
            style={{ width: 80 }}
          >
            <Option value="1h">1小时</Option>
            <Option value="6h">6小时</Option>
            <Option value="24h">24小时</Option>
            <Option value="7d">7天</Option>
            <Option value="30d">30天</Option>
          </Select>
        )}
        
        {onRefresh && (
          <Tooltip title="刷新">
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              onClick={onRefresh}
              loading={loading}
            />
          </Tooltip>
        )}
        
        {onFullscreen && (
          <Tooltip title="全屏">
            <Button
              type="text"
              size="small"
              icon={<FullscreenOutlined />}
              onClick={onFullscreen}
            />
          </Tooltip>
        )}
        
        {onExport && (
          <Tooltip title="导出">
            <Button
              type="text"
              size="small"
              icon={<DownloadOutlined />}
              onClick={onExport}
            />
          </Tooltip>
        )}
      </Space>
    </div>
  );

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height 
        }}>
          <Spin size="large" />
        </div>
      );
    }

    if (!data.length) {
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height 
        }}>
          <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    }

    return (
      <div 
        ref={chartRef} 
        style={{ width: '100%', height }}
      />
    );
  };

  return (
    <Card className={className} style={style} bodyStyle={{ padding: '16px' }}>
      {renderToolbar()}
      {renderContent()}
    </Card>
  );
};

export default MonitoringChart;
