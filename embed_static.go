//go:build !dev
// +build !dev

package main

import (
	"embed"
	"io/fs"
	"log"

	"k8s-helper/internal/web"
)

// StaticFiles 嵌入前端构建的静态文件
// 使用 embed 指令在编译时将 frontend/dist 目录下的所有文件嵌入到二进制中
//go:embed all:frontend/dist
var StaticFiles embed.FS

// GetEmbeddedStaticFS 获取嵌入的静态文件系统
func GetEmbeddedStaticFS() (fs.FS, error) {
	// 获取 dist 子目录
	return fs.Sub(StaticFiles, "frontend/dist")
}

// initEmbeddedFS 生产环境下初始化嵌入文件系统
func initEmbeddedFS() {
	if embeddedFS, err := GetEmbeddedStaticFS(); err == nil {
		web.EmbeddedFS = embeddedFS
		log.Println("[INFO] Production mode: using embedded static files")
	} else {
		log.Printf("[WARN] Failed to initialize embedded files: %v", err)
	}
}
