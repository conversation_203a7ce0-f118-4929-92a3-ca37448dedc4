import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { pageTitleMap, hasPermission, getCurrentMenuItem } from '@/config/navigation';
import { ROUTES } from '@/utils/constants';

/**
 * 路由守卫Hook
 * 
 * 功能：
 * - 权限检查
 * - 页面标题更新
 * - 路由变化监听
 */
export const useRouteGuard = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const pathname = location.pathname;
    
    // 更新页面标题
    const pageTitle = pageTitleMap[pathname] || 'K8s-Helper';
    document.title = pageTitle;
    
    // 权限检查
    const currentMenuItem = getCurrentMenuItem(pathname);
    if (currentMenuItem?.permission && !hasPermission(currentMenuItem.permission)) {
      // 如果没有权限，重定向到仪表板
      console.warn(`No permission for route: ${pathname}`);
      navigate(ROUTES.DASHBOARD, { replace: true });
      return;
    }
    
    // 路由变化事件（可用于埋点统计等）
    console.log(`Route changed to: ${pathname}`);
    
  }, [location.pathname, navigate]);

  return {
    pathname: location.pathname,
    currentMenuItem: getCurrentMenuItem(location.pathname),
  };
};

export default useRouteGuard;
