import { apiClient } from './api';
import type {
  ClusterInfoResponse,
  ClusterNode,
  ClusterNamespace,
  HealthCheckResponse,
} from '@/types/api';

/**
 * 集群信息服务类
 * 
 * 提供Kubernetes集群相关的API操作：
 * - 集群基本信息查询
 * - 节点状态监控
 * - 命名空间管理
 * - 健康状态检查
 */
export class ClusterService {
  private readonly basePath = '/cluster';
  private readonly healthPath = '/health';

  // ============ 集群信息查询 ============

  /**
   * 获取完整的集群信息
   * @returns 集群详细信息
   */
  async getClusterInfo(): Promise<ClusterInfoResponse> {
    return apiClient.get<ClusterInfoResponse>(`${this.basePath}/info`);
  }

  /**
   * 获取集群快速信息（轻量级接口）
   * @returns 集群基本信息
   */
  async getClusterInfoQuick(): Promise<Partial<ClusterInfoResponse>> {
    return apiClient.get<Partial<ClusterInfoResponse>>(`${this.basePath}/info/quick`);
  }

  // ============ 健康检查 ============

  /**
   * 获取服务健康状态
   * @returns 健康检查结果
   */
  async getHealthStatus(): Promise<HealthCheckResponse> {
    return apiClient.get<HealthCheckResponse>(`${this.healthPath}`);
  }

  /**
   * 获取存活性检查
   * @returns 存活性检查结果
   */
  async getLivenessCheck(): Promise<HealthCheckResponse> {
    return apiClient.get<HealthCheckResponse>(`${this.healthPath}/live`);
  }

  /**
   * 获取就绪性检查
   * @returns 就绪性检查结果
   */
  async getReadinessCheck(): Promise<HealthCheckResponse> {
    return apiClient.get<HealthCheckResponse>(`${this.healthPath}/ready`);
  }

  // ============ 数据处理工具方法 ============

  /**
   * 计算集群资源使用率
   * @param clusterInfo 集群信息
   * @returns 资源使用率统计
   */
  calculateResourceUsage(clusterInfo: ClusterInfoResponse) {
    const { nodes } = clusterInfo;
    
    let totalCpu = 0;
    let usedCpu = 0;
    let totalMemory = 0;
    let usedMemory = 0;
    let totalPods = 0;
    let usedPods = 0;

    nodes.forEach(node => {
      // CPU计算（假设单位为cores）
      const cpuCapacity = this.parseResourceValue(node.cpu_capacity);
      totalCpu += cpuCapacity;
      usedCpu += cpuCapacity * (node.cpu_usage / 100);

      // 内存计算（转换为GB）
      const memoryCapacity = this.parseMemoryValue(node.memory_capacity);
      totalMemory += memoryCapacity;
      usedMemory += memoryCapacity * (node.memory_usage / 100);

      // Pod计算
      totalPods += node.pod_capacity;
      usedPods += node.pod_count;
    });

    return {
      cpu: {
        total: totalCpu,
        used: usedCpu,
        percentage: totalCpu > 0 ? (usedCpu / totalCpu) * 100 : 0,
        unit: 'cores',
      },
      memory: {
        total: totalMemory,
        used: usedMemory,
        percentage: totalMemory > 0 ? (usedMemory / totalMemory) * 100 : 0,
        unit: 'GB',
      },
      pods: {
        total: totalPods,
        used: usedPods,
        percentage: totalPods > 0 ? (usedPods / totalPods) * 100 : 0,
        unit: 'count',
      },
    };
  }

  /**
   * 获取集群健康状态摘要
   * @param clusterInfo 集群信息
   * @returns 健康状态摘要
   */
  getHealthSummary(clusterInfo: ClusterInfoResponse) {
    const { nodes, health_status } = clusterInfo;
    
    const nodeStats = {
      total: nodes.length,
      ready: nodes.filter(node => node.status === 'Ready').length,
      notReady: nodes.filter(node => node.status === 'NotReady').length,
      unknown: nodes.filter(node => node.status === 'Unknown').length,
    };

    const overallHealth = health_status.overall;
    const criticalComponents = health_status.components.filter(
      comp => comp.status === 'critical'
    ).length;
    const warningComponents = health_status.components.filter(
      comp => comp.status === 'warning'
    ).length;

    return {
      overall: overallHealth,
      nodes: nodeStats,
      components: {
        total: health_status.components.length,
        healthy: health_status.components.length - criticalComponents - warningComponents,
        warning: warningComponents,
        critical: criticalComponents,
      },
    };
  }

  /**
   * 获取节点状态统计
   * @param nodes 节点列表
   * @returns 节点状态统计
   */
  getNodeStatusStats(nodes: ClusterNode[]) {
    const stats = {
      total: nodes.length,
      ready: 0,
      notReady: 0,
      unknown: 0,
      master: 0,
      worker: 0,
    };

    nodes.forEach(node => {
      // 状态统计
      switch (node.status) {
        case 'Ready':
          stats.ready++;
          break;
        case 'NotReady':
          stats.notReady++;
          break;
        case 'Unknown':
          stats.unknown++;
          break;
      }

      // 角色统计
      if (node.roles.includes('master') || node.roles.includes('control-plane')) {
        stats.master++;
      } else {
        stats.worker++;
      }
    });

    return stats;
  }

  /**
   * 获取命名空间统计
   * @param namespaces 命名空间列表
   * @returns 命名空间统计
   */
  getNamespaceStats(namespaces: ClusterNamespace[]) {
    const stats = {
      total: namespaces.length,
      active: namespaces.filter(ns => ns.status === 'Active').length,
      terminating: namespaces.filter(ns => ns.status === 'Terminating').length,
      totalPods: namespaces.reduce((sum, ns) => sum + ns.pod_count, 0),
      totalServices: namespaces.reduce((sum, ns) => sum + ns.service_count, 0),
      totalDeployments: namespaces.reduce((sum, ns) => sum + ns.deployment_count, 0),
    };

    return stats;
  }

  // ============ 私有工具方法 ============

  /**
   * 解析资源值（如CPU）
   * @param value 资源值字符串
   * @returns 数值
   */
  private parseResourceValue(value: string): number {
    // 移除单位并转换为数字
    const numericValue = parseFloat(value.replace(/[^0-9.]/g, ''));
    
    // 处理不同单位
    if (value.includes('m')) {
      // millicore转换为core
      return numericValue / 1000;
    }
    
    return numericValue || 0;
  }

  /**
   * 解析内存值并转换为GB
   * @param value 内存值字符串
   * @returns GB数值
   */
  private parseMemoryValue(value: string): number {
    const numericValue = parseFloat(value.replace(/[^0-9.]/g, ''));
    
    if (value.includes('Ki')) {
      return numericValue / (1024 * 1024); // KiB to GB
    } else if (value.includes('Mi')) {
      return numericValue / 1024; // MiB to GB
    } else if (value.includes('Gi')) {
      return numericValue; // GiB to GB (approximately)
    } else if (value.includes('Ti')) {
      return numericValue * 1024; // TiB to GB
    } else if (value.includes('K')) {
      return numericValue / (1000 * 1000); // KB to GB
    } else if (value.includes('M')) {
      return numericValue / 1000; // MB to GB
    } else if (value.includes('G')) {
      return numericValue; // GB
    } else if (value.includes('T')) {
      return numericValue * 1000; // TB to GB
    }
    
    // 假设默认单位为字节
    return numericValue / (1024 * 1024 * 1024);
  }

  /**
   * 格式化资源值显示
   * @param value 数值
   * @param unit 单位
   * @returns 格式化后的字符串
   */
  formatResourceValue(value: number, unit: string): string {
    if (unit === 'cores') {
      return value < 1 ? `${(value * 1000).toFixed(0)}m` : `${value.toFixed(2)}`;
    } else if (unit === 'GB') {
      if (value < 1) {
        return `${(value * 1024).toFixed(0)}MB`;
      } else if (value >= 1024) {
        return `${(value / 1024).toFixed(2)}TB`;
      } else {
        return `${value.toFixed(2)}GB`;
      }
    }
    
    return `${value.toFixed(2)} ${unit}`;
  }

  /**
   * 获取节点状态颜色
   * @param status 节点状态
   * @returns 状态对应的颜色
   */
  getNodeStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      'Ready': '#52c41a',      // 绿色
      'NotReady': '#ff4d4f',   // 红色
      'Unknown': '#faad14',    // 橙色
    };
    
    return colorMap[status] || '#d9d9d9'; // 默认灰色
  }

  /**
   * 获取健康状态颜色
   * @param status 健康状态
   * @returns 状态对应的颜色
   */
  getHealthStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      'healthy': '#52c41a',    // 绿色
      'warning': '#faad14',    // 橙色
      'critical': '#ff4d4f',   // 红色
    };
    
    return colorMap[status] || '#d9d9d9'; // 默认灰色
  }
}

// 创建并导出集群服务实例
export const clusterService = new ClusterService();

// 默认导出
export default clusterService;
