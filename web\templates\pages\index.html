{{template "layout/base.html" .}}

{{define "content"}}
<div class="container">
    <div class="card">
        <div class="card-header">
            <h2>欢迎使用 K8s-Helper</h2>
        </div>
        <div class="card-body">
            <p>K8s-Helper 是一个强大的 Kubernetes 集群管理工具，提供以下功能：</p>
            
            <div class="features">
                <div class="feature">
                    <h3>ETCD 管理</h3>
                    <ul>
                        <li>备份 ETCD 数据</li>
                        <li>从快照恢复</li>
                        <li>验证快照完整性</li>
                        <li>管理备份 CronJob</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>集群信息</h3>
                    <ul>
                        <li>查看集群状态</li>
                        <li>节点信息</li>
                        <li>资源使用情况</li>
                        <li>网络配置</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>Pod 管理</h3>
                    <ul>
                        <li>查看 Pod 日志</li>
                        <li>监控 Pod 状态</li>
                        <li>资源监控</li>
                        <li>故障排除工具</li>
                    </ul>
                </div>
                
                <div class="feature">
                    <h3>其他工具</h3>
                    <ul>
                        <li>配置管理</li>
                        <li>监控和告警</li>
                        <li>端口转发</li>
                        <li>清理未使用资源</li>
                    </ul>
                </div>
            </div>
            
            <div class="actions">
                <a href="/ui" class="btn btn-primary">进入管理界面</a>
                <a href="/api/docs/ui" class="btn btn-secondary">查看 API 文档</a>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "head"}}
<style>
    .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }
    
    .feature {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        border: 1px solid #e9ecef;
    }
    
    .feature h3 {
        margin-top: 0;
        color: #495057;
        border-bottom: 2px solid #667eea;
        padding-bottom: 10px;
    }
    
    .feature ul {
        padding-left: 20px;
        margin-bottom: 0;
    }
    
    .feature li {
        margin-bottom: 8px;
        color: #6c757d;
    }
    
    .actions {
        text-align: center;
        margin-top: 30px;
    }
    
    .actions .btn {
        margin: 0 10px;
        padding: 12px 24px;
        font-size: 1.1rem;
    }
</style>
{{end}}
