{{template "layout/base.html" .}}

{{define "content"}}
<div class="main-interface">
    <!-- 侧边栏导航 -->
    {{template "sidebar" .}}
    
    <!-- 主内容区域 -->
    <div class="main-content-area">
        <!-- Dashboard 模块 -->
        <div id="module-dashboard" class="module active">
            <div class="module-header">
                <h2>🏠 Dashboard - 集群概览</h2>
                <div class="module-actions">
                    <button class="btn btn-refresh" onclick="refreshDashboard()">
                        <span class="loading" id="dashboard-loading" style="display: none;"></span>
                        刷新数据
                    </button>
                </div>
            </div>

            {{template "dashboard" .}}
        </div>
        
        <!-- Cluster Info 模块 -->
        <div id="module-cluster-info" class="module">
            <div class="module-header">
                <h2>🔍 Cluster Info - 集群信息</h2>
                <div class="module-actions">
                    <button class="btn btn-refresh" onclick="loadClusterInfo()">
                        <span class="loading" id="cluster-info-loading" style="display: none;"></span>
                        刷新信息
                    </button>
                </div>
            </div>

            {{template "cluster-info" .}}
        </div>
        
        <!-- ETCD 模块 -->
        <div id="module-etcd" class="module">
            <div class="module-header">
                <h2>💾 ETCD - 数据管理</h2>
                <div class="module-actions">
                    <button class="btn btn-refresh" onclick="loadETCDData()">刷新数据</button>
                </div>
            </div>

            {{template "etcd" .}}
        </div>
        
        <!-- Logs 模块 -->
        <div id="module-logs" class="module">
            <div class="module-header">
                <h2>📋 Pod Logs - 日志查看</h2>
                <div class="module-actions">
                    <button class="btn btn-refresh" onclick="refreshLogsModule()">
                        <span class="loading" id="logs-module-loading" style="display: none;"></span>
                        刷新模块
                    </button>
                </div>
            </div>

            {{template "logs" .}}
        </div>
        
        <!-- Port Forward 模块 -->
        <div id="module-port-forward" class="module">
            <div class="module-header">
                <h2>🔗 Port Forward - 端口转发</h2>
                <div class="module-actions">
                    <button class="btn btn-refresh" onclick="refreshPortForwardModule()">
                        <span class="loading" id="port-forward-module-loading" style="display: none;"></span>
                        刷新模块
                    </button>
                </div>
            </div>

            {{template "port-forward" .}}
        </div>
        
        <!-- Cleanup 模块 -->
        <div id="module-cleanup" class="module">
            <div class="module-header">
                <h2>🧹 Cleanup - 资源清理</h2>
                <div class="module-actions">
                    <button class="btn btn-refresh" onclick="refreshCleanupModule()">
                        <span class="loading" id="cleanup-module-loading" style="display: none;"></span>
                        刷新模块
                    </button>
                </div>
            </div>

            {{template "cleanup" .}}
        </div>
        
        <!-- Monitoring 模块 -->
        <div id="module-monitoring" class="module">
            <div class="module-header">
                <h2>📊 Monitoring - 监控告警</h2>
                <div class="module-actions">
                    <button class="btn btn-refresh" onclick="refreshMonitoringModule()">
                        <span class="loading" id="monitoring-module-loading" style="display: none;"></span>
                        刷新监控
                    </button>
                </div>
            </div>

            {{template "monitoring" .}}
        </div>
        
        <!-- Configuration 模块 -->
        <div id="module-config" class="module">
            <div class="module-header">
                <h2>⚙️ Configuration - 配置管理</h2>
                <div class="module-actions">
                    <button class="btn btn-refresh" onclick="refreshConfigurationModule()">
                        <span class="loading" id="configuration-module-loading" style="display: none;"></span>
                        刷新配置
                    </button>
                </div>
            </div>

            {{template "config" .}}
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('主界面加载完成');
        
        // 初始化WebSocket连接
        if (window.K8sHelper) {
            window.K8sHelper.initializeWebSocket();
        }
        
        // 加载初始数据
        setTimeout(() => {
            if (typeof loadDashboardData === 'function') {
                loadDashboardData();
            }
        }, 1000);
        
        // 初始化模块切换功能
        initializeModuleSwitching();
    });
    
    // 初始化模块切换功能
    function initializeModuleSwitching() {
        // 确保默认显示Dashboard模块
        const defaultModule = 'dashboard';
        if (typeof switchModule === 'function') {
            switchModule(defaultModule);
        }
    }
</script>
{{end}}
