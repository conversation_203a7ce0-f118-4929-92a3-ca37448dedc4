// 通用组件导出文件
// 这里将导出所有可复用的通用组件

// 布局组件
export { default as AppLayout } from './Layout/AppLayout';
export { default as Sidebar } from './Layout/Sidebar';
export { default as Header } from './Layout/Header';

// 通用UI组件
export { default as MetricCard, MetricCardGrid } from './MetricCard';
export { default as StatusIndicator, StatusIndicatorList, StatusStats } from './StatusIndicator';
export { default as ChartCard, ChartGrid } from './ChartCard';
export { default as DataTable, StatusTag, ActionButtons } from './DataTable';
export { default as ErrorBoundary } from './ErrorBoundary';

// 图表组件
export { default as ResourceChart, MultiMetricChart } from './Charts/ResourceChart';
export { default as MonitoringChart } from './Charts/MonitoringChart';

// 日志组件
export { default as PodSelector } from './PodSelector';
export { default as LogViewer } from './LogViewer';

// 配置组件
export { default as CodeEditor } from './CodeEditor';
export { default as ConfigForm } from './ConfigForm';

// 监控组件
export { default as AlertManager } from './AlertManager';

// 性能优化组件
export {
  default as SkeletonLoader,
  CardSkeleton,
  ListSkeleton,
  TableSkeleton,
  ChartSkeleton,
  FormSkeleton,
  DashboardSkeleton,
  DetailSkeleton
} from './SkeletonLoader';
// export { default as PageContainer } from './Common/PageContainer';
// export { default as PageHeader } from './Common/PageHeader';
// export { default as DataTable } from './Common/DataTable';
// export { default as FormModal } from './Common/FormModal';
// export { default as ConfirmDialog } from './Common/ConfirmDialog';
// export { default as LoadingSpinner } from './Common/LoadingSpinner';

// 图表组件
// export { default as ResourceChart } from './Charts/ResourceChart';
// export { default as MonitoringChart } from './Charts/MonitoringChart';
// export { default as PerformanceChart } from './Charts/PerformanceChart';

// 业务组件
// export { default as LogViewer } from './LogViewer';
// export { default as PodSelector } from './PodSelector';
// export { default as ConfigEditor } from './ConfigEditor';
// export { default as AlertManager } from './AlertManager';

// 注意：以上导出语句在对应组件创建后取消注释
