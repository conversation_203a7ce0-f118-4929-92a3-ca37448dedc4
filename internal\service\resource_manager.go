package service

import (
	"fmt"
	"os"
	"runtime"
	"sync"
	"time"

	"go.uber.org/zap"
)

// FileHandle 文件句柄信息
type FileHandle struct {
	Path      string
	File      *os.File
	CreatedAt time.Time
	LastUsed  time.Time
	UseCount  int64
}

// TempFile 临时文件信息
type TempFile struct {
	Path      string
	CreatedAt time.Time
	Size      int64
	Purpose   string
}

// ResourceManager 资源管理器
type ResourceManager struct {
	logger          *zap.Logger
	fileHandles     map[string]*FileHandle
	tempFiles       map[string]*TempFile
	mutex           sync.RWMutex
	maxFileHandles  int
	tempDir         string
	cleanupTicker   *time.Ticker
	stopCleanup     chan struct{}
	memoryThreshold int64 // 内存阈值（字节）
}

// NewResourceManager 创建资源管理器
func NewResourceManager(logger *zap.Logger, maxFileHandles int, tempDir string) *ResourceManager {
	if tempDir == "" {
		tempDir = os.TempDir()
	}

	rm := &ResourceManager{
		logger:          logger,
		fileHandles:     make(map[string]*FileHandle),
		tempFiles:       make(map[string]*TempFile),
		maxFileHandles:  maxFileHandles,
		tempDir:         tempDir,
		stopCleanup:     make(chan struct{}),
		memoryThreshold: 100 * 1024 * 1024, // 100MB
	}

	// 启动定期清理
	rm.startCleanup()

	return rm
}

// GetFileHandle 获取文件句柄（带缓存和复用）
func (rm *ResourceManager) GetFileHandle(filePath string, flag int, perm os.FileMode) (*os.File, error) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	// 检查是否已有句柄
	if handle, exists := rm.fileHandles[filePath]; exists {
		handle.LastUsed = time.Now()
		handle.UseCount++

		rm.logger.Debug("复用文件句柄",
			zap.String("path", filePath),
			zap.Int64("useCount", handle.UseCount))

		return handle.File, nil
	}

	// 检查句柄数量限制
	if len(rm.fileHandles) >= rm.maxFileHandles {
		rm.evictOldestHandle()
	}

	// 打开新文件
	file, err := os.OpenFile(filePath, flag, perm)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}

	// 创建句柄信息
	handle := &FileHandle{
		Path:      filePath,
		File:      file,
		CreatedAt: time.Now(),
		LastUsed:  time.Now(),
		UseCount:  1,
	}

	rm.fileHandles[filePath] = handle

	rm.logger.Debug("创建新文件句柄",
		zap.String("path", filePath),
		zap.Int("totalHandles", len(rm.fileHandles)))

	return file, nil
}

// ReleaseFileHandle 释放文件句柄
func (rm *ResourceManager) ReleaseFileHandle(filePath string) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	handle, exists := rm.fileHandles[filePath]
	if !exists {
		return fmt.Errorf("文件句柄不存在: %s", filePath)
	}

	if err := handle.File.Close(); err != nil {
		rm.logger.Warn("关闭文件句柄失败",
			zap.String("path", filePath),
			zap.Error(err))
	}

	delete(rm.fileHandles, filePath)

	rm.logger.Debug("释放文件句柄", zap.String("path", filePath))
	return nil
}

// CreateTempFile 创建临时文件
func (rm *ResourceManager) CreateTempFile(prefix, suffix, purpose string) (string, error) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	// 创建临时文件
	file, err := os.CreateTemp(rm.tempDir, prefix+"*"+suffix)
	if err != nil {
		return "", fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer file.Close()

	tempPath := file.Name()

	// 获取文件信息
	info, err := file.Stat()
	if err != nil {
		rm.logger.Warn("获取临时文件信息失败", zap.Error(err))
	}

	// 记录临时文件
	tempFile := &TempFile{
		Path:      tempPath,
		CreatedAt: time.Now(),
		Size:      0,
		Purpose:   purpose,
	}

	if info != nil {
		tempFile.Size = info.Size()
	}

	rm.tempFiles[tempPath] = tempFile

	rm.logger.Debug("创建临时文件",
		zap.String("path", tempPath),
		zap.String("purpose", purpose))

	return tempPath, nil
}

// UpdateTempFileSize 更新临时文件大小
func (rm *ResourceManager) UpdateTempFileSize(tempPath string) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	tempFile, exists := rm.tempFiles[tempPath]
	if !exists {
		return
	}

	if info, err := os.Stat(tempPath); err == nil {
		tempFile.Size = info.Size()
	}
}

// CleanupTempFile 清理临时文件
func (rm *ResourceManager) CleanupTempFile(tempPath string) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if err := os.Remove(tempPath); err != nil && !os.IsNotExist(err) {
		rm.logger.Warn("删除临时文件失败",
			zap.String("path", tempPath),
			zap.Error(err))
		return err
	}

	delete(rm.tempFiles, tempPath)

	rm.logger.Debug("清理临时文件", zap.String("path", tempPath))
	return nil
}

// evictOldestHandle 驱逐最旧的文件句柄
func (rm *ResourceManager) evictOldestHandle() {
	var oldestPath string
	var oldestTime time.Time

	for path, handle := range rm.fileHandles {
		if oldestPath == "" || handle.LastUsed.Before(oldestTime) {
			oldestPath = path
			oldestTime = handle.LastUsed
		}
	}

	if oldestPath != "" {
		handle := rm.fileHandles[oldestPath]
		if err := handle.File.Close(); err != nil {
			rm.logger.Warn("关闭被驱逐的文件句柄失败",
				zap.String("path", oldestPath),
				zap.Error(err))
		}
		delete(rm.fileHandles, oldestPath)

		rm.logger.Debug("驱逐最旧的文件句柄", zap.String("path", oldestPath))
	}
}

// startCleanup 启动定期清理
func (rm *ResourceManager) startCleanup() {
	rm.cleanupTicker = time.NewTicker(5 * time.Minute)

	go func() {
		for {
			select {
			case <-rm.cleanupTicker.C:
				rm.cleanup()
			case <-rm.stopCleanup:
				rm.cleanupTicker.Stop()
				return
			}
		}
	}()
}

// cleanup 执行清理
func (rm *ResourceManager) cleanup() {
	rm.cleanupTempFiles()
	rm.cleanupFileHandles()
	rm.checkMemoryUsage()
}

// cleanupTempFiles 清理临时文件
func (rm *ResourceManager) cleanupTempFiles() {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	now := time.Now()
	cleanupThreshold := 1 * time.Hour // 清理1小时前的临时文件
	cleanedFiles := make([]string, 0)

	for path, tempFile := range rm.tempFiles {
		if now.Sub(tempFile.CreatedAt) > cleanupThreshold {
			if err := os.Remove(path); err != nil && !os.IsNotExist(err) {
				rm.logger.Warn("清理临时文件失败",
					zap.String("path", path),
					zap.Error(err))
				continue
			}
			cleanedFiles = append(cleanedFiles, path)
		}
	}

	for _, path := range cleanedFiles {
		delete(rm.tempFiles, path)
	}

	if len(cleanedFiles) > 0 {
		rm.logger.Info("清理过期临时文件",
			zap.Int("cleaned", len(cleanedFiles)),
			zap.Int("remaining", len(rm.tempFiles)))
	}
}

// cleanupFileHandles 清理文件句柄
func (rm *ResourceManager) cleanupFileHandles() {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	now := time.Now()
	idleThreshold := 30 * time.Minute // 清理30分钟未使用的句柄
	closedHandles := make([]string, 0)

	for path, handle := range rm.fileHandles {
		if now.Sub(handle.LastUsed) > idleThreshold {
			if err := handle.File.Close(); err != nil {
				rm.logger.Warn("关闭空闲文件句柄失败",
					zap.String("path", path),
					zap.Error(err))
			}
			closedHandles = append(closedHandles, path)
		}
	}

	for _, path := range closedHandles {
		delete(rm.fileHandles, path)
	}

	if len(closedHandles) > 0 {
		rm.logger.Info("清理空闲文件句柄",
			zap.Int("closed", len(closedHandles)),
			zap.Int("remaining", len(rm.fileHandles)))
	}
}

// checkMemoryUsage 检查内存使用情况
func (rm *ResourceManager) checkMemoryUsage() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	currentMemory := int64(m.Alloc)

	if currentMemory > rm.memoryThreshold {
		rm.logger.Warn("内存使用超过阈值",
			zap.Int64("current", currentMemory),
			zap.Int64("threshold", rm.memoryThreshold))

		// 强制垃圾回收
		runtime.GC()

		// 重新检查内存
		runtime.ReadMemStats(&m)
		afterGC := int64(m.Alloc)

		rm.logger.Info("执行垃圾回收",
			zap.Int64("before", currentMemory),
			zap.Int64("after", afterGC),
			zap.Int64("freed", currentMemory-afterGC))
	}
}

// GetStats 获取资源统计信息
func (rm *ResourceManager) GetStats() map[string]interface{} {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	totalTempSize := int64(0)
	for _, tempFile := range rm.tempFiles {
		totalTempSize += tempFile.Size
	}

	return map[string]interface{}{
		"fileHandles":     len(rm.fileHandles),
		"maxFileHandles":  rm.maxFileHandles,
		"tempFiles":       len(rm.tempFiles),
		"totalTempSize":   totalTempSize,
		"memoryAlloc":     int64(m.Alloc),
		"memoryThreshold": rm.memoryThreshold,
		"tempDir":         rm.tempDir,
	}
}

// Close 关闭资源管理器
func (rm *ResourceManager) Close() {
	close(rm.stopCleanup)

	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	// 关闭所有文件句柄
	for path, handle := range rm.fileHandles {
		if err := handle.File.Close(); err != nil {
			rm.logger.Warn("关闭文件句柄失败",
				zap.String("path", path),
				zap.Error(err))
		}
	}
	rm.fileHandles = make(map[string]*FileHandle)

	// 清理所有临时文件
	for path := range rm.tempFiles {
		if err := os.Remove(path); err != nil && !os.IsNotExist(err) {
			rm.logger.Warn("清理临时文件失败",
				zap.String("path", path),
				zap.Error(err))
		}
	}
	rm.tempFiles = make(map[string]*TempFile)

	rm.logger.Info("资源管理器已关闭")
}
