package cmd

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	"go.uber.org/zap"

	"k8s-helper/internal/container"
	"k8s-helper/internal/web"
	"k8s-helper/pkg/config"
)

var (
	webPort     int
	webHost     string
	webCertFile string
	webKeyFile  string
	webTLS      bool
	webCORS     bool
)

// webCmd represents the web command
var webCmd = &cobra.Command{
	Use:   "web",
	Short: "Start the web server",
	Long: `Start the web server to provide HTTP endpoints and web interface for all k8s-helper functionality.

The web server provides:
- RESTful API endpoints for cluster management
- Web-based user interface for easy interaction
- Cluster information and health monitoring
- Pod logs viewing and streaming
- Port forwarding management
- Resource cleanup operations
- Configuration management

Examples:
  # Start web server on default port 8080
  k8s-helper web

  # Start web server on custom port
  k8s-helper web --port 9090

  # Start web server with TLS
  k8s-helper web --tls --cert-file server.crt --key-file server.key

  # Start web server with CORS enabled
  k8s-helper web --cors

  # Start web server on specific host
  k8s-helper web --host 0.0.0.0 --port 8080`,
	RunE: runWebServer,
}

func init() {
	// Web server flags
	webCmd.Flags().IntVarP(&webPort, "port", "p", 8080, "Port to run the web server on")
	webCmd.Flags().StringVarP(&webHost, "host", "H", "localhost", "Host to bind the web server to")
	webCmd.Flags().StringVar(&webCertFile, "cert-file", "", "Path to TLS certificate file")
	webCmd.Flags().StringVar(&webKeyFile, "key-file", "", "Path to TLS private key file")
	webCmd.Flags().BoolVar(&webTLS, "tls", false, "Enable TLS (requires cert-file and key-file)")
	webCmd.Flags().BoolVar(&webCORS, "cors", false, "Enable CORS support")
}

func runWebServer(cmd *cobra.Command, args []string) error {
	// 加载配置（使用默认配置）
	cfg := config.DefaultConfig()

	// 初始化日志
	zapLogger, _ := zap.NewDevelopment()
	defer zapLogger.Sync()

	// 验证TLS配置
	if webTLS {
		if webCertFile == "" || webKeyFile == "" {
			return fmt.Errorf("TLS enabled but cert-file or key-file not provided")
		}

		if _, err := os.Stat(webCertFile); os.IsNotExist(err) {
			return fmt.Errorf("certificate file not found: %s", webCertFile)
		}

		if _, err := os.Stat(webKeyFile); os.IsNotExist(err) {
			return fmt.Errorf("private key file not found: %s", webKeyFile)
		}
	}

	// 初始化容器
	appContainer := container.NewContainer(zapLogger)
	if err := appContainer.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize container: %w", err)
	}

	// 获取ETCD服务
	etcdService := appContainer.GetETCDService()

	// 创建Web服务器配置
	serverConfig := &web.ServerConfig{
		Host:       webHost,
		Port:       webPort,
		TLS:        webTLS,
		CertFile:   webCertFile,
		KeyFile:    webKeyFile,
		CORS:       webCORS,
		Kubeconfig: cfg.Global.Kubeconfig,
		Logger:     zapLogger,
		DevMode:    true, // 开发模式，支持模板热重载
	}

	// 创建HTTP Web服务器
	server := web.NewHTTPServer(serverConfig).WithETCDService(etcdService)

	// 启动服务器
	return startHTTPServer(server, zapLogger)
}

func startHTTPServer(server *web.HTTPServer, logger *zap.Logger) error {
	// 创建上下文用于优雅关闭
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动服务器的goroutine
	serverErr := make(chan error, 1)
	go func() {
		logger.Info("启动Web服务器...")
		if err := server.Start(); err != nil {
			serverErr <- err
		}
	}()

	// 监听系统信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号或错误
	select {
	case err := <-serverErr:
		if err != nil {
			return fmt.Errorf("server error: %w", err)
		}
	case sig := <-sigChan:
		logger.Info("收到信号，开始优雅关闭", zap.String("signal", sig.String()))

		// 优雅关闭
		shutdownCtx, shutdownCancel := context.WithTimeout(ctx, 30*time.Second)
		defer shutdownCancel()

		if err := server.Stop(); err != nil {
			logger.Error("服务器关闭失败", zap.Error(err))
			return err
		}

		logger.Info("服务器已优雅关闭")
		_ = shutdownCtx // 避免未使用变量警告
	}

	return nil
}