package cmd

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/portforward"
	"k8s.io/client-go/transport/spdy"

	"k8s-helper/pkg/cmdhelp"
	"k8s-helper/pkg/common"
	"k8s-helper/pkg/k8s"
)

// portForwardCmd 代表 port-forward 命令
var portForwardCmd = cmdhelp.PortForwardHelp.BuildCommand(runPortForwardCommand, cmdhelp.PortForwardErrorConfig)

var (
	// 端口转发的命名空间
	pfNamespace string
	// 本地绑定地址
	address string
)

func init() {
	portForwardCmd.Flags().StringVarP(&pfNamespace, "namespace", "n", common.DefaultNamespace,
		"指定命名空间")
	portForwardCmd.Flags().StringVar(&address, "address", common.DefaultPortForwardAddress,
		"本地绑定地址")
}

// PortForwardSpec 表示端口转发规范
type PortForwardSpec struct {
	LocalPort  int
	RemotePort int
}

// runPortForwardCommand 执行端口转发命令的主要逻辑
func runPortForwardCommand(cmd *cobra.Command, args []string) error {
	podName := args[0]
	portSpecs := args[1:]

	// 解析端口规范
	ports, err := parsePortSpecs(portSpecs)
	if err != nil {
		return fmt.Errorf("解析端口规范失败: %w", err)
	}

	// 创建 Kubernetes 客户端和配置
	clientset, err := k8s.NewClient(kubeconfig)
	if err != nil {
		return fmt.Errorf("创建 Kubernetes 客户端失败: %w", err)
	}

	config, err := k8s.GetRestConfig(kubeconfig)
	if err != nil {
		return fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	ctx := context.Background()

	// 验证 Pod 是否存在
	pod, err := clientset.CoreV1().Pods(pfNamespace).Get(ctx, podName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取 Pod 信息失败: %w", err)
	}

	if verbose {
		common.PrintInfo(fmt.Sprintf("找到Pod: %s/%s", pfNamespace, podName))
		common.PrintInfo(fmt.Sprintf("Pod状态: %s", pod.Status.Phase))
	}

	// 检查 Pod 状态
	if pod.Status.Phase != "Running" {
		common.PrintWarning(fmt.Sprintf("Pod %s 状态不是 Running，当前状态: %s", podName, pod.Status.Phase))
		return fmt.Errorf("Pod %s 状态不是 Running，当前状态: %s", podName, pod.Status.Phase)
	}

	// 执行端口转发
	return performPortForward(config, pfNamespace, podName, ports)
}

// parsePortSpecs 解析端口规范
func parsePortSpecs(specs []string) ([]PortForwardSpec, error) {
	var ports []PortForwardSpec

	for _, spec := range specs {
		port, err := parsePortSpec(spec)
		if err != nil {
			return nil, fmt.Errorf("无效的端口规范 '%s': %w", spec, err)
		}
		ports = append(ports, port)
	}

	return ports, nil
}

// parsePortSpec 解析单个端口规范
func parsePortSpec(spec string) (PortForwardSpec, error) {
	parts := strings.Split(spec, ":")

	switch len(parts) {
	case 1:
		// 格式: 8080 (本地端口和远程端口相同)
		port, err := strconv.Atoi(parts[0])
		if err != nil {
			return PortForwardSpec{}, fmt.Errorf("无效的端口号: %s", parts[0])
		}
		return PortForwardSpec{LocalPort: port, RemotePort: port}, nil

	case 2:
		// 格式: 8080:80 或 :80
		var localPort, remotePort int
		var err error

		if parts[0] == "" {
			// 格式: :80 (自动选择本地端口)
			localPort = 0
		} else {
			localPort, err = strconv.Atoi(parts[0])
			if err != nil {
				return PortForwardSpec{}, fmt.Errorf("无效的本地端口号: %s", parts[0])
			}
		}

		remotePort, err = strconv.Atoi(parts[1])
		if err != nil {
			return PortForwardSpec{}, fmt.Errorf("无效的远程端口号: %s", parts[1])
		}

		return PortForwardSpec{LocalPort: localPort, RemotePort: remotePort}, nil

	default:
		return PortForwardSpec{}, fmt.Errorf("端口规范格式错误，应为 [LOCAL_PORT:]REMOTE_PORT")
	}
}

// performPortForward 执行实际的端口转发
func performPortForward(config *rest.Config, namespace, podName string, ports []PortForwardSpec) error {
	// 构建端口转发字符串
	var portStrings []string
	for _, port := range ports {
		if port.LocalPort == 0 {
			portStrings = append(portStrings, fmt.Sprintf(":%d", port.RemotePort))
		} else {
			portStrings = append(portStrings, fmt.Sprintf("%d:%d", port.LocalPort, port.RemotePort))
		}
	}

	// 创建端口转发请求
	roundTripper, upgrader, err := spdy.RoundTripperFor(config)
	if err != nil {
		return fmt.Errorf("创建 SPDY round tripper 失败: %w", err)
	}

	path := fmt.Sprintf("/api/v1/namespaces/%s/pods/%s/portforward", namespace, podName)
	hostIP := strings.TrimLeft(config.Host, "htps:/")
	serverURL := url.URL{Scheme: "https", Path: path, Host: hostIP}

	dialer := spdy.NewDialer(upgrader, &http.Client{Transport: roundTripper}, http.MethodPost, &serverURL)

	// 设置停止和就绪通道
	stopCh := make(chan struct{}, 1)
	readyCh := make(chan struct{}, 1)

	// 创建端口转发器
	forwarder, err := portforward.New(dialer, portStrings, stopCh, readyCh, os.Stdout, os.Stderr)
	if err != nil {
		return fmt.Errorf("创建端口转发器失败: %w", err)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		fmt.Println()
		common.PrintInfo("收到退出信号，正在停止端口转发...")
		close(stopCh)
	}()

	// 启动端口转发
	go func() {
		if err := forwarder.ForwardPorts(); err != nil {
			fmt.Printf("端口转发错误: %v\n", err)
		}
	}()

	// 等待端口转发就绪
	select {
	case <-readyCh:
		break
	case <-time.After(30 * time.Second):
		return fmt.Errorf("端口转发启动超时")
	}

	// 获取实际转发的端口
	forwardedPorts, err := forwarder.GetPorts()
	if err != nil {
		return fmt.Errorf("获取转发端口信息失败: %w", err)
	}

	// 显示端口转发信息
	fmt.Printf("端口转发已启动，转发到 Pod %s/%s:\n", namespace, podName)
	for _, port := range forwardedPorts {
		fmt.Printf("  本地端口 %d -> Pod 端口 %d\n", port.Local, port.Remote)
		fmt.Printf("  访问地址: http://%s:%d\n", address, port.Local)
	}
	fmt.Println("\n按 Ctrl+C 停止端口转发")

	// 等待停止信号
	<-stopCh
	common.PrintSuccess("端口转发已停止")

	return nil
}
