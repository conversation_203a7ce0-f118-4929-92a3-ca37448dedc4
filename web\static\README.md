# K8s-Helper Web Static Files

This directory contains static assets for the K8s-Helper web interface.

## Directory Structure

```
web/static/
├── css/           # Stylesheets
│   └── main.css   # Main application styles
├── js/            # JavaScript files
│   └── main.js    # Main application logic
├── images/        # Images and icons
├── fonts/         # Custom fonts (if any)
├── manifest.json  # PWA manifest
└── README.md      # This file
```

## Files Description

### CSS Files
- `main.css` - Main stylesheet with responsive design, component styles, and utility classes

### JavaScript Files
- `main.js` - Main application logic including:
  - Tab management
  - API communication
  - UI interactions
  - Utility functions

### Configuration Files
- `manifest.json` - Progressive Web App manifest for mobile support

## Usage

These static files are served by the Gin web server at the `/static` path.

Example URLs:
- CSS: `http://localhost:8080/static/css/main.css`
- JS: `http://localhost:8080/static/js/main.js`
- Manifest: `http://localhost:8080/static/manifest.json`

## Development

When adding new static files:
1. Place them in the appropriate subdirectory
2. Update this README if adding new file types
3. Reference them in the HTML templates using the `/static` prefix

## Browser Support

The CSS and JavaScript are designed to work with modern browsers:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
