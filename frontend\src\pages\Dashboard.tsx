import React, { useState, useEffect } from 'react';
import { Row, Col, Typography, Space, Button, Card, Statistic, Alert, Progress, List, Avatar, Spin } from 'antd';
import {
  ReloadOutlined,
  SettingOutlined,
  FullscreenOutlined,
  ClusterOutlined,
  DatabaseOutlined,
  ContainerOutlined,
  <PERSON>boltOutlined,
  HddOutlined,
  BellOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CloudServerOutlined,
  ApiOutlined,
  MonitorOutlined,
} from '@ant-design/icons';
import { useDashboardData, useClusterHealth } from '@/hooks';

const { Title } = Typography;

// 活动类型图标映射
const getActivityIcon = (type: string) => {
  switch (type) {
    case 'etcd':
      return <DatabaseOutlined />;
    case 'pod':
      return <ContainerOutlined />;
    case 'alert':
      return <ExclamationCircleOutlined />;
    case 'cluster':
      return <ClusterOutlined />;
    case 'service':
      return <ApiOutlined />;
    default:
      return <InfoCircleOutlined />;
  }
};

/**
 * 仪表板页面 - 集群概览
 *
 * 显示Kubernetes集群的关键指标和状态信息
 */
const Dashboard: React.FC = () => {
  // 获取Dashboard数据
  const { data: dashboardData, isLoading, error, refetch } = useDashboardData();
  const { data: healthData } = useClusterHealth();

  // 设置页面标题
  useEffect(() => {
    document.title = '集群概览 - K8s Helper';
  }, []);

  // 手动刷新
  const handleRefresh = () => {
    refetch();
  };

  // 如果数据加载中
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '400px'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // 如果加载出错
  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="数据加载失败"
          description="无法获取集群数据，请检查后端服务是否正常运行。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={handleRefresh}>
              重试
            </Button>
          }
        />
      </div>
    );
  }

  // 如果没有数据
  if (!dashboardData) {
    return null;
  }

  return (
    <div style={{
      padding: '16px 24px',
      minHeight: '100vh',
      backgroundColor: '#f5f7fa',
      width: '100%',
      maxWidth: 'none' // 移除最大宽度限制，充分利用宽屏
    }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <Title level={2} style={{ margin: 0 }}>集群概览</Title>
          <div style={{ marginTop: '8px', color: '#666' }}>
            Kubernetes 集群状态监控和管理
          </div>
        </div>

        <Space>
          <Button
            icon={<ReloadOutlined />}
            loading={isLoading}
            onClick={handleRefresh}
          >
            刷新
          </Button>
          <Button
            icon={<SettingOutlined />}
            type="default"
          >
            设置
          </Button>
          <Button
            icon={<FullscreenOutlined />}
            onClick={() => {
              if (document.fullscreenElement) {
                document.exitFullscreen();
              } else {
                document.documentElement.requestFullscreen();
              }
            }}
          >
            全屏
          </Button>
        </Space>
      </div>

      {/* 指标卡片 - 优化宽屏布局 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={8} lg={6} xl={4} xxl={4}>
          <Card>
            <Statistic
              title="集群节点"
              value={dashboardData.cluster.nodes}
              prefix={<ClusterOutlined />}
              suffix="个"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} xl={4} xxl={4}>
          <Card>
            <Statistic
              title="ETCD状态"
              value={dashboardData.etcd.healthy ? '健康' : '异常'}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: dashboardData.etcd.healthy ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} xl={4} xxl={4}>
          <Card>
            <Statistic
              title="运行Pod"
              value={dashboardData.cluster.pods.running}
              prefix={<ContainerOutlined />}
              suffix={`/ ${dashboardData.cluster.pods.total}`}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} xl={4} xxl={4}>
          <Card>
            <Statistic
              title="CPU使用率"
              value={dashboardData.metrics.cpu.toFixed(1)}
              prefix={<ThunderboltOutlined />}
              suffix="%"
              valueStyle={{
                color: dashboardData.metrics.cpu > 80 ? '#cf1322' :
                       dashboardData.metrics.cpu > 60 ? '#fa8c16' : '#3f8600'
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} xl={4} xxl={4}>
          <Card>
            <Statistic
              title="内存使用率"
              value={dashboardData.metrics.memory.toFixed(1)}
              prefix={<HddOutlined />}
              suffix="%"
              valueStyle={{
                color: dashboardData.metrics.memory > 85 ? '#cf1322' :
                       dashboardData.metrics.memory > 70 ? '#fa8c16' : '#3f8600'
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} xl={4} xxl={4}>
          <Card>
            <Statistic
              title="失败Pod"
              value={dashboardData.cluster.pods.failed}
              prefix={<BellOutlined />}
              suffix="个"
              valueStyle={{ color: dashboardData.cluster.pods.failed > 0 ? '#cf1322' : '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 资源使用情况 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={12}>
          <Card title="资源使用情况" style={{ height: '300px' }}>
            <div style={{ padding: '20px 0' }}>
              <div style={{ marginBottom: '24px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span>CPU使用率</span>
                  <span>{dashboardData.metrics.cpu.toFixed(1)}%</span>
                </div>
                <Progress
                  percent={dashboardData.metrics.cpu}
                  strokeColor={dashboardData.metrics.cpu > 80 ? '#ff4d4f' : dashboardData.metrics.cpu > 60 ? '#faad14' : '#52c41a'}
                  showInfo={false}
                />
              </div>

              <div style={{ marginBottom: '24px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span>内存使用率</span>
                  <span>{dashboardData.metrics.memory.toFixed(1)}%</span>
                </div>
                <Progress
                  percent={dashboardData.metrics.memory}
                  strokeColor={dashboardData.metrics.memory > 85 ? '#ff4d4f' : dashboardData.metrics.memory > 70 ? '#faad14' : '#52c41a'}
                  showInfo={false}
                />
              </div>

              <div style={{ marginBottom: '24px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span>磁盘使用率</span>
                  <span>{dashboardData.metrics.disk.toFixed(1)}%</span>
                </div>
                <Progress
                  percent={dashboardData.metrics.disk}
                  strokeColor={dashboardData.metrics.disk > 85 ? '#ff4d4f' : dashboardData.metrics.disk > 70 ? '#faad14' : '#52c41a'}
                  showInfo={false}
                />
              </div>

              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span>Pod运行率</span>
                  <span>{((dashboardData.cluster.pods.running / dashboardData.cluster.pods.total) * 100).toFixed(1)}%</span>
                </div>
                <Progress
                  percent={(dashboardData.cluster.pods.running / dashboardData.cluster.pods.total) * 100}
                  strokeColor="#1890ff"
                  showInfo={false}
                />
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="最近活动" style={{ height: '300px' }}>
            <List
              itemLayout="horizontal"
              dataSource={dashboardData.recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={getActivityIcon(item.type)}
                        style={{
                          backgroundColor:
                            item.status === 'success' ? '#52c41a' :
                            item.status === 'warning' ? '#faad14' :
                            item.status === 'error' ? '#ff4d4f' : '#1890ff'
                        }}
                      />
                    }
                    title={item.title}
                    description={
                      <div>
                        <div>{item.description}</div>
                        <div style={{ color: '#8c8c8c', fontSize: '12px', marginTop: '4px' }}>
                          {item.time}
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统概览 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} lg={16}>
          <Card title="系统概览" style={{ height: '300px' }}>
            <Row gutter={[16, 16]} style={{ height: '200px' }}>
              <Col span={12}>
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <CloudServerOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
                  <h3>K8s-Helper</h3>
                  <p style={{ color: '#666' }}>Kubernetes 集群管理助手</p>
                  <div style={{ marginTop: '16px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <span>版本</span>
                      <span>v1.0.0</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <span>运行时间</span>
                      <span>2天3小时</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>连接状态</span>
                      <span style={{ color: '#52c41a' }}>✅ 已连接</span>
                    </div>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ padding: '20px' }}>
                  <h4 style={{ marginBottom: '16px' }}>快速操作</h4>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Button type="primary" icon={<DatabaseOutlined />} block>
                      ETCD备份
                    </Button>
                    <Button icon={<MonitorOutlined />} block>
                      查看监控
                    </Button>
                    <Button icon={<ContainerOutlined />} block>
                      Pod管理
                    </Button>
                    <Button icon={<SettingOutlined />} block>
                      系统配置
                    </Button>
                  </Space>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="集群健康状态" style={{ height: '300px' }}>
            <div style={{ padding: '20px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '80px',
                backgroundColor: healthData?.overall === 'healthy' ? '#f6ffed' :
                                healthData?.overall === 'warning' ? '#fff7e6' : '#fff2f0',
                border: `1px solid ${healthData?.overall === 'healthy' ? '#b7eb8f' :
                                    healthData?.overall === 'warning' ? '#ffd591' : '#ffccc7'}`,
                borderRadius: '6px',
                color: healthData?.overall === 'healthy' ? '#52c41a' :
                       healthData?.overall === 'warning' ? '#fa8c16' : '#ff4d4f',
                fontSize: '18px',
                fontWeight: 'bold',
                marginBottom: '20px'
              }}>
                {healthData?.overall === 'healthy' ? <CheckCircleOutlined style={{ marginRight: '8px' }} /> :
                 healthData?.overall === 'warning' ? <ExclamationCircleOutlined style={{ marginRight: '8px' }} /> :
                 <ExclamationCircleOutlined style={{ marginRight: '8px' }} />}
                {healthData?.overall === 'healthy' ? '集群运行正常' :
                 healthData?.overall === 'warning' ? '集群有告警' : '集群状态异常'}
              </div>

              <div>
                <h4 style={{ marginBottom: '12px' }}>组件状态</h4>
                {healthData?.components.map((component, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    padding: '6px 0',
                    borderBottom: index < healthData.components.length - 1 ? '1px solid #f0f0f0' : 'none'
                  }}>
                    <span>{component.name}</span>
                    <span style={{
                      color: component.status === 'healthy' ? '#52c41a' :
                             component.status === 'warning' ? '#fa8c16' : '#ff4d4f'
                    }}>
                      <CheckCircleOutlined /> {component.message}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
