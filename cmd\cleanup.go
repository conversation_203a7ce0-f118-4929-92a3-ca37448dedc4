package cmd

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cobra"
	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"k8s-helper/pkg/cmdhelp"
	"k8s-helper/pkg/common"
	"k8s-helper/pkg/k8s"
)

// cleanupCmd 代表 cleanup 命令
var cleanupCmd = cmdhelp.CleanupHelp.BuildCommand(runCleanupCommand, &cmdhelp.ErrorConfig{
	CustomValidator: cmdhelp.ValidateCleanupType,
})

var (
	// 清理的命名空间
	cleanupNamespace string
	// 所有命名空间
	allNamespaces bool
	// 干运行模式
	dryRun bool
	// 清理早于指定时间的资源
	olderThan string
)

func init() {
	cleanupCmd.Flags().StringVarP(&cleanupNamespace, "namespace", "n", "",
		"指定命名空间 (为空时使用默认命名空间)")
	cleanupCmd.Flags().BoolVarP(&allNamespaces, "all-namespaces", "A", common.DefaultAllNamespaces,
		"清理所有命名空间的资源")
	cleanupCmd.Flags().BoolVar(&dryRun, "dry-run", common.DefaultDryRun,
		"预览模式，不实际删除资源")
	cleanupCmd.Flags().StringVar(&olderThan, "older-than", "",
		"仅清理早于指定时间的资源 (例如: 1h, 24h, 7d)")
}

// CleanupStats 清理统计信息
type CleanupStats struct {
	EvictedPods      int
	CompletedJobs    int
	FailedJobs       int
	FailedPods       int
	PendingPods      int
	UnusedConfigMaps int
	UnusedSecrets    int
	TotalCleaned     int
	Errors           []string
	ProcessedNS      []string
}

// runCleanupCommand 执行清理命令的主要逻辑
func runCleanupCommand(cmd *cobra.Command, args []string) error {
	cleanupType := args[0]

	// 验证清理类型
	validTypes := []string{"pods", "jobs", "failed-pods", "pending-pods", "all"}
	if !common.Contains(validTypes, cleanupType) {
		return fmt.Errorf("无效的清理类型 '%s'，支持的类型: %s",
			cleanupType, strings.Join(validTypes, ", "))
	}

	// 创建 Kubernetes 客户端
	clientset, err := k8s.NewClient(kubeconfig)
	if err != nil {
		return fmt.Errorf("创建 Kubernetes 客户端失败: %w", err)
	}

	ctx := context.Background()

	// 解析时间过滤器
	var olderThanDuration time.Duration
	if olderThan != "" {
		olderThanDuration, err = time.ParseDuration(olderThan)
		if err != nil {
			return fmt.Errorf("无效的时间格式 '%s': %w", olderThan, err)
		}
	}

	// 获取要清理的命名空间列表
	namespaces, err := getNamespacesToClean(ctx, clientset)
	if err != nil {
		return fmt.Errorf("获取命名空间列表失败: %w", err)
	}

	// 执行清理
	stats := &CleanupStats{}

	for _, ns := range namespaces {
		fmt.Printf("正在检查命名空间: %s\n", ns)
		stats.ProcessedNS = append(stats.ProcessedNS, ns)

		switch cleanupType {
		case "pods":
			if err := cleanupEvictedPods(ctx, clientset, ns, olderThanDuration, stats); err != nil {
				stats.Errors = append(stats.Errors, fmt.Sprintf("清理命名空间 %s 的 Evicted Pod 失败: %v", ns, err))
			}
		case "failed-pods":
			if err := cleanupFailedPods(ctx, clientset, ns, olderThanDuration, stats); err != nil {
				stats.Errors = append(stats.Errors, fmt.Sprintf("清理命名空间 %s 的 Failed Pod 失败: %v", ns, err))
			}
		case "pending-pods":
			if err := cleanupPendingPods(ctx, clientset, ns, olderThanDuration, stats); err != nil {
				stats.Errors = append(stats.Errors, fmt.Sprintf("清理命名空间 %s 的 Pending Pod 失败: %v", ns, err))
			}
		case "jobs":
			if err := cleanupCompletedJobs(ctx, clientset, ns, olderThanDuration, stats); err != nil {
				stats.Errors = append(stats.Errors, fmt.Sprintf("清理命名空间 %s 的 Job 失败: %v", ns, err))
			}
		case "all":
			// 清理所有类型的资源
			if err := cleanupEvictedPods(ctx, clientset, ns, olderThanDuration, stats); err != nil {
				stats.Errors = append(stats.Errors, fmt.Sprintf("清理命名空间 %s 的 Evicted Pod 失败: %v", ns, err))
			}
			if err := cleanupFailedPods(ctx, clientset, ns, olderThanDuration, stats); err != nil {
				stats.Errors = append(stats.Errors, fmt.Sprintf("清理命名空间 %s 的 Failed Pod 失败: %v", ns, err))
			}
			if err := cleanupPendingPods(ctx, clientset, ns, olderThanDuration, stats); err != nil {
				stats.Errors = append(stats.Errors, fmt.Sprintf("清理命名空间 %s 的 Pending Pod 失败: %v", ns, err))
			}
			if err := cleanupCompletedJobs(ctx, clientset, ns, olderThanDuration, stats); err != nil {
				stats.Errors = append(stats.Errors, fmt.Sprintf("清理命名空间 %s 的 Job 失败: %v", ns, err))
			}
		}
	}

	// 显示清理结果
	printCleanupStats(stats)

	return nil
}

// getNamespacesToClean 获取要清理的命名空间列表
func getNamespacesToClean(ctx context.Context, clientset *kubernetes.Clientset) ([]string, error) {
	if allNamespaces {
		// 获取所有命名空间
		namespaces, err := clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
		if err != nil {
			return nil, err
		}

		var nsNames []string
		for _, ns := range namespaces.Items {
			nsNames = append(nsNames, ns.Name)
		}
		return nsNames, nil
	}

	// 使用指定的命名空间或默认命名空间
	if cleanupNamespace == "" {
		cleanupNamespace = "default"
	}

	return []string{cleanupNamespace}, nil
}

// cleanupEvictedPods 清理被驱逐的 Pod
func cleanupEvictedPods(ctx context.Context, clientset *kubernetes.Clientset,
	namespace string, olderThan time.Duration, stats *CleanupStats) error {

	// 获取所有 Pod
	pods, err := clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}

	var evictedPods []v1.Pod
	for _, pod := range pods.Items {
		if pod.Status.Phase == v1.PodFailed && pod.Status.Reason == "Evicted" {
			// 检查时间过滤器
			if olderThan > 0 {
				if time.Since(pod.CreationTimestamp.Time) < olderThan {
					continue
				}
			}
			evictedPods = append(evictedPods, pod)
		}
	}

	if len(evictedPods) == 0 {
		fmt.Printf("  命名空间 %s: 未找到需要清理的 Evicted Pod\n", namespace)
		return nil
	}

	fmt.Printf("  命名空间 %s: 找到 %d 个 Evicted Pod\n", namespace, len(evictedPods))

	if dryRun {
		fmt.Println("  [DRY RUN] 将要删除的 Pod:")
		for _, pod := range evictedPods {
			fmt.Printf("    - %s (创建时间: %s)\n", pod.Name, pod.CreationTimestamp.Format(time.RFC3339))
		}
		return nil
	}

	// 确认删除
	if !force && !common.ConfirmDeletion(fmt.Sprintf("删除 %d 个 Evicted Pod", len(evictedPods))) {
		fmt.Println("  取消删除操作")
		return nil
	}

	// 删除 Pod
	for _, pod := range evictedPods {
		err := clientset.CoreV1().Pods(namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{})
		if err != nil {
			stats.Errors = append(stats.Errors, fmt.Sprintf("删除 Pod %s/%s 失败: %v", namespace, pod.Name, err))
		} else {
			stats.EvictedPods++
			stats.TotalCleaned++
			if verbose {
				fmt.Printf("    ✓ 已删除 Pod: %s\n", pod.Name)
			}
		}
	}

	return nil
}

// cleanupFailedPods 清理失败状态的 Pod（非 Evicted）
func cleanupFailedPods(ctx context.Context, clientset *kubernetes.Clientset,
	namespace string, olderThan time.Duration, stats *CleanupStats) error {

	// 获取所有 Pod
	pods, err := clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}

	var failedPods []v1.Pod
	for _, pod := range pods.Items {
		// 查找失败状态的 Pod（排除 Evicted）
		if pod.Status.Phase == v1.PodFailed && pod.Status.Reason != "Evicted" {
			// 检查时间过滤器
			if olderThan > 0 {
				if time.Since(pod.CreationTimestamp.Time) < olderThan {
					continue
				}
			}
			failedPods = append(failedPods, pod)
		}
	}

	if len(failedPods) == 0 {
		fmt.Printf("  命名空间 %s: 未找到需要清理的 Failed Pod\n", namespace)
		return nil
	}

	fmt.Printf("  命名空间 %s: 找到 %d 个 Failed Pod\n", namespace, len(failedPods))

	if dryRun {
		fmt.Println("  [DRY RUN] 将要删除的 Failed Pod:")
		for _, pod := range failedPods {
			fmt.Printf("    - %s (原因: %s, 创建时间: %s)\n",
				pod.Name, pod.Status.Reason, pod.CreationTimestamp.Format(time.RFC3339))
		}
		return nil
	}

	// 确认删除
	if !force && !common.ConfirmDeletion(fmt.Sprintf("删除 %d 个 Failed Pod", len(failedPods))) {
		fmt.Println("  取消删除操作")
		return nil
	}

	// 删除 Pod
	for _, pod := range failedPods {
		err := clientset.CoreV1().Pods(namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{})
		if err != nil {
			stats.Errors = append(stats.Errors, fmt.Sprintf("删除 Failed Pod %s/%s 失败: %v", namespace, pod.Name, err))
		} else {
			stats.FailedPods++
			stats.TotalCleaned++
			if verbose {
				fmt.Printf("    ✓ 已删除 Failed Pod: %s\n", pod.Name)
			}
		}
	}

	return nil
}

// cleanupPendingPods 清理长时间处于 Pending 状态的 Pod
func cleanupPendingPods(ctx context.Context, clientset *kubernetes.Clientset,
	namespace string, olderThan time.Duration, stats *CleanupStats) error {

	// 获取所有 Pod
	pods, err := clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}

	// 如果没有指定时间过滤器，默认为 1 小时
	if olderThan == 0 {
		olderThan = 1 * time.Hour
	}

	var pendingPods []v1.Pod
	for _, pod := range pods.Items {
		if pod.Status.Phase == v1.PodPending {
			// 检查是否长时间处于 Pending 状态
			if time.Since(pod.CreationTimestamp.Time) >= olderThan {
				// 检查是否有调度问题
				hasSchedulingIssues := false
				for _, condition := range pod.Status.Conditions {
					if condition.Type == v1.PodScheduled && condition.Status == v1.ConditionFalse {
						if strings.Contains(condition.Reason, "Unschedulable") {
							hasSchedulingIssues = true
							break
						}
					}
				}

				if hasSchedulingIssues {
					pendingPods = append(pendingPods, pod)
				}
			}
		}
	}

	if len(pendingPods) == 0 {
		fmt.Printf("  命名空间 %s: 未找到需要清理的长时间 Pending Pod\n", namespace)
		return nil
	}

	fmt.Printf("  命名空间 %s: 找到 %d 个长时间 Pending Pod\n", namespace, len(pendingPods))

	if dryRun {
		fmt.Println("  [DRY RUN] 将要删除的 Pending Pod:")
		for _, pod := range pendingPods {
			fmt.Printf("    - %s (Pending 时长: %v)\n",
				pod.Name, time.Since(pod.CreationTimestamp.Time).Round(time.Minute))
		}
		return nil
	}

	// 确认删除
	if !force && !common.ConfirmDeletion(fmt.Sprintf("删除 %d 个长时间 Pending Pod", len(pendingPods))) {
		fmt.Println("  取消删除操作")
		return nil
	}

	// 删除 Pod
	for _, pod := range pendingPods {
		err := clientset.CoreV1().Pods(namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{})
		if err != nil {
			stats.Errors = append(stats.Errors, fmt.Sprintf("删除 Pending Pod %s/%s 失败: %v", namespace, pod.Name, err))
		} else {
			stats.PendingPods++
			stats.TotalCleaned++
			if verbose {
				fmt.Printf("    ✓ 已删除 Pending Pod: %s\n", pod.Name)
			}
		}
	}

	return nil
}

// cleanupCompletedJobs 清理已完成的 Job
func cleanupCompletedJobs(ctx context.Context, clientset *kubernetes.Clientset,
	namespace string, olderThan time.Duration, stats *CleanupStats) error {

	// 获取所有 Job
	jobs, err := clientset.BatchV1().Jobs(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}

	var completedJobs, failedJobs []batchv1.Job
	for _, job := range jobs.Items {
		// 检查时间过滤器
		if olderThan > 0 {
			if time.Since(job.CreationTimestamp.Time) < olderThan {
				continue
			}
		}

		// 检查 Job 状态
		for _, condition := range job.Status.Conditions {
			if condition.Type == batchv1.JobComplete && condition.Status == v1.ConditionTrue {
				completedJobs = append(completedJobs, job)
				break
			} else if condition.Type == batchv1.JobFailed && condition.Status == v1.ConditionTrue {
				failedJobs = append(failedJobs, job)
				break
			}
		}
	}

	totalJobs := len(completedJobs) + len(failedJobs)
	if totalJobs == 0 {
		fmt.Printf("  命名空间 %s: 未找到需要清理的已完成 Job\n", namespace)
		return nil
	}

	fmt.Printf("  命名空间 %s: 找到 %d 个已完成的 Job (%d 成功, %d 失败)\n",
		namespace, totalJobs, len(completedJobs), len(failedJobs))

	if dryRun {
		fmt.Println("  [DRY RUN] 将要删除的 Job:")
		for _, job := range completedJobs {
			fmt.Printf("    - %s (完成, 创建时间: %s)\n", job.Name, job.CreationTimestamp.Format(time.RFC3339))
		}
		for _, job := range failedJobs {
			fmt.Printf("    - %s (失败, 创建时间: %s)\n", job.Name, job.CreationTimestamp.Format(time.RFC3339))
		}
		return nil
	}

	// 确认删除
	if !force && !common.ConfirmDeletion(fmt.Sprintf("删除 %d 个已完成的 Job", totalJobs)) {
		fmt.Println("  取消删除操作")
		return nil
	}

	// 删除已完成的 Job
	for _, job := range completedJobs {
		err := clientset.BatchV1().Jobs(namespace).Delete(ctx, job.Name, metav1.DeleteOptions{
			PropagationPolicy: &[]metav1.DeletionPropagation{metav1.DeletePropagationForeground}[0],
		})
		if err != nil {
			stats.Errors = append(stats.Errors, fmt.Sprintf("删除 Job %s/%s 失败: %v", namespace, job.Name, err))
		} else {
			stats.CompletedJobs++
			stats.TotalCleaned++
			if verbose {
				fmt.Printf("    ✓ 已删除完成的 Job: %s\n", job.Name)
			}
		}
	}

	// 删除失败的 Job
	for _, job := range failedJobs {
		err := clientset.BatchV1().Jobs(namespace).Delete(ctx, job.Name, metav1.DeleteOptions{
			PropagationPolicy: &[]metav1.DeletionPropagation{metav1.DeletePropagationForeground}[0],
		})
		if err != nil {
			stats.Errors = append(stats.Errors, fmt.Sprintf("删除 Job %s/%s 失败: %v", namespace, job.Name, err))
		} else {
			stats.FailedJobs++
			stats.TotalCleaned++
			if verbose {
				fmt.Printf("    ✓ 已删除失败的 Job: %s\n", job.Name)
			}
		}
	}

	return nil
}

// printCleanupStats 打印清理统计信息
func printCleanupStats(stats *CleanupStats) {
	fmt.Println("\n=== 清理统计 ===")
	fmt.Printf("处理的命名空间: %s\n", strings.Join(stats.ProcessedNS, ", "))
	fmt.Printf("已清理的 Evicted Pod: %d\n", stats.EvictedPods)
	fmt.Printf("已清理的 Failed Pod: %d\n", stats.FailedPods)
	fmt.Printf("已清理的 Pending Pod: %d\n", stats.PendingPods)
	fmt.Printf("已清理的完成 Job: %d\n", stats.CompletedJobs)
	fmt.Printf("已清理的失败 Job: %d\n", stats.FailedJobs)
	fmt.Printf("总计清理资源: %d\n", stats.TotalCleaned)

	if len(stats.Errors) > 0 {
		fmt.Printf("\n错误 (%d):\n", len(stats.Errors))
		for _, err := range stats.Errors {
			fmt.Printf("  ❌ %s\n", err)
		}
	}

	if stats.TotalCleaned > 0 {
		fmt.Println()
		common.PrintSuccess("清理操作完成！")
	} else {
		fmt.Println()
		common.PrintInfo("未找到需要清理的资源")
	}
}
