# Go 相关文件
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# 构建产物
k8s-helper
build/
dist/

# IDE 相关文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log

# 临时文件
tmp/
temp/

# 备份文件
*.backup
*.bak
etcd-backup-*.db

# 配置文件（可能包含敏感信息）
config.yaml
config.json
*.kubeconfig

# 测试覆盖率文件
coverage.out
coverage.html

# 依赖目录
vendor/
