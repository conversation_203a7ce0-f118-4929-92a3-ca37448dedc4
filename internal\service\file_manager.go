package service

import (
	"crypto/md5"
	"crypto/sha256"
	"fmt"
	"os"
	"sync"
	"time"

	"go.uber.org/zap"
)

// FileManagerConfig 文件管理器配置
type FileManagerConfig struct {
	EnableFileCache   bool
	EnableFileWatcher bool
	EnableAsyncHasher bool
	FileCacheConfig   *FileCacheConfig
	FileWatcherConfig *FileWatcherConfig
	AsyncHasherConfig *AsyncHasherConfig
}

// DefaultFileManagerConfig 默认文件管理器配置
func DefaultFileManagerConfig() *FileManagerConfig {
	return &FileManagerConfig{
		EnableFileCache:   true,
		EnableFileWatcher: true,
		EnableAsyncHasher: true,
		FileCacheConfig:   DefaultFileCacheConfig(),
		FileWatcherConfig: DefaultFileWatcherConfig(),
		AsyncHasherConfig: DefaultAsyncHasherConfig(),
	}
}

// FileManager 统一文件操作管理器
type FileManager struct {
	config      *FileManagerConfig
	logger      *zap.Logger
	fileCache   *FileCache
	fileWatcher *FileWatcher
	asyncHasher *AsyncHasher
	mutex       sync.RWMutex
}

// NewFileManager 创建文件管理器
func NewFileManager(config *FileManagerConfig, logger *zap.Logger) *FileManager {
	if config == nil {
		config = DefaultFileManagerConfig()
	}

	fm := &FileManager{
		config: config,
		logger: logger,
	}

	// 初始化文件缓存
	if config.EnableFileCache {
		fm.fileCache = NewFileCache(config.FileCacheConfig, logger.With(zap.String("component", "fileCache")))
	}

	// 初始化文件监听器
	if config.EnableFileWatcher {
		fm.fileWatcher = NewFileWatcher(config.FileWatcherConfig, logger.With(zap.String("component", "fileWatcher")))
	}

	// 初始化异步哈希计算器
	if config.EnableAsyncHasher {
		fm.asyncHasher = NewAsyncHasher(config.AsyncHasherConfig, logger.With(zap.String("component", "asyncHasher")))
	}

	// 设置文件监听器与缓存的集成
	if fm.fileWatcher != nil && fm.fileCache != nil {
		fm.setupCacheInvalidation()
	}

	fm.logger.Info("文件管理器已初始化",
		zap.Bool("fileCache", config.EnableFileCache),
		zap.Bool("fileWatcher", config.EnableFileWatcher),
		zap.Bool("asyncHasher", config.EnableAsyncHasher))

	return fm
}

// ReadFile 读取文件内容
func (fm *FileManager) ReadFile(filePath string) ([]byte, error) {
	if fm.fileCache != nil {
		return fm.fileCache.ReadFile(filePath)
	}

	// 回退到直接文件读取
	return os.ReadFile(filePath)
}

// ReadFileWithHash 读取文件内容并计算哈希
func (fm *FileManager) ReadFileWithHash(filePath string, hashType string) ([]byte, string, error) {
	if fm.fileCache != nil {
		return fm.fileCache.ReadFileWithHash(filePath, hashType)
	}

	// 回退到直接文件读取和哈希计算
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, "", err
	}

	if fm.asyncHasher != nil {
		result, err := fm.asyncHasher.CalculateHashSync(filePath, HashType(hashType))
		if err != nil {
			return content, "", err
		}
		return content, result.Hash, nil
	}

	// 最后回退到同步哈希计算
	hash, err := fm.calculateHashSync(content, hashType)
	return content, hash, err
}

// CalculateHashAsync 异步计算文件哈希
func (fm *FileManager) CalculateHashAsync(filePath string, hashType string, callback func(hash string, err error)) (string, error) {
	if fm.asyncHasher == nil {
		return "", fmt.Errorf("异步哈希计算器未启用")
	}

	wrappedCallback := func(result *HashResult) {
		if callback != nil {
			callback(result.Hash, result.Error)
		}
	}

	return fm.asyncHasher.CalculateHash(filePath, HashType(hashType), wrappedCallback)
}

// CalculateHashSync 同步计算文件哈希
func (fm *FileManager) CalculateHashSync(filePath string, hashType string) (string, error) {
	if fm.asyncHasher != nil {
		result, err := fm.asyncHasher.CalculateHashSync(filePath, HashType(hashType))
		if err != nil {
			return "", err
		}
		return result.Hash, nil
	}

	// 回退到直接文件读取和哈希计算
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}

	return fm.calculateHashSync(content, hashType)
}

// WatchFile 监听文件变化
func (fm *FileManager) WatchFile(filePath string, callback func(event *FileEventInfo)) error {
	if fm.fileWatcher == nil {
		return fmt.Errorf("文件监听器未启用")
	}

	return fm.fileWatcher.WatchFile(filePath, callback)
}

// UnwatchFile 停止监听文件
func (fm *FileManager) UnwatchFile(filePath string) error {
	if fm.fileWatcher == nil {
		return fmt.Errorf("文件监听器未启用")
	}

	return fm.fileWatcher.UnwatchFile(filePath)
}

// InvalidateCache 使文件缓存失效
func (fm *FileManager) InvalidateCache(filePath string) {
	if fm.fileCache != nil {
		fm.fileCache.InvalidateFile(filePath)
	}
}

// GetStats 获取所有组件的统计信息
func (fm *FileManager) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if fm.fileCache != nil {
		stats["fileCache"] = fm.fileCache.GetStats()
	}

	if fm.fileWatcher != nil {
		stats["fileWatcher"] = fm.fileWatcher.GetStats()
	}

	if fm.asyncHasher != nil {
		stats["asyncHasher"] = fm.asyncHasher.GetStats()
	}

	return stats
}

// Close 关闭文件管理器
func (fm *FileManager) Close() error {
	fm.logger.Info("关闭文件管理器")

	var errors []error

	// 关闭异步哈希计算器
	if fm.asyncHasher != nil {
		if err := fm.asyncHasher.Close(); err != nil {
			errors = append(errors, fmt.Errorf("关闭异步哈希计算器失败: %w", err))
		}
	}

	// 关闭文件监听器
	if fm.fileWatcher != nil {
		if err := fm.fileWatcher.Close(); err != nil {
			errors = append(errors, fmt.Errorf("关闭文件监听器失败: %w", err))
		}
	}

	// 关闭文件缓存
	if fm.fileCache != nil {
		if err := fm.fileCache.Close(); err != nil {
			errors = append(errors, fmt.Errorf("关闭文件缓存失败: %w", err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("关闭文件管理器时发生错误: %v", errors)
	}

	fm.logger.Info("文件管理器已关闭")
	return nil
}

// setupCacheInvalidation 设置缓存失效机制
func (fm *FileManager) setupCacheInvalidation() {
	// 当文件发生变化时，自动使缓存失效
	cacheInvalidationHandler := func(event *FileEventInfo) {
		switch event.Event {
		case FileModified, FileDeleted:
			fm.fileCache.InvalidateFile(event.Path)
			fm.logger.Debug("文件变化，缓存已失效",
				zap.String("path", event.Path),
				zap.String("event", event.Event.String()))
		}
	}

	// 注意：这里不能直接调用WatchFile，因为会导致循环依赖
	// 实际使用时，需要在外部设置监听
	_ = cacheInvalidationHandler
}

// calculateHashSync 同步计算内容哈希
func (fm *FileManager) calculateHashSync(content []byte, hashType string) (string, error) {
	switch hashType {
	case "md5":
		return fmt.Sprintf("%x", md5.Sum(content)), nil
	case "sha256":
		return fmt.Sprintf("%x", sha256.Sum256(content)), nil
	default:
		return "", fmt.Errorf("不支持的哈希类型: %s", hashType)
	}
}

// FileOperationResult 文件操作结果
type FileOperationResult struct {
	Success   bool
	Message   string
	Data      interface{}
	Duration  time.Duration
	Timestamp time.Time
}

// BatchReadFiles 批量读取文件
func (fm *FileManager) BatchReadFiles(filePaths []string) map[string]*FileOperationResult {
	results := make(map[string]*FileOperationResult)
	var wg sync.WaitGroup
	var mutex sync.Mutex

	for _, filePath := range filePaths {
		wg.Add(1)
		go func(path string) {
			defer wg.Done()

			startTime := time.Now()
			content, err := fm.ReadFile(path)
			duration := time.Since(startTime)

			result := &FileOperationResult{
				Success:   err == nil,
				Duration:  duration,
				Timestamp: time.Now(),
			}

			if err != nil {
				result.Message = err.Error()
			} else {
				result.Data = content
				result.Message = "读取成功"
			}

			mutex.Lock()
			results[path] = result
			mutex.Unlock()
		}(filePath)
	}

	wg.Wait()
	return results
}

// BatchCalculateHashes 批量计算文件哈希
func (fm *FileManager) BatchCalculateHashes(filePaths []string, hashType string) map[string]*FileOperationResult {
	results := make(map[string]*FileOperationResult)
	var wg sync.WaitGroup
	var mutex sync.Mutex

	for _, filePath := range filePaths {
		wg.Add(1)
		go func(path string) {
			defer wg.Done()

			startTime := time.Now()
			hash, err := fm.CalculateHashSync(path, hashType)
			duration := time.Since(startTime)

			result := &FileOperationResult{
				Success:   err == nil,
				Duration:  duration,
				Timestamp: time.Now(),
			}

			if err != nil {
				result.Message = err.Error()
			} else {
				result.Data = hash
				result.Message = "计算成功"
			}

			mutex.Lock()
			results[path] = result
			mutex.Unlock()
		}(filePath)
	}

	wg.Wait()
	return results
}
