package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// AuditEvent 审计事件类型
type AuditEvent string

const (
	// 备份相关事件
	AuditEventBackupStarted   AuditEvent = "backup_started"
	AuditEventBackupCompleted AuditEvent = "backup_completed"
	AuditEventBackupFailed    AuditEvent = "backup_failed"

	// 恢复相关事件
	AuditEventRestoreStarted   AuditEvent = "restore_started"
	AuditEventRestoreCompleted AuditEvent = "restore_completed"
	AuditEventRestoreFailed    AuditEvent = "restore_failed"

	// 验证相关事件
	AuditEventVerifyStarted   AuditEvent = "verify_started"
	AuditEventVerifyCompleted AuditEvent = "verify_completed"
	AuditEventVerifyFailed    AuditEvent = "verify_failed"

	// API相关事件
	AuditEventAPIRequest  AuditEvent = "api_request"
	AuditEventAPIResponse AuditEvent = "api_response"
	AuditEventAPIError    AuditEvent = "api_error"

	// 配置相关事件
	AuditEventConfigLoaded  AuditEvent = "config_loaded"
	AuditEventConfigChanged AuditEvent = "config_changed"
	AuditEventConfigError   AuditEvent = "config_error"

	// 系统相关事件
	AuditEventSystemStartup  AuditEvent = "system_startup"
	AuditEventSystemShutdown AuditEvent = "system_shutdown"
	AuditEventSystemError    AuditEvent = "system_error"
)

// AuditLevel 审计级别
type AuditLevel string

const (
	AuditLevelInfo    AuditLevel = "info"
	AuditLevelWarning AuditLevel = "warning"
	AuditLevelError   AuditLevel = "error"
	AuditLevelCritical AuditLevel = "critical"
)

// AuditRecord 审计记录
type AuditRecord struct {
	Timestamp   time.Time              `json:"timestamp"`
	Event       AuditEvent             `json:"event"`
	Level       AuditLevel             `json:"level"`
	UserID      string                 `json:"user_id,omitempty"`
	RequestID   string                 `json:"request_id,omitempty"`
	Operation   string                 `json:"operation"`
	Resource    string                 `json:"resource,omitempty"`
	Action      string                 `json:"action"`
	Result      string                 `json:"result"`
	Duration    time.Duration          `json:"duration,omitempty"`
	ErrorCode   string                 `json:"error_code,omitempty"`
	Message     string                 `json:"message"`
	Details     map[string]interface{} `json:"details,omitempty"`
	ClientIP    string                 `json:"client_ip,omitempty"`
	UserAgent   string                 `json:"user_agent,omitempty"`
	Source      string                 `json:"source"`
}

// AuditLogger 审计日志记录器
type AuditLogger struct {
	logger *zap.Logger
	config *AuditConfig
}

// AuditConfig 审计配置
type AuditConfig struct {
	Enabled     bool   `yaml:"enabled"`
	Level       string `yaml:"level"`
	OutputFile  string `yaml:"output_file"`
	MaxSize     int    `yaml:"max_size"`     // MB
	MaxBackups  int    `yaml:"max_backups"`
	MaxAge      int    `yaml:"max_age"`      // days
	Compress    bool   `yaml:"compress"`
	BufferSize  int    `yaml:"buffer_size"`
	FlushInterval time.Duration `yaml:"flush_interval"`
}

// DefaultAuditConfig 默认审计配置
func DefaultAuditConfig() *AuditConfig {
	return &AuditConfig{
		Enabled:       true,
		Level:         "info",
		OutputFile:    "audit.log",
		MaxSize:       100,
		MaxBackups:    10,
		MaxAge:        30,
		Compress:      true,
		BufferSize:    1000,
		FlushInterval: 5 * time.Second,
	}
}

// NewAuditLogger 创建审计日志记录器
func NewAuditLogger(config *AuditConfig) (*AuditLogger, error) {
	if config == nil {
		config = DefaultAuditConfig()
	}

	// 创建专门的审计日志配置
	zapConfig := zap.NewProductionConfig()
	zapConfig.OutputPaths = []string{config.OutputFile}
	zapConfig.ErrorOutputPaths = []string{config.OutputFile}
	zapConfig.Level = zap.NewAtomicLevelAt(zapcore.InfoLevel)

	// 自定义编码器配置
	zapConfig.EncoderConfig = zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	logger, err := zapConfig.Build()
	if err != nil {
		return nil, fmt.Errorf("创建审计日志记录器失败: %w", err)
	}

	return &AuditLogger{
		logger: logger,
		config: config,
	}, nil
}

// LogAuditEvent 记录审计事件
func (al *AuditLogger) LogAuditEvent(ctx context.Context, record *AuditRecord) {
	if !al.config.Enabled {
		return
	}

	// 设置默认值
	if record.Timestamp.IsZero() {
		record.Timestamp = time.Now()
	}
	if record.Source == "" {
		record.Source = "k8s-helper"
	}

	// 从上下文中提取信息
	if requestID := GetRequestIDFromContext(ctx); requestID != "" {
		record.RequestID = requestID
	}
	if userID := GetUserIDFromContext(ctx); userID != "" {
		record.UserID = userID
	}

	// 构建日志字段
	fields := []zap.Field{
		zap.String("event", string(record.Event)),
		zap.String("audit_level", string(record.Level)),
		zap.String("operation", record.Operation),
		zap.String("action", record.Action),
		zap.String("result", record.Result),
		zap.String("source", record.Source),
	}

	// 添加可选字段
	if record.UserID != "" {
		fields = append(fields, zap.String("user_id", record.UserID))
	}
	if record.RequestID != "" {
		fields = append(fields, zap.String("request_id", record.RequestID))
	}
	if record.Resource != "" {
		fields = append(fields, zap.String("resource", record.Resource))
	}
	if record.Duration > 0 {
		fields = append(fields, zap.Duration("duration", record.Duration))
	}
	if record.ErrorCode != "" {
		fields = append(fields, zap.String("error_code", record.ErrorCode))
	}
	if record.ClientIP != "" {
		fields = append(fields, zap.String("client_ip", record.ClientIP))
	}
	if record.UserAgent != "" {
		fields = append(fields, zap.String("user_agent", record.UserAgent))
	}
	if record.Details != nil && len(record.Details) > 0 {
		detailsJSON, _ := json.Marshal(record.Details)
		fields = append(fields, zap.String("details", string(detailsJSON)))
	}

	// 根据审计级别选择日志级别
	switch record.Level {
	case AuditLevelInfo:
		al.logger.Info(record.Message, fields...)
	case AuditLevelWarning:
		al.logger.Warn(record.Message, fields...)
	case AuditLevelError:
		al.logger.Error(record.Message, fields...)
	case AuditLevelCritical:
		al.logger.Error(record.Message, fields...)
	default:
		al.logger.Info(record.Message, fields...)
	}
}

// LogBackupOperation 记录备份操作
func (al *AuditLogger) LogBackupOperation(ctx context.Context, event AuditEvent, operation string, details map[string]interface{}, duration time.Duration, err error) {
	level := AuditLevelInfo
	result := "success"
	message := fmt.Sprintf("备份操作: %s", operation)
	errorCode := ""

	if err != nil {
		level = AuditLevelError
		result = "failed"
		message = fmt.Sprintf("备份操作失败: %s - %v", operation, err)
		
		if enhancedErr, ok := err.(*EnhancedServiceError); ok {
			errorCode = string(enhancedErr.ErrorCode)
		}
	}

	record := &AuditRecord{
		Event:     event,
		Level:     level,
		Operation: "backup",
		Action:    operation,
		Result:    result,
		Duration:  duration,
		ErrorCode: errorCode,
		Message:   message,
		Details:   details,
	}

	al.LogAuditEvent(ctx, record)
}

// LogRestoreOperation 记录恢复操作
func (al *AuditLogger) LogRestoreOperation(ctx context.Context, event AuditEvent, operation string, details map[string]interface{}, duration time.Duration, err error) {
	level := AuditLevelInfo
	result := "success"
	message := fmt.Sprintf("恢复操作: %s", operation)
	errorCode := ""

	if err != nil {
		level = AuditLevelError
		result = "failed"
		message = fmt.Sprintf("恢复操作失败: %s - %v", operation, err)
		
		if enhancedErr, ok := err.(*EnhancedServiceError); ok {
			errorCode = string(enhancedErr.ErrorCode)
		}
	}

	record := &AuditRecord{
		Event:     event,
		Level:     level,
		Operation: "restore",
		Action:    operation,
		Result:    result,
		Duration:  duration,
		ErrorCode: errorCode,
		Message:   message,
		Details:   details,
	}

	al.LogAuditEvent(ctx, record)
}

// LogAPIOperation 记录API操作
func (al *AuditLogger) LogAPIOperation(ctx context.Context, method, path string, statusCode int, duration time.Duration, clientIP, userAgent string, err error) {
	event := AuditEventAPIRequest
	level := AuditLevelInfo
	result := "success"
	message := fmt.Sprintf("API请求: %s %s", method, path)
	errorCode := ""

	if err != nil || statusCode >= 400 {
		event = AuditEventAPIError
		level = AuditLevelError
		result = "failed"
		message = fmt.Sprintf("API请求失败: %s %s - 状态码: %d", method, path, statusCode)
		
		if enhancedErr, ok := err.(*EnhancedServiceError); ok {
			errorCode = string(enhancedErr.ErrorCode)
		}
	}

	details := map[string]interface{}{
		"method":      method,
		"path":        path,
		"status_code": statusCode,
	}

	record := &AuditRecord{
		Event:     event,
		Level:     level,
		Operation: "api",
		Action:    fmt.Sprintf("%s %s", method, path),
		Result:    result,
		Duration:  duration,
		ErrorCode: errorCode,
		Message:   message,
		Details:   details,
		ClientIP:  clientIP,
		UserAgent: userAgent,
	}

	al.LogAuditEvent(ctx, record)
}

// LogSystemEvent 记录系统事件
func (al *AuditLogger) LogSystemEvent(ctx context.Context, event AuditEvent, message string, details map[string]interface{}) {
	level := AuditLevelInfo
	if event == AuditEventSystemError {
		level = AuditLevelError
	}

	record := &AuditRecord{
		Event:     event,
		Level:     level,
		Operation: "system",
		Action:    string(event),
		Result:    "completed",
		Message:   message,
		Details:   details,
	}

	al.LogAuditEvent(ctx, record)
}

// Close 关闭审计日志记录器
func (al *AuditLogger) Close() error {
	return al.logger.Sync()
}

// 上下文键类型
type contextKey string

const (
	requestIDKey contextKey = "request_id"
	userIDKey    contextKey = "user_id"
)

// GetRequestIDFromContext 从上下文获取请求ID
func GetRequestIDFromContext(ctx context.Context) string {
	if requestID, ok := ctx.Value(requestIDKey).(string); ok {
		return requestID
	}
	return ""
}

// GetUserIDFromContext 从上下文获取用户ID
func GetUserIDFromContext(ctx context.Context) string {
	if userID, ok := ctx.Value(userIDKey).(string); ok {
		return userID
	}
	return ""
}

// WithRequestID 向上下文添加请求ID
func WithRequestID(ctx context.Context, requestID string) context.Context {
	return context.WithValue(ctx, requestIDKey, requestID)
}

// WithUserID 向上下文添加用户ID
func WithUserID(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, userIDKey, userID)
}