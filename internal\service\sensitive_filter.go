package service

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
)

// SensitiveString 敏感字符串类型，用于防止敏感信息泄露
type SensitiveString string

// String 实现Stringer接口，返回脱敏后的字符串
func (s SensitiveString) String() string {
	return "[REDACTED]"
}

// MarshalJSON 实现JSON序列化接口，返回脱敏后的JSON
func (s SensitiveString) MarshalJSON() ([]byte, error) {
	return json.Marshal("[REDACTED]")
}

// GoString 实现GoStringer接口，用于fmt.Printf("%#v")
func (s SensitiveString) GoString() string {
	return `"[REDACTED]"`
}

// SensitiveFilter 敏感信息过滤器
type SensitiveFilter struct {
	patterns []*regexp.Regexp
	keywords []string
}

// NewSensitiveFilter 创建敏感信息过滤器
func NewSensitiveFilter() *SensitiveFilter {
	// 定义敏感信息的正则表达式模式（按优先级排序）
	patterns := []*regexp.Regexp{
		// 证书内容 (PEM格式)
		regexp.MustCompile(`(?s)-----BEGIN[^-]*-----.*?-----END[^-]*-----`),

		// 私钥内容
		regexp.MustCompile(`(?s)-----BEGIN[^-]*PRIVATE KEY-----.*?-----END[^-]*PRIVATE KEY-----`),

		// JWT Token (完整匹配，优先于Base64模式)
		regexp.MustCompile(`eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+`),

		// 密码字段（password=xxx, pwd=xxx等）
		regexp.MustCompile(`(?i)(password|pwd|pass|secret|token|key)\s*[:=]\s*[^\s,;]+`),

		// Base64编码的长字符串（可能是证书或密钥）
		regexp.MustCompile(`[A-Za-z0-9+/]{64,}={0,2}`),

		// 证书序列号和指纹
		regexp.MustCompile(`(?i)(serial|fingerprint|thumbprint)\s*[:=]\s*[a-fA-F0-9:]{16,}`),

		// Kubernetes Secret数据
		regexp.MustCompile(`(?i)data:\s*\{[^}]*\}`),
	}

	// 定义敏感关键词
	keywords := []string{
		"password", "passwd", "pwd", "secret", "token", "key", "cert", "certificate",
		"private", "public", "rsa", "ecdsa", "ed25519", "x509", "pem", "der",
		"ca-cert", "client-cert", "server-cert", "tls-cert",
	}

	return &SensitiveFilter{
		patterns: patterns,
		keywords: keywords,
	}
}

// FilterString 过滤字符串中的敏感信息
func (sf *SensitiveFilter) FilterString(input string) string {
	if input == "" {
		return input
	}

	result := input

	// 应用正则表达式模式
	for _, pattern := range sf.patterns {
		result = pattern.ReplaceAllString(result, "[REDACTED]")
	}

	// 过滤包含敏感关键词的行
	lines := strings.Split(result, "\n")
	filteredLines := make([]string, 0, len(lines))

	for _, line := range lines {
		if sf.containsSensitiveKeyword(line) {
			// 如果行包含敏感关键词，进行部分脱敏
			filteredLines = append(filteredLines, sf.redactSensitiveLine(line))
		} else {
			filteredLines = append(filteredLines, line)
		}
	}

	return strings.Join(filteredLines, "\n")
}

// FilterError 过滤错误信息中的敏感内容
func (sf *SensitiveFilter) FilterError(err error) error {
	if err == nil {
		return nil
	}

	filteredMsg := sf.FilterString(err.Error())
	return fmt.Errorf(filteredMsg)
}

// FilterMap 过滤map中的敏感信息
func (sf *SensitiveFilter) FilterMap(input map[string]interface{}) map[string]interface{} {
	if input == nil {
		return nil
	}

	result := make(map[string]interface{})
	for key, value := range input {
		if sf.isSensitiveKey(key) {
			result[key] = "[REDACTED]"
		} else {
			switch v := value.(type) {
			case string:
				result[key] = sf.FilterString(v)
			case map[string]interface{}:
				result[key] = sf.FilterMap(v)
			case []interface{}:
				result[key] = sf.filterSlice(v)
			default:
				result[key] = value
			}
		}
	}

	return result
}

// containsSensitiveKeyword 检查字符串是否包含敏感关键词
func (sf *SensitiveFilter) containsSensitiveKeyword(input string) bool {
	lowerInput := strings.ToLower(input)
	for _, keyword := range sf.keywords {
		if strings.Contains(lowerInput, keyword) {
			return true
		}
	}
	return false
}

// redactSensitiveLine 对包含敏感信息的行进行脱敏
func (sf *SensitiveFilter) redactSensitiveLine(line string) string {
	// 查找等号或冒号后的值
	if idx := strings.Index(line, "="); idx != -1 {
		return line[:idx+1] + "[REDACTED]"
	}
	if idx := strings.Index(line, ":"); idx != -1 {
		return line[:idx+1] + " [REDACTED]"
	}

	// 如果没有找到分隔符，返回部分脱敏的行
	if len(line) > 20 {
		return line[:10] + "...[REDACTED]"
	}
	return "[REDACTED]"
}

// isSensitiveKey 检查键名是否为敏感字段
func (sf *SensitiveFilter) isSensitiveKey(key string) bool {
	lowerKey := strings.ToLower(key)
	sensitiveKeys := []string{
		"password", "passwd", "pwd", "secret", "token", "key", "apikey", "api_key",
		"cert", "certificate", "private", "public", "ca", "tls", "ssl",
		"ca_file", "cert_file", "key_file", "cafile", "certfile", "keyfile",
		"client_cert", "client_key", "server_cert", "server_key",
		"authorization", "bearer", "oauth", "jwt",
	}

	for _, sensitiveKey := range sensitiveKeys {
		if strings.Contains(lowerKey, sensitiveKey) {
			return true
		}
	}
	return false
}

// filterSlice 过滤切片中的敏感信息
func (sf *SensitiveFilter) filterSlice(input []interface{}) []interface{} {
	result := make([]interface{}, len(input))
	for i, item := range input {
		switch v := item.(type) {
		case string:
			result[i] = sf.FilterString(v)
		case map[string]interface{}:
			result[i] = sf.FilterMap(v)
		case []interface{}:
			result[i] = sf.filterSlice(v)
		default:
			result[i] = item
		}
	}
	return result
}

// CreateSensitiveString 创建敏感字符串
func CreateSensitiveString(value string) SensitiveString {
	return SensitiveString(value)
}

// GetOriginalValue 获取敏感字符串的原始值（仅用于必要的业务逻辑）
func (s SensitiveString) GetOriginalValue() string {
	return string(s)
}

// IsEmpty 检查敏感字符串是否为空
func (s SensitiveString) IsEmpty() bool {
	return string(s) == ""
}

// Equals 比较两个敏感字符串是否相等
func (s SensitiveString) Equals(other SensitiveString) bool {
	return string(s) == string(other)
}

// SensitiveConfig 包含敏感信息的配置结构
type SensitiveConfig struct {
	Servers  string          `json:"servers"`
	CaFile   string          `json:"ca_file"`
	CertFile string          `json:"cert_file"`
	KeyFile  string          `json:"key_file"`
	CaData   SensitiveString `json:"ca_data,omitempty"`
	CertData SensitiveString `json:"cert_data,omitempty"`
	KeyData  SensitiveString `json:"key_data,omitempty"`
}

// String 实现Stringer接口，返回脱敏后的配置信息
func (sc *SensitiveConfig) String() string {
	return fmt.Sprintf("SensitiveConfig{Servers: %s, CaFile: %s, CertFile: %s, KeyFile: %s, CaData: [REDACTED], CertData: [REDACTED], KeyData: [REDACTED]}",
		sc.Servers, sc.CaFile, sc.CertFile, sc.KeyFile)
}

// GlobalSensitiveFilter 全局敏感信息过滤器实例
var GlobalSensitiveFilter = NewSensitiveFilter()

// FilterSensitiveString 全局函数，用于过滤敏感字符串
func FilterSensitiveString(input string) string {
	return GlobalSensitiveFilter.FilterString(input)
}

// FilterSensitiveError 全局函数，用于过滤敏感错误信息
func FilterSensitiveError(err error) error {
	return GlobalSensitiveFilter.FilterError(err)
}

// FilterSensitiveMap 全局函数，用于过滤敏感map信息
func FilterSensitiveMap(input map[string]interface{}) map[string]interface{} {
	return GlobalSensitiveFilter.FilterMap(input)
}
