// K8s-Helper Web Interface - Main JavaScript

// Global application state - 将在initializeApp中初始化

// ========================================
// 统一错误处理机制
// ========================================

/**
 * 统一的错误处理函数
 * @param {Error|string} error - 错误对象或错误消息
 * @param {Object} options - 错误处理选项
 * @param {string} options.context - 错误上下文（如：'加载集群信息'）
 * @param {string} options.type - 显示类型：'alert', 'console', 'element', 'toast'
 * @param {HTMLElement} options.element - 目标元素（当type为'element'时）
 * @param {boolean} options.showSuggestions - 是否显示建议
 */
function handleError(error, options = {}) {
    const {
        context = '操作',
        type = 'console',
        element = null,
        showSuggestions = false
    } = options;

    // 标准化错误信息
    const errorInfo = normalizeError(error, context);

    // 记录到控制台（始终记录）
    console.error(`[${errorInfo.context}] ${errorInfo.message}`, errorInfo.originalError);

    // 根据类型显示错误
    switch (type) {
        case 'alert':
            showErrorAlert(errorInfo, showSuggestions);
            break;
        case 'element':
            showErrorInElement(errorInfo, element, showSuggestions);
            break;
        case 'toast':
            showErrorToast(errorInfo, showSuggestions);
            break;
        case 'console':
        default:
            // 只记录到控制台，不显示给用户
            break;
    }

    return errorInfo;
}

/**
 * 标准化错误信息
 */
function normalizeError(error, context) {
    let message = '';
    let code = '';
    let suggestions = [];
    let originalError = error;

    if (typeof error === 'string') {
        message = error;
    } else if (error && typeof error === 'object') {
        // 处理API错误响应
        if (error.error) {
            message = error.error;
            code = error.code || '';
            suggestions = error.suggestions || [];
        } else if (error.message) {
            message = error.message;
        } else {
            message = '未知错误';
        }
    } else {
        message = '未知错误';
    }

    return {
        context,
        message,
        code,
        suggestions,
        originalError,
        timestamp: new Date().toISOString()
    };
}

/**
 * 显示错误弹窗
 */
function showErrorAlert(errorInfo, showSuggestions) {
    let alertMessage = `${errorInfo.context}失败: ${errorInfo.message}`;

    if (showSuggestions && errorInfo.suggestions.length > 0) {
        alertMessage += '\n\n建议:\n' + errorInfo.suggestions.map(s => `• ${s}`).join('\n');
    }

    alert(alertMessage);
}

/**
 * 在指定元素中显示错误
 */
function showErrorInElement(errorInfo, element, showSuggestions) {
    if (!element) return;

    element.className = 'error-display';
    element.innerHTML = `
        <div class="error-content">
            <div class="error-title">${errorInfo.context}失败</div>
            <div class="error-message">${errorInfo.message}</div>
            ${showSuggestions && errorInfo.suggestions.length > 0 ?
                `<div class="error-suggestions">
                    <div class="suggestions-title">建议:</div>
                    <ul>${errorInfo.suggestions.map(s => `<li>${s}</li>`).join('')}</ul>
                </div>` : ''
            }
        </div>
    `;
}

/**
 * 显示错误提示（Toast风格）
 */
function showErrorToast(errorInfo, showSuggestions) {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = 'toast toast-error';
    toast.innerHTML = `
        <div class="toast-content">
            <div class="toast-title">${errorInfo.context}失败</div>
            <div class="toast-message">${errorInfo.message}</div>
            ${showSuggestions && errorInfo.suggestions.length > 0 ?
                `<div class="toast-suggestions">${errorInfo.suggestions.join(', ')}</div>` : ''
            }
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">×</button>
    `;

    toastContainer.appendChild(toast);

    // 自动移除
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// ========================================
// 数据验证层
// ========================================

/**
 * 通用数据验证函数
 * @param {any} data - 要验证的数据
 * @param {Object} schema - 验证规则
 * @param {Object} options - 验证选项
 * @returns {Object} 验证后的安全数据
 */
function validateData(data, schema, options = {}) {
    const {
        strict = false,           // 严格模式：缺少字段时抛出错误
        fillDefaults = true,      // 是否填充默认值
        logWarnings = true        // 是否记录警告
    } = options;

    if (!data || typeof data !== 'object') {
        if (strict) {
            throw new Error('数据验证失败：数据不是有效对象');
        }
        return schema.default || {};
    }

    const result = {};
    const warnings = [];

    // 验证每个字段
    for (const [key, rule] of Object.entries(schema.fields || {})) {
        const value = data[key];
        const validatedValue = validateField(value, rule, key, warnings);

        if (validatedValue !== undefined) {
            result[key] = validatedValue;
        } else if (fillDefaults && rule.default !== undefined) {
            result[key] = typeof rule.default === 'function' ? rule.default() : rule.default;
        } else if (rule.required && strict) {
            throw new Error(`数据验证失败：缺少必需字段 "${key}"`);
        }
    }

    // 复制未在schema中定义的字段（如果允许）
    if (!schema.strict) {
        for (const [key, value] of Object.entries(data)) {
            if (!(key in schema.fields)) {
                result[key] = value;
            }
        }
    }

    // 记录警告
    if (logWarnings && warnings.length > 0) {
        console.warn('数据验证警告:', warnings);
    }

    return result;
}

/**
 * 验证单个字段
 */
function validateField(value, rule, fieldName, warnings) {
    // 处理null/undefined
    if (value === null || value === undefined) {
        if (rule.required) {
            warnings.push(`字段 "${fieldName}" 为空但被标记为必需`);
        }
        return rule.default !== undefined ?
            (typeof rule.default === 'function' ? rule.default() : rule.default) :
            value;
    }

    // 类型验证
    if (rule.type && !validateType(value, rule.type)) {
        warnings.push(`字段 "${fieldName}" 类型不匹配，期望 ${rule.type}，实际 ${typeof value}`);
        return rule.default !== undefined ?
            (typeof rule.default === 'function' ? rule.default() : rule.default) :
            value;
    }

    // 数组验证
    if (rule.type === 'array' && Array.isArray(value)) {
        if (rule.items) {
            return value.map((item, index) =>
                validateField(item, rule.items, `${fieldName}[${index}]`, warnings)
            );
        }
    }

    // 对象验证
    if (rule.type === 'object' && typeof value === 'object' && rule.fields) {
        return validateData(value, { fields: rule.fields }, {
            strict: false,
            fillDefaults: true,
            logWarnings: false
        });
    }

    return value;
}

/**
 * 类型验证辅助函数
 */
function validateType(value, expectedType) {
    switch (expectedType) {
        case 'string':
            return typeof value === 'string';
        case 'number':
            return typeof value === 'number' && !isNaN(value);
        case 'boolean':
            return typeof value === 'boolean';
        case 'array':
            return Array.isArray(value);
        case 'object':
            return typeof value === 'object' && value !== null && !Array.isArray(value);
        case 'date':
            return value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)));
        default:
            return true;
    }
}

// ========================================
// 预定义验证规则
// ========================================

/**
 * 集群节点数据验证规则
 */
const NODE_SCHEMA = {
    fields: {
        name: { type: 'string', required: true, default: 'unknown' },
        status: { type: 'string', required: true, default: 'Unknown' },
        roles: { type: 'array', required: false, default: () => [] },
        internal_ip: { type: 'string', required: false, default: '' },
        external_ip: { type: 'string', required: false, default: '' },
        version: { type: 'string', required: false, default: '' },
        os: { type: 'string', required: false, default: 'linux' },
        architecture: { type: 'string', required: false, default: 'amd64' },
        capacity: { type: 'object', required: false, default: () => ({}) },
        allocatable: { type: 'object', required: false, default: () => ({}) },
        conditions: { type: 'array', required: false, default: () => [] },
        created_at: { type: 'date', required: false, default: '' }
    }
};

/**
 * 命名空间数据验证规则
 */
const NAMESPACE_SCHEMA = {
    fields: {
        name: { type: 'string', required: true, default: 'unknown' },
        status: { type: 'string', required: true, default: 'Unknown' },
        created_at: { type: 'date', required: false, default: '' },
        labels: { type: 'object', required: false, default: () => ({}) }
    }
};

/**
 * 集群信息数据验证规则
 */
const CLUSTER_INFO_SCHEMA = {
    fields: {
        server_version: { type: 'object', required: false, default: () => ({}) },
        nodes: {
            type: 'array',
            required: true,
            default: () => [],
            items: { type: 'object', fields: NODE_SCHEMA.fields }
        },
        namespaces: {
            type: 'array',
            required: true,
            default: () => [],
            items: { type: 'object', fields: NAMESPACE_SCHEMA.fields }
        },
        resources: {
            type: 'object',
            required: false,
            default: () => ({
                total_nodes: 0,
                ready_nodes: 0,
                total_namespaces: 0,
                total_pods: 0,
                running_pods: 0,
                total_services: 0
            })
        },
        timestamp: { type: 'date', required: false, default: () => new Date().toISOString() }
    }
};

/**
 * Pod数据验证规则
 */
const POD_SCHEMA = {
    fields: {
        name: { type: 'string', required: true, default: 'unknown' },
        namespace: { type: 'string', required: true, default: 'default' },
        status: { type: 'string', required: true, default: 'Unknown' },
        ready: { type: 'string', required: false, default: '0/0' },
        restarts: { type: 'number', required: false, default: 0 },
        age: { type: 'string', required: false, default: '' },
        ip: { type: 'string', required: false, default: '' },
        node: { type: 'string', required: false, default: '' }
    }
};

// ========================================
// API响应验证辅助函数
// ========================================

/**
 * 验证API响应数据
 * @param {any} data - API响应数据
 * @param {string} dataType - 数据类型（'cluster', 'pods', 'namespaces', 'pod'等）
 * @returns {any} 验证后的安全数据
 */
function validateApiResponse(data, dataType) {
    if (!data) {
        console.warn(`API响应数据为空: ${dataType}`);
        return getDefaultData(dataType);
    }

    try {
        switch (dataType) {
            case 'cluster':
                return validateData(data, CLUSTER_INFO_SCHEMA, {
                    strict: false,
                    fillDefaults: true,
                    logWarnings: true
                });

            case 'pods':
                return {
                    pods: Array.isArray(data.pods) ?
                        data.pods.map(pod => validateData(pod, POD_SCHEMA, { strict: false, fillDefaults: true })) :
                        [],
                    ...data
                };

            case 'pod':
                return {
                    pod: data.pod ? validateData(data.pod, POD_SCHEMA, { strict: false, fillDefaults: true }) : null,
                    ...data
                };

            case 'namespaces':
                return {
                    namespaces: Array.isArray(data.namespaces) ?
                        data.namespaces.map(ns => validateData(ns, NAMESPACE_SCHEMA, { strict: false, fillDefaults: true })) :
                        [],
                    ...data
                };

            default:
                console.warn(`未知的数据类型: ${dataType}`);
                return data;
        }
    } catch (error) {
        console.error(`数据验证失败 (${dataType}):`, error);
        return getDefaultData(dataType);
    }
}

/**
 * 获取默认数据
 */
function getDefaultData(dataType) {
    switch (dataType) {
        case 'cluster':
            return {
                server_version: {},
                nodes: [],
                namespaces: [],
                resources: {
                    total_nodes: 0,
                    ready_nodes: 0,
                    total_namespaces: 0,
                    total_pods: 0,
                    running_pods: 0,
                    total_services: 0
                },
                timestamp: new Date().toISOString()
            };
        case 'pods':
            return { pods: [] };
        case 'pod':
            return { pod: null };
        case 'namespaces':
            return { namespaces: [] };
        default:
            return {};
    }
}

/**
 * 安全获取嵌套对象属性
 * @param {Object} obj - 对象
 * @param {string} path - 属性路径，如 'a.b.c'
 * @param {any} defaultValue - 默认值
 * @returns {any} 属性值或默认值
 */
function safeGet(obj, path, defaultValue = null) {
    if (!obj || typeof obj !== 'object') {
        return defaultValue;
    }

    const keys = path.split('.');
    let current = obj;

    for (const key of keys) {
        if (current === null || current === undefined || !(key in current)) {
            return defaultValue;
        }
        current = current[key];
    }

    return current !== null && current !== undefined ? current : defaultValue;
}

/**
 * 安全数组操作
 * @param {any} value - 可能是数组的值
 * @param {any[]} defaultValue - 默认数组值
 * @returns {any[]} 安全的数组
 */
function safeArray(value, defaultValue = []) {
    return Array.isArray(value) ? value : defaultValue;
}

// DOM Ready
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    console.log('Initializing K8s-Helper Web Interface...');

    // 初始化UI增强器
    if (typeof UIEnhancer !== 'undefined') {
        uiEnhancer = new UIEnhancer();
    }

    // 初始化键盘快捷键
    if (typeof KeyboardShortcuts !== 'undefined') {
        keyboardShortcuts = new KeyboardShortcuts();
    }

    // 扩展全局对象（保留已有属性）
    window.K8sHelper = Object.assign(window.K8sHelper || {}, {
        currentModule: 'dashboard',
        currentTab: 'dashboard', // 保持向后兼容
        wsConnection: null,
        statusWs: null,
        apiBase: '/api/v1',
        isConnected: false,
        wsConnections: {},
        refreshIntervals: {},
        performanceMode: false,
        sidebarCollapsed: false,
        isMobile: false,
        ui: typeof uiEnhancer !== 'undefined' ? uiEnhancer : null,
        shortcuts: typeof keyboardShortcuts !== 'undefined' ? keyboardShortcuts : null,
        initializeWebSocket: function() {
            initializeWebSocket();
        }
    });

    // 检测性能模式
    detectPerformanceMode();

    // Check if using sidebar layout
    if (document.body.classList.contains('sidebar-layout')) {
        initializeSidebar();
        initializeModules();
    } else {
        // Initialize traditional tabs
        initializeTabs();
    }

    // Initialize WebSocket connection
    initializeWebSocket();

    // Load initial data
    loadDashboardData();

    // Initialize responsive behavior
    initializeResponsive();

    // 设置智能刷新
    setupIntelligentRefresh();

    // 添加页面可见性监听
    setupVisibilityChangeHandler();

    // 添加网络状态监听
    setupNetworkStatusHandler();

    console.log('Application initialized successfully');
}

// Sidebar Management
function initializeSidebar() {
    console.log('Initializing sidebar navigation...');

    // Update mobile state
    updateMobileState();

    // Initialize sidebar state from localStorage
    initializeSidebarState();

    // Initialize navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const module = this.getAttribute('data-module');
            if (module) {
                switchModule(module);
            }
        });
    });

    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    if (window.K8sHelper.isMobile) {
        // Mobile: toggle sidebar visibility
        sidebar.classList.toggle('open');
        overlay.classList.toggle('active');
    } else {
        // Desktop: cycle through three states: expanded -> mini -> collapsed -> expanded
        const currentState = getSidebarState();
        let nextState;

        switch (currentState) {
            case 'expanded':
                nextState = 'mini';
                break;
            case 'mini':
                nextState = 'collapsed';
                break;
            case 'collapsed':
            default:
                nextState = 'expanded';
                break;
        }

        setSidebarState(nextState);
    }
}

function getSidebarState() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) {
        console.warn('Sidebar element not found');
        return 'expanded';
    }

    if (sidebar.classList.contains('collapsed')) {
        return 'collapsed';
    } else if (sidebar.classList.contains('mini')) {
        return 'mini';
    } else {
        return 'expanded';
    }
}

function initializeSidebarState() {
    // Only initialize state for desktop
    if (window.K8sHelper.isMobile) {
        console.log('Skipping sidebar state initialization for mobile');
        return;
    }

    try {
        const savedState = localStorage.getItem('sidebarState') || 'expanded';
        console.log('Initializing sidebar state:', savedState);
        setSidebarState(savedState);
    } catch (error) {
        console.warn('Failed to initialize sidebar state from localStorage:', error);
        setSidebarState('expanded'); // fallback to default state
    }
}

function setSidebarState(state) {
    const sidebar = document.getElementById('sidebar');

    if (!sidebar) {
        console.warn('Sidebar element not found');
        return;
    }

    // Validate state parameter
    const validStates = ['expanded', 'mini', 'collapsed'];
    if (!validStates.includes(state)) {
        console.warn('Invalid sidebar state:', state, 'defaulting to expanded');
        state = 'expanded';
    }

    // Remove all state classes
    sidebar.classList.remove('collapsed', 'mini');

    // Add the new state class
    if (state === 'collapsed') {
        sidebar.classList.add('collapsed');
        window.K8sHelper.sidebarCollapsed = true;
    } else if (state === 'mini') {
        sidebar.classList.add('mini');
        window.K8sHelper.sidebarCollapsed = false;
    } else {
        // expanded state - no additional classes needed
        window.K8sHelper.sidebarCollapsed = false;
    }

    // Save state to localStorage with error handling
    try {
        localStorage.setItem('sidebarState', state);
        console.log('Sidebar state saved:', state);
    } catch (error) {
        console.warn('Failed to save sidebar state to localStorage:', error);
    }
}

function closeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    sidebar.classList.remove('open');
    overlay.classList.remove('active');
}

// Module Management
function initializeModules() {
    console.log('Initializing modules...');

    // Show default module
    switchModule(window.K8sHelper.currentModule);
}

function switchModule(moduleName) {
    console.log('Switching to module:', moduleName);

    // Update current module
    window.K8sHelper.currentModule = moduleName;
    window.K8sHelper.currentTab = moduleName; // 保持向后兼容

    // Update navigation active state
    updateNavigationState(moduleName);

    // Update page title
    updatePageTitle(moduleName);

    // Show/hide modules
    showModule(moduleName);

    // Load module data
    loadModuleData(moduleName);

    // Close mobile sidebar if open
    if (window.K8sHelper.isMobile) {
        closeSidebar();
    }

    // Update URL hash (optional)
    if (history.pushState) {
        history.pushState(null, null, '#' + moduleName);
    }
}

function updateNavigationState(activeModule) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        const module = link.getAttribute('data-module');
        if (module === activeModule) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

function updatePageTitle(moduleName) {
    const titleMap = {
        'dashboard': 'Dashboard - 概览',
        'cluster-info': 'Cluster Info - 集群信息',
        'etcd': 'ETCD - 数据管理',
        'logs': 'Pod Logs - 日志查看',
        'port-forward': 'Port Forward - 端口转发',
        'cleanup': 'Cleanup - 资源清理',
        'monitoring': 'Monitoring - 监控告警',
        'config': 'Configuration - 配置管理'
    };

    const pageTitle = document.querySelector('.page-title');
    if (pageTitle) {
        pageTitle.textContent = titleMap[moduleName] || moduleName;
    }
}

function showModule(moduleName) {
    // Hide all modules
    const modules = document.querySelectorAll('.module');
    modules.forEach(module => {
        module.classList.remove('active');
    });

    // Show target module
    const targetModule = document.getElementById('module-' + moduleName);
    if (targetModule) {
        targetModule.classList.add('active');
        window.K8sHelper.currentModule = moduleName;

        // 连接模块WebSocket（如果需要）
        connectModuleWebSocketIfNeeded(moduleName);
    }
}

// 根据需要连接模块WebSocket
function connectModuleWebSocketIfNeeded(moduleId) {
    const wsModules = ['monitoring', 'cleanup', 'config', 'port-forward', 'logs'];

    if (wsModules.includes(moduleId)) {
        // 连接当前模块的WebSocket
        connectModuleWebSocket(moduleId);
    }
}

function loadModuleData(moduleName) {
    switch (moduleName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'cluster-info':
            loadClusterInfo();
            break;
        case 'etcd':
            loadETCDData();
            break;
        case 'logs':
            loadLogsData();
            break;
        case 'port-forward':
            loadPortForwardData();
            break;
        case 'cleanup':
            loadCleanupData();
            break;
        case 'monitoring':
            loadMonitoringData();
            break;
        case 'config':
            loadConfigData();
            break;
        default:
            console.warn('Unknown module:', moduleName);
    }
}

// Responsive Behavior
function initializeResponsive() {
    // Update mobile state on resize
    window.addEventListener('resize', function() {
        updateMobileState();
    });

    // Handle hash changes
    window.addEventListener('hashchange', function() {
        const hash = window.location.hash.substring(1);
        if (hash && document.body.classList.contains('sidebar-layout')) {
            switchModule(hash);
        }
    });

    // Load module from hash on init
    const hash = window.location.hash.substring(1);
    if (hash && document.body.classList.contains('sidebar-layout')) {
        switchModule(hash);
    }
}

function updateMobileState() {
    const wasMobile = window.K8sHelper.isMobile;
    window.K8sHelper.isMobile = window.innerWidth <= 768;

    if (wasMobile !== window.K8sHelper.isMobile) {
        // Mobile state changed
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        if (window.K8sHelper.isMobile) {
            // Switched to mobile - remove all desktop states
            sidebar.classList.remove('collapsed', 'mini', 'open');
            overlay.classList.remove('active');
        } else {
            // Switched to desktop - restore saved state
            sidebar.classList.remove('open');
            overlay.classList.remove('active');

            // Restore sidebar state from localStorage
            const savedState = localStorage.getItem('sidebarState') || 'expanded';
            setSidebarState(savedState);
        }
    }
}

// Keyboard Shortcuts
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Only handle shortcuts when not in input fields
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case '1':
                    e.preventDefault();
                    switchModule('dashboard');
                    break;
                case '2':
                    e.preventDefault();
                    switchModule('cluster-info');
                    break;
                case '3':
                    e.preventDefault();
                    switchModule('etcd');
                    break;
                case 'l':
                    e.preventDefault();
                    switchModule('logs');
                    break;
                case 'p':
                    e.preventDefault();
                    switchModule('port-forward');
                    break;
                case 'c':
                    e.preventDefault();
                    switchModule('cleanup');
                    break;
                case 'm':
                    e.preventDefault();
                    switchModule('monitoring');
                    break;
                case 's':
                    e.preventDefault();
                    switchModule('config');
                    break;
            }
        }

        // ESC to close mobile sidebar
        if (e.key === 'Escape' && window.K8sHelper.isMobile) {
            closeSidebar();
        }
    });
}

// Module Data Loading Functions (Placeholders)
function loadClusterInfo() {
    console.log('Loading cluster info...');
    const loadingElement = document.getElementById('cluster-info-loading');

    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    // 首先尝试快速接口获取基本信息
    fetch('/api/v1/cluster/info/quick')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(quickData => {
            // 显示快速获取的基本信息
            updateClusterInfoQuick(quickData);

            // 在后台获取完整信息
            return fetch('/api/v1/cluster/info');
        })
        .then(response => {
            if (!response.ok) {
                // 如果完整信息获取失败，至少已经有基本信息了
                console.warn('Failed to load full cluster info, using quick data only');
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }
                return null;
            }
            return response.json();
        })
        .then(fullData => {
            if (fullData) {
                // 验证和标准化完整数据
                const validatedData = validateApiResponse(fullData, 'cluster');
                updateClusterInfoDisplay(validatedData);
            }
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Failed to load cluster info:', error);
            // 尝试显示错误信息
            showClusterInfoError(error.message);
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
        });
}

// 更新集群信息快速显示（基本信息）
function updateClusterInfoQuick(data) {
    console.log('Updating cluster info quick display with data:', data);

    // 更新服务端版本信息
    if (data.server_version) {
        const versionElement = document.getElementById('k8s-version');
        if (versionElement) {
            versionElement.textContent = data.server_version.git_version || '--';
        }
    }

    // 更新节点信息
    if (data.nodes) {
        const totalNodesElement = document.getElementById('total-nodes');
        const readyNodesElement = document.getElementById('ready-nodes');

        if (totalNodesElement) {
            totalNodesElement.textContent = data.nodes.total || 0;
        }
        if (readyNodesElement) {
            readyNodesElement.textContent = data.nodes.ready || 0;
        }
    }

    // 显示"加载中"状态给其他字段
    const loadingFields = ['total-namespaces', 'running-pods', 'total-services'];
    loadingFields.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '...';
        }
    });
}

// 更新集群信息显示
function updateClusterInfoDisplay(data) {
    console.log('Updating cluster info display with data:', data);

    // 更新概览卡片
    updateOverviewCards(data);

    // 更新服务端版本信息
    updateServerVersionInfo(data.server_version);

    // 更新节点信息表格
    updateNodesTable(data.nodes);

    // 更新命名空间信息
    updateNamespacesGrid(data.namespaces);

    // 更新资源统计图表
    updateResourceCharts(data.resources);
}

// 更新概览卡片
function updateOverviewCards(data) {
    const elements = {
        'k8s-version': data.server_version?.git_version || 'Unknown',
        'total-nodes': data.resources?.total_nodes || 0,
        'ready-nodes': data.resources?.ready_nodes || 0,
        'total-namespaces': data.resources?.total_namespaces || 0,
        'running-pods': data.resources?.running_pods || 0,
        'total-services': data.resources?.total_services || 0
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// 更新服务端版本信息
function updateServerVersionInfo(versionInfo) {
    if (!versionInfo) return;

    const elements = {
        'git-version': versionInfo.git_version,
        'major-version': versionInfo.major,
        'minor-version': versionInfo.minor,
        'platform': versionInfo.platform,
        'build-date': versionInfo.build_date,
        'go-version': versionInfo.go_version
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value || '--';
        }
    });
}

// 更新节点信息表格
function updateNodesTable(nodes) {
    const tableBody = document.getElementById('nodes-table-body');
    if (!tableBody || !nodes) return;

    if (nodes.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="loading-cell">未找到节点信息</td></tr>';
        return;
    }

    const rows = nodes.map(node => {
        const statusClass = node.status.toLowerCase().replace(/\s+/g, '-');
        const rolesHtml = (node.roles || []).map(role =>
            `<span class="role-tag ${role}">${role}</span>`
        ).join('');

        return `
            <tr>
                <td>${node.name}</td>
                <td><span class="node-status ${statusClass}">${node.status}</span></td>
                <td><div class="node-roles">${rolesHtml}</div></td>
                <td>${node.internal_ip || '--'}</td>
                <td>${node.version}</td>
                <td>${node.os}</td>
                <td>${node.architecture}</td>
                <td>${formatDate(node.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-secondary" onclick="showNodeDetails('${node.name}')">详情</button>
                </td>
            </tr>
        `;
    }).join('');

    tableBody.innerHTML = rows;

    // 初始化表格搜索和过滤功能
    initializeTableFilters(nodes);
}

// 更新命名空间网格
function updateNamespacesGrid(namespaces) {
    const grid = document.getElementById('namespaces-grid');
    if (!grid || !namespaces) return;

    if (namespaces.length === 0) {
        grid.innerHTML = '<div class="loading-placeholder"><p>未找到命名空间信息</p></div>';
        return;
    }

    const cards = namespaces.map(ns => {
        const cardClass = getNamespaceCardClass(ns.name);
        return `
            <div class="namespace-card ${cardClass}">
                <div class="namespace-name">${ns.name}</div>
                <div class="namespace-info">
                    状态: ${ns.status}<br>
                    创建时间: ${formatDate(ns.created_at)}
                </div>
            </div>
        `;
    }).join('');

    grid.innerHTML = cards;
}

// 更新资源统计图表
function updateResourceCharts(resources) {
    if (!resources) return;

    // 更新节点状态图表
    const readyNodes = document.getElementById('chart-ready-nodes');
    const notReadyNodes = document.getElementById('chart-not-ready-nodes');
    if (readyNodes) readyNodes.textContent = resources.ready_nodes || 0;
    if (notReadyNodes) notReadyNodes.textContent = (resources.total_nodes - resources.ready_nodes) || 0;

    // 更新Pod状态图表
    const runningPods = document.getElementById('chart-running-pods');
    const totalPods = document.getElementById('chart-total-pods');
    if (runningPods) runningPods.textContent = resources.running_pods || 0;
    if (totalPods) totalPods.textContent = resources.total_pods || 0;
}

// 显示集群信息错误
function showClusterInfoError(message) {
    const elements = ['k8s-version', 'total-nodes', 'ready-nodes', 'total-namespaces', 'running-pods', 'total-services'];
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '错误';
            element.style.color = '#dc3545';
        }
    });

    const tableBody = document.getElementById('nodes-table-body');
    if (tableBody) {
        tableBody.innerHTML = `<tr><td colspan="9" class="loading-cell" style="color: #dc3545;">加载失败: ${message}</td></tr>`;
    }

    const grid = document.getElementById('namespaces-grid');
    if (grid) {
        grid.innerHTML = `<div class="loading-placeholder"><p style="color: #dc3545;">加载失败: ${message}</p></div>`;
    }
}



function loadLogsData() {
    console.log('Loading logs data...');
    // 初始化日志模块
    initializeLogsModule();
}

// 刷新日志模块
function refreshLogsModule() {
    console.log('Refreshing logs module...');
    const loadingElement = document.getElementById('logs-module-loading');

    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    // 重新初始化模块
    setTimeout(() => {
        initializeLogsModule();
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }, 1000);
}

// 初始化日志模块
function initializeLogsModule() {
    console.log('Initializing logs module...');

    // 加载命名空间列表
    loadNamespaces();

    // 初始化事件监听器
    initializeLogsEventListeners();

    // 重置模块状态
    resetLogsModule();
}

// 加载命名空间列表
function loadNamespaces() {
    const namespaceSelect = document.getElementById('namespace-select');
    if (!namespaceSelect) return;

    namespaceSelect.innerHTML = '<option value="">加载中...</option>';

    fetch('/api/v1/pods')
        .then(response => response.json())
        .then(data => {
            namespaceSelect.innerHTML = '<option value="">选择命名空间...</option>';

            if (data.namespaces && data.namespaces.length > 0) {
                data.namespaces.forEach(namespace => {
                    const option = document.createElement('option');
                    option.value = namespace;
                    option.textContent = namespace;
                    namespaceSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('加载命名空间失败:', error);
            namespaceSelect.innerHTML = '<option value="">加载失败</option>';
        });
}

// 加载指定命名空间的Pod列表
function loadPods(namespace) {
    const podSelect = document.getElementById('pod-select');
    if (!podSelect || !namespace) return;

    podSelect.innerHTML = '<option value="">加载中...</option>';
    podSelect.disabled = true;

    fetch(`/api/v1/pods/${namespace}`)
        .then(response => response.json())
        .then(data => {
            // 验证Pod数据
            const validatedData = validateApiResponse(data, 'pods');
            podSelect.innerHTML = '<option value="">选择Pod...</option>';

            if (validatedData.pods && validatedData.pods.length > 0) {
                validatedData.pods.forEach(pod => {
                    const option = document.createElement('option');
                    option.value = pod.name;
                    option.textContent = `${pod.name} (${pod.status})`;
                    option.dataset.podData = JSON.stringify(pod);
                    podSelect.appendChild(option);
                });
                podSelect.disabled = false;
            } else {
                podSelect.innerHTML = '<option value="">无可用Pod</option>';
            }
        })
        .catch(error => {
            console.error('加载Pod列表失败:', error);
            podSelect.innerHTML = '<option value="">加载失败</option>';
        });
}

// 加载指定Pod的容器列表
function loadContainers(namespace, podName) {
    const containerSelect = document.getElementById('container-select');
    if (!containerSelect || !namespace || !podName) return;

    containerSelect.innerHTML = '<option value="">加载中...</option>';
    containerSelect.disabled = true;

    fetch(`/api/v1/pods/${namespace}/${podName}`)
        .then(response => response.json())
        .then(data => {
            containerSelect.innerHTML = '<option value="">选择容器...</option>';

            if (data.pod && data.pod.containers && data.pod.containers.length > 0) {
                data.pod.containers.forEach(container => {
                    const option = document.createElement('option');
                    option.value = container.name;
                    option.textContent = `${container.name} (${container.ready ? '就绪' : '未就绪'})`;
                    containerSelect.appendChild(option);
                });
                containerSelect.disabled = false;

                // 如果只有一个容器，自动选择
                if (data.pod.containers.length === 1) {
                    containerSelect.value = data.pod.containers[0].name;
                }

                // 更新Pod信息面板
                updatePodInfoPanel(data.pod);
            } else {
                containerSelect.innerHTML = '<option value="">无可用容器</option>';
            }
        })
        .catch(error => {
            console.error('加载容器列表失败:', error);
            containerSelect.innerHTML = '<option value="">加载失败</option>';
        });
}

// 初始化事件监听器
function initializeLogsEventListeners() {
    // 命名空间选择
    const namespaceSelect = document.getElementById('namespace-select');
    if (namespaceSelect) {
        namespaceSelect.addEventListener('change', function() {
            const namespace = this.value;
            resetPodSelection();
            if (namespace) {
                loadPods(namespace);
            }
        });
    }

    // Pod选择
    const podSelect = document.getElementById('pod-select');
    if (podSelect) {
        podSelect.addEventListener('change', function() {
            const namespace = document.getElementById('namespace-select').value;
            const podName = this.value;
            resetContainerSelection();
            if (namespace && podName) {
                loadContainers(namespace, podName);
            }
        });
    }

    // 容器选择
    const containerSelect = document.getElementById('container-select');
    if (containerSelect) {
        containerSelect.addEventListener('change', function() {
            updateLoadLogsButton();
        });
    }

    // 加载日志按钮
    const loadLogsBtn = document.getElementById('load-logs-btn');
    if (loadLogsBtn) {
        loadLogsBtn.addEventListener('click', function() {
            loadPodLogs();
        });
    }

    // 搜索功能
    const searchBtn = document.getElementById('search-btn');
    const clearSearchBtn = document.getElementById('clear-search-btn');
    const logSearch = document.getElementById('log-search');

    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            performLogSearch();
        });
    }

    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', function() {
            clearLogSearch();
        });
    }

    if (logSearch) {
        logSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performLogSearch();
            }
        });
    }

    // 日志级别过滤
    const levelFilter = document.getElementById('log-level-filter');
    if (levelFilter) {
        levelFilter.addEventListener('change', function() {
            filterLogsByLevel(this.value);
        });
    }

    // 清空日志
    const clearLogsBtn = document.getElementById('clear-logs-btn');
    if (clearLogsBtn) {
        clearLogsBtn.addEventListener('click', function() {
            clearLogViewer();
        });
    }

    // 下载日志
    const downloadLogsBtn = document.getElementById('download-logs-btn');
    if (downloadLogsBtn) {
        downloadLogsBtn.addEventListener('click', function() {
            downloadLogs();
        });
    }

    // 自动滚动切换
    const autoScrollBtn = document.getElementById('auto-scroll-btn');
    if (autoScrollBtn) {
        autoScrollBtn.addEventListener('click', function() {
            toggleAutoScroll();
        });
    }
}

function loadPortForwardData() {
    console.log('Loading port forward data...');
    // 初始化端口转发模块
    initializePortForwardModule();
}

// 刷新端口转发模块
function refreshPortForwardModule() {
    console.log('Refreshing port forward module...');
    const loadingElement = document.getElementById('port-forward-module-loading');

    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    // 重新初始化模块
    setTimeout(() => {
        initializePortForwardModule();
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }, 1000);
}

// 初始化端口转发模块
function initializePortForwardModule() {
    console.log('Initializing port forward module...');

    // 加载命名空间列表
    loadPortForwardNamespaces();

    // 加载端口转发列表
    loadPortForwardsList();

    // 初始化事件监听器
    initializePortForwardEventListeners();

    // 更新统计信息
    updatePortForwardStats();
}

// 加载命名空间列表
function loadPortForwardNamespaces() {
    const namespaceSelect = document.getElementById('pf-namespace');
    if (!namespaceSelect) return;

    namespaceSelect.innerHTML = '<option value="">加载中...</option>';

    fetch('/api/v1/pods')
        .then(response => response.json())
        .then(data => {
            namespaceSelect.innerHTML = '<option value="">选择命名空间...</option>';

            if (data.namespaces && data.namespaces.length > 0) {
                data.namespaces.forEach(namespace => {
                    const option = document.createElement('option');
                    option.value = namespace;
                    option.textContent = namespace;
                    namespaceSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('加载命名空间失败:', error);
            namespaceSelect.innerHTML = '<option value="">加载失败</option>';
        });
}

// 加载指定命名空间的Pod列表
function loadPortForwardPods(namespace) {
    const podSelect = document.getElementById('pf-pod');
    if (!podSelect || !namespace) return;

    podSelect.innerHTML = '<option value="">加载中...</option>';
    podSelect.disabled = true;

    fetch(`/api/v1/pods/${namespace}`)
        .then(response => response.json())
        .then(data => {
            podSelect.innerHTML = '<option value="">选择Pod...</option>';

            if (data.pods && data.pods.length > 0) {
                data.pods.forEach(pod => {
                    const option = document.createElement('option');
                    option.value = pod.name;
                    option.textContent = `${pod.name} (${pod.status})`;
                    podSelect.appendChild(option);
                });
                podSelect.disabled = false;
            } else {
                podSelect.innerHTML = '<option value="">无可用Pod</option>';
            }
        })
        .catch(error => {
            console.error('加载Pod列表失败:', error);
            podSelect.innerHTML = '<option value="">加载失败</option>';
        });
}

// 加载端口转发列表
function loadPortForwardsList() {
    const grid = document.getElementById('forwards-grid');
    if (!grid) return;

    grid.innerHTML = '<div class="loading-placeholder"><p>正在加载端口转发列表...</p></div>';

    fetch('/api/v1/port-forward')
        .then(response => response.json())
        .then(data => {
            displayPortForwards(data.forwards || []);
            updatePortForwardStats(data.forwards || []);
        })
        .catch(error => {
            console.error('加载端口转发列表失败:', error);
            grid.innerHTML = '<div class="loading-placeholder"><p style="color: #dc3545;">加载失败: ' + error.message + '</p></div>';
        });
}

// 显示端口转发列表
function displayPortForwards(forwards) {
    const grid = document.getElementById('forwards-grid');
    if (!grid) return;

    if (forwards.length === 0) {
        grid.innerHTML = '<div class="loading-placeholder"><p>暂无端口转发</p></div>';
        return;
    }

    grid.innerHTML = '';
    forwards.forEach(forward => {
        const card = createPortForwardCard(forward);
        grid.appendChild(card);
    });
}

// 创建端口转发卡片
function createPortForwardCard(forward) {
    const template = document.getElementById('forward-card-template');
    const card = template.content.cloneNode(true);

    const cardDiv = card.querySelector('.forward-card');
    cardDiv.dataset.id = forward.id;
    cardDiv.dataset.status = forward.status;

    // 设置标题和状态
    card.querySelector('.forward-title').textContent = `${forward.namespace}/${forward.pod}`;
    const statusSpan = card.querySelector('.forward-status');
    statusSpan.textContent = forward.status;
    statusSpan.className = `forward-status ${forward.status}`;

    // 设置详细信息
    card.querySelector('.local-address').textContent = `localhost:${forward.local_port}`;
    card.querySelector('.target').textContent = `${forward.namespace}/${forward.pod}:${forward.pod_port}`;
    card.querySelector('.created-time').textContent = formatDate(forward.created_at);

    // 设置错误信息
    if (forward.error) {
        const errorItem = card.querySelector('.error-message');
        errorItem.style.display = 'flex';
        card.querySelector('.error-text').textContent = forward.error;
    }

    // 绑定事件
    bindPortForwardCardEvents(card, forward);

    return card;
}

// 绑定端口转发卡片事件
function bindPortForwardCardEvents(card, forward) {
    // 测试连接
    card.querySelector('.test-btn').addEventListener('click', () => {
        testPortForward(forward);
    });

    // 停止转发
    card.querySelector('.stop-btn').addEventListener('click', () => {
        stopPortForward(forward.id);
    });

    // 删除转发
    card.querySelector('.delete-btn').addEventListener('click', () => {
        deletePortForward(forward.id);
    });

    // 复制地址
    card.querySelector('.copy-btn').addEventListener('click', () => {
        copyToClipboard(`http://localhost:${forward.local_port}`);
    });
}

function loadCleanupData() {
    console.log('Loading cleanup data...');
    // 初始化资源清理模块
    initializeCleanupModule();
}

// 刷新资源清理模块
function refreshCleanupModule() {
    console.log('Refreshing cleanup module...');
    const loadingElement = document.getElementById('cleanup-module-loading');

    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    // 重新初始化模块
    setTimeout(() => {
        initializeCleanupModule();
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }, 1000);
}

// 初始化资源清理模块
function initializeCleanupModule() {
    console.log('Initializing cleanup module...');

    // 加载命名空间列表
    loadCleanupNamespaces();

    // 初始化事件监听器
    initializeCleanupEventListeners();

    // 重置模块状态
    resetCleanupModule();
}

// 加载命名空间列表
function loadCleanupNamespaces() {
    const namespacesSelect = document.getElementById('cleanup-namespaces');
    if (!namespacesSelect) return;

    namespacesSelect.innerHTML = '<option value="">加载中...</option>';

    fetch('/api/v1/cleanup/namespaces')
        .then(response => response.json())
        .then(data => {
            namespacesSelect.innerHTML = '';

            if (data.namespaces && data.namespaces.length > 0) {
                data.namespaces.forEach(namespace => {
                    const option = document.createElement('option');
                    option.value = namespace;
                    option.textContent = namespace;
                    namespacesSelect.appendChild(option);
                });

                // 默认选择 default 命名空间
                if (data.namespaces.includes('default')) {
                    namespacesSelect.value = 'default';
                }
            }
        })
        .catch(error => {
            console.error('加载命名空间失败:', error);
            namespacesSelect.innerHTML = '<option value="">加载失败</option>';
        });
}

// 初始化事件监听器
function initializeCleanupEventListeners() {
    // 扫描按钮
    const scanBtn = document.getElementById('scan-btn');
    if (scanBtn) {
        scanBtn.addEventListener('click', function() {
            performCleanupScan();
        });
    }

    // 执行按钮
    const executeBtn = document.getElementById('execute-btn');
    if (executeBtn) {
        executeBtn.addEventListener('click', function() {
            performCleanupExecute();
        });
    }

    // 表单重置
    const cleanupForm = document.getElementById('cleanup-form');
    if (cleanupForm) {
        cleanupForm.addEventListener('reset', function() {
            resetCleanupModule();
        });
    }

    // 全选复选框
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            toggleSelectAllResources(this.checked);
        });
    }

    // 搜索和过滤
    const resourceSearch = document.getElementById('resource-search');
    const typeFilter = document.getElementById('resource-type-filter');
    const statusFilter = document.getElementById('resource-status-filter');

    if (resourceSearch) {
        resourceSearch.addEventListener('input', function() {
            filterResources();
        });
    }

    if (typeFilter) {
        typeFilter.addEventListener('change', function() {
            filterResources();
        });
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            filterResources();
        });
    }

    // 确认对话框
    const confirmDialog = document.getElementById('confirm-dialog');
    const confirmYes = document.getElementById('confirm-yes');
    const confirmNo = document.getElementById('confirm-no');
    const modalClose = document.querySelector('.modal-close');

    if (confirmYes) {
        confirmYes.addEventListener('click', function() {
            executeCleanupConfirmed();
        });
    }

    if (confirmNo) {
        confirmNo.addEventListener('click', function() {
            hideConfirmDialog();
        });
    }

    if (modalClose) {
        modalClose.addEventListener('click', function() {
            hideConfirmDialog();
        });
    }

    // 点击对话框外部关闭
    if (confirmDialog) {
        confirmDialog.addEventListener('click', function(e) {
            if (e.target === confirmDialog) {
                hideConfirmDialog();
            }
        });
    }
}

// 执行清理扫描
function performCleanupScan() {
    const type = document.getElementById('cleanup-type').value;
    const namespacesSelect = document.getElementById('cleanup-namespaces');
    const olderThan = document.getElementById('older-than').value;

    if (!type) {
        alert('请选择清理类型');
        return;
    }

    const selectedNamespaces = Array.from(namespacesSelect.selectedOptions).map(option => option.value);
    if (selectedNamespaces.length === 0) {
        alert('请选择至少一个命名空间');
        return;
    }

    const loadingElement = document.getElementById('scan-loading');
    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    const requestData = {
        type: type,
        namespaces: selectedNamespaces,
        older_than: olderThan,
        dry_run: true
    };

    fetch('/api/v1/cleanup/scan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (data.error) {
            alert('扫描失败: ' + data.error);
        } else {
            displayScanResults(data.result);
            enableExecuteButton();
        }
    })
    .catch(error => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
        handleError(error, {
            context: '资源扫描',
            type: 'alert',
            showSuggestions: true
        });
    });
}

function loadMonitoringData() {
    console.log('Loading monitoring data...');
    // 初始化监控模块
    initializeMonitoringModule();
}

// 刷新监控模块
function refreshMonitoringModule() {
    console.log('Refreshing monitoring module...');
    const loadingElement = document.getElementById('monitoring-module-loading');

    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    // 重新初始化模块
    setTimeout(() => {
        initializeMonitoringModule();
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }, 1000);
}

// 初始化监控模块
function initializeMonitoringModule() {
    console.log('Initializing monitoring module...');

    // 加载监控数据
    loadMetricsData();
    loadHealthData();
    loadPerformanceData();
    loadAlertsData();

    // 初始化事件监听器
    initializeMonitoringEventListeners();

    // 启动自动刷新
    startMonitoringAutoRefresh();
}

// 加载指标数据
function loadMetricsData() {
    fetch('/api/v1/monitoring/metrics')
        .then(response => response.json())
        .then(data => {
            if (data.metrics) {
                updateMetricsDisplay(data.metrics);
            }
        })
        .catch(error => {
            console.error('加载指标数据失败:', error);
        });
}

// 加载健康检查数据
function loadHealthData() {
    fetch('/api/v1/monitoring/health')
        .then(response => response.json())
        .then(data => {
            if (data.health) {
                updateHealthDisplay(data.health);
            }
        })
        .catch(error => {
            console.error('加载健康数据失败:', error);
        });
}

// 加载性能数据
function loadPerformanceData() {
    fetch('/api/v1/monitoring/performance')
        .then(response => response.json())
        .then(data => {
            if (data.performance) {
                updatePerformanceDisplay(data.performance);
            }
        })
        .catch(error => {
            console.error('加载性能数据失败:', error);
        });
}

// 加载告警数据
function loadAlertsData() {
    const loadingElement = document.getElementById('alerts-loading');
    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    fetch('/api/v1/monitoring/alerts')
        .then(response => response.json())
        .then(data => {
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
            if (data.alerts) {
                updateAlertsDisplay(data.alerts);
            }
        })
        .catch(error => {
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
            console.error('加载告警数据失败:', error);
        });
}

// 更新指标显示
function updateMetricsDisplay(metrics) {
    // 更新系统指标
    if (metrics.system) {
        updateElement('cpu-usage', metrics.system.cpu_usage);
        updateElement('memory-usage', metrics.system.memory_usage);
        updateElement('disk-usage', metrics.system.disk_usage);
        updateElement('network-io', metrics.system.network_io);
    }

    // 更新Kubernetes指标
    if (metrics.kubernetes) {
        updateElement('pods-running', metrics.kubernetes.pods_running);
        updateElement('pods-pending', metrics.kubernetes.pods_pending);
        updateElement('pods-failed', metrics.kubernetes.pods_failed);
        updateElement('nodes-ready', metrics.kubernetes.nodes_ready);
        updateElement('nodes-not-ready', metrics.kubernetes.nodes_not_ready);
    }
}

// 更新健康检查显示
function updateHealthDisplay(health) {
    // 更新整体状态
    updateElement('overall-status', health.overall_status, 'status-indicator ' + health.overall_status);
    updateElement('system-uptime', health.uptime);

    // 更新组件状态
    if (health.components) {
        updateComponentHealth('k8s-api', health.components.kubernetes_api);
        updateComponentHealth('etcd', health.components.etcd);
        updateComponentHealth('database', health.components.database);
        updateComponentHealth('external', health.components.external_services);
    }
}

// 更新组件健康状态
function updateComponentHealth(prefix, component) {
    if (component) {
        updateElement(prefix + '-health', component.status, 'component-status ' + component.status);
        updateElement(prefix + '-response', component.response_time);
    }
}

// 更新性能数据显示
function updatePerformanceDisplay(performance) {
    // 更新CPU图表
    if (performance.cpu) {
        updateChart('cpu-bars', performance.cpu.history);
    }

    // 更新内存图表
    if (performance.memory) {
        updateChart('memory-bars', performance.memory.history);
    }

    // 更新网络统计
    if (performance.network) {
        updateElement('network-inbound', performance.network.inbound);
        updateElement('network-outbound', performance.network.outbound);
        updateElement('network-total', performance.network.total);
    }

    // 更新操作统计
    if (performance.operations) {
        updateElement('total-requests', performance.operations.total_requests);
        updateElement('successful-requests', performance.operations.successful_requests);
        updateElement('failed-requests', performance.operations.failed_requests);
        updateElement('avg-response-time', performance.operations.average_response_time);
    }
}

// 更新图表
function updateChart(containerId, data) {
    const container = document.getElementById(containerId);
    if (!container || !data) return;

    container.innerHTML = '';
    const maxValue = Math.max(...data);

    data.forEach(value => {
        const bar = document.createElement('div');
        bar.className = 'chart-bar';
        bar.style.height = `${(value / maxValue) * 100}%`;
        bar.title = `${value}%`;
        container.appendChild(bar);
    });
}

// 更新告警显示
function updateAlertsDisplay(alerts) {
    const alertsList = document.getElementById('alerts-list');
    if (!alertsList) return;

    if (alerts.length === 0) {
        alertsList.innerHTML = '<div class="loading-placeholder"><p>暂无告警信息</p></div>';
        return;
    }

    alertsList.innerHTML = '';
    alerts.forEach(alert => {
        const alertElement = createAlertElement(alert);
        alertsList.appendChild(alertElement);
    });
}

// 创建告警元素
function createAlertElement(alert) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert-item ${alert.severity}`;
    alertDiv.dataset.alertId = alert.id;

    const severityIcon = getSeverityIcon(alert.severity);
    const timeAgo = getTimeAgo(alert.created_at);

    alertDiv.innerHTML = `
        <div class="alert-icon">${severityIcon}</div>
        <div class="alert-content">
            <div class="alert-header">
                <h4 class="alert-name">${alert.name}</h4>
                <span class="alert-severity ${alert.severity}">${alert.severity}</span>
            </div>
            <div class="alert-description">${alert.description}</div>
            <div class="alert-meta">
                <div class="alert-time">${timeAgo}</div>
                <div class="alert-tags">
                    ${alert.tags ? alert.tags.map(tag => `<span class="alert-tag">${tag}</span>`).join('') : ''}
                </div>
            </div>
        </div>
        <div class="alert-actions">
            ${alert.status === 'active' ? '<button class="alert-action resolve" onclick="resolveAlert(\'' + alert.id + '\')">解决</button>' : ''}
            <button class="alert-action delete" onclick="deleteAlert('${alert.id}')">删除</button>
        </div>
    `;

    return alertDiv;
}

function loadConfigData() {
    console.log('Loading config data...');
    // 初始化配置管理模块
    initializeConfigurationModule();
}

// 刷新配置管理模块
function refreshConfigurationModule() {
    console.log('Refreshing configuration module...');
    const loadingElement = document.getElementById('configuration-module-loading');

    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    // 重新初始化模块
    setTimeout(() => {
        initializeConfigurationModule();
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }, 1000);
}

// 初始化配置管理模块
function initializeConfigurationModule() {
    console.log('Initializing configuration module...');

    // 加载配置数据
    loadConfigurationData();
    loadConfigurationHistory();

    // 初始化事件监听器
    initializeConfigurationEventListeners();

    // 设置默认选中的配置部分
    showConfigSection('global');
}

// 加载配置数据
function loadConfigurationData() {
    fetch('/api/v1/config')
        .then(response => response.json())
        .then(data => {
            if (data.config) {
                updateConfigurationDisplay(data.config);
                updateConfigurationOverview(data);
            }
        })
        .catch(error => {
            console.error('加载配置数据失败:', error);
        });
}

// 加载配置历史
function loadConfigurationHistory() {
    const limit = document.getElementById('history-limit')?.value || 20;
    const loadingElement = document.getElementById('history-loading');

    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    fetch(`/api/v1/config/history?limit=${limit}`)
        .then(response => response.json())
        .then(data => {
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
            if (data.history) {
                updateConfigurationHistory(data.history);
            }
        })
        .catch(error => {
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
            console.error('加载配置历史失败:', error);
        });
}

// 更新配置概览显示
function updateConfigurationOverview(data) {
    updateElement('config-version', data.version || '--');
    updateElement('config-last-modified', formatDateTime(data.last_modified) || '--');
    updateElement('config-status', '正常', 'info-value status-indicator healthy');
}

// 更新配置显示
function updateConfigurationDisplay(config) {
    // 更新全局配置
    if (config.global) {
        updateElement('global-kubeconfig', config.global.kubeconfig, '', 'value');
        updateElement('global-namespace', config.global.namespace, '', 'value');
        updateCheckbox('global-verbose', config.global.verbose);
        updateCheckbox('global-force', config.global.force);
    }

    // 更新ETCD配置
    if (config.etcd) {
        updateElement('etcd-base-url', config.etcd.base_url, '', 'value');
        updateElement('etcd-version', config.etcd.version, '', 'value');
        updateElement('etcd-os', config.etcd.os, '', 'value');
        updateElement('etcd-data-dir', config.etcd.data_dir, '', 'value');
        updateElement('etcd-backup-dir', config.etcd.backup_dir, '', 'value');
        updateElement('etcd-api-server-manifest', config.etcd.api_server_manifest, '', 'value');
    }

    // 更新日志配置
    if (config.logging) {
        updateElement('logging-level', config.logging.level, '', 'value');
        updateElement('logging-format', config.logging.format, '', 'value');
        updateElement('logging-file', config.logging.file, '', 'value');
        updateCheckbox('logging-color', config.logging.color);
    }

    // 更新清理配置
    if (config.cleanup) {
        updateElement('cleanup-default-older-than', config.cleanup.default_older_than, '', 'value');
        updateElement('cleanup-concurrent-workers', config.cleanup.concurrent_workers, '', 'value');
        updateCheckbox('cleanup-default-dry-run', config.cleanup.default_dry_run);
        updateCheckbox('cleanup-auto-enabled', config.cleanup.auto_cleanup_enabled);
        updateElement('cleanup-auto-interval', config.cleanup.auto_cleanup_interval, '', 'value');
    }

    // 更新监控配置
    if (config.monitoring) {
        updateElement('monitoring-port', config.monitoring.port, '', 'value');
        updateElement('monitoring-metrics-interval', config.monitoring.metrics_interval, '', 'value');
        updateElement('monitoring-notification-url', config.monitoring.notification_url, '', 'value');
        updateCheckbox('monitoring-enabled', config.monitoring.enabled);
        updateCheckbox('monitoring-alerts-enabled', config.monitoring.alerts_enabled);
    }

    // 更新Web配置
    if (config.web) {
        updateElement('web-host', config.web.host, '', 'value');
        updateElement('web-port', config.web.port, '', 'value');
        updateElement('web-cert-file', config.web.cert_file, '', 'value');
        updateElement('web-key-file', config.web.key_file, '', 'value');
        updateElement('web-rate-limit-rps', config.web.rate_limit_rps, '', 'value');
        updateCheckbox('web-tls-enabled', config.web.tls_enabled);
        updateCheckbox('web-cors-enabled', config.web.cors_enabled);
    }

    // 更新自定义配置
    if (config.custom) {
        const customTextarea = document.getElementById('custom-config-json');
        if (customTextarea) {
            customTextarea.value = JSON.stringify(config.custom, null, 2);
        }
    }
}

// 更新配置历史显示
function updateConfigurationHistory(history) {
    const historyList = document.getElementById('history-list');
    if (!historyList) return;

    if (history.length === 0) {
        historyList.innerHTML = '<div class="loading-placeholder"><p>暂无配置历史记录</p></div>';
        return;
    }

    historyList.innerHTML = '';
    history.forEach(item => {
        const historyElement = createHistoryElement(item);
        historyList.appendChild(historyElement);
    });
}

// 创建历史记录元素
function createHistoryElement(item) {
    const historyDiv = document.createElement('div');
    historyDiv.className = `history-item ${item.operation}`;
    historyDiv.dataset.historyId = item.id;

    const operationIcon = getOperationIcon(item.operation);
    const timeAgo = getTimeAgo(item.timestamp);

    historyDiv.innerHTML = `
        <div class="history-icon">${operationIcon}</div>
        <div class="history-content">
            <div class="history-header">
                <h4 class="history-description">${item.description}</h4>
                <span class="history-timestamp">${timeAgo}</span>
            </div>
            <div class="history-meta">
                <span class="history-section">${item.section}</span>
                <span class="history-size">${formatFileSize(item.size)}</span>
            </div>
        </div>
        <div class="history-actions">
            <button class="history-action restore" onclick="restoreConfiguration('${item.id}')">恢复</button>
        </div>
    `;

    return historyDiv;
}

// Dashboard specific functions
function refreshDashboard() {
    console.log('Refreshing dashboard...');
    const loadingElement = document.getElementById('dashboard-loading');
    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    // 模拟加载过程
    setTimeout(() => {
        loadDashboardData();
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }, 1000);
}

function checkHealth() {
    console.log('Checking system health...');
    const healthResult = document.getElementById('health-result');
    if (healthResult) {
        healthResult.textContent = '检查中...';

        // 模拟健康检查
        setTimeout(() => {
            healthResult.textContent = '健康';
            healthResult.style.color = '#27ae60';
        }, 1500);
    }
}

function loadSystemMetrics() {
    console.log('Loading system metrics...');
    const metricsElement = document.getElementById('system-metrics');
    if (metricsElement) {
        metricsElement.textContent = '加载中...';

        // 模拟指标加载
        setTimeout(() => {
            metricsElement.innerHTML = 'CPU: 45%<br>内存: 62%<br>磁盘: 78%';
        }, 1200);
    }
}

function reconnectWebSocket() {
    console.log('Reconnecting WebSocket...');
    const wsElement = document.getElementById('ws-connections');
    if (wsElement) {
        wsElement.textContent = '连接中...';

        // 模拟重连过程
        setTimeout(() => {
            wsElement.textContent = '1';
            wsElement.style.color = '#27ae60';
        }, 2000);
    }
}

// Update status display
function updateSystemStatus() {
    const k8sStatus = document.getElementById('k8s-api-status');
    const etcdStatus = document.getElementById('etcd-status');
    const lastUpdate = document.getElementById('last-update');

    if (k8sStatus) k8sStatus.textContent = '正常';
    if (etcdStatus) etcdStatus.textContent = '正常';
    if (lastUpdate) lastUpdate.textContent = new Date().toLocaleTimeString();
}

// ETCD Module Functions
function performBackup() {
    console.log('Performing ETCD backup...');
    const loadingElement = document.getElementById('backup-loading');
    const resultElement = document.getElementById('backup-result');
    const outputPath = document.getElementById('backup-output').value;

    if (loadingElement) loadingElement.style.display = 'inline-block';
    if (resultElement) {
        resultElement.className = 'result-display show';
        resultElement.textContent = '正在创建备份...';
    }

    // 构建请求数据
    const requestData = {
        output_path: outputPath || ''
    };

    // 发送备份请求
    fetch('/api/v1/etcd/backup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (loadingElement) loadingElement.style.display = 'none';
        if (resultElement) {
            if (data.error) {
                resultElement.className = 'result-display show error';
                resultElement.textContent = '备份失败: ' + data.error;
            } else {
                resultElement.className = 'result-display show success';
                resultElement.textContent = `备份成功!\n路径: ${data.path}\n大小: ${data.size} bytes\n时间: ${data.timestamp}`;
                // 刷新备份列表
                loadBackupList();
            }
        }
    })
    .catch(error => {
        if (loadingElement) loadingElement.style.display = 'none';
        if (resultElement) {
            handleError(error, {
                context: 'ETCD备份',
                type: 'element',
                element: resultElement,
                showSuggestions: true
            });
        }
    });
}

function loadBackupList() {
    console.log('Loading backup list...');
    const listElement = document.getElementById('backup-files-list');

    if (listElement) {
        listElement.innerHTML = '<div class="loading-placeholder"><p>正在加载备份文件列表...</p></div>';

        // 模拟加载备份列表
        setTimeout(() => {
            listElement.innerHTML = `
                <div class="backup-file-item">
                    <div class="backup-file-name">etcd-backup-20250801-140000.db</div>
                    <div class="backup-file-info">大小: 2.5 MB | 创建时间: 2025-08-01 14:00:00</div>
                    <div class="backup-file-actions">
                        <button class="btn btn-sm btn-secondary" onclick="downloadBackup('etcd-backup-20250801-140000.db')">下载</button>
                        <button class="btn btn-sm btn-warning" onclick="restoreFromBackup('etcd-backup-20250801-140000.db')">恢复</button>
                    </div>
                </div>
                <div class="backup-file-item">
                    <div class="backup-file-name">etcd-backup-20250801-120000.db</div>
                    <div class="backup-file-info">大小: 2.3 MB | 创建时间: 2025-08-01 12:00:00</div>
                    <div class="backup-file-actions">
                        <button class="btn btn-sm btn-secondary" onclick="downloadBackup('etcd-backup-20250801-120000.db')">下载</button>
                        <button class="btn btn-sm btn-warning" onclick="restoreFromBackup('etcd-backup-20250801-120000.db')">恢复</button>
                    </div>
                </div>
            `;
        }, 1000);
    }
}

function performRestore() {
    console.log('Performing ETCD restore...');
    const loadingElement = document.getElementById('restore-loading');
    const resultElement = document.getElementById('restore-result');
    const snapshotPath = document.getElementById('restore-snapshot').value;
    const dataDir = document.getElementById('restore-data-dir').value;

    if (!snapshotPath) {
        if (resultElement) {
            resultElement.className = 'result-display show error';
            resultElement.textContent = '请输入快照文件路径';
        }
        return;
    }

    if (loadingElement) loadingElement.style.display = 'inline-block';
    if (resultElement) {
        resultElement.className = 'result-display show';
        resultElement.textContent = '正在恢复ETCD...';
    }

    // 模拟恢复过程
    setTimeout(() => {
        if (loadingElement) loadingElement.style.display = 'none';
        if (resultElement) {
            resultElement.className = 'result-display show success';
            resultElement.textContent = `恢复完成!\n快照: ${snapshotPath}\n数据目录: ${dataDir || '/var/lib/etcd'}`;
        }
    }, 3000);
}

function performVerify() {
    console.log('Performing snapshot verification...');
    const loadingElement = document.getElementById('verify-loading');
    const resultElement = document.getElementById('verify-result');
    const snapshotPath = document.getElementById('verify-snapshot').value;

    if (!snapshotPath) {
        if (resultElement) {
            resultElement.className = 'result-display show error';
            resultElement.textContent = '请输入快照文件路径';
        }
        return;
    }

    if (loadingElement) loadingElement.style.display = 'inline-block';
    if (resultElement) {
        resultElement.className = 'result-display show';
        resultElement.textContent = '正在验证快照...';
    }

    // 模拟验证过程
    setTimeout(() => {
        if (loadingElement) loadingElement.style.display = 'none';
        if (resultElement) {
            resultElement.className = 'result-display show success';
            resultElement.textContent = `验证成功!\n快照文件: ${snapshotPath}\n状态: 完整且有效\n版本: 3.5.0`;
        }
    }, 2000);
}

function checkETCDStatus() {
    console.log('Checking ETCD status...');
    const connectionStatus = document.getElementById('etcd-connection-status');
    const dbSize = document.getElementById('etcd-db-size');
    const lastBackup = document.getElementById('etcd-last-backup');

    if (connectionStatus) connectionStatus.textContent = '检查中...';
    if (dbSize) dbSize.textContent = '检查中...';
    if (lastBackup) lastBackup.textContent = '检查中...';

    // 模拟状态检查
    setTimeout(() => {
        if (connectionStatus) connectionStatus.textContent = '已连接';
        if (dbSize) dbSize.textContent = '2.5 MB';
        if (lastBackup) lastBackup.textContent = '2小时前';
    }, 1500);
}

function downloadBackup(filename) {
    console.log('Downloading backup:', filename);
    // TODO: 实现备份文件下载
    alert('下载功能将在后续版本中实现');
}

function restoreFromBackup(filename) {
    console.log('Restoring from backup:', filename);
    const restoreInput = document.getElementById('restore-snapshot');
    if (restoreInput) {
        restoreInput.value = './' + filename;
    }
    // 切换到恢复区域
    document.querySelector('.restore-section').scrollIntoView({ behavior: 'smooth' });
}

// Cluster Info 辅助函数
function formatDate(dateString) {
    if (!dateString) return '--';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getNamespaceCardClass(name) {
    if (name.startsWith('kube-')) return 'kube-system';
    if (name === 'default') return 'system';
    return '';
}

function showNodeDetails(nodeName) {
    console.log('Showing details for node:', nodeName);
    // TODO: 实现节点详情弹窗
    alert(`节点详情功能将在后续版本中实现\n节点: ${nodeName}`);
}

function initializeTableFilters(nodes) {
    const searchInput = document.getElementById('node-search');
    const statusFilter = document.getElementById('status-filter');
    const roleFilter = document.getElementById('role-filter');

    if (!searchInput || !statusFilter || !roleFilter) return;

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const roleValue = roleFilter.value;

        const filteredNodes = nodes.filter(node => {
            const matchesSearch = node.name.toLowerCase().includes(searchTerm) ||
                                node.internal_ip.includes(searchTerm);
            const matchesStatus = !statusValue || node.status === statusValue;
            const matchesRole = !roleValue || node.roles.includes(roleValue);

            return matchesSearch && matchesStatus && matchesRole;
        });

        updateNodesTableWithFiltered(filteredNodes);
    }

    searchInput.addEventListener('input', filterTable);
    statusFilter.addEventListener('change', filterTable);
    roleFilter.addEventListener('change', filterTable);
}

function updateNodesTableWithFiltered(nodes) {
    const tableBody = document.getElementById('nodes-table-body');
    if (!tableBody) return;

    if (nodes.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="loading-cell">没有匹配的节点</td></tr>';
        return;
    }

    const rows = nodes.map(node => {
        const statusClass = node.status.toLowerCase().replace(/\s+/g, '-');
        const rolesHtml = (node.roles || []).map(role =>
            `<span class="role-tag ${role}">${role}</span>`
        ).join('');

        return `
            <tr>
                <td>${node.name}</td>
                <td><span class="node-status ${statusClass}">${node.status}</span></td>
                <td><div class="node-roles">${rolesHtml}</div></td>
                <td>${node.internal_ip || '--'}</td>
                <td>${node.version}</td>
                <td>${node.os}</td>
                <td>${node.architecture}</td>
                <td>${formatDate(node.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-secondary" onclick="showNodeDetails('${node.name}')">详情</button>
                </td>
            </tr>
        `;
    }).join('');

    tableBody.innerHTML = rows;
}

// Pod日志相关辅助函数
function resetLogsModule() {
    resetPodSelection();
    resetContainerSelection();
    clearLogViewer();
    hidePodInfoPanel();
    updateConnectionStatus('disconnected', '未连接');
}

function resetPodSelection() {
    const podSelect = document.getElementById('pod-select');
    if (podSelect) {
        podSelect.innerHTML = '<option value="">请先选择命名空间</option>';
        podSelect.disabled = true;
    }
    resetContainerSelection();
}

function resetContainerSelection() {
    const containerSelect = document.getElementById('container-select');
    if (containerSelect) {
        containerSelect.innerHTML = '<option value="">请先选择Pod</option>';
        containerSelect.disabled = true;
    }
    updateLoadLogsButton();
}

function updateLoadLogsButton() {
    const loadLogsBtn = document.getElementById('load-logs-btn');
    const namespace = document.getElementById('namespace-select')?.value;
    const pod = document.getElementById('pod-select')?.value;
    const container = document.getElementById('container-select')?.value;

    if (loadLogsBtn) {
        loadLogsBtn.disabled = !(namespace && pod && container);
    }
}

function updatePodInfoPanel(podData) {
    const panel = document.getElementById('pod-info-panel');
    if (!panel || !podData) return;

    // 更新基本信息
    document.getElementById('pod-name').textContent = podData.name || '--';
    document.getElementById('pod-namespace').textContent = podData.namespace || '--';
    document.getElementById('pod-status').textContent = podData.status || '--';
    document.getElementById('pod-created').textContent = formatDate(podData.created_at) || '--';

    // 更新容器列表
    const containersList = document.getElementById('containers-list');
    if (containersList && podData.containers) {
        containersList.innerHTML = '';
        podData.containers.forEach(container => {
            const containerDiv = document.createElement('div');
            containerDiv.className = `container-item ${container.ready ? '' : 'not-ready'}`;
            containerDiv.innerHTML = `
                <div class="container-name">${container.name}</div>
                <div class="container-info">
                    镜像: ${container.image}<br>
                    状态: ${container.ready ? '就绪' : '未就绪'}
                </div>
            `;
            containersList.appendChild(containerDiv);
        });
    }

    // 显示面板
    panel.style.display = 'block';
}

function hidePodInfoPanel() {
    const panel = document.getElementById('pod-info-panel');
    if (panel) {
        panel.style.display = 'none';
    }
}

function loadPodLogs() {
    const namespace = document.getElementById('namespace-select').value;
    const pod = document.getElementById('pod-select').value;
    const container = document.getElementById('container-select').value;
    const timestamps = document.getElementById('show-timestamps').checked;
    const tailLines = document.getElementById('tail-lines').value;
    const follow = document.getElementById('follow-logs').checked;

    if (!namespace || !pod || !container) {
        alert('请选择命名空间、Pod和容器');
        return;
    }

    const loadingElement = document.getElementById('logs-loading');
    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    // 清空当前日志
    clearLogViewer();

    // 构建请求URL
    const params = new URLSearchParams({
        container: container,
        timestamps: timestamps,
        tail: tailLines
    });

    const url = `/api/v1/pods/${namespace}/${pod}/logs?${params}`;

    updateConnectionStatus('connecting', '连接中...');

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('收到日志数据:', data);
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }

            updateConnectionStatus('connected', '已连接');

            if (data.logs && data.logs.length > 0) {
                console.log('显示日志，数量:', data.logs.length);
                displayLogs(data.logs);
            } else if (data.content) {
                // 处理纯文本格式的日志
                console.log('处理纯文本日志');
                displayTextLogs(data.content);
            } else {
                console.log('无日志数据，显示空消息');
                displayNoLogsMessage();
            }

            // 如果启用了实时跟踪，建立WebSocket连接
            if (follow) {
                connectLogWebSocket(namespace, pod, container);
            }
        })
        .catch(error => {
            console.error('加载日志失败:', error);
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
            updateConnectionStatus('disconnected', '连接失败');
            displayErrorMessage(error.message);
        });
}

function displayLogs(logs) {
    const logViewer = document.getElementById('log-viewer');
    if (!logViewer) {
        console.error('log-viewer元素未找到');
        return;
    }

    console.log('开始显示日志，清空占位符');

    // 最简单的方式：直接设置HTML
    let html = '';
    logs.forEach(logEntry => {
        const timestamp = logEntry.timestamp ? new Date(logEntry.timestamp).toLocaleTimeString() : '';
        const level = logEntry.level ? `[${logEntry.level.toUpperCase()}]` : '';
        html += `<div style="padding: 5px; border-bottom: 1px solid #ccc; color: #333; background: white;">
            <span style="color: #666;">${timestamp}</span>
            <span style="color: #007bff; font-weight: bold;">${level}</span>
            <span>${logEntry.message}</span>
        </div>`;
    });

    logViewer.innerHTML = html;
    console.log('日志HTML已设置，内容长度:', html.length);
}

function displayTextLogs(content) {
    const logViewer = document.getElementById('log-viewer');
    if (!logViewer) {
        console.error('log-viewer元素未找到');
        return;
    }

    console.log('显示纯文本日志');
    // 清空占位符
    logViewer.innerHTML = '';

    // 将纯文本按行分割并显示
    const lines = content.split('\n');
    lines.forEach((line, index) => {
        if (line.trim()) { // 跳过空行
            const logEntry = {
                message: line,
                timestamp: new Date().toISOString(),
                level: 'info'
            };
            appendLogLine(logEntry);
        }
    });

    updateLogStats();
    scrollToBottom();
}

function appendLogLine(logEntry) {
    const logViewer = document.getElementById('log-viewer');

    if (!logViewer) {
        console.error('log-viewer元素未找到');
        return;
    }

    console.log('添加日志行:', logEntry.message?.substring(0, 50) + '...');

    // 直接创建DOM元素，不使用模板
    const lineDiv = document.createElement('div');
    lineDiv.className = 'log-line';
    lineDiv.style.cssText = 'display: flex !important; padding: 2px 10px; border-bottom: 1px solid #2d2d2d; color: #d4d4d4; min-height: 20px;';

    // 设置日志级别
    if (logEntry.level) {
        lineDiv.dataset.level = logEntry.level;
        lineDiv.classList.add(`level-${logEntry.level}`);
    }

    // 创建时间戳元素
    const timestampSpan = document.createElement('span');
    timestampSpan.className = 'log-timestamp';
    timestampSpan.style.cssText = 'color: #569cd6; margin-right: 10px;';
    if (logEntry.timestamp) {
        timestampSpan.textContent = formatLogTimestamp(logEntry.timestamp);
    }

    // 创建日志级别标签
    const levelSpan = document.createElement('span');
    levelSpan.className = 'log-level';
    if (logEntry.level) {
        levelSpan.textContent = logEntry.level.toUpperCase();
        levelSpan.className = `log-level ${logEntry.level}`;
    }

    // 创建日志内容
    const contentSpan = document.createElement('span');
    contentSpan.className = 'log-content';
    contentSpan.style.cssText = 'color: #d4d4d4; flex: 1;';
    contentSpan.textContent = logEntry.message || '';

    // 组装元素
    lineDiv.appendChild(timestampSpan);
    lineDiv.appendChild(levelSpan);
    lineDiv.appendChild(contentSpan);

    console.log('创建的日志行元素:', lineDiv);
    console.log('logViewer当前子元素数量:', logViewer.children.length);

    logViewer.appendChild(lineDiv);

    console.log('添加后logViewer子元素数量:', logViewer.children.length);
    console.log('logViewer当前HTML:', logViewer.innerHTML.substring(0, 200) + '...');
}

function displayNoLogsMessage() {
    const logViewer = document.getElementById('log-viewer');
    if (!logViewer) return;

    logViewer.innerHTML = `
        <div class="log-placeholder">
            <div class="placeholder-content">
                <i class="placeholder-icon">📄</i>
                <h4>暂无日志</h4>
                <p>该Pod/容器当前没有日志输出</p>
            </div>
        </div>
    `;
}

function displayErrorMessage(message) {
    const logViewer = document.getElementById('log-viewer');
    if (!logViewer) return;

    logViewer.innerHTML = `
        <div class="log-placeholder">
            <div class="placeholder-content">
                <i class="placeholder-icon">❌</i>
                <h4>加载失败</h4>
                <p>${message}</p>
            </div>
        </div>
    `;
}

// 日志辅助函数
function clearLogViewer() {
    const logViewer = document.getElementById('log-viewer');
    if (logViewer) {
        logViewer.innerHTML = `
            <div class="log-placeholder">
                <div class="placeholder-content">
                    <i class="placeholder-icon">📋</i>
                    <h4>选择Pod开始查看日志</h4>
                    <p>请在上方选择命名空间、Pod和容器，然后点击"加载日志"按钮</p>
                </div>
            </div>
        `;
    }
    updateLogStats();
}

function updateConnectionStatus(status, text) {
    const indicator = document.getElementById('connection-indicator');
    const statusText = document.getElementById('connection-text');

    if (indicator) {
        indicator.className = `status-indicator ${status}`;
    }

    if (statusText) {
        statusText.textContent = text;
    }
}

function updateLogStats() {
    const logLines = document.querySelectorAll('.log-line');
    const visibleLines = document.querySelectorAll('.log-line:not(.hidden)');

    const totalElement = document.getElementById('total-lines');
    const filteredElement = document.getElementById('filtered-lines');

    if (totalElement) {
        totalElement.textContent = logLines.length;
    }

    if (filteredElement) {
        filteredElement.textContent = visibleLines.length;
    }
}

function formatLogTimestamp(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3
    });
}

function scrollToBottom() {
    const logViewer = document.getElementById('log-viewer');
    const autoScrollBtn = document.getElementById('auto-scroll-btn');

    if (logViewer && autoScrollBtn && autoScrollBtn.dataset.enabled === 'true') {
        logViewer.scrollTop = logViewer.scrollHeight;
    }
}

function performLogSearch() {
    const searchInput = document.getElementById('log-search');
    const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';

    const logLines = document.querySelectorAll('.log-line');
    logLines.forEach(line => {
        const content = line.querySelector('.log-content');
        if (content) {
            const text = content.textContent.toLowerCase();
            if (!searchTerm || text.includes(searchTerm)) {
                line.classList.remove('hidden');
                if (searchTerm) {
                    line.classList.add('highlight');
                } else {
                    line.classList.remove('highlight');
                }
            } else {
                line.classList.add('hidden');
                line.classList.remove('highlight');
            }
        }
    });

    updateLogStats();
}

function clearLogSearch() {
    const searchInput = document.getElementById('log-search');
    if (searchInput) {
        searchInput.value = '';
    }

    const logLines = document.querySelectorAll('.log-line');
    logLines.forEach(line => {
        line.classList.remove('hidden', 'highlight');
    });

    updateLogStats();
}

function filterLogsByLevel(level) {
    const logLines = document.querySelectorAll('.log-line');
    logLines.forEach(line => {
        if (!level || line.dataset.level === level) {
            line.classList.remove('hidden');
        } else {
            line.classList.add('hidden');
        }
    });

    updateLogStats();
}

function toggleAutoScroll() {
    const autoScrollBtn = document.getElementById('auto-scroll-btn');
    if (!autoScrollBtn) return;

    const enabled = autoScrollBtn.dataset.enabled === 'true';
    autoScrollBtn.dataset.enabled = enabled ? 'false' : 'true';
    autoScrollBtn.classList.toggle('active', !enabled);
    autoScrollBtn.textContent = enabled ? '自动滚动' : '自动滚动';
}

function downloadLogs() {
    const logLines = document.querySelectorAll('.log-line:not(.hidden)');
    if (logLines.length === 0) {
        alert('没有可下载的日志');
        return;
    }

    let logContent = '';
    logLines.forEach(line => {
        const timestamp = line.querySelector('.log-timestamp').textContent;
        const level = line.querySelector('.log-level').textContent;
        const content = line.querySelector('.log-content').textContent;
        logContent += `${timestamp} ${level} ${content}\n`;
    });

    const namespace = document.getElementById('namespace-select').value;
    const pod = document.getElementById('pod-select').value;
    const container = document.getElementById('container-select').value;

    const filename = `${namespace}-${pod}-${container}-${new Date().toISOString().slice(0, 19)}.log`;

    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function connectLogWebSocket(namespace, pod, container) {
    // 断开现有的日志WebSocket连接
    disconnectModuleWebSocket('logs');

    // 建立新的日志WebSocket连接
    const wsUrl = `ws://${window.location.host}/api/v1/ws/logs?namespace=${namespace}&pod=${pod}&container=${container}`;
    const ws = new WebSocket(wsUrl);

    ws.onopen = function() {
        console.log('日志WebSocket连接已建立');
        if (!window.K8sHelper.wsConnections) {
            window.K8sHelper.wsConnections = {};
        }
        window.K8sHelper.wsConnections['logs'] = ws;

        // 显示连接状态
        const statusElement = document.getElementById('log-stream-status');
        if (statusElement) {
            statusElement.textContent = '实时日志流已连接';
            statusElement.className = 'status healthy';
        }
    };

    ws.onmessage = function(event) {
        const message = JSON.parse(event.data);
        if (message.type === 'log_line') {
            appendLogMessage(message.data);
        }
    };

    ws.onclose = function() {
        console.log('日志WebSocket连接已断开');
        if (window.K8sHelper.wsConnections) {
            delete window.K8sHelper.wsConnections['logs'];
        }

        // 更新连接状态
        const statusElement = document.getElementById('log-stream-status');
        if (statusElement) {
            statusElement.textContent = '实时日志流已断开';
            statusElement.className = 'status unknown';
        }
    };

    ws.onerror = function(error) {
        console.error('日志WebSocket错误:', error);

        // 显示错误状态
        const statusElement = document.getElementById('log-stream-status');
        if (statusElement) {
            statusElement.textContent = '实时日志流连接错误';
            statusElement.className = 'status unhealthy';
        }
    };
}

// 追加日志消息到日志显示区域
function appendLogMessage(logData) {
    const logContainer = document.getElementById('log-content');
    if (!logContainer) return;

    const logLine = document.createElement('div');
    logLine.className = 'log-line';

    // 格式化时间戳
    const timestamp = new Date(logData.timestamp || Date.now()).toLocaleTimeString();

    logLine.innerHTML = `
        <span class="log-timestamp">${timestamp}</span>
        <span class="log-message">${escapeHtml(logData.message || logData)}</span>
    `;

    logContainer.appendChild(logLine);

    // 自动滚动到底部
    logContainer.scrollTop = logContainer.scrollHeight;

    // 限制日志行数，避免内存占用过多
    const maxLines = 1000;
    const lines = logContainer.querySelectorAll('.log-line');
    if (lines.length > maxLines) {
        for (let i = 0; i < lines.length - maxLines; i++) {
            lines[i].remove();
        }
    }
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 端口转发相关函数
function initializePortForwardEventListeners() {
    // 命名空间选择
    const namespaceSelect = document.getElementById('pf-namespace');
    if (namespaceSelect) {
        namespaceSelect.addEventListener('change', function() {
            const namespace = this.value;
            resetPodSelection();
            if (namespace) {
                loadPortForwardPods(namespace);
            }
        });
    }

    // 创建端口转发表单
    const createForm = document.getElementById('create-forward-form');
    if (createForm) {
        createForm.addEventListener('submit', function(e) {
            e.preventDefault();
            createPortForward();
        });
    }

    // 刷新列表
    const refreshBtn = document.getElementById('refresh-forwards');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            loadPortForwardsList();
        });
    }

    // 端口检查
    const checkPortBtn = document.getElementById('check-port-btn');
    if (checkPortBtn) {
        checkPortBtn.addEventListener('click', function() {
            checkPortAvailability();
        });
    }

    // 端口建议标签
    const portTags = document.querySelectorAll('.port-tag');
    portTags.forEach(tag => {
        tag.addEventListener('click', function() {
            const port = this.dataset.port;
            const localPortInput = document.getElementById('pf-local-port');
            if (localPortInput) {
                localPortInput.value = port;
            }
        });
    });

    // 搜索过滤
    const searchInput = document.getElementById('forward-search');
    const statusFilter = document.getElementById('status-filter');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterPortForwards();
        });
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            filterPortForwards();
        });
    }
}

function resetPodSelection() {
    const podSelect = document.getElementById('pf-pod');
    if (podSelect) {
        podSelect.innerHTML = '<option value="">请先选择命名空间</option>';
        podSelect.disabled = true;
    }
}

function createPortForward() {
    const namespace = document.getElementById('pf-namespace').value;
    const pod = document.getElementById('pf-pod').value;
    const localPort = parseInt(document.getElementById('pf-local-port').value) || 0;
    const podPort = parseInt(document.getElementById('pf-pod-port').value);

    if (!namespace || !pod || !podPort) {
        showCreateResult('请填写所有必填字段', 'error');
        return;
    }

    const loadingElement = document.getElementById('create-loading');
    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    const requestData = {
        namespace: namespace,
        pod: pod,
        local_port: localPort,
        pod_port: podPort
    };

    fetch('/api/v1/port-forward', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (data.error) {
            showCreateResult('创建失败: ' + data.error, 'error');
        } else {
            showCreateResult(`端口转发创建成功！本地端口: ${data.forward.local_port}`, 'success');
            document.getElementById('create-forward-form').reset();
            resetPodSelection();
            loadPortForwardsList();
        }
    })
    .catch(error => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
        showCreateResult('创建失败: ' + error.message, 'error');
    });
}

function showCreateResult(message, type) {
    const resultElement = document.getElementById('create-result');
    if (resultElement) {
        resultElement.className = `result-display show ${type}`;
        resultElement.textContent = message;

        // 3秒后自动隐藏
        setTimeout(() => {
            resultElement.className = 'result-display';
        }, 3000);
    }
}

function testPortForward(forward) {
    const url = `http://localhost:${forward.local_port}`;
    window.open(url, '_blank');
}

function stopPortForward(id) {
    if (!confirm('确定要停止这个端口转发吗？')) {
        return;
    }

    fetch(`/api/v1/port-forward/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('停止失败: ' + data.error);
        } else {
            loadPortForwardsList();
        }
    })
    .catch(error => {
        alert('停止失败: ' + error.message);
    });
}

function deletePortForward(id) {
    if (!confirm('确定要删除这个端口转发吗？此操作不可撤销。')) {
        return;
    }

    // TODO: 实现删除API
    console.log('删除端口转发:', id);
    alert('删除功能将在后续版本中实现');
}

function checkPortAvailability() {
    const port = document.getElementById('check-port').value;
    const resultElement = document.getElementById('port-check-result');

    if (!port) {
        resultElement.className = 'check-result unavailable';
        resultElement.textContent = '请输入端口号';
        resultElement.style.display = 'block';
        return;
    }

    // 简单的端口检查（实际应该调用API）
    const portNum = parseInt(port);
    if (portNum < 1 || portNum > 65535) {
        resultElement.className = 'check-result unavailable';
        resultElement.textContent = '端口号必须在1-65535之间';
        resultElement.style.display = 'block';
        return;
    }

    // 模拟端口检查
    const isAvailable = Math.random() > 0.3; // 70%概率可用
    resultElement.className = `check-result ${isAvailable ? 'available' : 'unavailable'}`;
    resultElement.textContent = isAvailable ?
        `端口 ${port} 可用` :
        `端口 ${port} 已被占用`;
    resultElement.style.display = 'block';
}

function updatePortForwardStats(forwards = []) {
    const totalElement = document.getElementById('total-forwards');
    const runningElement = document.getElementById('running-forwards');
    const errorElement = document.getElementById('error-forwards');
    const usedPortsElement = document.getElementById('used-ports');

    const total = forwards.length;
    const running = forwards.filter(f => f.status === 'running').length;
    const error = forwards.filter(f => f.status === 'error').length;
    const usedPorts = new Set(forwards.map(f => f.local_port)).size;

    if (totalElement) totalElement.textContent = total;
    if (runningElement) runningElement.textContent = running;
    if (errorElement) errorElement.textContent = error;
    if (usedPortsElement) usedPortsElement.textContent = usedPorts;
}

function filterPortForwards() {
    const searchTerm = document.getElementById('forward-search')?.value.toLowerCase() || '';
    const statusFilter = document.getElementById('status-filter')?.value || '';

    const cards = document.querySelectorAll('.forward-card');
    cards.forEach(card => {
        const title = card.querySelector('.forward-title').textContent.toLowerCase();
        const status = card.dataset.status;

        const matchesSearch = title.includes(searchTerm);
        const matchesStatus = !statusFilter || status === statusFilter;

        if (matchesSearch && matchesStatus) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        // 简单的提示
        const originalText = event.target.textContent;
        event.target.textContent = '已复制!';
        setTimeout(() => {
            event.target.textContent = originalText;
        }, 1000);
    }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制: ' + text);
    });
}

// 资源清理相关函数
function resetCleanupModule() {
    // 隐藏扫描结果
    const scanResults = document.getElementById('scan-results');
    if (scanResults) {
        scanResults.style.display = 'none';
    }

    // 禁用执行按钮
    const executeBtn = document.getElementById('execute-btn');
    if (executeBtn) {
        executeBtn.disabled = true;
    }

    // 重置表格
    const tableBody = document.getElementById('resources-table-body');
    if (tableBody) {
        tableBody.innerHTML = '<tr><td colspan="8" class="loading-cell">请先执行扫描操作</td></tr>';
    }
}

function displayScanResults(result) {
    // 显示扫描结果区域
    const scanResults = document.getElementById('scan-results');
    if (scanResults) {
        scanResults.style.display = 'block';
    }

    // 更新统计信息
    updateScanSummary(result);

    // 显示资源列表
    displayResourcesTable(result.resources || []);
}

function updateScanSummary(result) {
    const totalScanned = document.getElementById('total-scanned');
    const totalCleanup = document.getElementById('total-cleanup');
    const scanDuration = document.getElementById('scan-duration');
    const errorCount = document.getElementById('error-count');

    if (totalScanned) totalScanned.textContent = result.stats?.total_scanned || 0;
    if (totalCleanup) totalCleanup.textContent = (result.resources || []).length;
    if (scanDuration) scanDuration.textContent = formatDuration(result.execution_time);
    if (errorCount) errorCount.textContent = result.stats?.error_count || 0;
}

function displayResourcesTable(resources) {
    const tableBody = document.getElementById('resources-table-body');
    if (!tableBody) return;

    if (resources.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="8" class="loading-cell">未找到需要清理的资源</td></tr>';
        return;
    }

    tableBody.innerHTML = '';
    resources.forEach((resource, index) => {
        const row = createResourceRow(resource, index);
        tableBody.appendChild(row);
    });
}

function createResourceRow(resource, index) {
    const row = document.createElement('tr');
    row.dataset.resourceIndex = index;

    row.innerHTML = `
        <td>
            <label class="checkbox-label">
                <input type="checkbox" class="resource-checkbox" value="${index}" />
                <span></span>
            </label>
        </td>
        <td>${resource.name}</td>
        <td><span class="resource-type ${resource.type.toLowerCase()}">${resource.type}</span></td>
        <td>${resource.namespace}</td>
        <td><span class="resource-status ${resource.status.toLowerCase()}">${resource.status}</span></td>
        <td>${resource.reason || '--'}</td>
        <td>${resource.age}</td>
        <td>${resource.owner_ref || '--'}</td>
    `;

    return row;
}

function enableExecuteButton() {
    const executeBtn = document.getElementById('execute-btn');
    if (executeBtn) {
        executeBtn.disabled = false;
    }
}

function performCleanupExecute() {
    const dryRun = document.getElementById('dry-run').checked;
    const force = document.getElementById('force-cleanup').checked;

    // 获取选中的资源
    const selectedResources = getSelectedResources();
    if (selectedResources.length === 0) {
        alert('请选择要清理的资源');
        return;
    }

    if (!dryRun && !force) {
        // 显示确认对话框
        showConfirmDialog(selectedResources);
    } else {
        // 直接执行
        executeCleanup(selectedResources, dryRun);
    }
}

function getSelectedResources() {
    const checkboxes = document.querySelectorAll('.resource-checkbox:checked');
    const resources = [];

    checkboxes.forEach(checkbox => {
        const index = parseInt(checkbox.value);
        const row = checkbox.closest('tr');
        if (row) {
            resources.push({
                index: index,
                name: row.cells[1].textContent,
                type: row.cells[2].textContent,
                namespace: row.cells[3].textContent,
                status: row.cells[4].textContent
            });
        }
    });

    return resources;
}

function showConfirmDialog(resources) {
    const dialog = document.getElementById('confirm-dialog');
    const countElement = document.getElementById('confirm-count');
    const listElement = document.getElementById('confirm-list');

    if (countElement) {
        countElement.textContent = resources.length;
    }

    if (listElement) {
        listElement.innerHTML = '';
        resources.forEach(resource => {
            const li = document.createElement('li');
            li.textContent = `${resource.type}: ${resource.namespace}/${resource.name}`;
            listElement.appendChild(li);
        });
    }

    if (dialog) {
        dialog.style.display = 'flex';
    }
}

function hideConfirmDialog() {
    const dialog = document.getElementById('confirm-dialog');
    if (dialog) {
        dialog.style.display = 'none';
    }
}

function executeCleanupConfirmed() {
    hideConfirmDialog();
    const selectedResources = getSelectedResources();
    executeCleanup(selectedResources, false);
}

function executeCleanup(selectedResources, dryRun) {
    const type = document.getElementById('cleanup-type').value;
    const namespacesSelect = document.getElementById('cleanup-namespaces');
    const olderThan = document.getElementById('older-than').value;

    const selectedNamespaces = Array.from(namespacesSelect.selectedOptions).map(option => option.value);

    const loadingElement = document.getElementById('execute-loading');
    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    const requestData = {
        type: type,
        namespaces: selectedNamespaces,
        older_than: olderThan,
        dry_run: dryRun,
        force: true
    };

    fetch('/api/v1/cleanup/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (data.error) {
            alert('清理失败: ' + data.error);
        } else {
            const result = data.result;
            if (dryRun) {
                alert(`预览完成！扫描了 ${result.stats.total_scanned} 个资源，找到 ${result.resources.length} 个待清理资源。`);
            } else {
                alert(`清理完成！成功清理了 ${result.stats.total_cleaned} 个资源。`);
                // 重新扫描以更新结果
                performCleanupScan();
            }
        }
    })
    .catch(error => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
        handleError(error, {
            context: '资源清理',
            type: 'alert',
            showSuggestions: true
        });
    });
}

function toggleSelectAllResources(checked) {
    const checkboxes = document.querySelectorAll('.resource-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = checked;
    });
}

function filterResources() {
    const searchTerm = document.getElementById('resource-search')?.value.toLowerCase() || '';
    const typeFilter = document.getElementById('resource-type-filter')?.value || '';
    const statusFilter = document.getElementById('resource-status-filter')?.value || '';

    const rows = document.querySelectorAll('#resources-table-body tr');
    rows.forEach(row => {
        if (row.cells.length < 8) return; // 跳过加载行

        const name = row.cells[1].textContent.toLowerCase();
        const type = row.cells[2].textContent;
        const status = row.cells[4].textContent;

        const matchesSearch = name.includes(searchTerm);
        const matchesType = !typeFilter || type.includes(typeFilter);
        const matchesStatus = !statusFilter || status.includes(statusFilter);

        if (matchesSearch && matchesType && matchesStatus) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function formatDuration(nanoseconds) {
    if (!nanoseconds) return '0ms';

    const ms = nanoseconds / 1000000;
    if (ms < 1000) {
        return Math.round(ms) + 'ms';
    } else {
        return Math.round(ms / 1000 * 100) / 100 + 's';
    }
}

// 监控模块相关函数
function initializeMonitoringEventListeners() {
    // 刷新告警按钮
    const refreshAlertsBtn = document.getElementById('refresh-alerts');
    if (refreshAlertsBtn) {
        refreshAlertsBtn.addEventListener('click', function() {
            loadAlertsData();
        });
    }

    // 创建告警按钮
    const createAlertBtn = document.getElementById('create-alert');
    if (createAlertBtn) {
        createAlertBtn.addEventListener('click', function() {
            showCreateAlertDialog();
        });
    }

    // 告警过滤器
    const severityFilter = document.getElementById('alert-severity-filter');
    const statusFilter = document.getElementById('alert-status-filter');

    if (severityFilter) {
        severityFilter.addEventListener('change', function() {
            filterAlerts();
        });
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            filterAlerts();
        });
    }

    // 配置选项
    const refreshInterval = document.getElementById('refresh-interval');
    const autoRefresh = document.getElementById('auto-refresh');
    const enableNotifications = document.getElementById('enable-notifications');

    if (refreshInterval) {
        refreshInterval.addEventListener('change', function() {
            updateRefreshInterval(parseInt(this.value));
        });
    }

    if (autoRefresh) {
        autoRefresh.addEventListener('change', function() {
            if (this.checked) {
                startMonitoringAutoRefresh();
            } else {
                stopMonitoringAutoRefresh();
            }
        });
    }

    if (enableNotifications) {
        enableNotifications.addEventListener('change', function() {
            if (this.checked) {
                requestNotificationPermission();
            }
        });
    }

    // 创建告警对话框事件
    const createAlertDialog = document.getElementById('create-alert-dialog');
    const submitAlert = document.getElementById('submit-alert');
    const cancelAlert = document.getElementById('cancel-alert');
    const modalClose = document.querySelector('#create-alert-dialog .modal-close');

    if (submitAlert) {
        submitAlert.addEventListener('click', function() {
            submitCreateAlert();
        });
    }

    if (cancelAlert) {
        cancelAlert.addEventListener('click', function() {
            hideCreateAlertDialog();
        });
    }

    if (modalClose) {
        modalClose.addEventListener('click', function() {
            hideCreateAlertDialog();
        });
    }

    // 点击对话框外部关闭
    if (createAlertDialog) {
        createAlertDialog.addEventListener('click', function(e) {
            if (e.target === createAlertDialog) {
                hideCreateAlertDialog();
            }
        });
    }
}

// 启动自动刷新
let monitoringRefreshInterval;
function startMonitoringAutoRefresh() {
    const intervalSelect = document.getElementById('refresh-interval');
    const autoRefreshCheckbox = document.getElementById('auto-refresh');

    if (!autoRefreshCheckbox || !autoRefreshCheckbox.checked) return;

    const interval = intervalSelect ? parseInt(intervalSelect.value) * 1000 : 10000;

    stopMonitoringAutoRefresh(); // 清除现有的定时器

    monitoringRefreshInterval = setInterval(() => {
        loadMetricsData();
        loadHealthData();
        loadPerformanceData();
    }, interval);
}

// 停止自动刷新
function stopMonitoringAutoRefresh() {
    if (monitoringRefreshInterval) {
        clearInterval(monitoringRefreshInterval);
        monitoringRefreshInterval = null;
    }
}

// 更新刷新间隔
function updateRefreshInterval(seconds) {
    const autoRefreshCheckbox = document.getElementById('auto-refresh');
    if (autoRefreshCheckbox && autoRefreshCheckbox.checked) {
        startMonitoringAutoRefresh();
    }
}

// 显示创建告警对话框
function showCreateAlertDialog() {
    const dialog = document.getElementById('create-alert-dialog');
    if (dialog) {
        dialog.style.display = 'flex';
        // 重置表单
        const form = document.getElementById('create-alert-form');
        if (form) {
            form.reset();
        }
    }
}

// 隐藏创建告警对话框
function hideCreateAlertDialog() {
    const dialog = document.getElementById('create-alert-dialog');
    if (dialog) {
        dialog.style.display = 'none';
    }
}

// 提交创建告警
function submitCreateAlert() {
    const name = document.getElementById('alert-name').value;
    const severity = document.getElementById('alert-severity').value;
    const description = document.getElementById('alert-description').value;
    const tags = document.getElementById('alert-tags').value;

    if (!name || !severity || !description) {
        alert('请填写所有必填字段');
        return;
    }

    const alertData = {
        name: name,
        severity: severity,
        description: description,
        tags: tags ? tags.split(',').map(tag => tag.trim()) : []
    };

    fetch('/api/v1/monitoring/alerts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(alertData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('创建告警失败: ' + data.error);
        } else {
            hideCreateAlertDialog();
            loadAlertsData(); // 重新加载告警列表
            showNotification('告警创建成功', `告警 "${name}" 已创建`);
        }
    })
    .catch(error => {
        alert('创建告警失败: ' + error.message);
    });
}

// 解决告警
function resolveAlert(alertId) {
    if (!confirm('确定要解决这个告警吗？')) {
        return;
    }

    // 这里应该调用解决告警的API，目前模拟删除
    deleteAlert(alertId);
}

// 删除告警
function deleteAlert(alertId) {
    if (!confirm('确定要删除这个告警吗？')) {
        return;
    }

    fetch(`/api/v1/monitoring/alerts/${alertId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('删除告警失败: ' + data.error);
        } else {
            loadAlertsData(); // 重新加载告警列表
            showNotification('告警已删除', '告警已成功删除');
        }
    })
    .catch(error => {
        alert('删除告警失败: ' + error.message);
    });
}

// 过滤告警
function filterAlerts() {
    const severityFilter = document.getElementById('alert-severity-filter').value;
    const statusFilter = document.getElementById('alert-status-filter').value;

    const alertItems = document.querySelectorAll('.alert-item');
    alertItems.forEach(item => {
        const severity = item.classList.contains('critical') ? 'critical' :
                        item.classList.contains('warning') ? 'warning' : 'info';
        const status = item.querySelector('.alert-action.resolve') ? 'active' : 'resolved';

        const matchesSeverity = !severityFilter || severity === severityFilter;
        const matchesStatus = !statusFilter || status === statusFilter;

        if (matchesSeverity && matchesStatus) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
}

// 辅助函数
function updateElement(id, value, className = '') {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
        if (className) {
            element.className = className;
        }
    }
}

function getSeverityIcon(severity) {
    switch (severity) {
        case 'critical': return '🔴';
        case 'warning': return '🟡';
        case 'info': return '🔵';
        default: return '⚪';
    }
}

function getTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now - time;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
        return `${diffDays}天前`;
    } else if (diffHours > 0) {
        return `${diffHours}小时前`;
    } else if (diffMins > 0) {
        return `${diffMins}分钟前`;
    } else {
        return '刚刚';
    }
}

function requestNotificationPermission() {
    if ('Notification' in window) {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                showNotification('通知已启用', '您将收到重要告警的浏览器通知');
            }
        });
    }
}

function showNotification(title, message, type = 'info') {
    // 使用UI增强器显示Toast通知
    if (window.K8sHelper && window.K8sHelper.ui) {
        const fullMessage = title ? `${title}: ${message}` : message;
        window.K8sHelper.ui.showToast(fullMessage, type, 4000);
    }

    // 同时显示浏览器原生通知（如果启用）
    const enableNotifications = document.getElementById('enable-notifications');
    if (enableNotifications && enableNotifications.checked && 'Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
            body: message,
            icon: '/static/favicon.ico'
        });
    }
}

// 配置管理相关函数
function initializeConfigurationEventListeners() {
    // 配置部分切换
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const section = this.dataset.section;
            showConfigSection(section);
        });
    });

    // 配置操作按钮
    const reloadBtn = document.getElementById('reload-config');
    const backupBtn = document.getElementById('backup-config');
    const validateBtn = document.getElementById('validate-config');
    const saveBtn = document.getElementById('save-config');
    const resetBtn = document.getElementById('reset-config');

    if (reloadBtn) {
        reloadBtn.addEventListener('click', function() {
            reloadConfiguration();
        });
    }

    if (backupBtn) {
        backupBtn.addEventListener('click', function() {
            backupConfiguration();
        });
    }

    if (validateBtn) {
        validateBtn.addEventListener('click', function() {
            validateConfiguration();
        });
    }

    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            saveConfiguration();
        });
    }

    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            resetConfiguration();
        });
    }

    // 历史操作按钮
    const refreshHistoryBtn = document.getElementById('refresh-history');
    const historyLimitSelect = document.getElementById('history-limit');

    if (refreshHistoryBtn) {
        refreshHistoryBtn.addEventListener('click', function() {
            loadConfigurationHistory();
        });
    }

    if (historyLimitSelect) {
        historyLimitSelect.addEventListener('change', function() {
            loadConfigurationHistory();
        });
    }

    // 监听配置变化
    const configInputs = document.querySelectorAll('.config-section input, .config-section select, .config-section textarea');
    configInputs.forEach(input => {
        input.addEventListener('change', function() {
            enableSaveButton();
        });
        input.addEventListener('input', function() {
            enableSaveButton();
        });
    });
}

function showConfigSection(section) {
    // 隐藏所有配置部分
    const sections = document.querySelectorAll('.config-section');
    sections.forEach(s => s.classList.remove('active'));

    // 显示选中的配置部分
    const targetSection = document.getElementById(`section-${section}`);
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // 更新标签状态
    const tabs = document.querySelectorAll('.tab-btn');
    tabs.forEach(tab => {
        tab.classList.remove('active');
        if (tab.dataset.section === section) {
            tab.classList.add('active');
        }
    });
}

function enableSaveButton() {
    const saveBtn = document.getElementById('save-config');
    if (saveBtn) {
        saveBtn.disabled = false;
    }
}

function disableSaveButton() {
    const saveBtn = document.getElementById('save-config');
    if (saveBtn) {
        saveBtn.disabled = true;
    }
}

function reloadConfiguration() {
    const loadingElement = document.getElementById('reload-loading');
    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    fetch('/api/v1/config/reload', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (data.error) {
            alert('重载配置失败: ' + data.error);
        } else {
            alert('配置重载成功');
            loadConfigurationData(); // 重新加载配置数据
        }
    })
    .catch(error => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
        alert('重载配置失败: ' + error.message);
    });
}

function backupConfiguration() {
    // 这里可以触发一个配置更新来创建备份
    alert('配置备份功能将在保存配置时自动执行');
}

function validateConfiguration() {
    const currentSection = document.querySelector('.tab-btn.active')?.dataset.section || 'global';
    const configData = collectConfigData(currentSection);

    fetch('/api/v1/config/validate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(configData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.valid) {
            alert('配置验证通过');
        } else {
            alert('配置验证失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('配置验证失败: ' + error.message);
    });
}

function saveConfiguration() {
    const currentSection = document.querySelector('.tab-btn.active')?.dataset.section || 'global';
    const configData = collectConfigData(currentSection);

    const loadingElement = document.getElementById('save-loading');
    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }

    const requestData = {
        section: currentSection,
        content: configData,
        validate: true,
        backup: true
    };

    fetch(`/api/v1/config/${currentSection}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (data.error) {
            alert('保存配置失败: ' + data.error);
        } else {
            alert('配置保存成功');
            disableSaveButton();
            loadConfigurationData(); // 重新加载配置数据
            loadConfigurationHistory(); // 重新加载历史记录
        }
    })
    .catch(error => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
        handleError(error, {
            context: '保存配置',
            type: 'alert',
            showSuggestions: true
        });
    });
}

function resetConfiguration() {
    if (!confirm('确定要重置当前配置部分吗？未保存的更改将丢失。')) {
        return;
    }

    loadConfigurationData(); // 重新加载配置数据
    disableSaveButton();
}

function collectConfigData(section) {
    const data = {};

    switch (section) {
        case 'global':
            data.kubeconfig = getElementValue('global-kubeconfig');
            data.namespace = getElementValue('global-namespace');
            data.verbose = getCheckboxValue('global-verbose');
            data.force = getCheckboxValue('global-force');
            break;
        case 'etcd':
            data.base_url = getElementValue('etcd-base-url');
            data.version = getElementValue('etcd-version');
            data.os = getElementValue('etcd-os');
            data.data_dir = getElementValue('etcd-data-dir');
            data.backup_dir = getElementValue('etcd-backup-dir');
            data.api_server_manifest = getElementValue('etcd-api-server-manifest');
            break;
        case 'logging':
            data.level = getElementValue('logging-level');
            data.format = getElementValue('logging-format');
            data.file = getElementValue('logging-file');
            data.color = getCheckboxValue('logging-color');
            break;
        case 'cleanup':
            data.default_older_than = getElementValue('cleanup-default-older-than');
            data.concurrent_workers = parseInt(getElementValue('cleanup-concurrent-workers')) || 5;
            data.default_dry_run = getCheckboxValue('cleanup-default-dry-run');
            data.auto_cleanup_enabled = getCheckboxValue('cleanup-auto-enabled');
            data.auto_cleanup_interval = getElementValue('cleanup-auto-interval');
            break;
        case 'monitoring':
            data.port = parseInt(getElementValue('monitoring-port')) || 9090;
            data.metrics_interval = getElementValue('monitoring-metrics-interval');
            data.notification_url = getElementValue('monitoring-notification-url');
            data.enabled = getCheckboxValue('monitoring-enabled');
            data.alerts_enabled = getCheckboxValue('monitoring-alerts-enabled');
            break;
        case 'web':
            data.host = getElementValue('web-host');
            data.port = parseInt(getElementValue('web-port')) || 8080;
            data.cert_file = getElementValue('web-cert-file');
            data.key_file = getElementValue('web-key-file');
            data.rate_limit_rps = parseInt(getElementValue('web-rate-limit-rps')) || 100;
            data.tls_enabled = getCheckboxValue('web-tls-enabled');
            data.cors_enabled = getCheckboxValue('web-cors-enabled');
            break;
        case 'custom':
            try {
                const customJson = getElementValue('custom-config-json');
                data = customJson ? JSON.parse(customJson) : {};
            } catch (e) {
                throw new Error('自定义配置JSON格式错误: ' + e.message);
            }
            break;
    }

    return data;
}

function restoreConfiguration(backupId) {
    if (!confirm('确定要恢复到此配置版本吗？当前配置将被覆盖。')) {
        return;
    }

    fetch(`/api/v1/config/restore/${backupId}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('恢复配置失败: ' + data.error);
        } else {
            alert('配置恢复成功');
            loadConfigurationData(); // 重新加载配置数据
            loadConfigurationHistory(); // 重新加载历史记录
        }
    })
    .catch(error => {
        alert('恢复配置失败: ' + error.message);
    });
}

// 辅助函数
function updateCheckbox(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.checked = !!value;
    }
}

function getElementValue(id) {
    const element = document.getElementById(id);
    return element ? element.value : '';
}

function getCheckboxValue(id) {
    const element = document.getElementById(id);
    return element ? element.checked : false;
}

function getOperationIcon(operation) {
    switch (operation) {
        case 'backup': return '💾';
        case 'update': return '✏️';
        case 'restore': return '🔄';
        default: return '📝';
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDateTime(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 性能优化相关函数
function detectPerformanceMode() {
    // 检测设备性能和网络状况
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    const isSlowConnection = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');
    const isLowEndDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2;

    if (isSlowConnection || isLowEndDevice) {
        window.K8sHelper.performanceMode = true;
        console.log('启用性能模式');
    }
}

// 设置智能刷新
function setupIntelligentRefresh() {
    const refreshInterval = window.K8sHelper.performanceMode ? 60000 : 30000; // 性能模式下降低刷新频率

    window.K8sHelper.refreshIntervals.main = setInterval(() => {
        if (document.visibilityState === 'visible' && window.K8sHelper.currentModule) {
            loadModuleData(window.K8sHelper.currentModule);
        }
    }, refreshInterval);
}

// 页面可见性变化处理
function setupVisibilityChangeHandler() {
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'hidden') {
            // 页面隐藏时暂停WebSocket连接和刷新
            pauseRealTimeUpdates();
        } else {
            // 页面显示时恢复WebSocket连接和刷新
            resumeRealTimeUpdates();
        }
    });
}

// 网络状态处理
function setupNetworkStatusHandler() {
    window.addEventListener('online', function() {
        console.log('网络连接已恢复');
        resumeRealTimeUpdates();
        showNotification('网络状态', '网络连接已恢复');
    });

    window.addEventListener('offline', function() {
        console.log('网络连接已断开');
        pauseRealTimeUpdates();
        showNotification('网络状态', '网络连接已断开');
    });
}

// 暂停实时更新
function pauseRealTimeUpdates() {
    // 暂停所有WebSocket连接
    if (window.K8sHelper.wsConnections) {
        Object.keys(window.K8sHelper.wsConnections).forEach(module => {
            const ws = window.K8sHelper.wsConnections[module];
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        });
    }

    // 暂停定时刷新
    if (window.K8sHelper.refreshIntervals.main) {
        clearInterval(window.K8sHelper.refreshIntervals.main);
    }
}

// 恢复实时更新
function resumeRealTimeUpdates() {
    // 重新连接当前模块的WebSocket
    if (window.K8sHelper.currentModule) {
        connectModuleWebSocketIfNeeded(window.K8sHelper.currentModule);
    }

    // 恢复定时刷新
    setupIntelligentRefresh();

    // 立即刷新当前模块数据
    if (window.K8sHelper.currentModule) {
        loadModuleData(window.K8sHelper.currentModule);
    }
}

// 性能优化的防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 性能优化的节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 用户体验增强功能
class UIEnhancer {
    constructor() {
        this.toastContainer = null;
        this.loadingOverlay = null;
        this.confirmDialog = null;
        this.init();
    }

    init() {
        this.createToastContainer();
        this.createLoadingOverlay();
        this.createConfirmDialog();
        this.initThemeToggle();
        this.initMobileMenu();
        this.initAccessibility();
        this.initErrorHandling();
    }

    // Toast通知系统
    createToastContainer() {
        this.toastContainer = document.createElement('div');
        this.toastContainer.className = 'toast-container';
        document.body.appendChild(this.toastContainer);
    }

    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const closeBtn = document.createElement('button');
        closeBtn.className = 'toast-close';
        closeBtn.innerHTML = '×';
        closeBtn.onclick = () => this.hideToast(toast);

        const progress = document.createElement('div');
        progress.className = 'toast-progress';

        toast.innerHTML = message;
        toast.appendChild(closeBtn);
        toast.appendChild(progress);

        this.toastContainer.appendChild(toast);

        // 显示动画
        setTimeout(() => toast.classList.add('show'), 10);

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => this.hideToast(toast), duration);
        }

        return toast;
    }

    hideToast(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    // 加载覆盖层
    createLoadingOverlay() {
        this.loadingOverlay = document.createElement('div');
        this.loadingOverlay.className = 'loading-overlay';
        this.loadingOverlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            </div>
        `;
        document.body.appendChild(this.loadingOverlay);
    }

    showLoading(text = '加载中...') {
        const loadingText = this.loadingOverlay.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = text;
        }
        this.loadingOverlay.classList.add('show');
    }

    hideLoading() {
        this.loadingOverlay.classList.remove('show');
    }

    // 确认对话框
    createConfirmDialog() {
        this.confirmDialog = document.createElement('div');
        this.confirmDialog.className = 'confirm-dialog';
        this.confirmDialog.innerHTML = `
            <div class="confirm-content">
                <div class="confirm-icon">⚠️</div>
                <div class="confirm-title">确认操作</div>
                <div class="confirm-message">您确定要执行此操作吗？</div>
                <div class="confirm-actions">
                    <button class="btn btn-secondary" id="confirm-cancel">取消</button>
                    <button class="btn btn-danger" id="confirm-ok">确认</button>
                </div>
            </div>
        `;
        document.body.appendChild(this.confirmDialog);

        // 绑定事件
        this.confirmDialog.addEventListener('click', (e) => {
            if (e.target === this.confirmDialog) {
                this.hideConfirm();
            }
        });
    }

    showConfirm(options = {}) {
        const {
            title = '确认操作',
            message = '您确定要执行此操作吗？',
            icon = '⚠️',
            confirmText = '确认',
            cancelText = '取消',
            onConfirm = () => {},
            onCancel = () => {}
        } = options;

        const titleEl = this.confirmDialog.querySelector('.confirm-title');
        const messageEl = this.confirmDialog.querySelector('.confirm-message');
        const iconEl = this.confirmDialog.querySelector('.confirm-icon');
        const confirmBtn = this.confirmDialog.querySelector('#confirm-ok');
        const cancelBtn = this.confirmDialog.querySelector('#confirm-cancel');

        titleEl.textContent = title;
        messageEl.textContent = message;
        iconEl.textContent = icon;
        confirmBtn.textContent = confirmText;
        cancelBtn.textContent = cancelText;

        // 重新绑定事件
        confirmBtn.onclick = () => {
            this.hideConfirm();
            onConfirm();
        };
        cancelBtn.onclick = () => {
            this.hideConfirm();
            onCancel();
        };

        this.confirmDialog.classList.add('show');
    }

    hideConfirm() {
        this.confirmDialog.classList.remove('show');
    }

    // 主题切换
    initThemeToggle() {
        const themeToggle = document.createElement('button');
        themeToggle.className = 'theme-toggle';
        themeToggle.innerHTML = '🌙';
        themeToggle.title = '切换主题';
        themeToggle.setAttribute('aria-label', '切换深色/浅色主题');

        const currentTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', currentTheme);
        this.updateThemeIcon(themeToggle, currentTheme);

        themeToggle.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            this.updateThemeIcon(themeToggle, newTheme);

            this.showToast(`已切换到${newTheme === 'dark' ? '深色' : '浅色'}主题`, 'info', 2000);
        });

        document.body.appendChild(themeToggle);
    }

    updateThemeIcon(button, theme) {
        button.innerHTML = theme === 'dark' ? '☀️' : '🌙';
    }

    // 移动端菜单
    initMobileMenu() {
        const mobileToggle = document.createElement('button');
        mobileToggle.className = 'mobile-menu-toggle';
        mobileToggle.innerHTML = '☰';
        mobileToggle.style.display = 'none';
        mobileToggle.setAttribute('aria-label', '打开菜单');

        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';

        const sidebar = document.querySelector('.sidebar');

        if (sidebar) {
            mobileToggle.addEventListener('click', () => {
                sidebar.classList.add('open');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            });

            overlay.addEventListener('click', () => {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            });

            document.body.appendChild(mobileToggle);
            document.body.appendChild(overlay);

            // 响应式显示/隐藏
            const checkMobile = () => {
                if (window.innerWidth <= 992) {
                    mobileToggle.style.display = 'block';
                } else {
                    mobileToggle.style.display = 'none';
                    sidebar.classList.remove('open');
                    overlay.classList.remove('show');
                    document.body.style.overflow = '';
                }
            };

            window.addEventListener('resize', checkMobile);
            checkMobile();
        }
    }

    // 无障碍功能
    initAccessibility() {
        // 键盘导航支持
        document.addEventListener('keydown', (e) => {
            // ESC键关闭模态框
            if (e.key === 'Escape') {
                if (this.confirmDialog.classList.contains('show')) {
                    this.hideConfirm();
                }
                if (this.loadingOverlay.classList.contains('show')) {
                    this.hideLoading();
                }
            }
        });

        // 焦点管理
        this.initFocusManagement();

        // 高对比度检测
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            document.body.classList.add('high-contrast');
        }

        // 减少动画检测
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.body.classList.add('reduced-motion');
        }
    }

    initFocusManagement() {
        // 为所有交互元素添加焦点指示
        const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';

        document.addEventListener('focusin', (e) => {
            if (e.target.matches(focusableElements)) {
                e.target.classList.add('focus-visible');
            }
        });

        document.addEventListener('focusout', (e) => {
            e.target.classList.remove('focus-visible');
        });
    }

    // 错误处理
    initErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', (e) => {
            console.error('全局错误:', e.error);
            this.showToast('发生了一个错误，请刷新页面重试', 'error', 5000);
        });

        // Promise错误处理
        window.addEventListener('unhandledrejection', (e) => {
            console.error('未处理的Promise错误:', e.reason);
            this.showToast('网络请求失败，请检查网络连接', 'error', 5000);
        });

        // 网络状态监听
        window.addEventListener('online', () => {
            this.showToast('网络连接已恢复', 'success', 3000);
        });

        window.addEventListener('offline', () => {
            this.showToast('网络连接已断开', 'warning', 5000);
        });
    }

    // 骨架屏
    showSkeleton(container, type = 'card') {
        if (typeof container === 'string') {
            container = document.getElementById(container);
        }

        if (!container) return;

        const skeletonHTML = this.generateSkeletonHTML(type);
        container.innerHTML = skeletonHTML;
    }

    generateSkeletonHTML(type) {
        switch (type) {
            case 'card':
                return `
                    <div class="skeleton-card">
                        <div class="loading-skeleton skeleton-title"></div>
                        <div class="loading-skeleton skeleton-text"></div>
                        <div class="loading-skeleton skeleton-text short"></div>
                        <div class="loading-skeleton skeleton-button"></div>
                    </div>
                `;
            case 'list':
                return Array(5).fill().map(() => `
                    <div class="skeleton-card">
                        <div class="loading-skeleton skeleton-text"></div>
                        <div class="loading-skeleton skeleton-text medium"></div>
                    </div>
                `).join('');
            case 'table':
                return `
                    <div class="skeleton-card">
                        <div class="loading-skeleton skeleton-title"></div>
                        ${Array(3).fill().map(() => `
                            <div class="loading-skeleton skeleton-text" style="margin: 8px 0;"></div>
                        `).join('')}
                    </div>
                `;
            default:
                return '<div class="loading-skeleton skeleton-text"></div>';
        }
    }

    // 进度条
    showProgress(container, progress = 0) {
        if (typeof container === 'string') {
            container = document.getElementById(container);
        }

        if (!container) return;

        let progressBar = container.querySelector('.progress-bar');
        if (!progressBar) {
            progressBar = document.createElement('div');
            progressBar.className = 'progress-bar';
            progressBar.innerHTML = '<div class="progress-fill"></div>';
            container.appendChild(progressBar);
        }

        const fill = progressBar.querySelector('.progress-fill');
        if (progress >= 0 && progress <= 100) {
            fill.style.width = progress + '%';
            fill.classList.remove('progress-indeterminate');
        } else {
            fill.classList.add('progress-indeterminate');
        }
    }

    hideProgress(container) {
        if (typeof container === 'string') {
            container = document.getElementById(container);
        }

        if (!container) return;

        const progressBar = container.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.remove();
        }
    }

    // 空状态显示
    showEmptyState(container, options = {}) {
        const {
            icon = '📭',
            title = '暂无数据',
            description = '当前没有可显示的内容',
            actionText = '刷新',
            onAction = () => window.location.reload()
        } = options;

        if (typeof container === 'string') {
            container = document.getElementById(container);
        }

        if (!container) return;

        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">${icon}</div>
                <div class="empty-title">${title}</div>
                <div class="empty-description">${description}</div>
                <div class="empty-actions">
                    <button class="btn btn-primary" onclick="(${onAction.toString()})()">${actionText}</button>
                </div>
            </div>
        `;
    }

    // 错误状态显示
    showErrorState(container, options = {}) {
        const {
            icon = '❌',
            title = '加载失败',
            description = '数据加载时发生错误，请稍后重试',
            actionText = '重试',
            onAction = () => window.location.reload()
        } = options;

        if (typeof container === 'string') {
            container = document.getElementById(container);
        }

        if (!container) return;

        container.innerHTML = `
            <div class="error-state">
                <div class="error-icon">${icon}</div>
                <div class="error-message">${title}</div>
                <div class="error-details">${description}</div>
                <div class="error-actions">
                    <button class="btn btn-primary" onclick="(${onAction.toString()})()">${actionText}</button>
                </div>
            </div>
        `;
    }
}

// 初始化UI增强器
let uiEnhancer;

// UI增强器初始化已合并到主initializeApp函数

// 键盘快捷键系统
class KeyboardShortcuts {
    constructor() {
        this.shortcuts = {
            'ctrl+1': () => this.switchModule('dashboard'),
            'ctrl+2': () => this.switchModule('cluster-info'),
            'ctrl+3': () => this.switchModule('etcd'),
            'ctrl+4': () => this.switchModule('logs'),
            'ctrl+5': () => this.switchModule('port-forward'),
            'ctrl+6': () => this.switchModule('cleanup'),
            'ctrl+7': () => this.switchModule('monitoring'),
            'ctrl+8': () => this.switchModule('config'),
            'ctrl+r': (e) => { e.preventDefault(); this.refreshCurrentModule(); },
            'ctrl+h': (e) => { e.preventDefault(); this.showHelp(); },
            'ctrl+k': (e) => { e.preventDefault(); this.showCommandPalette(); },
            'f1': (e) => { e.preventDefault(); this.showHelp(); },
            'f5': (e) => { e.preventDefault(); this.refreshCurrentModule(); },
            '?': () => this.showKeyboardShortcuts()
        };

        this.commandPalette = null;
        this.helpModal = null;
        this.init();
    }

    init() {
        document.addEventListener('keydown', (e) => {
            const key = this.getKeyCombo(e);
            if (this.shortcuts[key]) {
                const handler = this.shortcuts[key];
                handler(e);
            }
        });

        this.createCommandPalette();
        this.createHelpModal();
    }

    getKeyCombo(e) {
        const parts = [];

        if (e.ctrlKey) parts.push('ctrl');
        if (e.altKey) parts.push('alt');
        if (e.shiftKey) parts.push('shift');
        if (e.metaKey) parts.push('meta');

        const key = e.key.toLowerCase();
        if (key !== 'control' && key !== 'alt' && key !== 'shift' && key !== 'meta') {
            parts.push(key);
        }

        return parts.join('+');
    }

    switchModule(moduleId) {
        if (typeof showModule === 'function') {
            showModule(moduleId);
            if (window.K8sHelper && window.K8sHelper.ui) {
                window.K8sHelper.ui.showToast(`切换到${this.getModuleName(moduleId)}`, 'info', 2000);
            }
        }
    }

    getModuleName(moduleId) {
        const names = {
            'dashboard': '仪表板',
            'cluster-info': '集群信息',
            'etcd': 'ETCD管理',
            'logs': '日志查看',
            'port-forward': '端口转发',
            'cleanup': '资源清理',
            'monitoring': '监控',
            'config': '配置管理'
        };
        return names[moduleId] || moduleId;
    }

    refreshCurrentModule() {
        if (window.K8sHelper && window.K8sHelper.currentModule) {
            if (typeof loadModuleData === 'function') {
                loadModuleData(window.K8sHelper.currentModule);
                if (window.K8sHelper.ui) {
                    window.K8sHelper.ui.showToast('已刷新当前模块', 'success', 2000);
                }
            }
        }
    }

    showHelp() {
        this.helpModal.classList.add('show');
    }

    hideHelp() {
        this.helpModal.classList.remove('show');
    }

    showCommandPalette() {
        this.commandPalette.classList.add('show');
        const input = this.commandPalette.querySelector('.command-input');
        if (input) {
            input.focus();
            input.value = '';
            this.updateCommandResults('');
        }
    }

    hideCommandPalette() {
        this.commandPalette.classList.remove('show');
    }

    createCommandPalette() {
        this.commandPalette = document.createElement('div');
        this.commandPalette.className = 'command-palette';
        this.commandPalette.innerHTML = `
            <div class="command-content">
                <div class="command-header">
                    <input type="text" class="command-input" placeholder="输入命令或搜索功能...">
                    <button class="command-close">×</button>
                </div>
                <div class="command-results"></div>
            </div>
        `;

        // 样式
        const style = document.createElement('style');
        style.textContent = `
            .command-palette {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: flex-start;
                justify-content: center;
                z-index: 10000;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.3s ease, visibility 0.3s ease;
                padding-top: 10vh;
            }

            .command-palette.show {
                opacity: 1;
                visibility: visible;
            }

            .command-content {
                background: var(--bg-primary);
                border-radius: 8px;
                width: 90%;
                max-width: 600px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.2);
                transform: scale(0.9);
                transition: transform 0.3s ease;
            }

            .command-palette.show .command-content {
                transform: scale(1);
            }

            .command-header {
                display: flex;
                align-items: center;
                padding: 15px;
                border-bottom: 1px solid var(--border-color);
            }

            .command-input {
                flex: 1;
                border: none;
                outline: none;
                font-size: 16px;
                background: transparent;
                color: var(--text-primary);
            }

            .command-close {
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                color: var(--text-secondary);
                padding: 5px;
            }

            .command-results {
                max-height: 400px;
                overflow-y: auto;
            }

            .command-item {
                padding: 12px 15px;
                cursor: pointer;
                border-bottom: 1px solid var(--border-color);
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .command-item:hover,
            .command-item.selected {
                background: var(--bg-secondary);
            }

            .command-icon {
                font-size: 16px;
                width: 20px;
                text-align: center;
            }

            .command-info {
                flex: 1;
            }

            .command-name {
                font-weight: 500;
                color: var(--text-primary);
            }

            .command-desc {
                font-size: 12px;
                color: var(--text-secondary);
            }

            .command-shortcut {
                font-size: 11px;
                background: var(--bg-tertiary);
                padding: 2px 6px;
                border-radius: 3px;
                color: var(--text-secondary);
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(this.commandPalette);

        // 事件绑定
        const input = this.commandPalette.querySelector('.command-input');
        const closeBtn = this.commandPalette.querySelector('.command-close');

        input.addEventListener('input', (e) => {
            this.updateCommandResults(e.target.value);
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideCommandPalette();
            } else if (e.key === 'Enter') {
                this.executeSelectedCommand();
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.selectNextCommand();
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.selectPrevCommand();
            }
        });

        closeBtn.addEventListener('click', () => {
            this.hideCommandPalette();
        });

        this.commandPalette.addEventListener('click', (e) => {
            if (e.target === this.commandPalette) {
                this.hideCommandPalette();
            }
        });
    }

    updateCommandResults(query) {
        const commands = [
            { name: '仪表板', desc: '查看系统概览', icon: '📊', action: () => this.switchModule('dashboard'), shortcut: 'Ctrl+1' },
            { name: '集群信息', desc: '查看集群详细信息', icon: '🏗️', action: () => this.switchModule('cluster-info'), shortcut: 'Ctrl+2' },
            { name: 'ETCD管理', desc: '管理ETCD备份和恢复', icon: '💾', action: () => this.switchModule('etcd'), shortcut: 'Ctrl+3' },
            { name: '日志查看', desc: '查看Pod日志', icon: '📝', action: () => this.switchModule('logs'), shortcut: 'Ctrl+4' },
            { name: '端口转发', desc: '管理端口转发', icon: '🔗', action: () => this.switchModule('port-forward'), shortcut: 'Ctrl+5' },
            { name: '资源清理', desc: '清理集群资源', icon: '🧹', action: () => this.switchModule('cleanup'), shortcut: 'Ctrl+6' },
            { name: '监控', desc: '查看系统监控', icon: '📈', action: () => this.switchModule('monitoring'), shortcut: 'Ctrl+7' },
            { name: '配置管理', desc: '管理系统配置', icon: '⚙️', action: () => this.switchModule('config'), shortcut: 'Ctrl+8' },
            { name: '刷新', desc: '刷新当前模块', icon: '🔄', action: () => this.refreshCurrentModule(), shortcut: 'Ctrl+R' },
            { name: '帮助', desc: '显示帮助信息', icon: '❓', action: () => this.showHelp(), shortcut: 'F1' },
            { name: '切换主题', desc: '切换深色/浅色主题', icon: '🌙', action: () => this.toggleTheme(), shortcut: '' }
        ];

        const filtered = commands.filter(cmd =>
            cmd.name.toLowerCase().includes(query.toLowerCase()) ||
            cmd.desc.toLowerCase().includes(query.toLowerCase())
        );

        const resultsContainer = this.commandPalette.querySelector('.command-results');
        resultsContainer.innerHTML = filtered.map((cmd, index) => `
            <div class="command-item ${index === 0 ? 'selected' : ''}" data-index="${index}">
                <span class="command-icon">${cmd.icon}</span>
                <div class="command-info">
                    <div class="command-name">${cmd.name}</div>
                    <div class="command-desc">${cmd.desc}</div>
                </div>
                ${cmd.shortcut ? `<span class="command-shortcut">${cmd.shortcut}</span>` : ''}
            </div>
        `).join('');

        // 绑定点击事件
        resultsContainer.querySelectorAll('.command-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                filtered[index].action();
                this.hideCommandPalette();
            });
        });

        this.currentCommands = filtered;
        this.selectedIndex = 0;
    }

    selectNextCommand() {
        const items = this.commandPalette.querySelectorAll('.command-item');
        if (items.length === 0) return;

        items[this.selectedIndex].classList.remove('selected');
        this.selectedIndex = (this.selectedIndex + 1) % items.length;
        items[this.selectedIndex].classList.add('selected');
        items[this.selectedIndex].scrollIntoView({ block: 'nearest' });
    }

    selectPrevCommand() {
        const items = this.commandPalette.querySelectorAll('.command-item');
        if (items.length === 0) return;

        items[this.selectedIndex].classList.remove('selected');
        this.selectedIndex = this.selectedIndex === 0 ? items.length - 1 : this.selectedIndex - 1;
        items[this.selectedIndex].classList.add('selected');
        items[this.selectedIndex].scrollIntoView({ block: 'nearest' });
    }

    executeSelectedCommand() {
        if (this.currentCommands && this.currentCommands[this.selectedIndex]) {
            this.currentCommands[this.selectedIndex].action();
            this.hideCommandPalette();
        }
    }

    toggleTheme() {
        const themeToggle = document.querySelector('.theme-toggle');
        if (themeToggle) {
            themeToggle.click();
        }
    }

    createHelpModal() {
        this.helpModal = document.createElement('div');
        this.helpModal.className = 'confirm-dialog';
        this.helpModal.innerHTML = `
            <div class="confirm-content" style="max-width: 600px;">
                <div class="confirm-icon">❓</div>
                <div class="confirm-title">键盘快捷键</div>
                <div class="confirm-message" style="text-align: left;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0;">
                        <div>
                            <h4>模块切换</h4>
                            <div class="shortcut-list">
                                <div><kbd>Ctrl+1</kbd> 仪表板</div>
                                <div><kbd>Ctrl+2</kbd> 集群信息</div>
                                <div><kbd>Ctrl+3</kbd> ETCD管理</div>
                                <div><kbd>Ctrl+4</kbd> 日志查看</div>
                                <div><kbd>Ctrl+5</kbd> 端口转发</div>
                                <div><kbd>Ctrl+6</kbd> 资源清理</div>
                                <div><kbd>Ctrl+7</kbd> 监控</div>
                                <div><kbd>Ctrl+8</kbd> 配置管理</div>
                            </div>
                        </div>
                        <div>
                            <h4>常用操作</h4>
                            <div class="shortcut-list">
                                <div><kbd>Ctrl+R</kbd> 刷新当前模块</div>
                                <div><kbd>Ctrl+K</kbd> 命令面板</div>
                                <div><kbd>Ctrl+H</kbd> 显示帮助</div>
                                <div><kbd>F1</kbd> 显示帮助</div>
                                <div><kbd>F5</kbd> 刷新页面</div>
                                <div><kbd>?</kbd> 快捷键列表</div>
                                <div><kbd>Esc</kbd> 关闭对话框</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="confirm-actions">
                    <button class="btn btn-primary" onclick="keyboardShortcuts.hideHelp()">关闭</button>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .shortcut-list {
                font-size: 14px;
                line-height: 1.6;
            }

            .shortcut-list div {
                display: flex;
                justify-content: space-between;
                margin-bottom: 5px;
            }

            kbd {
                background: var(--bg-tertiary);
                border: 1px solid var(--border-color);
                border-radius: 3px;
                padding: 2px 6px;
                font-size: 11px;
                font-family: monospace;
                color: var(--text-primary);
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(this.helpModal);

        this.helpModal.addEventListener('click', (e) => {
            if (e.target === this.helpModal) {
                this.hideHelp();
            }
        });
    }

    showKeyboardShortcuts() {
        this.showHelp();
    }
}

// 初始化键盘快捷键
let keyboardShortcuts;

// 键盘快捷键初始化已合并到主initializeApp函数

// Tab Management (Legacy Support)
function initializeTabs() {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            switchTab(targetTab);
        });
    });
}

function switchTab(tabName) {
    // Update active tab
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');
    
    // Update active content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');
    
    // Update global state
    window.K8sHelper.currentTab = tabName;
    
    // Load tab-specific data
    loadTabData(tabName);
}

// Load tab-specific data
function loadTabData(tabName) {
    switch(tabName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'etcd':
            loadETCDData();
            break;
        case 'info':
            // Cluster info is loaded on demand
            break;
        case 'logs':
            // Logs are loaded on demand
            break;
    }
}

// Dashboard Functions
function loadDashboardData() {
    // 显示骨架屏
    const dashboardContainer = document.getElementById('module-dashboard');
    if (dashboardContainer && window.K8sHelper && window.K8sHelper.ui) {
        window.K8sHelper.ui.showSkeleton(dashboardContainer, 'card');
    }

    // Load health status
    checkHealth();

    // Load system metrics if available
    loadSystemMetrics();

    // Load backup list
    loadBackupList();
}

// loadSystemMetrics函数已在下方定义，使用apiBase

// loadBackupList函数已在下方定义，使用apiBase

function checkHealth() {
    const resultDiv = document.getElementById('health-result');
    if (!resultDiv) return;
    
    showLoading(resultDiv, 'Checking health...');
    
    fetch('/health')
        .then(response => response.json())
        .then(data => {
            showResult(resultDiv, `Status: ${data.status} at ${data.timestamp}`, 'success');
        })
        .catch(error => {
            showResult(resultDiv, `Error: ${error.message}`, 'error');
        });
}

function loadSystemMetrics() {
    fetch(`${window.K8sHelper.apiBase}/monitoring/system`)
        .then(response => response.json())
        .then(data => {
            if (data.message !== '功能待实现') {
                updateSystemMetricsDisplay(data);
            }
        })
        .catch(error => {
            console.warn('System metrics not available:', error);
        });
}

// ETCD Functions
function loadETCDData() {
    // Load backup list
    loadBackupList();
}

function performBackup() {
    const resultDiv = document.getElementById('backup-result');
    const outputPath = document.getElementById('backup-output').value;
    
    showLoading(resultDiv, 'Starting backup...');
    
    const requestBody = {};
    if (outputPath) {
        requestBody.output_path = outputPath;
    }
    
    fetch(`${window.K8sHelper.apiBase}/etcd/backup`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showResult(resultDiv, `Error: ${data.error}`, 'error');
        } else {
            showResult(resultDiv, `Backup completed successfully! Path: ${data.path}, Size: ${data.size} bytes`, 'success');
            // Refresh backup list
            loadBackupList();
        }
    })
    .catch(error => {
        showResult(resultDiv, `Error: ${error.message}`, 'error');
    });
}

function performVerify() {
    const resultDiv = document.getElementById('verify-result');
    const snapshotPath = document.getElementById('verify-snapshot').value;
    
    if (!snapshotPath) {
        showResult(resultDiv, 'Please specify snapshot path', 'error');
        return;
    }
    
    showLoading(resultDiv, 'Starting verification...');
    
    fetch(`${window.K8sHelper.apiBase}/etcd/verify`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            snapshot_path: snapshotPath
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showResult(resultDiv, `Error: ${data.error}`, 'error');
        } else {
            showResult(resultDiv, `Verification completed!\n${JSON.stringify(data, null, 2)}`, 'success');
        }
    })
    .catch(error => {
        showResult(resultDiv, `Error: ${error.message}`, 'error');
    });
}

function loadBackupList() {
    fetch(`${window.K8sHelper.apiBase}/etcd/backups`)
        .then(response => response.json())
        .then(data => {
            if (data.backups && data.backups.length > 0) {
                updateBackupListDisplay(data.backups);
            }
        })
        .catch(error => {
            console.warn('Backup list not available:', error);
        });
}

// Cluster Info Functions
function getClusterInfo() {
    const resultDiv = document.getElementById('info-result');
    showLoading(resultDiv, 'Fetching cluster information...');
    
    fetch(`${window.K8sHelper.apiBase}/cluster/info`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showResult(resultDiv, `Error: ${data.error}`, 'error');
            } else {
                showResult(resultDiv, JSON.stringify(data, null, 2), 'success');
            }
        })
        .catch(error => {
            showResult(resultDiv, `Error: ${error.message}`, 'error');
        });
}

// Utility Functions
function showLoading(element, message) {
    element.style.display = 'block';
    element.className = 'result info';
    element.innerHTML = `<span class="loading"></span>${message}`;
}

function showResult(element, message, type) {
    element.style.display = 'block';
    element.className = `result ${type}`;
    element.innerHTML = message;
}

function updateSystemMetricsDisplay(data) {
    // Update system metrics in dashboard
    const metricsContainer = document.getElementById('system-metrics');
    if (metricsContainer && data.system) {
        metricsContainer.innerHTML = `
            <h4>System Metrics</h4>
            <p>Memory: ${formatBytes(data.system.memory_usage || 0)}</p>
            <p>Goroutines: ${data.system.goroutines || 0}</p>
            <p>Last Updated: ${new Date().toLocaleTimeString()}</p>
        `;
    }
}

function updateBackupListDisplay(backups) {
    const listContainer = document.getElementById('backup-list');
    if (listContainer) {
        listContainer.innerHTML = `
            <h4>Recent Backups</h4>
            <ul>
                ${backups.map(backup => `<li>${backup}</li>`).join('')}
            </ul>
        `;
    }
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// WebSocket Functions
function initializeWebSocket() {
    // 初始化状态WebSocket连接
    const statusWsUrl = 'ws://' + window.location.host + '/api/v1/ws/status';
    const statusWs = new WebSocket(statusWsUrl);

    statusWs.onopen = function() {
        updateConnectionStatus('ws-status', '已连接', 'healthy');
        console.log('状态WebSocket连接已建立');
    };

    statusWs.onmessage = function(event) {
        const message = JSON.parse(event.data);
        if (message.type === 'status') {
            updateSystemStatus(message.data);
        }
    };

    statusWs.onclose = function() {
        updateConnectionStatus('ws-status', '已断开', 'unknown');
        console.log('状态WebSocket连接已断开');
    };

    statusWs.onerror = function(error) {
        updateConnectionStatus('ws-status', '连接错误', 'unhealthy');
        console.error('状态WebSocket错误:', error);
    };

    // 保存WebSocket连接引用
    window.K8sHelper.statusWs = statusWs;
}

// 连接到指定模块的WebSocket
function connectModuleWebSocket(module) {
    if (window.K8sHelper.wsConnections && window.K8sHelper.wsConnections[module]) {
        // 如果已经连接，先关闭
        window.K8sHelper.wsConnections[module].close();
    }

    const wsUrl = 'ws://' + window.location.host + '/api/v1/ws/' + module;
    const ws = new WebSocket(wsUrl);

    ws.onopen = function() {
        console.log(`${module} WebSocket连接已建立`);
        if (!window.K8sHelper.wsConnections) {
            window.K8sHelper.wsConnections = {};
        }
        window.K8sHelper.wsConnections[module] = ws;
    };

    ws.onmessage = function(event) {
        const message = JSON.parse(event.data);
        handleModuleWebSocketMessage(module, message);
    };

    ws.onclose = function() {
        console.log(`${module} WebSocket连接已断开`);
        if (window.K8sHelper.wsConnections) {
            delete window.K8sHelper.wsConnections[module];
        }
    };

    ws.onerror = function(error) {
        console.error(`${module} WebSocket错误:`, error);
    };

    return ws;
}

// 处理模块WebSocket消息
function handleModuleWebSocketMessage(module, message) {
    switch (module) {
        case 'monitoring':
            handleMonitoringWebSocketMessage(message);
            break;
        case 'cleanup':
            handleCleanupWebSocketMessage(message);
            break;
        case 'config':
            handleConfigWebSocketMessage(message);
            break;
        case 'port-forward':
            handlePortForwardWebSocketMessage(message);
            break;
        case 'logs':
            handleLogsWebSocketMessage(message);
            break;
        default:
            console.log(`收到${module}模块消息:`, message);
    }
}

// 监控模块WebSocket消息处理
function handleMonitoringWebSocketMessage(message) {
    if (message.type === 'monitoring_update') {
        // 刷新监控数据
        if (window.K8sHelper.currentModule === 'monitoring') {
            loadMetricsData();
        }
    }
}

// 清理模块WebSocket消息处理
function handleCleanupWebSocketMessage(message) {
    if (message.type === 'cleanup_update') {
        // 刷新清理状态
        if (window.K8sHelper.currentModule === 'cleanup') {
            // 可以在这里更新清理进度或状态
            console.log('清理状态更新:', message.data);
        }
    }
}

// 配置模块WebSocket消息处理
function handleConfigWebSocketMessage(message) {
    if (message.type === 'config_update') {
        // 配置更新通知
        if (window.K8sHelper.currentModule === 'config') {
            loadConfigurationData();
            showNotification('配置更新', '配置文件已更新，请刷新查看最新配置');
        }
    }
}

// 端口转发模块WebSocket消息处理
function handlePortForwardWebSocketMessage(message) {
    if (message.type === 'port_forward_update') {
        // 端口转发状态更新
        if (window.K8sHelper.currentModule === 'port-forward') {
            loadPortForwardsList();
        }
    }
}

// 日志模块WebSocket消息处理
function handleLogsWebSocketMessage(message) {
    if (message.type === 'log_update') {
        // 实时日志更新
        appendLogMessage(message.data);
    }
}

// 断开指定模块的WebSocket连接
function disconnectModuleWebSocket(module) {
    if (window.K8sHelper.wsConnections && window.K8sHelper.wsConnections[module]) {
        window.K8sHelper.wsConnections[module].close();
        delete window.K8sHelper.wsConnections[module];
        console.log(`${module} WebSocket连接已断开`);
    }
}

// Additional utility functions
function updateBackupListDisplay(backups) {
    // Update dashboard backup list
    const dashboardContainer = document.getElementById('backup-list');
    if (dashboardContainer) {
        if (backups.length === 0) {
            dashboardContainer.innerHTML = '暂无备份文件';
        } else {
            dashboardContainer.innerHTML = backups.length + ' 个备份文件';
        }
    }

    // Update ETCD page backup files list
    const etcdContainer = document.getElementById('backup-files-list');
    if (etcdContainer) {
        if (backups.length === 0) {
            etcdContainer.innerHTML = '<p class="text-center">暂无备份文件</p>';
            return;
        }

        const html = backups.map(backup =>
            '<div class="backup-item">' +
                '<div class="backup-info">' +
                    '<h5>' + (backup.name || 'Unknown') + '</h5>' +
                    '<p>大小: ' + formatBytes(backup.size || 0) + ' | 创建时间: ' + new Date(backup.created_at || Date.now()).toLocaleString() + '</p>' +
                    '<p>状态: <span class="status ' + (backup.status || 'unknown') + '">' + (backup.status || 'unknown') + '</span></p>' +
                '</div>' +
                '<div class="backup-actions">' +
                    '<button class="btn btn-primary btn-sm" onclick="verifyBackup(\'' + (backup.path || '') + '\')">验证</button>' +
                    '<button class="btn btn-warning btn-sm" onclick="useForRestore(\'' + (backup.path || '') + '\')">用于恢复</button>' +
                '</div>' +
            '</div>'
        ).join('');

        etcdContainer.innerHTML = html;
    }
}

function verifyBackup(path) {
    if (!path) {
        alert('备份路径无效');
        return;
    }
    document.getElementById('verify-snapshot').value = path;
    switchTab('etcd');
    setTimeout(() => {
        const verifyCard = document.querySelector('#etcd .card:last-child');
        if (verifyCard) {
            verifyCard.scrollIntoView({ behavior: 'smooth' });
        }
    }, 100);
}

function useForRestore(path) {
    if (!path) {
        alert('备份路径无效');
        return;
    }
    document.getElementById('restore-snapshot').value = path;
    switchTab('etcd');
    setTimeout(() => {
        const restoreCard = document.querySelector('#etcd .card:nth-child(3)');
        if (restoreCard) {
            restoreCard.scrollIntoView({ behavior: 'smooth' });
        }
    }, 100);
}

function showLoading(element, message) {
    if (element) {
        element.style.display = 'block';
        element.className = 'result';
        element.innerHTML = '<span class="loading"></span>' + message;
    }
}

function showResult(element, message, type) {
    if (element) {
        element.style.display = 'block';
        element.className = 'result ' + type;
        element.textContent = message;
    }
}

function updateConnectionStatus(elementId, status, statusClass) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = 'WebSocket: ' + status;
        element.className = 'status ' + statusClass;
    }
}

function updateSystemStatus(statusData) {
    // 更新系统状态显示
    const healthStatus = document.getElementById('health-status');
    if (healthStatus) {
        healthStatus.textContent = '系统: ' + statusData.status;
        healthStatus.className = 'status ' + (statusData.status === 'healthy' ? 'healthy' : 'unknown');
    }

    // 更新WebSocket连接数
    const wsConnections = document.getElementById('ws-connections');
    if (wsConnections) {
        wsConnections.textContent = '当前连接: ' + (statusData.connections || 0) + ' 个';
    }
}

// Error Handling
window.addEventListener('error', function(event) {
    console.error('JavaScript Error:', event.error);
});

// 测试日志显示功能
function testLogDisplay() {
    console.log('开始测试日志显示功能');

    // 首先测试基本的DOM操作
    const logViewer = document.getElementById('log-viewer');
    const template = document.getElementById('log-line-template');

    console.log('DOM元素检查:');
    console.log('logViewer:', logViewer);
    console.log('template:', template);
    console.log('logViewer是否可见:', logViewer ? window.getComputedStyle(logViewer).display : 'N/A');

    if (!logViewer) {
        alert('错误：找不到log-viewer元素！');
        return;
    }

    if (!template) {
        alert('错误：找不到log-line-template模板！');
        return;
    }

    // 直接添加一个简单的测试元素
    console.log('直接添加测试元素...');
    logViewer.innerHTML = '<div style="color: red; padding: 10px; border: 1px solid red;">🧪 这是一个直接添加的测试元素</div>';

    setTimeout(() => {
        // 模拟日志数据
        const testLogs = [
            {
                timestamp: new Date().toISOString(),
                message: '这是一条测试信息日志',
                level: 'info',
                container: 'test-container'
            },
            {
                timestamp: new Date().toISOString(),
                message: '这是一条测试警告日志',
                level: 'warning',
                container: 'test-container'
            }
        ];

        console.log('测试数据:', testLogs);

        // 显示测试日志
        displayLogs(testLogs);

        // 更新连接状态
        updateConnectionStatus('connected', '测试连接');
    }, 2000);
}

// Export functions for global access
window.switchTab = switchTab;
window.checkHealth = checkHealth;
window.performBackup = performBackup;
window.performVerify = performVerify;
window.getClusterInfo = getClusterInfo;
window.testLogDisplay = testLogDisplay;

// 添加DOM检查函数
window.checkLogDOM = function() {
    console.log('=== DOM检查 ===');
    const logViewer = document.getElementById('log-viewer');
    const template = document.getElementById('log-line-template');
    const logSection = document.querySelector('.log-display');

    console.log('log-viewer元素:', logViewer);
    console.log('log-line-template模板:', template);
    console.log('log-display区域:', logSection);

    if (logViewer) {
        console.log('logViewer样式:', window.getComputedStyle(logViewer));
        console.log('logViewer位置:', logViewer.getBoundingClientRect());
        console.log('logViewer父元素:', logViewer.parentElement);
        console.log('logViewer当前内容:', logViewer.innerHTML);
    }

    if (template) {
        console.log('模板内容:', template.innerHTML);
        console.log('模板content:', template.content);
    }
};
