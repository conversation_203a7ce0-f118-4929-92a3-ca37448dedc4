package service

import (
	"fmt"
	"os"
	"strings"

	"go.uber.org/zap"
	"gopkg.in/yaml.v2"
	"k8s-helper/internal/domain"
)

// configParser 配置解析器实现
type configParser struct {
	logger          *zap.Logger
	commonValidator *CommonValidator
}

// NewConfigParser 创建配置解析器
func NewConfigParser(logger *zap.Logger) domain.ConfigParser {
	return &configParser{
		logger:          logger,
		commonValidator: NewCommonValidator(logger),
	}
}

// ApiServerConfig API Server配置结构
type ApiServerConfig struct {
	Spec struct {
		Containers []struct {
			Name    string   `yaml:"name"`
			Command []string `yaml:"command"`
		} `yaml:"containers"`
	} `yaml:"spec"`
}

// ParseAPIServerConfig 解析API Server配置
func (cp *configParser) ParseAPIServerConfig(manifestPath string) (*domain.ETCDConfig, error) {
	cp.logger.Info("解析API Server配置", zap.String("path", manifestPath))

	// 读取配置文件
	data, err := os.ReadFile(manifestPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析 YAML
	var config ApiServerConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析 YAML 失败: %w", err)
	}

	// 查找 kube-apiserver 容器
	for _, container := range config.Spec.Containers {
		if container.Name == "kube-apiserver" {
			etcdConfig, err := cp.extractETCDConfig(container.Command)
			if err != nil {
				return nil, err
			}

			// 验证配置
			if err := cp.ValidateConfig(etcdConfig); err != nil {
				return nil, fmt.Errorf("配置验证失败: %w", err)
			}

			cp.logger.Info("成功解析ETCD配置",
				zap.String("servers", etcdConfig.Servers),
				zap.String("caFile", etcdConfig.CaFile))

			return etcdConfig, nil
		}
	}

	return nil, fmt.Errorf("未找到 kube-apiserver 容器配置")
}

// ValidateConfig 验证配置
func (cp *configParser) ValidateConfig(config *domain.ETCDConfig) error {
	// 使用通用验证器进行验证
	result := cp.commonValidator.ValidateETCDConfig(config)

	if !result.Valid {
		// 返回第一个错误
		errors := result.GetErrors()
		if len(errors) > 0 {
			return fmt.Errorf(errors[0])
		}
		return fmt.Errorf("配置验证失败")
	}

	cp.logger.Debug("配置验证通过", zap.Any("config", config))
	return nil
}

// extractETCDConfig 从命令行参数中提取ETCD配置
func (cp *configParser) extractETCDConfig(command []string) (*domain.ETCDConfig, error) {
	config := &domain.ETCDConfig{}

	// 解析命令行参数
	for _, arg := range command {
		if strings.HasPrefix(arg, "--etcd-servers=") {
			config.Servers = strings.TrimPrefix(arg, "--etcd-servers=")
		} else if strings.HasPrefix(arg, "--etcd-cafile=") {
			config.CaFile = strings.TrimPrefix(arg, "--etcd-cafile=")
		} else if strings.HasPrefix(arg, "--etcd-certfile=") {
			config.CertFile = strings.TrimPrefix(arg, "--etcd-certfile=")
		} else if strings.HasPrefix(arg, "--etcd-keyfile=") {
			config.KeyFile = strings.TrimPrefix(arg, "--etcd-keyfile=")
		}
	}

	// 检查是否找到了必需的配置
	if config.Servers == "" {
		return nil, fmt.Errorf("未找到 --etcd-servers 参数")
	}

	if config.CaFile == "" {
		return nil, fmt.Errorf("未找到 --etcd-cafile 参数")
	}

	if config.CertFile == "" {
		return nil, fmt.Errorf("未找到 --etcd-certfile 参数")
	}

	if config.KeyFile == "" {
		return nil, fmt.Errorf("未找到 --etcd-keyfile 参数")
	}

	cp.logger.Debug("从命令行提取ETCD配置成功", zap.Any("config", config))
	return config, nil
}
