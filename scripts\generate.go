package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"time"
)

const (
	frontendDir = "frontend"
	distDir     = "frontend/dist"
)

// getProjectRoot 获取项目根目录
func getProjectRoot() (string, error) {
	// 获取当前工作目录
	wd, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// 向上查找包含go.mod的目录
	for {
		if _, err := os.Stat(filepath.Join(wd, "go.mod")); err == nil {
			return wd, nil
		}

		parent := filepath.Dir(wd)
		if parent == wd {
			// 已经到达根目录
			break
		}
		wd = parent
	}

	return "", fmt.Errorf("project root not found (no go.mod file)")
}

// BuildConfig 构建配置
type BuildConfig struct {
	FrontendDir string
	DistDir     string
	NodeCmd     string
	NpmCmd      string
}

// NewBuildConfig 创建构建配置
func NewBuildConfig() (*BuildConfig, error) {
	// 获取项目根目录
	projectRoot, err := getProjectRoot()
	if err != nil {
		return nil, fmt.Errorf("failed to find project root: %v", err)
	}

	config := &BuildConfig{
		FrontendDir: filepath.Join(projectRoot, frontendDir),
		DistDir:     filepath.Join(projectRoot, distDir),
		NodeCmd:     "node",
		NpmCmd:      "npm",
	}

	// Windows系统下使用.cmd后缀
	if runtime.GOOS == "windows" {
		config.NodeCmd = "node.exe"
		config.NpmCmd = "npm.cmd"
	}

	return config, nil
}

// checkPrerequisites 检查构建前提条件
func (c *BuildConfig) checkPrerequisites() error {
	// 检查frontend目录是否存在
	if _, err := os.Stat(c.FrontendDir); os.IsNotExist(err) {
		return fmt.Errorf("frontend directory not found: %s", c.FrontendDir)
	}

	// 检查package.json是否存在
	packageJSON := filepath.Join(c.FrontendDir, "package.json")
	if _, err := os.Stat(packageJSON); os.IsNotExist(err) {
		return fmt.Errorf("package.json not found: %s", packageJSON)
	}

	// 检查Node.js是否安装
	if err := c.checkCommand(c.NodeCmd, "--version"); err != nil {
		return fmt.Errorf("Node.js not found: %v", err)
	}

	// 检查npm是否安装
	if err := c.checkCommand(c.NpmCmd, "--version"); err != nil {
		return fmt.Errorf("npm not found: %v", err)
	}

	return nil
}

// checkCommand 检查命令是否可用
func (c *BuildConfig) checkCommand(command string, args ...string) error {
	cmd := exec.Command(command, args...)
	cmd.Stdout = nil
	cmd.Stderr = nil
	return cmd.Run()
}

// installDependencies 安装依赖
func (c *BuildConfig) installDependencies() error {
	fmt.Println("📦 Installing frontend dependencies...")
	
	cmd := exec.Command(c.NpmCmd, "ci")
	cmd.Dir = c.FrontendDir
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to install dependencies: %v", err)
	}

	fmt.Println("✅ Dependencies installed successfully")
	return nil
}

// buildFrontend 构建前端
func (c *BuildConfig) buildFrontend() error {
	fmt.Println("🔨 Building frontend application...")
	
	cmd := exec.Command(c.NpmCmd, "run", "build")
	cmd.Dir = c.FrontendDir
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to build frontend: %v", err)
	}

	fmt.Println("✅ Frontend built successfully")
	return nil
}

// verifyBuild 验证构建结果
func (c *BuildConfig) verifyBuild() error {
	fmt.Println("🔍 Verifying build output...")

	// 检查dist目录是否存在
	if _, err := os.Stat(c.DistDir); os.IsNotExist(err) {
		return fmt.Errorf("build output directory not found: %s", c.DistDir)
	}

	// 检查index.html是否存在
	indexHTML := filepath.Join(c.DistDir, "index.html")
	if _, err := os.Stat(indexHTML); os.IsNotExist(err) {
		return fmt.Errorf("index.html not found in build output: %s", indexHTML)
	}

	// 检查assets目录是否存在
	assetsDir := filepath.Join(c.DistDir, "assets")
	if _, err := os.Stat(assetsDir); os.IsNotExist(err) {
		fmt.Printf("⚠️  Warning: assets directory not found: %s\n", assetsDir)
	}

	fmt.Println("✅ Build verification completed")
	return nil
}

// printBuildInfo 打印构建信息
func (c *BuildConfig) printBuildInfo() {
	fmt.Println("🚀 Go Generate Frontend Build")
	fmt.Println("=============================")
	fmt.Printf("Frontend Directory: %s\n", c.FrontendDir)
	fmt.Printf("Output Directory: %s\n", c.DistDir)
	fmt.Printf("Node Command: %s\n", c.NodeCmd)
	fmt.Printf("NPM Command: %s\n", c.NpmCmd)
	fmt.Printf("OS: %s\n", runtime.GOOS)
	fmt.Printf("Architecture: %s\n", runtime.GOARCH)
	fmt.Println("=============================")
}

// cleanBuild 清理旧的构建产物
func (c *BuildConfig) cleanBuild() error {
	if _, err := os.Stat(c.DistDir); err == nil {
		fmt.Println("🧹 Cleaning previous build...")
		if err := os.RemoveAll(c.DistDir); err != nil {
			return fmt.Errorf("failed to clean build directory: %v", err)
		}
		fmt.Println("✅ Previous build cleaned")

		// 创建空的dist目录以满足embed要求
		if err := os.MkdirAll(c.DistDir, 0755); err != nil {
			return fmt.Errorf("failed to create dist directory: %v", err)
		}

		// 创建一个临时的index.html文件以满足embed要求
		indexPath := filepath.Join(c.DistDir, "index.html")
		tempContent := `<!DOCTYPE html><html><head><title>Building...</title></head><body><p>Building frontend...</p></body></html>`
		if err := os.WriteFile(indexPath, []byte(tempContent), 0644); err != nil {
			return fmt.Errorf("failed to create temporary index.html: %v", err)
		}
	}
	return nil
}

// getBuildStats 获取构建统计信息
func (c *BuildConfig) getBuildStats() {
	if _, err := os.Stat(c.DistDir); err != nil {
		return
	}

	var totalSize int64
	var fileCount int

	err := filepath.Walk(c.DistDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			totalSize += info.Size()
			fileCount++
		}
		return nil
	})

	if err == nil {
		fmt.Printf("📊 Build Statistics:\n")
		fmt.Printf("   Files: %d\n", fileCount)
		fmt.Printf("   Total Size: %.2f KB\n", float64(totalSize)/1024)
	}
}

func main() {
	startTime := time.Now()

	config, err := NewBuildConfig()
	if err != nil {
		log.Fatalf("❌ Failed to initialize build config: %v", err)
	}
	config.printBuildInfo()

	// 检查前提条件
	if err := config.checkPrerequisites(); err != nil {
		log.Fatalf("❌ Prerequisites check failed: %v", err)
	}

	// 清理旧构建
	if err := config.cleanBuild(); err != nil {
		log.Fatalf("❌ Clean build failed: %v", err)
	}

	// 安装依赖
	if err := config.installDependencies(); err != nil {
		log.Fatalf("❌ Install dependencies failed: %v", err)
	}

	// 构建前端
	if err := config.buildFrontend(); err != nil {
		log.Fatalf("❌ Build frontend failed: %v", err)
	}

	// 验证构建
	if err := config.verifyBuild(); err != nil {
		log.Fatalf("❌ Build verification failed: %v", err)
	}

	// 显示构建统计
	config.getBuildStats()

	duration := time.Since(startTime)
	fmt.Printf("\n🎉 Frontend build completed successfully in %v\n", duration)
	fmt.Println("Ready for Go build with embedded static files!")
}
