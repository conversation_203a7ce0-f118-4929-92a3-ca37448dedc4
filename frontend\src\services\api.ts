import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { API_BASE_URL } from '@/utils/constants';
import type { ApiResponse, ErrorResponse } from '@/types';

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  skipErrorHandler?: boolean; // 跳过全局错误处理
  retryCount?: number; // 重试次数
  timeout?: number; // 超时时间
}

// HTTP客户端类
export class ApiClient {
  private instance: AxiosInstance;
  private requestInterceptorId: number;
  private responseInterceptorId: number;

  constructor(baseURL: string = API_BASE_URL) {
    // 创建axios实例
    this.instance = axios.create({
      baseURL,
      timeout: 30000, // 30秒超时
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // 设置拦截器
    this.setupInterceptors();
  }

  // 设置请求和响应拦截器
  private setupInterceptors() {
    // 请求拦截器
    this.requestInterceptorId = this.instance.interceptors.request.use(
      (config) => {
        // 添加请求ID用于追踪
        const requestId = this.generateRequestId();
        config.headers['X-Request-ID'] = requestId;
        
        // 添加时间戳
        config.metadata = {
          ...config.metadata,
          startTime: Date.now(),
          requestId,
        };

        // 开发环境下打印请求信息
        if (process.env.NODE_ENV === 'development') {
          console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
            requestId,
            data: config.data,
            params: config.params,
          });
        }

        return config;
      },
      (error) => {
        console.error('[API Request Error]', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.responseInterceptorId = this.instance.interceptors.response.use(
      (response) => {
        // 计算请求耗时
        const duration = Date.now() - (response.config.metadata?.startTime || 0);
        
        // 开发环境下打印响应信息
        if (process.env.NODE_ENV === 'development') {
          console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
            requestId: response.config.metadata?.requestId,
            status: response.status,
            duration: `${duration}ms`,
            data: response.data,
          });
        }

        return response;
      },
      (error: AxiosError) => {
        return this.handleResponseError(error);
      }
    );
  }

  // 处理响应错误
  private handleResponseError(error: AxiosError): Promise<never> {
    const requestId = error.config?.metadata?.requestId;
    const duration = Date.now() - (error.config?.metadata?.startTime || 0);

    // 构建错误信息
    let errorResponse: ErrorResponse = {
      error: 'Unknown Error',
      code: 'UNKNOWN_ERROR',
      message: '未知错误',
      requestId,
      timestamp: new Date().toISOString(),
      path: error.config?.url || '',
      method: error.config?.method?.toUpperCase() || '',
      recoverable: false,
      suggestions: [],
    };

    if (error.response) {
      // 服务器返回了错误响应
      const { status, data } = error.response;
      
      if (data && typeof data === 'object') {
        errorResponse = { ...errorResponse, ...data };
      } else {
        errorResponse.error = `HTTP ${status}`;
        errorResponse.message = this.getHttpErrorMessage(status);
      }
    } else if (error.request) {
      // 请求发出但没有收到响应
      errorResponse.error = 'Network Error';
      errorResponse.code = 'NETWORK_ERROR';
      errorResponse.message = '网络连接失败，请检查网络连接';
      errorResponse.recoverable = true;
      errorResponse.suggestions = ['检查网络连接', '稍后重试'];
    } else {
      // 请求配置错误
      errorResponse.error = 'Request Error';
      errorResponse.code = 'REQUEST_ERROR';
      errorResponse.message = '请求配置错误';
      errorResponse.details = { originalError: error.message };
    }

    // 开发环境下打印错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error(`[API Error] ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        requestId,
        duration: `${duration}ms`,
        error: errorResponse,
        originalError: error,
      });
    }

    // 检查是否需要重试
    if (this.shouldRetry(error, errorResponse)) {
      return this.retryRequest(error);
    }

    return Promise.reject(errorResponse);
  }

  // 生成请求ID
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 获取HTTP错误消息
  private getHttpErrorMessage(status: number): string {
    const messages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '权限不足',
      404: '请求的资源不存在',
      408: '请求超时',
      429: '请求过于频繁',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂时不可用',
      504: '网关超时',
    };
    return messages[status] || `HTTP错误 ${status}`;
  }

  // 判断是否应该重试
  private shouldRetry(error: AxiosError, errorResponse: ErrorResponse): boolean {
    const config = error.config as RequestConfig;
    const retryCount = config.retryCount || 0;
    const maxRetries = 3;

    // 超过最大重试次数
    if (retryCount >= maxRetries) {
      return false;
    }

    // 只对特定错误进行重试
    const retryableErrors = ['NETWORK_ERROR', 'TIMEOUT', 'SERVICE_UNAVAILABLE'];
    const retryableStatuses = [408, 429, 500, 502, 503, 504];

    return (
      retryableErrors.includes(errorResponse.code) ||
      (error.response && retryableStatuses.includes(error.response.status)) ||
      errorResponse.recoverable
    );
  }

  // 重试请求
  private async retryRequest(error: AxiosError): Promise<never> {
    const config = error.config as RequestConfig;
    const retryCount = (config.retryCount || 0) + 1;
    const delay = Math.min(1000 * Math.pow(2, retryCount - 1), 10000); // 指数退避，最大10秒

    // 等待指定时间后重试
    await new Promise(resolve => setTimeout(resolve, delay));

    // 更新重试次数
    config.retryCount = retryCount;

    console.log(`[API Retry] ${config.method?.toUpperCase()} ${config.url} (attempt ${retryCount})`);

    return this.instance.request(config);
  }

  // GET请求
  async get<T = any>(url: string, config?: RequestConfig): Promise<T> {
    const response = await this.instance.get<ApiResponse<T>>(url, config);
    return response.data.data || response.data;
  }

  // POST请求
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config);
    return response.data.data || response.data;
  }

  // PUT请求
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config);
    return response.data.data || response.data;
  }

  // DELETE请求
  async delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config);
    return response.data.data || response.data;
  }

  // 获取原始响应（包含完整的响应信息）
  async getRaw<T = any>(url: string, config?: RequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.get<T>(url, config);
  }

  // 销毁客户端
  destroy() {
    this.instance.interceptors.request.eject(this.requestInterceptorId);
    this.instance.interceptors.response.eject(this.responseInterceptorId);
  }
}

// 创建默认的API客户端实例
export const apiClient = new ApiClient();

// 导出默认实例
export default apiClient;
