#!/bin/bash

# K8s Helper 前后端联合构建脚本
# 用途：构建前端React应用和后端Go应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
DIST_DIR="$FRONTEND_DIR/dist"

log_info "项目根目录: $PROJECT_ROOT"
log_info "前端目录: $FRONTEND_DIR"

# 解析命令行参数
BUILD_FRONTEND=true
BUILD_BACKEND=true
SKIP_TESTS=false
SKIP_LINT=false
PRODUCTION=false
DOCKER_BUILD=false
COMPRESS_STATIC=true
CLEAN_BUILD=false
FAST_MODE=false
SKIP_DEPS_CHECK=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --frontend-only)
            BUILD_BACKEND=false
            shift
            ;;
        --backend-only)
            BUILD_FRONTEND=false
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-lint)
            SKIP_LINT=true
            shift
            ;;
        --production)
            PRODUCTION=true
            shift
            ;;
        --docker)
            DOCKER_BUILD=true
            shift
            ;;
        --no-compress)
            COMPRESS_STATIC=false
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --fast)
            FAST_MODE=true
            SKIP_TESTS=true
            SKIP_LINT=true
            COMPRESS_STATIC=false
            SKIP_DEPS_CHECK=true
            shift
            ;;
        --skip-deps)
            SKIP_DEPS_CHECK=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --frontend-only    仅构建前端"
            echo "  --backend-only     仅构建后端"
            echo "  --skip-tests       跳过测试"
            echo "  --skip-lint        跳过代码检查"
            echo "  --production       生产环境构建"
            echo "  --docker           Docker构建"
            echo "  --no-compress      跳过静态文件压缩"
            echo "  --clean            清理构建产物"
            echo "  --fast             快速模式（跳过测试、检查、压缩）"
            echo "  --skip-deps        跳过依赖检查（如果已安装）"
            echo "  -h, --help         显示帮助信息"
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            exit 1
            ;;
    esac
done

# 检查必要的命令
if [ "$BUILD_FRONTEND" = true ]; then
    check_command "node"
    check_command "npm"
fi

if [ "$BUILD_BACKEND" = true ]; then
    check_command "go"
fi

if [ "$DOCKER_BUILD" = true ]; then
    check_command "docker"
fi

# 清理构建产物
if [ "$CLEAN_BUILD" = true ]; then
    log_info "清理构建产物..."

    # 清理前端构建产物
    if [ -d "$DIST_DIR" ]; then
        log_info "清理前端构建产物: $DIST_DIR"
        rm -rf "$DIST_DIR"
    fi

    # 清理后端构建产物
    for binary in "k8s-helper" "k8s-helper.exe" "k8s-helper-dev" "k8s-helper-dev.exe" "k8s-helper-prod" "k8s-helper-prod.exe"; do
        if [ -f "$PROJECT_ROOT/$binary" ]; then
            log_info "清理后端构建产物: $binary"
            rm -f "$PROJECT_ROOT/$binary"
        fi
    done

    # 清理测试构建产物
    for test_binary in "k8s-helper-test" "k8s-helper-test.exe" "k8s-helper-generate-test.exe" "k8s-helper-dev-test.exe" "k8s-helper-prod-test.exe"; do
        if [ -f "$PROJECT_ROOT/$test_binary" ]; then
            log_info "清理测试构建产物: $test_binary"
            rm -f "$PROJECT_ROOT/$test_binary"
        fi
    done

    log_success "构建产物清理完成"
fi

# 构建前端
if [ "$BUILD_FRONTEND" = true ]; then
    log_info "开始构建前端应用..."
    
    cd "$FRONTEND_DIR"
    
    # 检查package.json是否存在
    if [ ! -f "package.json" ]; then
        log_error "package.json 未找到"
        exit 1
    fi
    
    # 安装依赖（快速模式下检查是否已存在）
    if [ "$SKIP_DEPS_CHECK" = true ] && [ -d "node_modules" ]; then
        log_info "跳过前端依赖安装（已存在）..."
    else
        log_info "安装前端依赖..."
        if [ "$FAST_MODE" = true ]; then
            npm ci --silent
        else
            npm install
        fi
    fi
    
    # 运行测试（如果不跳过）
    if [ "$SKIP_TESTS" = false ]; then
        log_info "运行前端测试..."
        npm run test:run || {
            log_warning "前端测试失败，但继续构建"
        }
    fi
    
    # 运行代码检查（如果不跳过）
    if [ "$SKIP_LINT" = false ]; then
        log_info "运行代码检查..."
        npm run lint || {
            log_warning "代码检查发现问题，但继续构建"
        }
    else
        log_info "跳过代码检查..."
    fi
    
    # 构建前端应用
    log_info "构建前端应用..."
    if [ "$PRODUCTION" = true ]; then
        NODE_ENV=production npm run build
    else
        npm run build
    fi
    
    # 检查构建产物
    if [ ! -d "$DIST_DIR" ]; then
        log_error "前端构建失败，dist目录不存在"
        exit 1
    fi
    
    if [ ! -f "$DIST_DIR/index.html" ]; then
        log_error "前端构建失败，index.html不存在"
        exit 1
    fi
    
    log_success "前端构建完成"

    # 压缩静态文件
    if [ "$COMPRESS_STATIC" = true ] && [ "$PRODUCTION" = true ]; then
        log_info "开始压缩静态文件..."
        cd "$PROJECT_ROOT"

        # 检查压缩脚本是否存在
        if [ -f "scripts/compress.sh" ]; then
            chmod +x scripts/compress.sh
            ./scripts/compress.sh "$DIST_DIR" || {
                log_warning "静态文件压缩失败，但继续构建"
            }
        else
            log_warning "压缩脚本不存在: scripts/compress.sh"
        fi
    elif [ "$COMPRESS_STATIC" = true ]; then
        log_info "跳过静态文件压缩（仅在生产环境启用）"
    fi

    cd "$PROJECT_ROOT"
fi

# 构建后端
if [ "$BUILD_BACKEND" = true ]; then
    log_info "开始构建后端应用..."
    
    cd "$PROJECT_ROOT"
    
    # 检查go.mod是否存在
    if [ ! -f "go.mod" ]; then
        log_error "go.mod 未找到"
        exit 1
    fi
    
    # 下载依赖（快速模式下可能跳过）
    if [ "$SKIP_DEPS_CHECK" = true ]; then
        log_info "跳过后端依赖检查..."
    else
        log_info "下载后端依赖..."
        go mod download
        go mod tidy
    fi
    
    # 运行测试（如果不跳过）
    if [ "$SKIP_TESTS" = false ]; then
        log_info "运行后端测试..."
        go test ./... || {
            log_warning "后端测试失败，但继续构建"
        }
    fi
    
    # 运行go generate（确保静态文件是最新的）
    log_info "运行go generate..."
    go generate ./internal/web || {
        log_warning "go generate失败，但继续构建"
    }

    # 构建后端应用
    log_info "构建后端应用..."

    # 设置构建标志
    BUILD_FLAGS=""
    LDFLAGS="-w -s"

    if [ "$PRODUCTION" = true ]; then
        # 生产环境构建
        BUILD_FLAGS="-a -installsuffix cgo"
        LDFLAGS="$LDFLAGS -X main.Version=$(git describe --tags --always --dirty 2>/dev/null || echo 'dev')"
        LDFLAGS="$LDFLAGS -X main.BuildTime=$(date -u '+%Y-%m-%d_%H:%M:%S')"
        LDFLAGS="$LDFLAGS -X main.GitCommit=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"

        # 跨平台构建选项
        if [ "${GOOS:-}" = "linux" ] || [ "${GOOS:-}" = "" ]; then
            CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build $BUILD_FLAGS -ldflags="$LDFLAGS" -o k8s-helper-linux .
        fi

        if [ "${GOOS:-}" = "windows" ] || [ "${GOOS:-}" = "" ]; then
            CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build $BUILD_FLAGS -ldflags="$LDFLAGS" -o k8s-helper-windows.exe .
        fi

        if [ "${GOOS:-}" = "darwin" ] || [ "${GOOS:-}" = "" ]; then
            CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 go build $BUILD_FLAGS -ldflags="$LDFLAGS" -o k8s-helper-darwin .
        fi

        # 默认构建（当前平台）
        go build $BUILD_FLAGS -ldflags="$LDFLAGS" -o k8s-helper .
    else
        # 开发环境构建
        go build -tags=dev -ldflags="$LDFLAGS" -o k8s-helper-dev .
        # 也构建生产版本用于测试
        go build -ldflags="$LDFLAGS" -o k8s-helper .
    fi
    
    # 检查构建产物
    build_success=false

    for binary in "k8s-helper" "k8s-helper.exe" "k8s-helper-dev" "k8s-helper-dev.exe" "k8s-helper-linux" "k8s-helper-windows.exe" "k8s-helper-darwin"; do
        if [ -f "$binary" ]; then
            build_success=true
            log_info "构建成功: $binary ($(ls -lh "$binary" | awk '{print $5}'))"
        fi
    done

    if [ "$build_success" = false ]; then
        log_error "后端构建失败，没有生成可执行文件"
        exit 1
    fi

    log_success "后端构建完成"
fi

# Docker构建
if [ "$DOCKER_BUILD" = true ]; then
    log_info "开始Docker构建..."
    
    cd "$PROJECT_ROOT"
    
    # 构建Docker镜像
    if [ "$PRODUCTION" = true ]; then
        docker build -t k8s-helper:latest -t k8s-helper:prod .
    else
        docker build -t k8s-helper:dev .
    fi
    
    log_success "Docker构建完成"
fi

# 显示构建结果
log_success "构建完成！"

if [ "$BUILD_FRONTEND" = true ]; then
    log_info "前端构建产物: $DIST_DIR"
    du -sh "$DIST_DIR" 2>/dev/null || true
fi

if [ "$BUILD_BACKEND" = true ]; then
    log_info "后端可执行文件:"
    for binary in "k8s-helper" "k8s-helper.exe" "k8s-helper-dev" "k8s-helper-dev.exe" "k8s-helper-linux" "k8s-helper-windows.exe" "k8s-helper-darwin"; do
        if [ -f "$PROJECT_ROOT/$binary" ]; then
            ls -lh "$PROJECT_ROOT/$binary"
        fi
    done

    # 显示压缩文件统计
    if [ "$COMPRESS_STATIC" = true ] && [ "$PRODUCTION" = true ] && [ -d "$DIST_DIR" ]; then
        gz_count=$(find "$DIST_DIR" -name "*.gz" -type f 2>/dev/null | wc -l)
        br_count=$(find "$DIST_DIR" -name "*.br" -type f 2>/dev/null | wc -l)
        if [ $gz_count -gt 0 ] || [ $br_count -gt 0 ]; then
            log_info "压缩文件统计:"
            [ $gz_count -gt 0 ] && log_info "  Gzip文件: $gz_count"
            [ $br_count -gt 0 ] && log_info "  Brotli文件: $br_count"
        fi
    fi
fi

if [ "$DOCKER_BUILD" = true ]; then
    log_info "Docker镜像:"
    docker images k8s-helper
fi

log_success "所有构建任务完成！"
