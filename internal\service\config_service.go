package service

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// ConfigService 配置管理服务
type ConfigService struct {
	configPath    string
	backupDir     string
	maxBackups    int
	currentConfig *ConfigData
}

// NewConfigService 创建配置管理服务
func NewConfigService(configPath string) *ConfigService {
	backupDir := filepath.Join(filepath.Dir(configPath), ".config-backups")
	return &ConfigService{
		configPath: configPath,
		backupDir:  backupDir,
		maxBackups: 10,
	}
}

// ConfigData 配置数据结构
type ConfigData struct {
	Version      string                 `yaml:"version" json:"version"`
	LastModified time.Time              `yaml:"last_modified" json:"last_modified"`
	Global       GlobalConfig           `yaml:"global" json:"global"`
	ETCD         ETCDConfig             `yaml:"etcd" json:"etcd"`
	Logging      LoggingConfig          `yaml:"logging" json:"logging"`
	Cleanup      CleanupConfig          `yaml:"cleanup" json:"cleanup"`
	Monitoring   ConfigMonitoringConfig `yaml:"monitoring" json:"monitoring"`
	Web          WebConfig              `yaml:"web" json:"web"`
	Custom       map[string]interface{} `yaml:"custom,omitempty" json:"custom,omitempty"`
}

// GlobalConfig 全局配置
type GlobalConfig struct {
	Kubeconfig string `yaml:"kubeconfig" json:"kubeconfig"`
	Namespace  string `yaml:"namespace" json:"namespace"`
	Verbose    bool   `yaml:"verbose" json:"verbose"`
	Force      bool   `yaml:"force" json:"force"`
}

// ETCDConfig ETCD配置
type ETCDConfig struct {
	BaseURL           string `yaml:"base_url" json:"base_url"`
	Version           string `yaml:"version" json:"version"`
	OS                string `yaml:"os" json:"os"`
	DataDir           string `yaml:"data_dir" json:"data_dir"`
	BackupDir         string `yaml:"backup_dir" json:"backup_dir"`
	APIServerManifest string `yaml:"api_server_manifest" json:"api_server_manifest"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string `yaml:"level" json:"level"`
	Format string `yaml:"format" json:"format"`
	File   string `yaml:"file" json:"file"`
	Color  bool   `yaml:"color" json:"color"`
}

// CleanupConfig 清理配置
type CleanupConfig struct {
	DefaultOlderThan    string `yaml:"default_older_than" json:"default_older_than"`
	DefaultDryRun       bool   `yaml:"default_dry_run" json:"default_dry_run"`
	ConcurrentWorkers   int    `yaml:"concurrent_workers" json:"concurrent_workers"`
	AutoCleanupEnabled  bool   `yaml:"auto_cleanup_enabled" json:"auto_cleanup_enabled"`
	AutoCleanupInterval string `yaml:"auto_cleanup_interval" json:"auto_cleanup_interval"`
}

// ConfigMonitoringConfig 监控配置
type ConfigMonitoringConfig struct {
	Enabled         bool   `yaml:"enabled" json:"enabled"`
	Port            int    `yaml:"port" json:"port"`
	MetricsInterval string `yaml:"metrics_interval" json:"metrics_interval"`
	AlertsEnabled   bool   `yaml:"alerts_enabled" json:"alerts_enabled"`
	NotificationURL string `yaml:"notification_url" json:"notification_url"`
}

// WebConfig Web配置
type WebConfig struct {
	Port            int    `yaml:"port" json:"port"`
	Host            string `yaml:"host" json:"host"`
	TLSEnabled      bool   `yaml:"tls_enabled" json:"tls_enabled"`
	CertFile        string `yaml:"cert_file" json:"cert_file"`
	KeyFile         string `yaml:"key_file" json:"key_file"`
	StaticDir       string `yaml:"static_dir" json:"static_dir"`
	TemplateDir     string `yaml:"template_dir" json:"template_dir"`
	SessionSecret   string `yaml:"session_secret" json:"session_secret"`
	CORSEnabled     bool   `yaml:"cors_enabled" json:"cors_enabled"`
	RateLimitRPS    int    `yaml:"rate_limit_rps" json:"rate_limit_rps"`
}

// ConfigUpdateRequest 配置更新请求
type ConfigUpdateRequest struct {
	Section  string      `json:"section" binding:"required"`
	Content  interface{} `json:"content" binding:"required"`
	Validate bool        `json:"validate"`
	Backup   bool        `json:"backup"`
}

// ConfigHistory 配置历史记录
type ConfigHistory struct {
	ID          string    `json:"id"`
	Timestamp   time.Time `json:"timestamp"`
	Section     string    `json:"section"`
	Operation   string    `json:"operation"` // create, update, delete
	Description string    `json:"description"`
	BackupPath  string    `json:"backup_path,omitempty"`
	Size        int64     `json:"size"`
}

// GetConfig 获取配置
func (cs *ConfigService) GetConfig(section string) (*ConfigData, error) {
	if cs.currentConfig == nil {
		if err := cs.loadConfig(); err != nil {
			return nil, err
		}
	}

	return cs.currentConfig, nil
}

// GetConfigSection 获取配置的特定部分
func (cs *ConfigService) GetConfigSection(section string) (interface{}, error) {
	config, err := cs.GetConfig("")
	if err != nil {
		return nil, err
	}

	switch section {
	case "global":
		return config.Global, nil
	case "etcd":
		return config.ETCD, nil
	case "logging":
		return config.Logging, nil
	case "cleanup":
		return config.Cleanup, nil
	case "monitoring":
		return config.Monitoring, nil
	case "web":
		return config.Web, nil
	case "custom":
		return config.Custom, nil
	case "":
		return config, nil
	default:
		return nil, fmt.Errorf("未知的配置部分: %s", section)
	}
}

// UpdateConfig 更新配置
func (cs *ConfigService) UpdateConfig(section string, content interface{}) error {
	// 备份当前配置
	if err := cs.backupConfig(); err != nil {
		return fmt.Errorf("备份配置失败: %w", err)
	}

	// 加载当前配置
	if err := cs.loadConfig(); err != nil {
		return err
	}

	// 更新指定部分
	if err := cs.updateConfigSection(section, content); err != nil {
		return err
	}

	// 保存配置
	if err := cs.saveConfig(); err != nil {
		return err
	}

	return nil
}

// ValidateConfig 验证配置
func (cs *ConfigService) ValidateConfig(content interface{}) error {
	// 基本的配置验证逻辑
	if content == nil {
		return fmt.Errorf("配置内容不能为空")
	}

	// 这里可以添加更复杂的验证逻辑
	// 例如：检查端口范围、文件路径有效性等

	return nil
}

// ReloadConfig 重载配置
func (cs *ConfigService) ReloadConfig() error {
	return cs.loadConfig()
}

// GetConfigHistory 获取配置历史
func (cs *ConfigService) GetConfigHistory(limit int) ([]ConfigHistory, error) {
	// 扫描备份目录
	if _, err := os.Stat(cs.backupDir); os.IsNotExist(err) {
		return []ConfigHistory{}, nil
	}

	files, err := ioutil.ReadDir(cs.backupDir)
	if err != nil {
		return nil, err
	}

	var history []ConfigHistory
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		// 解析文件名获取时间戳
		timestamp, err := time.Parse("20060102-150405", file.Name()[:15])
		if err != nil {
			continue
		}

		history = append(history, ConfigHistory{
			ID:          file.Name(),
			Timestamp:   timestamp,
			Section:     "all",
			Operation:   "backup",
			Description: "自动备份",
			BackupPath:  filepath.Join(cs.backupDir, file.Name()),
			Size:        file.Size(),
		})

		if len(history) >= limit {
			break
		}
	}

	return history, nil
}

// RestoreConfig 恢复配置
func (cs *ConfigService) RestoreConfig(backupID string) error {
	backupPath := filepath.Join(cs.backupDir, backupID)
	
	// 检查备份文件是否存在
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		return fmt.Errorf("备份文件不存在: %s", backupID)
	}

	// 备份当前配置
	if err := cs.backupConfig(); err != nil {
		return fmt.Errorf("备份当前配置失败: %w", err)
	}

	// 复制备份文件到配置文件位置
	backupData, err := ioutil.ReadFile(backupPath)
	if err != nil {
		return fmt.Errorf("读取备份文件失败: %w", err)
	}

	if err := ioutil.WriteFile(cs.configPath, backupData, 0644); err != nil {
		return fmt.Errorf("恢复配置文件失败: %w", err)
	}

	// 重新加载配置
	return cs.loadConfig()
}

// 私有方法
func (cs *ConfigService) loadConfig() error {
	// 检查配置文件是否存在
	if _, err := os.Stat(cs.configPath); os.IsNotExist(err) {
		// 创建默认配置
		cs.currentConfig = cs.getDefaultConfig()
		return cs.saveConfig()
	}

	// 读取配置文件
	data, err := ioutil.ReadFile(cs.configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML
	var config ConfigData
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	cs.currentConfig = &config
	return nil
}

func (cs *ConfigService) saveConfig() error {
	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(cs.configPath), 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 更新时间戳
	cs.currentConfig.LastModified = time.Now()

	// 序列化为YAML
	data, err := yaml.Marshal(cs.currentConfig)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	if err := ioutil.WriteFile(cs.configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

func (cs *ConfigService) backupConfig() error {
	// 确保备份目录存在
	if err := os.MkdirAll(cs.backupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(cs.configPath); os.IsNotExist(err) {
		return nil // 配置文件不存在，无需备份
	}

	// 生成备份文件名
	timestamp := time.Now().Format("20060102-150405")
	backupPath := filepath.Join(cs.backupDir, fmt.Sprintf("%s.yaml", timestamp))

	// 复制配置文件
	data, err := ioutil.ReadFile(cs.configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	if err := ioutil.WriteFile(backupPath, data, 0644); err != nil {
		return fmt.Errorf("创建备份文件失败: %w", err)
	}

	// 清理旧备份
	cs.cleanupOldBackups()

	return nil
}

func (cs *ConfigService) updateConfigSection(section string, content interface{}) error {
	// 将content转换为JSON再转换为对应的结构体
	contentBytes, err := json.Marshal(content)
	if err != nil {
		return fmt.Errorf("序列化配置内容失败: %w", err)
	}

	switch section {
	case "global":
		var global GlobalConfig
		if err := json.Unmarshal(contentBytes, &global); err != nil {
			return fmt.Errorf("解析global配置失败: %w", err)
		}
		cs.currentConfig.Global = global
	case "etcd":
		var etcd ETCDConfig
		if err := json.Unmarshal(contentBytes, &etcd); err != nil {
			return fmt.Errorf("解析etcd配置失败: %w", err)
		}
		cs.currentConfig.ETCD = etcd
	case "logging":
		var logging LoggingConfig
		if err := json.Unmarshal(contentBytes, &logging); err != nil {
			return fmt.Errorf("解析logging配置失败: %w", err)
		}
		cs.currentConfig.Logging = logging
	case "cleanup":
		var cleanup CleanupConfig
		if err := json.Unmarshal(contentBytes, &cleanup); err != nil {
			return fmt.Errorf("解析cleanup配置失败: %w", err)
		}
		cs.currentConfig.Cleanup = cleanup
	case "monitoring":
		var monitoring ConfigMonitoringConfig
		if err := json.Unmarshal(contentBytes, &monitoring); err != nil {
			return fmt.Errorf("解析monitoring配置失败: %w", err)
		}
		cs.currentConfig.Monitoring = monitoring
	case "web":
		var web WebConfig
		if err := json.Unmarshal(contentBytes, &web); err != nil {
			return fmt.Errorf("解析web配置失败: %w", err)
		}
		cs.currentConfig.Web = web
	case "custom":
		var custom map[string]interface{}
		if err := json.Unmarshal(contentBytes, &custom); err != nil {
			return fmt.Errorf("解析custom配置失败: %w", err)
		}
		cs.currentConfig.Custom = custom
	default:
		return fmt.Errorf("未知的配置部分: %s", section)
	}

	return nil
}

func (cs *ConfigService) cleanupOldBackups() {
	files, err := ioutil.ReadDir(cs.backupDir)
	if err != nil {
		return
	}

	if len(files) <= cs.maxBackups {
		return
	}

	// 删除最旧的备份文件
	for i := 0; i < len(files)-cs.maxBackups; i++ {
		os.Remove(filepath.Join(cs.backupDir, files[i].Name()))
	}
}

func (cs *ConfigService) getDefaultConfig() *ConfigData {
	return &ConfigData{
		Version:      "1.0.0",
		LastModified: time.Now(),
		Global: GlobalConfig{
			Kubeconfig: "",
			Namespace:  "default",
			Verbose:    false,
			Force:      false,
		},
		ETCD: ETCDConfig{
			BaseURL:           "https://github.com/etcd-io/etcd/releases/download",
			Version:           "v3.5.9",
			OS:                "linux",
			DataDir:           "/var/lib/etcd",
			BackupDir:         "/var/backups/etcd",
			APIServerManifest: "/etc/kubernetes/manifests/kube-apiserver.yaml",
		},
		Logging: LoggingConfig{
			Level:  "info",
			Format: "text",
			File:   "",
			Color:  true,
		},
		Cleanup: CleanupConfig{
			DefaultOlderThan:    "24h",
			DefaultDryRun:       true,
			ConcurrentWorkers:   5,
			AutoCleanupEnabled:  false,
			AutoCleanupInterval: "24h",
		},
		Monitoring: ConfigMonitoringConfig{
			Enabled:         true,
			Port:            9090,
			MetricsInterval: "30s",
			AlertsEnabled:   true,
			NotificationURL: "",
		},
		Web: WebConfig{
			Port:          8080,
			Host:          "localhost",
			TLSEnabled:    false,
			CertFile:      "",
			KeyFile:       "",
			StaticDir:     "web/static",
			TemplateDir:   "web/templates",
			SessionSecret: "",
			CORSEnabled:   true,
			RateLimitRPS:  100,
		},
		Custom: make(map[string]interface{}),
	}
}
