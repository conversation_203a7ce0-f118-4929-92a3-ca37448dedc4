import React, { useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { Tabs } from 'antd';
import {
  DatabaseOutlined,
  SaveOutlined,
  ReloadOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useAppState } from '@/hooks';

/**
 * ETCD管理主页面
 *
 * 功能特性：
 * - Tab导航
 * - 子页面路由管理
 * - 页面标题设置
 */
const ETCDManagement: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setPageTitle } = useAppState();

  // 设置页面标题
  useEffect(() => {
    setPageTitle('ETCD管理 - K8s-Helper');
  }, [setPageTitle]);

  // 获取当前激活的Tab
  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes('/backup')) return 'backup';
    if (path.includes('/restore')) return 'restore';
    if (path.includes('/cronjobs')) return 'cronjobs';
    return 'overview';
  };

  // 处理Tab切换
  const handleTabChange = (key: string) => {
    const basePath = '/etcd';
    switch (key) {
      case 'overview':
        navigate(basePath); // 默认路由到概览
        break;
      case 'backup':
        navigate(`${basePath}/backup`);
        break;
      case 'restore':
        navigate(`${basePath}/restore`);
        break;
      case 'cronjobs':
        navigate(`${basePath}/cronjobs`);
        break;
      default:
        navigate(basePath);
    }
  };

  // Tab配置
  const tabItems = [
    {
      key: 'overview',
      label: (
        <span>
          <DatabaseOutlined />
          概览
        </span>
      ),
    },
    {
      key: 'backup',
      label: (
        <span>
          <SaveOutlined />
          备份管理
        </span>
      ),
    },
    {
      key: 'restore',
      label: (
        <span>
          <ReloadOutlined />
          恢复管理
        </span>
      ),
    },
    {
      key: 'cronjobs',
      label: (
        <span>
          <ClockCircleOutlined />
          定时任务
        </span>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f7fa' }}>
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
          ETCD管理
        </h1>
        <p style={{ margin: '8px 0 0 0', color: '#666' }}>
          管理ETCD集群的备份、恢复和定时任务
        </p>
      </div>

      <Tabs
        activeKey={getActiveTab()}
        onChange={handleTabChange}
        items={tabItems}
        size="large"
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '0 24px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
      >
      </Tabs>

      <div style={{
        marginTop: '24px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        minHeight: '500px'
      }}>
        <Outlet />
      </div>
    </div>
  );
};

export default ETCDManagement;
