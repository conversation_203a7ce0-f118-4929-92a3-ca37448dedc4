// 全局类型定义

// API 响应基础类型
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  code?: number;
  success?: boolean;
  timestamp?: string;
}

// 错误响应类型
export interface ErrorResponse {
  error: string;
  code: string;
  message: string;
  requestId?: string;
  timestamp: string;
  path?: string;
  method?: string;
  recoverable?: boolean;
  suggestions?: string[];
  details?: Record<string, any>;
}

// 分页类型
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  total?: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 集群相关类型
export interface ClusterInfo {
  name: string;
  version: string;
  nodes: Node[];
  status: 'healthy' | 'warning' | 'error';
  createdAt: string;
}

export interface Node {
  name: string;
  status: 'Ready' | 'NotReady' | 'Unknown';
  roles: string[];
  age: string;
  version: string;
  internalIP: string;
  externalIP?: string;
  osImage: string;
  kernelVersion: string;
  containerRuntime: string;
  cpu: ResourceInfo;
  memory: ResourceInfo;
}

export interface ResourceInfo {
  capacity: string;
  allocatable: string;
  usage: string;
  percentage: number;
}

// ETCD 相关类型
export interface ETCDBackup {
  id: string;
  name: string;
  path: string;
  size: number;
  timestamp: string;
  status: 'success' | 'failed' | 'in_progress';
}

export interface ETCDStatus {
  healthy: boolean;
  members: ETCDMember[];
  leader: string;
  version: string;
}

export interface ETCDMember {
  id: string;
  name: string;
  peerURLs: string[];
  clientURLs: string[];
  isLearner: boolean;
}

// Pod 相关类型
export interface Pod {
  name: string;
  namespace: string;
  status: PodStatus;
  restarts: number;
  age: string;
  ip: string;
  node: string;
  containers: Container[];
}

export interface Container {
  name: string;
  image: string;
  ready: boolean;
  restartCount: number;
  state: ContainerState;
}

export interface ContainerState {
  running?: {
    startedAt: string;
  };
  waiting?: {
    reason: string;
    message?: string;
  };
  terminated?: {
    exitCode: number;
    reason: string;
    message?: string;
    startedAt: string;
    finishedAt: string;
  };
}

export type PodStatus = 'Running' | 'Pending' | 'Succeeded' | 'Failed' | 'Unknown';

// 日志相关类型
export interface LogEntry {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  source?: string;
  metadata?: Record<string, any>;
}

// WebSocket 消息类型
export interface WebSocketMessage<T = any> {
  type: string;
  data: T;
  timestamp: string;
  id?: string;
}

// 应用状态类型
export interface AppState {
  currentModule: string;
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  loading: boolean;
  error: string | null;
}

// 用户偏好类型
export interface UserPreferences {
  theme: 'light' | 'dark';
  language: 'zh-CN' | 'en-US';
  autoRefresh: boolean;
  refreshInterval: number;
  performanceMode: boolean;
}

// 连接状态类型
export type ConnectionStatus = 'connected' | 'disconnected' | 'connecting' | 'error';

// 工具函数类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// 导入其他类型模块
export type * from './api';
export type * from './websocket';
