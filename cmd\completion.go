package cmd

import (
	"context"
	"os"

	"github.com/spf13/cobra"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"k8s-helper/pkg/cmdhelp"
	"k8s-helper/pkg/k8s"
)

// completionCmd 代表 completion 命令
var completionCmd = cmdhelp.CompletionHelp.BuildCommand(runCompletionCommand, nil)

func runCompletionCommand(cmd *cobra.Command, args []string) error {
	if len(args) == 0 {
		return cmd.Help()
	}

	switch args[0] {
	case "bash":
		return cmd.Root().GenBashCompletion(os.Stdout)
	case "zsh":
		return cmd.Root().GenZshCompletion(os.Stdout)
	case "fish":
		return cmd.Root().GenFishCompletion(os.Stdout, true)
	case "powershell":
		return cmd.Root().GenPowerShellCompletionWithDesc(os.Stdout)
	default:
		return cmd.Help()
	}
}

func init() {
	// 设置自动补全函数
	setupCompletionFunctions()
}

// setupCompletionFunctions 设置自动补全函数
func setupCompletionFunctions() {
	// 为 namespace 参数设置补全
	rootCmd.RegisterFlagCompletionFunc("namespace", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		return getNamespaceCompletions(), cobra.ShellCompDirectiveNoFileComp
	})

	// 为 kubeconfig 参数设置补全
	rootCmd.RegisterFlagCompletionFunc("kubeconfig", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		return nil, cobra.ShellCompDirectiveDefault
	})

	// 为 logs 命令的 pod 参数设置补全
	logsCmd.ValidArgsFunction = func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		if len(args) == 0 {
			return getPodCompletions(cmd), cobra.ShellCompDirectiveNoFileComp
		}
		return nil, cobra.ShellCompDirectiveNoFileComp
	}

	// 为 port-forward 命令的 pod 参数设置补全
	portForwardCmd.ValidArgsFunction = func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		if len(args) == 0 {
			return getPodCompletions(cmd), cobra.ShellCompDirectiveNoFileComp
		}
		return nil, cobra.ShellCompDirectiveNoFileComp
	}

	// 为 logs 命令的 container 参数设置补全
	logsCmd.RegisterFlagCompletionFunc("container", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		if len(args) > 0 {
			return getContainerCompletions(cmd, args[0]), cobra.ShellCompDirectiveNoFileComp
		}
		return nil, cobra.ShellCompDirectiveNoFileComp
	})

	// 为 cleanup 命令设置补全
	cleanupCmd.ValidArgsFunction = func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		if len(args) == 0 {
			return []string{"pods", "jobs", "all"}, cobra.ShellCompDirectiveNoFileComp
		}
		return nil, cobra.ShellCompDirectiveNoFileComp
	}

	// 为 etcd 子命令设置补全
	etcdBackupCmd.RegisterFlagCompletionFunc("output", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		return nil, cobra.ShellCompDirectiveDefault
	})

	etcdRestoreCmd.RegisterFlagCompletionFunc("snapshot", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		return nil, cobra.ShellCompDirectiveFilterFileExt
	})

	etcdVerifyCmd.RegisterFlagCompletionFunc("snapshot", func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		return nil, cobra.ShellCompDirectiveFilterFileExt
	})
}

// getNamespaceCompletions 获取命名空间补全
func getNamespaceCompletions() []string {
	// 尝试获取 Kubernetes 客户端
	client, err := getKubernetesClient()
	if err != nil {
		return []string{"default", "kube-system", "kube-public", "kube-node-lease"}
	}

	// 获取命名空间列表
	namespaces, err := client.CoreV1().Namespaces().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return []string{"default", "kube-system", "kube-public", "kube-node-lease"}
	}

	var names []string
	for _, ns := range namespaces.Items {
		names = append(names, ns.Name)
	}

	return names
}

// getPodCompletions 获取 Pod 补全
func getPodCompletions(cmd *cobra.Command) []string {
	// 尝试获取 Kubernetes 客户端
	client, err := getKubernetesClient()
	if err != nil {
		return nil
	}

	// 获取命名空间
	namespace, _ := cmd.Flags().GetString("namespace")
	if namespace == "" {
		namespace = "default"
	}

	// 获取 Pod 列表
	pods, err := client.CoreV1().Pods(namespace).List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return nil
	}

	var names []string
	for _, pod := range pods.Items {
		names = append(names, pod.Name)
	}

	return names
}

// getContainerCompletions 获取容器补全
func getContainerCompletions(cmd *cobra.Command, podName string) []string {
	// 尝试获取 Kubernetes 客户端
	client, err := getKubernetesClient()
	if err != nil {
		return nil
	}

	// 获取命名空间
	namespace, _ := cmd.Flags().GetString("namespace")
	if namespace == "" {
		namespace = "default"
	}

	// 获取 Pod 详情
	pod, err := client.CoreV1().Pods(namespace).Get(context.Background(), podName, metav1.GetOptions{})
	if err != nil {
		return nil
	}

	var names []string
	for _, container := range pod.Spec.Containers {
		names = append(names, container.Name)
	}

	return names
}

// getKubernetesClient 获取 Kubernetes 客户端（用于补全）
func getKubernetesClient() (kubernetes.Interface, error) {
	// 这里复用现有的客户端创建逻辑
	kubeconfig, _ := rootCmd.Flags().GetString("kubeconfig")
	return k8s.NewClient(kubeconfig)
}
