package service

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// LogsService Pod日志服务
type LogsService struct {
	clientset kubernetes.Interface
}

// NewLogsService 创建日志服务
func NewLogsService(clientset kubernetes.Interface) *LogsService {
	return &LogsService{
		clientset: clientset,
	}
}

// PodInfo Pod信息
type PodInfo struct {
	Name       string            `json:"name"`
	Namespace  string            `json:"namespace"`
	Status     string            `json:"status"`
	Containers []ContainerInfo   `json:"containers"`
	Labels     map[string]string `json:"labels"`
	CreatedAt  time.Time         `json:"created_at"`
}

// ContainerInfo 容器信息
type ContainerInfo struct {
	Name  string `json:"name"`
	Image string `json:"image"`
	Ready bool   `json:"ready"`
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time `json:"timestamp"`
	Message   string    `json:"message"`
	Container string    `json:"container"`
	Level     string    `json:"level,omitempty"`
}

// LogOptions 日志选项
type LogOptions struct {
	Container    string    `json:"container"`
	Follow       bool      `json:"follow"`
	Timestamps   bool      `json:"timestamps"`
	TailLines    *int64    `json:"tail_lines,omitempty"`
	SinceTime    *time.Time `json:"since_time,omitempty"`
	SinceSeconds *int64    `json:"since_seconds,omitempty"`
	Previous     bool      `json:"previous"`
}

// GetNamespaces 获取所有命名空间
func (ls *LogsService) GetNamespaces(ctx context.Context) ([]string, error) {
	namespaces, err := ls.clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	var names []string
	for _, ns := range namespaces.Items {
		names = append(names, ns.Name)
	}

	return names, nil
}

// GetPods 获取指定命名空间的所有Pod
func (ls *LogsService) GetPods(ctx context.Context, namespace string) ([]PodInfo, error) {
	pods, err := ls.clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	var podInfos []PodInfo
	for _, pod := range pods.Items {
		podInfo := PodInfo{
			Name:      pod.Name,
			Namespace: pod.Namespace,
			Status:    string(pod.Status.Phase),
			Labels:    pod.Labels,
			CreatedAt: pod.CreationTimestamp.Time,
		}

		// 获取容器信息
		for _, container := range pod.Spec.Containers {
			containerInfo := ContainerInfo{
				Name:  container.Name,
				Image: container.Image,
				Ready: ls.isContainerReady(&pod, container.Name),
			}
			podInfo.Containers = append(podInfo.Containers, containerInfo)
		}

		podInfos = append(podInfos, podInfo)
	}

	return podInfos, nil
}

// GetPodInfo 获取指定Pod的详细信息
func (ls *LogsService) GetPodInfo(ctx context.Context, namespace, podName string) (*PodInfo, error) {
	pod, err := ls.clientset.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	podInfo := &PodInfo{
		Name:      pod.Name,
		Namespace: pod.Namespace,
		Status:    string(pod.Status.Phase),
		Labels:    pod.Labels,
		CreatedAt: pod.CreationTimestamp.Time,
	}

	// 获取容器信息
	for _, container := range pod.Spec.Containers {
		containerInfo := ContainerInfo{
			Name:  container.Name,
			Image: container.Image,
			Ready: ls.isContainerReady(pod, container.Name),
		}
		podInfo.Containers = append(podInfo.Containers, containerInfo)
	}

	return podInfo, nil
}

// GetLogs 获取Pod日志
func (ls *LogsService) GetLogs(ctx context.Context, namespace, podName string, options *LogOptions) ([]LogEntry, error) {
	// 构建Kubernetes日志选项
	logOptions := &v1.PodLogOptions{
		Container:  options.Container,
		Follow:     false, // 非流式获取
		Timestamps: options.Timestamps,
		Previous:   options.Previous,
	}

	if options.TailLines != nil {
		logOptions.TailLines = options.TailLines
	}

	if options.SinceSeconds != nil {
		logOptions.SinceSeconds = options.SinceSeconds
	}

	// 获取日志流
	req := ls.clientset.CoreV1().Pods(namespace).GetLogs(podName, logOptions)
	logs, err := req.Stream(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取日志流失败: %w", err)
	}
	defer logs.Close()

	// 解析日志
	var logEntries []LogEntry
	scanner := bufio.NewScanner(logs)
	for scanner.Scan() {
		line := scanner.Text()
		entry := ls.parseLogLine(line, options.Container, options.Timestamps)
		logEntries = append(logEntries, entry)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取日志失败: %w", err)
	}

	return logEntries, nil
}

// GetLogStream 获取日志流（用于WebSocket）
func (ls *LogsService) GetLogStream(ctx context.Context, namespace, podName string, options *LogOptions) (io.ReadCloser, error) {
	// 构建Kubernetes日志选项
	logOptions := &v1.PodLogOptions{
		Container:  options.Container,
		Follow:     options.Follow,
		Timestamps: options.Timestamps,
		Previous:   options.Previous,
	}

	if options.TailLines != nil {
		logOptions.TailLines = options.TailLines
	}

	if options.SinceSeconds != nil {
		logOptions.SinceSeconds = options.SinceSeconds
	}

	// 获取日志流
	req := ls.clientset.CoreV1().Pods(namespace).GetLogs(podName, logOptions)
	return req.Stream(ctx)
}

// ValidateContainer 验证容器是否存在
func (ls *LogsService) ValidateContainer(ctx context.Context, namespace, podName, containerName string) error {
	pod, err := ls.clientset.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取Pod信息失败: %w", err)
	}

	// 检查容器是否存在
	for _, container := range pod.Spec.Containers {
		if container.Name == containerName {
			return nil
		}
	}

	for _, container := range pod.Spec.InitContainers {
		if container.Name == containerName {
			return nil
		}
	}

	return fmt.Errorf("容器 '%s' 在 Pod '%s' 中不存在", containerName, podName)
}

// 辅助方法
func (ls *LogsService) isContainerReady(pod *v1.Pod, containerName string) bool {
	for _, status := range pod.Status.ContainerStatuses {
		if status.Name == containerName {
			return status.Ready
		}
	}
	return false
}

func (ls *LogsService) parseLogLine(line, container string, hasTimestamp bool) LogEntry {
	entry := LogEntry{
		Container: container,
		Message:   line,
	}

	// 如果包含时间戳，尝试解析
	if hasTimestamp && len(line) > 30 {
		// Kubernetes日志时间戳格式: 2023-01-01T12:00:00.000000000Z
		if timestampEnd := strings.Index(line, " "); timestampEnd > 0 {
			timestampStr := line[:timestampEnd]
			if timestamp, err := time.Parse(time.RFC3339Nano, timestampStr); err == nil {
				entry.Timestamp = timestamp
				entry.Message = line[timestampEnd+1:]
			}
		}
	}

	// 简单的日志级别检测
	message := strings.ToUpper(entry.Message)
	if strings.Contains(message, "ERROR") || strings.Contains(message, "FATAL") {
		entry.Level = "error"
	} else if strings.Contains(message, "WARN") {
		entry.Level = "warning"
	} else if strings.Contains(message, "INFO") {
		entry.Level = "info"
	} else if strings.Contains(message, "DEBUG") {
		entry.Level = "debug"
	}

	return entry
}
