//go:build dev
// +build dev

package web

import (
	"fmt"
	"log"
	"net/http"
	"net/http/httputil"
	"net/url"
	"time"
)

// DevServerConfig 开发服务器配置
type DevServerConfig struct {
	// Host 前端开发服务器主机地址
	Host string
	
	// Port 前端开发服务器端口
	Port string
	
	// Scheme 协议（http/https）
	Scheme string
	
	// Timeout 连接超时时间
	Timeout time.Duration
	
	// RetryAttempts 重试次数
	RetryAttempts int
	
	// RetryDelay 重试延迟
	RetryDelay time.Duration
}

// DefaultDevServerConfig 默认开发服务器配置
var DefaultDevServerConfig = DevServerConfig{
	Host:          "localhost",
	Port:          "5173", // Vite 默认端口
	Scheme:        "http",
	Timeout:       10 * time.Second,
	RetryAttempts: 3,
	RetryDelay:    1 * time.Second,
}

// GetStaticHandler 返回开发环境的静态文件处理器
// 实现反向代理到前端开发服务器，保持热重载功能
func GetStaticHandler() http.Handler {
	return GetStaticHandlerWithConfig(DefaultDevServerConfig)
}

// GetStaticHandlerWithConfig 使用自定义配置创建开发环境处理器
func GetStaticHandlerWithConfig(config DevServerConfig) http.Handler {
	log.Printf("[INFO] Initializing development static file handler")
	log.Printf("[INFO] Proxying to %s://%s:%s", config.Scheme, config.Host, config.Port)
	
	// 构建目标URL
	targetURL := fmt.Sprintf("%s://%s:%s", config.Scheme, config.Host, config.Port)
	target, err := url.Parse(targetURL)
	if err != nil {
		log.Printf("[ERROR] Failed to parse target URL %s: %v", targetURL, err)
		return createErrorHandler(fmt.Sprintf("Invalid target URL: %v", err))
	}
	
	// 创建反向代理
	proxy := httputil.NewSingleHostReverseProxy(target)
	
	// 自定义Director函数以处理请求
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		originalDirector(req)
		
		// 设置代理相关的头
		req.Header.Set("X-Forwarded-Proto", req.URL.Scheme)
		req.Header.Set("X-Forwarded-Host", req.Host)
		
		// 保持原始Host头以支持Vite的HMR
		req.Host = target.Host
		
		// 记录代理请求（仅在调试模式下）
		if req.URL.Query().Get("debug") == "1" {
			log.Printf("[DEBUG] Proxying request: %s %s", req.Method, req.URL.Path)
		}
	}
	
	// 自定义错误处理
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		log.Printf("[ERROR] Proxy error for %s: %v", r.URL.Path, err)
		
		// 检查是否是连接错误
		if isConnectionError(err) {
			handleConnectionError(w, r, config)
		} else {
			// 其他错误
			w.WriteHeader(http.StatusBadGateway)
			fmt.Fprintf(w, `
<!DOCTYPE html>
<html>
<head>
    <title>Proxy Error</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .error { color: #d32f2f; }
        .info { color: #1976d2; }
    </style>
</head>
<body>
    <h1 class="error">Proxy Error</h1>
    <p>Failed to proxy request to frontend development server.</p>
    <p class="info">Error: %v</p>
    <p class="info">Target: %s</p>
    <hr>
    <p><small>Development Mode - K8s Helper</small></p>
</body>
</html>`, err, targetURL)
		}
	}
	
	// 包装代理以添加连接检查和重试机制
	return &DevProxyHandler{
		proxy:  proxy,
		config: config,
		target: target,
	}
}

// DevProxyHandler 开发环境代理处理器
type DevProxyHandler struct {
	proxy  *httputil.ReverseProxy
	config DevServerConfig
	target *url.URL
}

// ServeHTTP 实现 http.Handler 接口
func (h *DevProxyHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// 首先检查前端服务器是否可用
	if !h.isServerAvailable() {
		handleConnectionError(w, r, h.config)
		return
	}
	
	// 设置开发环境相关的头
	w.Header().Set("X-Dev-Mode", "true")
	w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
	
	// 代理请求
	h.proxy.ServeHTTP(w, r)
}

// isServerAvailable 检查前端服务器是否可用
func (h *DevProxyHandler) isServerAvailable() bool {
	client := &http.Client{
		Timeout: h.config.Timeout,
	}
	
	for i := 0; i < h.config.RetryAttempts; i++ {
		resp, err := client.Get(h.target.String())
		if err == nil {
			resp.Body.Close()
			return true
		}
		
		if i < h.config.RetryAttempts-1 {
			log.Printf("[WARN] Frontend server not available, retrying in %v... (attempt %d/%d)", 
				h.config.RetryDelay, i+1, h.config.RetryAttempts)
			time.Sleep(h.config.RetryDelay)
		}
	}
	
	return false
}

// isConnectionError 检查是否是连接错误
func isConnectionError(err error) bool {
	if err == nil {
		return false
	}
	
	// 检查常见的连接错误
	errStr := err.Error()
	return contains(errStr, "connection refused") ||
		   contains(errStr, "no such host") ||
		   contains(errStr, "timeout") ||
		   contains(errStr, "network is unreachable")
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr ||
		      containsSubstring(s, substr))))
}

// containsSubstring 简单的子字符串检查
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// handleConnectionError 处理连接错误
func handleConnectionError(w http.ResponseWriter, r *http.Request, config DevServerConfig) {
	targetURL := fmt.Sprintf("%s://%s:%s", config.Scheme, config.Host, config.Port)
	
	w.WriteHeader(http.StatusServiceUnavailable)
	fmt.Fprintf(w, `
<!DOCTYPE html>
<html>
<head>
    <title>Frontend Server Not Available</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background-color: #f5f5f5; 
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .error { color: #d32f2f; }
        .info { color: #1976d2; }
        .command { 
            background: #f0f0f0; 
            padding: 10px; 
            border-radius: 4px; 
            font-family: monospace; 
            margin: 10px 0; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="error">Frontend Development Server Not Available</h1>
        <p>The frontend development server is not running or not accessible.</p>
        
        <h3>To start the frontend development server:</h3>
        <div class="command">cd frontend && npm run dev</div>
        
        <h3>Server Details:</h3>
        <ul>
            <li><strong>Target URL:</strong> <span class="info">%s</span></li>
            <li><strong>Expected Port:</strong> <span class="info">%s</span></li>
            <li><strong>Request Path:</strong> <span class="info">%s</span></li>
        </ul>
        
        <p><small>Development Mode - K8s Helper</small></p>
        <p><small>This page will automatically work once the frontend server is started.</small></p>
    </div>
</body>
</html>`, targetURL, config.Port, r.URL.Path)
}

// createErrorHandler 创建错误处理器
func createErrorHandler(message string) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprintf(w, `
<!DOCTYPE html>
<html>
<head>
    <title>Configuration Error</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .error { color: #d32f2f; }
    </style>
</head>
<body>
    <h1 class="error">Configuration Error</h1>
    <p>%s</p>
    <p><small>Development Mode - K8s Helper</small></p>
</body>
</html>`, message)
	})
}
