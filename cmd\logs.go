package cmd

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"k8s-helper/pkg/cmdhelp"
	"k8s-helper/pkg/common"
	"k8s-helper/pkg/k8s"
)

// logsCmd 代表 logs 命令
var logsCmd = cmdhelp.LogsHelp.BuildCommand(runLogsCommand, cmdhelp.LogsErrorConfig)

var (
	// 命名空间
	namespace string
	// 容器名称
	containerName string
	// 是否跟踪日志
	follow bool
	// 显示时间戳
	timestamps bool
	// 限制日志行数
	tailLines int64
	// 显示之前的日志
	previous bool
	// 日志开始时间
	sinceTime string
)

func init() {
	logsCmd.Flags().StringVarP(&namespace, "namespace", "n", common.DefaultNamespace,
		"指定命名空间")
	logsCmd.Flags().StringVarP(&containerName, "container", "c", "",
		"指定容器名称 (多容器 Pod 时使用)")
	logsCmd.Flags().BoolVarP(&follow, "follow", "f", common.DefaultFollowLogs,
		"实时跟踪日志输出")
	logsCmd.Flags().BoolVar(&timestamps, "timestamps", common.DefaultShowTimestamps,
		"显示时间戳")
	logsCmd.Flags().Int64Var(&tailLines, "tail", common.DefaultTailLines,
		"显示最后 N 行日志 (-1 表示显示所有)")
	logsCmd.Flags().BoolVarP(&previous, "previous", "p", common.DefaultShowPrevious,
		"显示之前容器实例的日志")
	logsCmd.Flags().StringVar(&sinceTime, "since", "",
		"显示指定时间之后的日志 (例如: 2h, 1h30m)")
}

// runLogsCommand 执行 logs 命令的主要逻辑
func runLogsCommand(cmd *cobra.Command, args []string) error {
	podName := args[0]

	// 创建 Kubernetes 客户端
	clientset, err := k8s.NewClient(kubeconfig)
	if err != nil {
		return fmt.Errorf("创建 Kubernetes 客户端失败: %w", err)
	}

	ctx := context.Background()

	// 获取 Pod 信息
	pod, err := clientset.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取 Pod 信息失败: %w", err)
	}

	// 如果没有指定容器且 Pod 有多个容器，列出所有容器
	if containerName == "" && len(pod.Spec.Containers) > 1 {
		common.PrintWarning(fmt.Sprintf("Pod %s 包含多个容器，请使用 -c 参数指定容器：", podName))
		for _, c := range pod.Spec.Containers {
			fmt.Printf("  - %s\n", c.Name)
		}
		return nil
	}

	// 如果没有指定容器，使用第一个容器
	if containerName == "" {
		containerName = pod.Spec.Containers[0].Name
	}

	if verbose {
		common.PrintInfo(fmt.Sprintf("使用容器: %s", containerName))
		common.PrintInfo(fmt.Sprintf("日志选项: 跟踪=%v, 时间戳=%v, 行数=%d", follow, timestamps, tailLines))
	}

	// 验证容器是否存在
	if !containerExists(pod, containerName) {
		return fmt.Errorf("容器 '%s' 在 Pod '%s' 中不存在", containerName, podName)
	}

	// 构建日志选项
	logOptions := buildLogOptions()

	// 获取日志
	if follow {
		return streamLogs(ctx, clientset, namespace, podName, containerName, logOptions)
	} else {
		return getLogs(ctx, clientset, namespace, podName, containerName, logOptions)
	}
}

// containerExists 检查容器是否存在于 Pod 中
func containerExists(pod *v1.Pod, containerName string) bool {
	for _, c := range pod.Spec.Containers {
		if c.Name == containerName {
			return true
		}
	}
	for _, c := range pod.Spec.InitContainers {
		if c.Name == containerName {
			return true
		}
	}
	return false
}

// buildLogOptions 构建日志选项
func buildLogOptions() *v1.PodLogOptions {
	options := &v1.PodLogOptions{
		Container:  containerName,
		Follow:     follow,
		Timestamps: timestamps,
		Previous:   previous,
	}

	if tailLines >= 0 {
		options.TailLines = &tailLines
	}

	if sinceTime != "" {
		if duration, err := time.ParseDuration(sinceTime); err == nil {
			sinceSeconds := int64(duration.Seconds())
			options.SinceSeconds = &sinceSeconds
		}
	}

	return options
}

// getLogs 获取一次性日志
func getLogs(ctx context.Context, clientset *kubernetes.Clientset,
	namespace, podName, container string, options *v1.PodLogOptions) error {

	req := clientset.CoreV1().Pods(namespace).GetLogs(podName, options)

	logs, err := req.Stream(ctx)
	if err != nil {
		return fmt.Errorf("获取日志流失败: %w", err)
	}
	defer logs.Close()

	// 读取并输出日志
	_, err = io.Copy(os.Stdout, logs)
	if err != nil {
		return fmt.Errorf("读取日志失败: %w", err)
	}

	return nil
}

// streamLogs 流式获取日志
func streamLogs(ctx context.Context, clientset *kubernetes.Clientset,
	namespace, podName, container string, options *v1.PodLogOptions) error {

	// 设置信号处理，允许用户通过 Ctrl+C 退出
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 创建可取消的上下文
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// 启动信号处理 goroutine
	go func() {
		<-sigChan
		fmt.Println()
		common.PrintInfo("收到退出信号，正在停止日志跟踪...")
		cancel()
	}()

	fmt.Printf("开始跟踪 Pod %s/%s 容器 %s 的日志 (按 Ctrl+C 退出):\n",
		namespace, podName, containerName)
	fmt.Println(strings.Repeat("-", 60))

	for {
		select {
		case <-ctx.Done():
			return nil
		default:
			// 获取日志流
			req := clientset.CoreV1().Pods(namespace).GetLogs(podName, options)
			logs, err := req.Stream(ctx)
			if err != nil {
				if ctx.Err() != nil {
					return nil // 上下文已取消
				}
				fmt.Printf("获取日志流失败: %v，5秒后重试...\n", err)
				time.Sleep(5 * time.Second)
				continue
			}

			// 读取日志
			scanner := bufio.NewScanner(logs)
			for scanner.Scan() {
				select {
				case <-ctx.Done():
					logs.Close()
					return nil
				default:
					fmt.Println(scanner.Text())
				}
			}

			logs.Close()

			// 检查扫描错误
			if err := scanner.Err(); err != nil {
				if ctx.Err() != nil {
					return nil // 上下文已取消
				}
				fmt.Printf("读取日志时发生错误: %v\n", err)
			}

			// 如果不是跟踪模式，退出
			if !follow {
				return nil
			}

			// 短暂等待后重新连接
			select {
			case <-ctx.Done():
				return nil
			case <-time.After(1 * time.Second):
				// 继续循环
			}
		}
	}
}
