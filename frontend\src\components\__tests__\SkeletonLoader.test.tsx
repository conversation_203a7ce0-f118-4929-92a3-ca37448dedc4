import { describe, it, expect } from 'vitest';
import { screen } from '@testing-library/react';
import { 
  SkeletonLoader, 
  CardSkeleton, 
  ListSkeleton, 
  TableSkeleton,
  ChartSkeleton,
  FormSkeleton,
  DashboardSkeleton,
  DetailSkeleton
} from '../SkeletonLoader';
import { renderWithProviders } from '@/test/utils';

describe('SkeletonLoader', () => {
  it('应该在加载状态下显示骨架屏', () => {
    renderWithProviders(
      <SkeletonLoader loading={true} type="card" />
    );

    // Ant Design的Skeleton组件会渲染特定的类名
    const skeleton = document.querySelector('.ant-skeleton');
    expect(skeleton).toBeInTheDocument();
  });

  it('应该在非加载状态下显示子组件', () => {
    renderWithProviders(
      <SkeletonLoader loading={false} type="card">
        <div data-testid="child-content">实际内容</div>
      </SkeletonLoader>
    );

    expect(screen.getByTestId('child-content')).toBeInTheDocument();
    expect(screen.getByText('实际内容')).toBeInTheDocument();
    
    // 不应该显示骨架屏
    const skeleton = document.querySelector('.ant-skeleton');
    expect(skeleton).not.toBeInTheDocument();
  });

  it('应该支持不同的骨架屏类型', () => {
    const { rerender } = renderWithProviders(
      <SkeletonLoader loading={true} type="card" />
    );

    let skeleton = document.querySelector('.ant-skeleton');
    expect(skeleton).toBeInTheDocument();

    // 测试列表类型
    rerender(
      <SkeletonLoader loading={true} type="list" rows={3} />
    );

    skeleton = document.querySelector('.ant-skeleton');
    expect(skeleton).toBeInTheDocument();

    // 测试表格类型
    rerender(
      <SkeletonLoader loading={true} type="table" />
    );

    skeleton = document.querySelector('.ant-skeleton');
    expect(skeleton).toBeInTheDocument();
  });

  it('应该支持自定义行数', () => {
    renderWithProviders(
      <SkeletonLoader loading={true} type="card" rows={5} />
    );

    const skeleton = document.querySelector('.ant-skeleton');
    expect(skeleton).toBeInTheDocument();
  });

  it('应该支持图表类型骨架屏', () => {
    renderWithProviders(
      <SkeletonLoader loading={true} type="chart" />
    );

    expect(screen.getByText('图表加载中...')).toBeInTheDocument();
  });

  it('应该支持表单类型骨架屏', () => {
    renderWithProviders(
      <SkeletonLoader loading={true} type="form" rows={3} />
    );

    const skeleton = document.querySelector('.ant-skeleton');
    expect(skeleton).toBeInTheDocument();
  });

  it('应该支持仪表板类型骨架屏', () => {
    renderWithProviders(
      <SkeletonLoader loading={true} type="dashboard" />
    );

    expect(screen.getByText('图表加载中...')).toBeInTheDocument();
    const skeletons = document.querySelectorAll('.ant-skeleton');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('应该支持详情页类型骨架屏', () => {
    renderWithProviders(
      <SkeletonLoader loading={true} type="detail" />
    );

    const skeletons = document.querySelectorAll('.ant-skeleton');
    expect(skeletons.length).toBeGreaterThan(0);
  });
});

describe('预定义骨架屏组件', () => {
  it('CardSkeleton应该正确渲染', () => {
    renderWithProviders(
      <CardSkeleton loading={true} />
    );

    const skeleton = document.querySelector('.ant-skeleton');
    expect(skeleton).toBeInTheDocument();
  });

  it('ListSkeleton应该正确渲染', () => {
    renderWithProviders(
      <ListSkeleton loading={true} rows={3} />
    );

    const skeletons = document.querySelectorAll('.ant-skeleton');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('TableSkeleton应该正确渲染', () => {
    renderWithProviders(
      <TableSkeleton loading={true} />
    );

    const skeleton = document.querySelector('.ant-skeleton');
    expect(skeleton).toBeInTheDocument();
  });

  it('ChartSkeleton应该正确渲染', () => {
    renderWithProviders(
      <ChartSkeleton loading={true} />
    );

    expect(screen.getByText('图表加载中...')).toBeInTheDocument();
  });

  it('FormSkeleton应该正确渲染', () => {
    renderWithProviders(
      <FormSkeleton loading={true} rows={4} />
    );

    const skeleton = document.querySelector('.ant-skeleton');
    expect(skeleton).toBeInTheDocument();
  });

  it('DashboardSkeleton应该正确渲染', () => {
    renderWithProviders(
      <DashboardSkeleton loading={true} />
    );

    expect(screen.getByText('图表加载中...')).toBeInTheDocument();
    const skeletons = document.querySelectorAll('.ant-skeleton');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('DetailSkeleton应该正确渲染', () => {
    renderWithProviders(
      <DetailSkeleton loading={true} />
    );

    const skeletons = document.querySelectorAll('.ant-skeleton');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('预定义组件应该支持children', () => {
    renderWithProviders(
      <CardSkeleton loading={false}>
        <div data-testid="card-content">卡片内容</div>
      </CardSkeleton>
    );

    expect(screen.getByTestId('card-content')).toBeInTheDocument();
    expect(screen.getByText('卡片内容')).toBeInTheDocument();
  });

  it('预定义组件应该支持自定义样式', () => {
    renderWithProviders(
      <CardSkeleton 
        loading={true} 
        className="custom-skeleton"
        style={{ backgroundColor: 'red' }}
      />
    );

    const skeletonContainer = document.querySelector('.custom-skeleton');
    expect(skeletonContainer).toBeInTheDocument();
  });
});
