import { apiClient } from './api';

/**
 * 监控告警服务类
 * 
 * 提供监控和告警相关的API操作：
 * - 系统指标查询
 * - 性能监控
 * - 告警管理
 * - 健康检查
 */
export class MonitoringService {
  private readonly basePath = '/monitoring';

  // ============ 指标查询 ============

  /**
   * 获取系统指标
   * @returns 系统指标数据
   */
  async getMetrics(params?: { timeRange?: string }): Promise<any> {
    try {
      const queryParams = params?.timeRange ? `?timeRange=${params.timeRange}` : '';
      const result = await apiClient.get<any>(`${this.basePath}/metrics${queryParams}`);

      // 返回模拟数据结构以支持图表显示
      return {
        cpu: Array.isArray(result?.cpu) ? result.cpu : [],
        memory: Array.isArray(result?.memory) ? result.memory : [],
        network: Array.isArray(result?.network) ? result.network : [],
        disk: Array.isArray(result?.disk) ? result.disk : [],
        timestamp: result?.timestamp || new Date().toISOString()
      };
    } catch (error) {
      console.warn('获取监控指标失败，返回空数据:', error);
      return {
        cpu: [],
        memory: [],
        network: [],
        disk: [],
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 获取健康状态
   * @returns 健康状态信息
   */
  async getHealth(): Promise<HealthStatus> {
    try {
      return await apiClient.get<HealthStatus>(`${this.basePath}/health`);
    } catch (error) {
      console.warn('获取健康状态失败，返回默认状态:', error);
      return {
        overall: 'warning',
        components: [],
        last_check: new Date().toISOString()
      };
    }
  }

  /**
   * 获取性能数据
   * @returns 性能数据
   */
  async getPerformance(): Promise<PerformanceData> {
    try {
      return await apiClient.get<PerformanceData>(`${this.basePath}/performance`);
    } catch (error) {
      console.warn('获取性能数据失败，返回默认数据:', error);
      return {
        response_times: { avg: 0, p50: 0, p95: 0, p99: 0 },
        throughput: { requests_per_second: 0, operations_per_second: 0 },
        error_rates: { total_errors: 0, error_percentage: 0 },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 获取操作统计
   * @returns 操作统计数据
   */
  async getOperations(): Promise<OperationStats> {
    return apiClient.get<OperationStats>(`${this.basePath}/operations`);
  }

  /**
   * 获取系统指标
   * @returns 系统指标数据
   */
  async getSystemMetrics(): Promise<SystemMetrics> {
    return apiClient.get<SystemMetrics>(`${this.basePath}/system`);
  }

  // ============ 告警管理 ============

  /**
   * 获取告警列表
   * @returns 告警列表
   */
  async getAlerts(): Promise<Alert[]> {
    try {
      const result = await apiClient.get<Alert[]>(`${this.basePath}/alerts`);
      // 确保返回的是数组
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.warn('获取告警列表失败，返回空数组:', error);
      return [];
    }
  }

  /**
   * 创建告警
   * @param alert 告警信息
   * @returns 创建结果
   */
  async createAlert(alert: CreateAlertRequest): Promise<Alert> {
    return apiClient.post<Alert>(`${this.basePath}/alerts`, alert);
  }

  /**
   * 删除告警
   * @param id 告警ID
   * @returns 删除结果
   */
  async deleteAlert(id: string): Promise<{ success: boolean; message: string }> {
    return apiClient.delete<{ success: boolean; message: string }>(`${this.basePath}/alerts/${encodeURIComponent(id)}`);
  }
}

// 系统指标类型
export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    load_average: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage_percentage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage_percentage: number;
  };
  network: {
    bytes_sent: number;
    bytes_received: number;
    packets_sent: number;
    packets_received: number;
  };
  timestamp: string;
}

// 健康状态类型
export interface HealthStatus {
  overall: 'healthy' | 'warning' | 'critical';
  components: ComponentHealth[];
  last_check: string;
}

export interface ComponentHealth {
  name: string;
  status: 'healthy' | 'warning' | 'critical';
  message?: string;
  last_check: string;
}

// 性能数据类型
export interface PerformanceData {
  response_times: {
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  };
  throughput: {
    requests_per_second: number;
    operations_per_second: number;
  };
  error_rates: {
    total_errors: number;
    error_percentage: number;
  };
  timestamp: string;
}

// 操作统计类型
export interface OperationStats {
  total_operations: number;
  successful_operations: number;
  failed_operations: number;
  operations_by_type: Record<string, number>;
  recent_operations: RecentOperation[];
  timestamp: string;
}

export interface RecentOperation {
  id: string;
  type: string;
  status: 'success' | 'failed' | 'in_progress';
  started_at: string;
  completed_at?: string;
  duration?: number;
  error?: string;
}

// 告警类型
export interface Alert {
  id: string;
  name: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'active' | 'resolved' | 'suppressed';
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  source: string;
  tags: string[];
  metadata: Record<string, any>;
}

// 创建告警请求类型
export interface CreateAlertRequest {
  name: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

// 创建并导出监控服务实例
export const monitoringService = new MonitoringService();

export default monitoringService;
