package k8s

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"k8s-helper/pkg/common"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

// NewClient 创建一个新的 Kubernetes 客户端
// 如果 kubeconfig 为空，则尝试使用默认路径或集群内配置
func NewClient(kubeconfig string) (*kubernetes.Clientset, error) {
	var config *rest.Config
	var err error

	if kubeconfig == "" {
		// 尝试使用默认的 kubeconfig 路径
		home, err := os.UserHomeDir()
		if err == nil {
			kubeconfig = filepath.Join(home, ".kube", "config")
		}
	}

	// 检查 kubeconfig 文件是否存在
	if kubeconfig != "" && fileExists(kubeconfig) {
		// 使用 kubeconfig 文件创建配置
		config, err = clientcmd.BuildConfigFromFlags("", kubeconfig)
		if err != nil {
			return nil, fmt.Errorf("从 kubeconfig 文件创建配置失败: %w", err)
		}
	} else {
		// 尝试使用集群内配置（当在 Pod 中运行时）
		config, err = rest.InClusterConfig()
		if err != nil {
			return nil, fmt.Errorf("无法创建 Kubernetes 配置，请确保 kubeconfig 文件存在或在集群内运行: %w", err)
		}
	}

	// 创建客户端
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建 Kubernetes 客户端失败: %w", err)
	}

	return clientset, nil
}

// fileExists 检查文件是否存在
func fileExists(filename string) bool {
	info, err := os.Stat(filename)
	if os.IsNotExist(err) {
		return false
	}
	return !info.IsDir()
}

// GetKubeconfig 获取 kubeconfig 文件路径
// 优先级：命令行参数 > 环境变量 KUBECONFIG > 默认路径
func GetKubeconfig(kubeconfigFlag string) string {
	if kubeconfigFlag != "" {
		return kubeconfigFlag
	}

	// 检查环境变量
	if kubeconfig := os.Getenv("KUBECONFIG"); kubeconfig != "" {
		return kubeconfig
	}

	// 使用默认路径
	home, err := os.UserHomeDir()
	if err != nil {
		return ""
	}

	return filepath.Join(home, ".kube", "config")
}

// GetRestConfig 获取 REST 配置
func GetRestConfig(kubeconfig string) (*rest.Config, error) {
	if kubeconfig == "" {
		kubeconfig = GetKubeconfig("")
	}

	// 检查 kubeconfig 文件是否存在
	if kubeconfig != "" && fileExists(kubeconfig) {
		// 使用 kubeconfig 文件创建配置
		config, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
		if err != nil {
			return nil, fmt.Errorf("从 kubeconfig 文件创建配置失败: %w", err)
		}
		return config, nil
	}

	// 尝试使用集群内配置（当在 Pod 中运行时）
	config, err := rest.InClusterConfig()
	if err != nil {
		return nil, fmt.Errorf("无法创建 Kubernetes 配置，请确保 kubeconfig 文件存在或在集群内运行: %w", err)
	}

	return config, nil
}

// VersionInfo Kubernetes 版本信息
type VersionInfo struct {
	Major      int    // 主版本号
	Minor      int    // 次版本号
	GitVersion string // 完整版本字符串
}

// GetKubernetesVersion 获取 Kubernetes 集群版本信息
func GetKubernetesVersion(kubeconfig string) (*VersionInfo, error) {
	clientset, err := NewClient(kubeconfig)
	if err != nil {
		return nil, fmt.Errorf("创建 Kubernetes 客户端失败: %w", err)
	}

	version, err := clientset.Discovery().ServerVersion()
	if err != nil {
		return nil, fmt.Errorf("获取 Kubernetes 版本失败: %w", err)
	}

	// 解析主版本号
	major, err := strconv.Atoi(version.Major)
	if err != nil {
		return nil, fmt.Errorf("解析主版本号失败: %w", err)
	}

	// 解析次版本号（去除可能的后缀，如 "24+"）
	minorStr := strings.TrimSuffix(version.Minor, "+")
	minor, err := strconv.Atoi(minorStr)
	if err != nil {
		return nil, fmt.Errorf("解析次版本号失败: %w", err)
	}

	return &VersionInfo{
		Major:      major,
		Minor:      minor,
		GitVersion: version.GitVersion,
	}, nil
}

// SupportsTimeZone 检查 Kubernetes 版本是否支持 CronJob timeZone 字段
func (v *VersionInfo) SupportsTimeZone() bool {
	// timeZone 字段在 Kubernetes 1.24+ 中支持
	if v.Major > common.TimeZoneSupportMajor {
		return true
	}
	if v.Major == common.TimeZoneSupportMajor && v.Minor >= common.TimeZoneSupportMinor {
		return true
	}
	return false
}

// String 返回版本信息的字符串表示
func (v *VersionInfo) String() string {
	return fmt.Sprintf("v%d.%d", v.Major, v.Minor)
}
