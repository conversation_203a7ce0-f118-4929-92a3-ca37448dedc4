import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { DataTable } from '../DataTable';
import { renderWithProviders, mockKubernetesData } from '@/test/utils';

// 模拟数据
const mockColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '命名空间',
    dataIndex: 'namespace',
    key: 'namespace',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
];

const mockData = mockKubernetesData.pods;

describe('DataTable', () => {
  const mockOnRefresh = vi.fn();
  const mockOnExport = vi.fn();
  const mockOnSearch = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确渲染表格', () => {
    renderWithProviders(
      <DataTable
        columns={mockColumns}
        data={mockData}
        title="测试表格"
      />
    );

    expect(screen.getByText('测试表格')).toBeInTheDocument();
    expect(screen.getByText('test-pod-1')).toBeInTheDocument();
    expect(screen.getByText('test-pod-2')).toBeInTheDocument();
  });

  it('应该显示加载状态', () => {
    renderWithProviders(
      <DataTable
        columns={mockColumns}
        data={[]}
        loading={true}
        title="加载中的表格"
      />
    );

    expect(screen.getByText('加载中的表格')).toBeInTheDocument();
    // Ant Design的加载状态通过CSS类名控制，这里检查表格容器
    const table = screen.getByRole('table');
    expect(table).toBeInTheDocument();
  });

  it('应该支持搜索功能', async () => {
    const user = userEvent.setup();
    
    renderWithProviders(
      <DataTable
        columns={mockColumns}
        data={mockData}
        searchable={true}
        onSearch={mockOnSearch}
      />
    );

    const searchInput = screen.getByPlaceholderText('搜索...');
    expect(searchInput).toBeInTheDocument();

    await user.type(searchInput, 'test-pod-1');
    
    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith('test-pod-1');
    });
  });

  it('应该支持刷新功能', async () => {
    const user = userEvent.setup();
    
    renderWithProviders(
      <DataTable
        columns={mockColumns}
        data={mockData}
        refreshable={true}
        onRefresh={mockOnRefresh}
      />
    );

    const refreshButton = screen.getByRole('button', { name: /刷新/i });
    expect(refreshButton).toBeInTheDocument();

    await user.click(refreshButton);
    
    expect(mockOnRefresh).toHaveBeenCalledTimes(1);
  });

  it('应该支持导出功能', async () => {
    const user = userEvent.setup();
    
    renderWithProviders(
      <DataTable
        columns={mockColumns}
        data={mockData}
        exportable={true}
        onExport={mockOnExport}
      />
    );

    const exportButton = screen.getByRole('button', { name: /导出/i });
    expect(exportButton).toBeInTheDocument();

    await user.click(exportButton);
    
    // 检查下拉菜单是否出现
    await waitFor(() => {
      expect(screen.getByText('CSV格式')).toBeInTheDocument();
      expect(screen.getByText('Excel格式')).toBeInTheDocument();
      expect(screen.getByText('JSON格式')).toBeInTheDocument();
    });

    // 点击CSV导出
    await user.click(screen.getByText('CSV格式'));
    
    expect(mockOnExport).toHaveBeenCalledWith('csv');
  });

  it('应该正确处理空数据', () => {
    renderWithProviders(
      <DataTable
        columns={mockColumns}
        data={[]}
        title="空数据表格"
      />
    );

    expect(screen.getByText('空数据表格')).toBeInTheDocument();
    // Ant Design表格在空数据时会显示"暂无数据"
    expect(screen.getByText('暂无数据')).toBeInTheDocument();
  });

  it('应该支持分页功能', () => {
    const largeData = Array.from({ length: 25 }, (_, i) => ({
      id: `pod-${i}`,
      name: `test-pod-${i}`,
      namespace: 'default',
      status: 'Running',
    }));

    renderWithProviders(
      <DataTable
        columns={mockColumns}
        data={largeData}
        pageSize={10}
      />
    );

    // 检查分页器是否存在
    const pagination = screen.getByRole('navigation');
    expect(pagination).toBeInTheDocument();
    
    // 检查页码
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument(); // 25条数据，每页10条，共3页
  });

  it('应该支持行选择', async () => {
    const user = userEvent.setup();
    
    renderWithProviders(
      <DataTable
        columns={mockColumns}
        data={mockData}
        rowSelection={{
          onChange: vi.fn(),
        }}
      />
    );

    // 检查全选复选框
    const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i });
    expect(selectAllCheckbox).toBeInTheDocument();

    // 检查行复选框
    const rowCheckboxes = screen.getAllByRole('checkbox');
    expect(rowCheckboxes).toHaveLength(3); // 1个全选 + 2个行选择

    // 点击第一行的复选框
    await user.click(rowCheckboxes[1]);
    
    // 验证复选框被选中
    expect(rowCheckboxes[1]).toBeChecked();
  });

  it('应该支持自定义渲染', () => {
    const customColumns = [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        render: (text: string) => <span data-testid="custom-name">{text}</span>,
      },
    ];

    renderWithProviders(
      <DataTable
        columns={customColumns}
        data={mockData}
      />
    );

    expect(screen.getByTestId('custom-name')).toBeInTheDocument();
    expect(screen.getByTestId('custom-name')).toHaveTextContent('test-pod-1');
  });

  it('应该正确处理错误状态', () => {
    // 模拟控制台错误，避免测试输出中的错误信息
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    renderWithProviders(
      <DataTable
        columns={mockColumns}
        data={mockData}
        title="错误测试表格"
      />
    );

    // 恢复控制台
    consoleSpy.mockRestore();
  });

  it('应该支持键盘快捷键', () => {
    renderWithProviders(
      <DataTable
        columns={mockColumns}
        data={mockData}
        keyboardShortcuts={true}
        onRefresh={mockOnRefresh}
      />
    );

    // 模拟F5按键
    fireEvent.keyDown(document, { key: 'F5', code: 'F5' });
    
    expect(mockOnRefresh).toHaveBeenCalledTimes(1);
  });
});
