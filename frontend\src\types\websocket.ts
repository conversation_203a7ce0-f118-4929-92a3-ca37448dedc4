// WebSocket消息类型定义

// WebSocket连接状态
export type WebSocketReadyState = 'CONNECTING' | 'OPEN' | 'CLOSING' | 'CLOSED';

// WebSocket连接类型
export type WebSocketType = 'logs' | 'status' | 'monitoring' | 'cleanup' | 'config' | 'port-forward';

// 基础消息接口
export interface BaseWebSocketMessage {
  id: string;
  type: string;
  timestamp: number;
  source?: string;
}

// 请求消息
export interface WebSocketRequest extends BaseWebSocketMessage {
  action: string;
  data?: any;
}

// 响应消息
export interface WebSocketResponse extends BaseWebSocketMessage {
  success: boolean;
  data?: any;
  error?: string;
}

// 事件消息
export interface WebSocketEvent extends BaseWebSocketMessage {
  event: string;
  data: any;
}

// 心跳消息
export interface HeartbeatMessage extends BaseWebSocketMessage {
  type: 'heartbeat';
  action: 'ping' | 'pong';
}

// 日志相关消息
export interface LogMessage extends BaseWebSocketMessage {
  type: 'log';
  namespace: string;
  pod: string;
  container?: string;
  content: string;
  level?: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL';
}

// 状态更新消息
export interface StatusUpdateMessage extends BaseWebSocketMessage {
  type: 'status_update';
  resource_type: 'pod' | 'node' | 'service' | 'deployment';
  resource_name: string;
  namespace?: string;
  status: any;
}

// 监控指标消息
export interface MetricsMessage extends BaseWebSocketMessage {
  type: 'metrics';
  metrics: {
    cpu: number;
    memory: number;
    disk: number;
    network: {
      bytes_sent: number;
      bytes_received: number;
    };
  };
}

// 告警消息
export interface AlertMessage extends BaseWebSocketMessage {
  type: 'alert';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  source: string;
  metadata?: Record<string, any>;
}

// 端口转发状态消息
export interface PortForwardStatusMessage extends BaseWebSocketMessage {
  type: 'port_forward_status';
  forward_id: string;
  status: 'active' | 'stopped' | 'error';
  local_port: number;
  remote_port: number;
  error_message?: string;
}

// 清理进度消息
export interface CleanupProgressMessage extends BaseWebSocketMessage {
  type: 'cleanup_progress';
  scan_id: string;
  progress: number;
  current_resource: string;
  total_resources: number;
  completed_resources: number;
}

// 配置变更消息
export interface ConfigChangeMessage extends BaseWebSocketMessage {
  type: 'config_change';
  section: string;
  changes: Record<string, any>;
  user: string;
}

// 联合消息类型
export type WebSocketMessage = 
  | WebSocketRequest
  | WebSocketResponse
  | WebSocketEvent
  | HeartbeatMessage
  | LogMessage
  | StatusUpdateMessage
  | MetricsMessage
  | AlertMessage
  | PortForwardStatusMessage
  | CleanupProgressMessage
  | ConfigChangeMessage;

// WebSocket连接配置
export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  messageQueueSize?: number;
  timeout?: number;
}

// WebSocket连接选项
export interface WebSocketOptions {
  onOpen?: (event: Event) => void;
  onClose?: (event: CloseEvent) => void;
  onError?: (event: Event) => void;
  onMessage?: (message: WebSocketMessage) => void;
  onReconnect?: (attempt: number) => void;
  onReconnectFailed?: () => void;
}

// WebSocket连接状态
export interface WebSocketConnectionState {
  readyState: WebSocketReadyState;
  url: string;
  connected: boolean;
  connecting: boolean;
  reconnecting: boolean;
  reconnectAttempts: number;
  lastConnected?: number;
  lastDisconnected?: number;
  lastError?: string;
  messageQueue: WebSocketMessage[];
  heartbeatInterval?: NodeJS.Timeout;
}

// WebSocket Hook返回类型
export interface UseWebSocketReturn {
  // 连接状态
  readyState: WebSocketReadyState;
  connected: boolean;
  connecting: boolean;
  reconnecting: boolean;
  
  // 连接信息
  url: string;
  reconnectAttempts: number;
  lastError?: string;
  
  // 操作方法
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;
  sendMessage: (message: WebSocketMessage) => boolean;
  
  // 消息队列
  messageQueue: WebSocketMessage[];
  clearMessageQueue: () => void;
  
  // 事件处理
  addEventListener: (type: string, listener: (message: WebSocketMessage) => void) => void;
  removeEventListener: (type: string, listener: (message: WebSocketMessage) => void) => void;
}

// WebSocket管理器接口
export interface WebSocketManager {
  // 连接管理
  createConnection: (type: WebSocketType, config: WebSocketConfig, options?: WebSocketOptions) => string;
  getConnection: (id: string) => WebSocketConnectionState | null;
  removeConnection: (id: string) => void;
  
  // 批量操作
  connectAll: () => void;
  disconnectAll: () => void;
  
  // 消息处理
  broadcast: (message: WebSocketMessage, excludeTypes?: WebSocketType[]) => void;
  sendToConnection: (id: string, message: WebSocketMessage) => boolean;
  
  // 状态查询
  getActiveConnections: () => string[];
  getConnectionsByType: (type: WebSocketType) => string[];
  isConnected: (id: string) => boolean;
  
  // 事件监听
  on: (event: string, listener: (...args: any[]) => void) => void;
  off: (event: string, listener: (...args: any[]) => void) => void;
  emit: (event: string, ...args: any[]) => void;
  
  // 清理
  destroy: () => void;
}
