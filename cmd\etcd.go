package cmd

import (
	"time"

	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"k8s-helper/internal/container"
	"k8s-helper/internal/handler"
	"k8s-helper/pkg/cmdhelp"
	"k8s-helper/pkg/common"
)

var (
	// 全局容器实例
	appContainer *container.Container
	etcdHandler  *handler.ETCDHandler
)

// initETCDContainer 初始化ETCD容器
func initETCDContainer() error {
	if appContainer != nil {
		return nil // 已经初始化
	}

	// 根据verbose标志创建适当的logger
	var logger *zap.Logger
	var err error

	if verbose {
		// 详细模式使用开发配置
		logger, err = zap.NewDevelopment()
	} else {
		// 正常模式使用生产配置
		logger, err = zap.NewProduction()
	}

	if err != nil {
		return err
	}

	// 创建容器
	appContainer = container.NewContainer(logger)

	// 初始化容器
	if err := appContainer.Initialize(); err != nil {
		return err
	}

	// 创建处理器
	etcdHandler = handler.NewETCDHandler(appContainer)

	return nil
}

// etcdCmd ETCD命令根命令
var etcdCmd = cmdhelp.EtcdHelp.BuildCommand(nil, nil)

func init() {
	// 设置PersistentPreRunE
	etcdCmd.PersistentPreRunE = func(cmd *cobra.Command, args []string) error {
		return initETCDContainer()
	}
}

// 包装函数，用于在运行时调用etcdHandler
func runETCDBackup(cmd *cobra.Command, args []string) error {
	return etcdHandler.HandleBackup(cmd, args)
}

func runETCDRestore(cmd *cobra.Command, args []string) error {
	return etcdHandler.HandleRestore(cmd, args)
}

func runETCDVerify(cmd *cobra.Command, args []string) error {
	return etcdHandler.HandleVerify(cmd, args)
}

func runETCDCronJobCreate(cmd *cobra.Command, args []string) error {
	return etcdHandler.HandleCronJobCreate(cmd, args)
}

func runETCDCronJobList(cmd *cobra.Command, args []string) error {
	return etcdHandler.HandleCronJobList(cmd, args)
}

func runETCDCronJobSuspend(cmd *cobra.Command, args []string) error {
	return etcdHandler.HandleCronJobSuspend(cmd, args)
}

func runETCDCronJobResume(cmd *cobra.Command, args []string) error {
	return etcdHandler.HandleCronJobResume(cmd, args)
}

func runETCDCronJobDelete(cmd *cobra.Command, args []string) error {
	return etcdHandler.HandleCronJobDelete(cmd, args)
}

// etcdBackupCmd ETCD备份命令
var etcdBackupCmd = cmdhelp.EtcdBackupHelp.BuildCommand(runETCDBackup, nil)

// etcdRestoreCmd ETCD恢复命令
var etcdRestoreCmd = cmdhelp.EtcdRestoreHelp.BuildCommand(runETCDRestore, cmdhelp.EtcdRestoreErrorConfig)

// etcdVerifyCmd ETCD验证命令
var etcdVerifyCmd = cmdhelp.EtcdVerifyHelp.BuildCommand(runETCDVerify, cmdhelp.EtcdVerifyErrorConfig)

// etcdCronJobCmd ETCD定时任务命令
var etcdCronJobCmd = cmdhelp.EtcdCronJobHelp.BuildCommand(nil, nil)

// etcdCronJobCreateCmd 创建定时任务命令
var etcdCronJobCreateCmd = cmdhelp.EtcdCronJobCreateHelp.BuildCommand(runETCDCronJobCreate, cmdhelp.EtcdCronJobCreateErrorConfig)

// etcdCronJobListCmd 列出定时任务命令
var etcdCronJobListCmd = cmdhelp.EtcdCronJobListHelp.BuildCommand(runETCDCronJobList, cmdhelp.EtcdCronJobListErrorConfig)

// etcdCronJobSuspendCmd 暂停定时任务命令
var etcdCronJobSuspendCmd = cmdhelp.EtcdCronJobSuspendHelp.BuildCommand(runETCDCronJobSuspend, cmdhelp.EtcdCronJobSuspendErrorConfig)

// etcdCronJobResumeCmd 恢复定时任务命令
var etcdCronJobResumeCmd = cmdhelp.EtcdCronJobResumeHelp.BuildCommand(runETCDCronJobResume, cmdhelp.EtcdCronJobResumeErrorConfig)

// etcdCronJobDeleteCmd 删除定时任务命令
var etcdCronJobDeleteCmd = cmdhelp.EtcdCronJobDeleteHelp.BuildCommand(runETCDCronJobDelete, cmdhelp.EtcdCronJobDeleteErrorConfig)

func init() {
	// 注册子命令
	etcdCmd.AddCommand(etcdBackupCmd)
	etcdCmd.AddCommand(etcdRestoreCmd)
	etcdCmd.AddCommand(etcdVerifyCmd)
	etcdCmd.AddCommand(etcdCronJobCmd)

	// 注册CronJob子命令
	etcdCronJobCmd.AddCommand(etcdCronJobCreateCmd)
	etcdCronJobCmd.AddCommand(etcdCronJobListCmd)
	etcdCronJobCmd.AddCommand(etcdCronJobSuspendCmd)
	etcdCronJobCmd.AddCommand(etcdCronJobResumeCmd)
	etcdCronJobCmd.AddCommand(etcdCronJobDeleteCmd)

	// 备份命令参数
	etcdBackupCmd.Flags().StringP("output", "o", "", "备份文件输出路径")
	etcdBackupCmd.Flags().String("apiserver-manifest", common.DefaultAPIServerManifest, "kube-apiserver manifest文件路径")
	etcdBackupCmd.Flags().Bool("use-sdk", true, "优先使用SDK进行备份")
	etcdBackupCmd.Flags().Bool("fallback-to-tool", true, "SDK失败时回退到工具")
	etcdBackupCmd.Flags().Duration("sdk-timeout", 5*time.Minute, "SDK操作超时时间")

	// ETCD工具下载配置参数
	etcdBackupCmd.Flags().String("base-url", common.DefaultEtcdBaseURL, "ETCD工具下载基础URL")
	etcdBackupCmd.Flags().String("etcd-version", common.DefaultEtcdVersion, "ETCD工具版本")
	etcdBackupCmd.Flags().String("etcd-os", common.DefaultEtcdOS, "ETCD工具操作系统")

	// 恢复命令参数
	etcdRestoreCmd.Flags().StringP("snapshot", "s", "", "快照文件路径 (必需)")
	etcdRestoreCmd.Flags().String("data-dir", "", "恢复数据目录路径")
	etcdRestoreCmd.Flags().Bool("use-sdk-restore", true, "优先使用SDK进行恢复")
	etcdRestoreCmd.Flags().Bool("fallback-to-tool", true, "SDK失败时回退到工具")
	etcdRestoreCmd.Flags().Duration("restore-timeout", 10*time.Minute, "恢复操作超时时间")
	etcdRestoreCmd.Flags().Bool("skip-hash-check", false, "跳过哈希检查")
	etcdRestoreCmd.Flags().Bool("mark-compacted", false, "标记为已压缩")
	etcdRestoreCmd.Flags().String("name", "default", "节点名称")
	etcdRestoreCmd.Flags().String("initial-advertise-peer-urls", "http://localhost:2380", "初始对等URL")

	// 标记必需参数
	etcdRestoreCmd.MarkFlagRequired("snapshot")

	// 验证命令参数
	etcdVerifyCmd.Flags().StringP("snapshot", "s", "", "快照文件路径 (必需)")
	etcdVerifyCmd.Flags().Bool("use-sdk-verify", true, "优先使用SDK进行验证")
	etcdVerifyCmd.Flags().Bool("fallback-to-tool", true, "SDK失败时回退到工具")
	etcdVerifyCmd.Flags().Duration("verify-timeout", 2*time.Minute, "验证操作超时时间")
	etcdVerifyCmd.Flags().Bool("detailed-verify", false, "详细验证模式")

	// 标记必需参数
	etcdVerifyCmd.MarkFlagRequired("snapshot")

	// CronJob创建命令参数
	etcdCronJobCreateCmd.Flags().String("name", common.DefaultCronJobName, "CronJob名称")
	etcdCronJobCreateCmd.Flags().String("namespace", common.DefaultCronJobNamespace, "CronJob命名空间")
	etcdCronJobCreateCmd.Flags().String("schedule", common.DefaultCronJobSchedule, "Cron调度表达式")
	etcdCronJobCreateCmd.Flags().String("timezone", common.DefaultCronJobTimezone, "时区设置")
	etcdCronJobCreateCmd.Flags().String("image", common.DefaultCronJobImage, "备份容器镜像")
	etcdCronJobCreateCmd.Flags().Int("backup-retain", common.DefaultBackupRetainCount, "备份保留天数")
	etcdCronJobCreateCmd.Flags().String("apiserver-manifest", common.DefaultAPIServerManifest, "kube-apiserver manifest文件路径")

	// CronJob列表命令参数
	etcdCronJobListCmd.Flags().String("namespace", common.DefaultCronJobNamespace, "CronJob命名空间")
	etcdCronJobListCmd.Flags().Bool("all-namespaces", common.DefaultAllNamespaces, "列出所有命名空间的CronJob")

	// CronJob暂停命令参数
	etcdCronJobSuspendCmd.Flags().String("name", "", "要暂停的CronJob名称")
	etcdCronJobSuspendCmd.Flags().String("namespace", common.DefaultCronJobNamespace, "CronJob命名空间")
	etcdCronJobSuspendCmd.MarkFlagRequired("name")

	// CronJob恢复命令参数
	etcdCronJobResumeCmd.Flags().String("name", "", "要恢复的CronJob名称")
	etcdCronJobResumeCmd.Flags().String("namespace", common.DefaultCronJobNamespace, "CronJob命名空间")
	etcdCronJobResumeCmd.MarkFlagRequired("name")

	// CronJob删除命令参数
	etcdCronJobDeleteCmd.Flags().String("name", "", "要删除的CronJob名称")
	etcdCronJobDeleteCmd.Flags().String("namespace", common.DefaultCronJobNamespace, "CronJob命名空间")
	etcdCronJobDeleteCmd.Flags().Bool("force", common.DefaultDryRun, "强制删除，跳过确认")
	etcdCronJobDeleteCmd.MarkFlagRequired("name")
}
