package web

import (
	"fmt"
	"html/template"
	"io/fs"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// TemplateManager 模板管理器
type TemplateManager struct {
	templates   *template.Template
	logger      *zap.Logger
	templateDir string
	devMode     bool
	funcMap     template.FuncMap
}

// NewTemplateManager 创建模板管理器
func NewTemplateManager(templateDir string, logger *zap.Logger, devMode bool) *TemplateManager {
	tm := &TemplateManager{
		logger:      logger,
		templateDir: templateDir,
		devMode:     devMode,
		funcMap:     createTemplateFuncMap(),
	}

	if err := tm.loadTemplates(); err != nil {
		logger.Error("加载模板失败", zap.Error(err))
	}

	return tm
}

// createTemplateFuncMap 创建模板函数映射
func createTemplateFuncMap() template.FuncMap {
	return template.FuncMap{
		"default": func(defaultValue, value interface{}) interface{} {
			if value == nil || value == "" {
				return defaultValue
			}
			return value
		},
		"formatTime": func(t time.Time) string {
			return t.Format("2006-01-02 15:04:05")
		},
		"formatBytes": func(bytes int64) string {
			const unit = 1024
			if bytes < unit {
				return fmt.Sprintf("%d B", bytes)
			}
			div, exp := int64(unit), 0
			for n := bytes / unit; n >= unit; n /= unit {
				div *= unit
				exp++
			}
			return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
		},
		"statusClass": func(status string) string {
			switch strings.ToLower(status) {
			case "healthy", "success", "completed":
				return "healthy"
			case "unhealthy", "error", "failed":
				return "unhealthy"
			default:
				return "unknown"
			}
		},
		"buildURL": func(base, path string) string {
			return strings.TrimSuffix(base, "/") + "/" + strings.TrimPrefix(path, "/")
		},
		"safeHTML": func(s string) template.HTML {
			return template.HTML(s)
		},
		"year": func() string {
			return time.Now().Format("2006")
		},
	}
}

// loadTemplates 加载所有模板
func (tm *TemplateManager) loadTemplates() error {
	tm.logger.Info("开始加载模板", zap.String("template_dir", tm.templateDir))

	// 创建新的模板实例
	tmpl := template.New("").Funcs(tm.funcMap)

	// 遍历模板目录
	err := filepath.WalkDir(tm.templateDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 只处理.html文件
		if d.IsDir() || !strings.HasSuffix(path, ".html") {
			return nil
		}

		// 读取模板文件
		content, err := os.ReadFile(path)
		if err != nil {
			return fmt.Errorf("读取模板文件失败 %s: %w", path, err)
		}

		// 计算模板名称（相对路径）
		relPath, err := filepath.Rel(tm.templateDir, path)
		if err != nil {
			return fmt.Errorf("计算相对路径失败 %s: %w", path, err)
		}

		// 使用Unix风格的路径分隔符
		templateName := filepath.ToSlash(relPath)

		// 解析模板
		_, err = tmpl.New(templateName).Parse(string(content))
		if err != nil {
			return fmt.Errorf("解析模板失败 %s: %w", templateName, err)
		}

		tm.logger.Debug("加载模板", zap.String("template", templateName))
		return nil
	})

	if err != nil {
		return fmt.Errorf("遍历模板目录失败: %w", err)
	}

	tm.templates = tmpl
	tm.logger.Info("模板加载完成")
	return nil
}

// Render 渲染模板
func (tm *TemplateManager) Render(c *gin.Context, templateName string, data interface{}) {
	// 开发模式下重新加载模板
	if tm.devMode {
		if err := tm.loadTemplates(); err != nil {
			tm.logger.Error("重新加载模板失败", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "模板加载失败"})
			return
		}
	}

	// 检查模板是否存在
	if tm.templates.Lookup(templateName) == nil {
		tm.logger.Error("模板不存在", zap.String("template", templateName))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "模板不存在"})
		return
	}

	// 设置响应头
	c.Header("Content-Type", "text/html; charset=utf-8")

	// 渲染模板
	err := tm.templates.ExecuteTemplate(c.Writer, templateName, data)
	if err != nil {
		tm.logger.Error("渲染模板失败", 
			zap.String("template", templateName),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "模板渲染失败"})
		return
	}

	tm.logger.Debug("模板渲染成功", zap.String("template", templateName))
}

// RenderString 渲染模板为字符串
func (tm *TemplateManager) RenderString(templateName string, data interface{}) (string, error) {
	// 开发模式下重新加载模板
	if tm.devMode {
		if err := tm.loadTemplates(); err != nil {
			return "", fmt.Errorf("重新加载模板失败: %w", err)
		}
	}

	// 检查模板是否存在
	if tm.templates.Lookup(templateName) == nil {
		return "", fmt.Errorf("模板不存在: %s", templateName)
	}

	// 渲染到字符串
	var buf strings.Builder
	err := tm.templates.ExecuteTemplate(&buf, templateName, data)
	if err != nil {
		return "", fmt.Errorf("渲染模板失败: %w", err)
	}

	return buf.String(), nil
}

// GetTemplateNames 获取所有模板名称
func (tm *TemplateManager) GetTemplateNames() []string {
	if tm.templates == nil {
		return nil
	}

	var names []string
	for _, tmpl := range tm.templates.Templates() {
		if tmpl.Name() != "" {
			names = append(names, tmpl.Name())
		}
	}
	return names
}

// IsDevMode 检查是否为开发模式
func (tm *TemplateManager) IsDevMode() bool {
	return tm.devMode
}

// SetDevMode 设置开发模式
func (tm *TemplateManager) SetDevMode(devMode bool) {
	tm.devMode = devMode
	if devMode {
		tm.logger.Info("模板管理器切换到开发模式")
	} else {
		tm.logger.Info("模板管理器切换到生产模式")
	}
}
