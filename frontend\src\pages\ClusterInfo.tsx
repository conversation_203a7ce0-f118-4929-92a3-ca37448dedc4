import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Tabs,
  Space,
  Button,
  Tag,
  Tooltip,
  Progress,
  Descriptions,
  Alert
} from 'antd';
import {
  ReloadOutlined,
  SettingOutlined,
  NodeIndexOutlined,
  DatabaseOutlined,
  MonitorOutlined,
  GlobalOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { DataTable, StatusTag, ActionButtons } from '@/components/DataTable';
import { ResourceChart, MultiMetricChart } from '@/components/Charts/ResourceChart';
import { MetricCard } from '@/components/MetricCard';
import {
  useClusterNodes,
  useClusterPods,
  useClusterServices,
  useClusterMetrics,
  useAppState,
  useNotifications
} from '@/hooks';
import type { ColumnsType } from 'antd/es/table';
import type { Node, Pod, Service } from '@/types/api';

/**
 * 集群信息页面
 *
 * 功能特性：
 * - 集群概览信息
 * - 节点状态管理
 * - Pod资源监控
 * - 服务状态展示
 * - 实时资源图表
 * - 数据导出功能
 */
const ClusterInfo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  const { setPageTitle } = useAppState();
  const { showSuccess, showError } = useNotifications();

  // 数据查询
  const {
    data: nodes = [],
    isLoading: nodesLoading,
    refetch: refetchNodes
  } = useClusterNodes({
    refetchInterval: 30000, // 30秒刷新
  });

  const {
    data: pods = [],
    isLoading: podsLoading,
    refetch: refetchPods
  } = useClusterPods({
    refetchInterval: 15000, // 15秒刷新
  });

  const {
    data: services = [],
    isLoading: servicesLoading,
    refetch: refetchServices
  } = useClusterServices({
    refetchInterval: 30000,
  });

  const {
    data: metrics,
    isLoading: metricsLoading
  } = useClusterMetrics({
    refetchInterval: 5000, // 5秒刷新指标
  });

  // 设置页面标题
  useEffect(() => {
    setPageTitle('集群信息 - K8s-Helper');
  }, [setPageTitle]);

  // 手动刷新所有数据
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        refetchNodes(),
        refetchPods(),
        refetchServices(),
      ]);
      showSuccess('刷新成功', '集群数据已更新');
    } catch (error) {
      showError('刷新失败', '无法获取最新数据');
    } finally {
      setRefreshing(false);
    }
  };

  // 构建概览指标
  const buildOverviewMetrics = () => {
    const readyNodes = nodes.filter(node => node.status === 'Ready').length;
    const runningPods = pods.filter(pod => pod.status === 'Running').length;
    const activeServices = services.filter(service => service.status === 'Active').length;

    return [
      {
        title: '集群节点',
        value: nodes.length,
        prefix: <NodeIndexOutlined />,
        status: readyNodes === nodes.length ? 'healthy' : 'warning',
        description: `${readyNodes}/${nodes.length} 节点就绪`,
      },
      {
        title: '运行Pod',
        value: runningPods,
        prefix: <DatabaseOutlined />,
        status: 'healthy',
        description: `共 ${pods.length} 个Pod`,
        trend: {
          value: 2.5,
          isPositive: true,
        },
      },
      {
        title: '活跃服务',
        value: activeServices,
        prefix: <GlobalOutlined />,
        status: 'healthy',
        description: `共 ${services.length} 个服务`,
      },
      {
        title: '集群健康度',
        value: `${Math.round((readyNodes / Math.max(nodes.length, 1)) * 100)}%`,
        prefix: <MonitorOutlined />,
        status: readyNodes === nodes.length ? 'healthy' : 'warning',
        description: '基于节点就绪状态',
      },
    ];
  };

  // 节点表格列定义
  const nodeColumns: ColumnsType<Node> = [
    {
      title: '节点名称',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 200,
      render: (name: string, record: Node) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.internal_ip}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          'Ready': { status: 'success', text: '就绪' },
          'NotReady': { status: 'error', text: '未就绪' },
          'Unknown': { status: 'warning', text: '未知' },
        };
        const config = statusMap[status as keyof typeof statusMap] || statusMap.Unknown;
        return <StatusTag {...config} />;
      },
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      width: 120,
      render: (roles: string[]) => (
        <div>
          {roles.map(role => (
            <Tag key={role} color={role === 'master' ? 'gold' : 'blue'}>
              {role}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: 'CPU',
      key: 'cpu',
      width: 150,
      render: (_, record: Node) => (
        <div>
          <Progress
            percent={record.cpu.percentage}
            size="small"
            status={record.cpu.percentage > 80 ? 'exception' : 'normal'}
          />
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.cpu.usage} / {record.cpu.capacity}
          </div>
        </div>
      ),
    },
    {
      title: '内存',
      key: 'memory',
      width: 150,
      render: (_, record: Node) => (
        <div>
          <Progress
            percent={record.memory.percentage}
            size="small"
            status={record.memory.percentage > 85 ? 'exception' : 'normal'}
          />
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.memory.usage} / {record.memory.capacity}
          </div>
        </div>
      ),
    },
    {
      title: '运行时间',
      dataIndex: 'age',
      key: 'age',
      width: 100,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 120,
      render: (version: string) => (
        <code style={{ fontSize: '12px' }}>{version}</code>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record: Node) => (
        <ActionButtons
          actions={[
            {
              key: 'edit',
              label: '编辑',
              icon: <EditOutlined />,
              onClick: () => handleNodeEdit(record),
            },
            {
              key: 'drain',
              label: '排空',
              icon: <ExclamationCircleOutlined />,
              danger: true,
              onClick: () => handleNodeDrain(record),
            },
          ]}
        />
      ),
    },
  ];

  // Pod表格列定义
  const podColumns: ColumnsType<Pod> = [
    {
      title: 'Pod名称',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 200,
      render: (name: string, record: Pod) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.namespace}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          'Running': { status: 'success', text: '运行中' },
          'Pending': { status: 'warning', text: '等待中' },
          'Failed': { status: 'error', text: '失败' },
          'Succeeded': { status: 'success', text: '成功' },
          'Unknown': { status: 'warning', text: '未知' },
        };
        const config = statusMap[status as keyof typeof statusMap] || statusMap.Unknown;
        return <StatusTag {...config} />;
      },
    },
    {
      title: '重启次数',
      dataIndex: 'restarts',
      key: 'restarts',
      width: 100,
      render: (restarts: number) => (
        <Tag color={restarts > 0 ? 'orange' : 'green'}>
          {restarts}
        </Tag>
      ),
    },
    {
      title: '节点',
      dataIndex: 'node',
      key: 'node',
      width: 150,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 120,
      render: (ip: string) => (
        <code style={{ fontSize: '12px' }}>{ip}</code>
      ),
    },
    {
      title: '运行时间',
      dataIndex: 'age',
      key: 'age',
      width: 100,
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record: Pod) => (
        <ActionButtons
          actions={[
            {
              key: 'logs',
              label: '日志',
              onClick: () => handlePodLogs(record),
            },
            {
              key: 'exec',
              label: '终端',
              onClick: () => handlePodExec(record),
            },
            {
              key: 'delete',
              label: '删除',
              icon: <DeleteOutlined />,
              danger: true,
              onClick: () => handlePodDelete(record),
            },
          ]}
        />
      ),
    },
  ];

  // 服务表格列定义
  const serviceColumns: ColumnsType<Service> = [
    {
      title: '服务名称',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 200,
      render: (name: string, record: Service) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.namespace}
          </div>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '集群IP',
      dataIndex: 'cluster_ip',
      key: 'cluster_ip',
      width: 150,
      render: (ip: string) => (
        <code style={{ fontSize: '12px' }}>{ip}</code>
      ),
    },
    {
      title: '外部IP',
      dataIndex: 'external_ip',
      key: 'external_ip',
      width: 150,
      render: (ip: string) => (
        ip ? <code style={{ fontSize: '12px' }}>{ip}</code> : <span style={{ color: '#999' }}>无</span>
      ),
    },
    {
      title: '端口',
      dataIndex: 'ports',
      key: 'ports',
      width: 200,
      render: (ports: Array<{ port: number; target_port: number; protocol: string }>) => (
        <div>
          {ports.map((port, index) => (
            <Tag key={index} style={{ marginBottom: '2px' }}>
              {port.port}:{port.target_port}/{port.protocol}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '运行时间',
      dataIndex: 'age',
      key: 'age',
      width: 100,
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record: Service) => (
        <ActionButtons
          actions={[
            {
              key: 'edit',
              label: '编辑',
              icon: <EditOutlined />,
              onClick: () => handleServiceEdit(record),
            },
            {
              key: 'delete',
              label: '删除',
              icon: <DeleteOutlined />,
              danger: true,
              onClick: () => handleServiceDelete(record),
            },
          ]}
        />
      ),
    },
  ];

  // 事件处理函数
  const handleNodeEdit = (node: Node) => {
    showSuccess('编辑节点', `编辑节点: ${node.name}`);
  };

  const handleNodeDrain = (node: Node) => {
    showSuccess('排空节点', `排空节点: ${node.name}`);
  };

  const handlePodLogs = (pod: Pod) => {
    // 跳转到日志页面
    window.open(`/logs?namespace=${pod.namespace}&pod=${pod.name}`, '_blank');
  };

  const handlePodExec = (pod: Pod) => {
    showSuccess('Pod终端', `连接到Pod: ${pod.name}`);
  };

  const handlePodDelete = (pod: Pod) => {
    showSuccess('删除Pod', `删除Pod: ${pod.name}`);
  };

  const handleServiceEdit = (service: Service) => {
    showSuccess('编辑服务', `编辑服务: ${service.name}`);
  };

  const handleServiceDelete = (service: Service) => {
    showSuccess('删除服务', `删除服务: ${service.name}`);
  };

  // Tab配置
  const tabItems = [
    {
      key: 'overview',
      label: '概览',
      children: (
        <div>
          {/* 概览指标 */}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            {buildOverviewMetrics().map((metric, index) => (
              <Col key={index} xs={24} sm={12} md={6}>
                <MetricCard {...metric} />
              </Col>
            ))}
          </Row>

          {/* 资源使用图表 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <ResourceChart
                title="集群CPU使用率"
                data={metrics?.cpu || []}
                loading={metricsLoading}
                color="#1890ff"
                unit="%"
                type="area"
              />
            </Col>
            <Col xs={24} lg={12}>
              <ResourceChart
                title="集群内存使用率"
                data={metrics?.memory || []}
                loading={metricsLoading}
                color="#52c41a"
                unit="%"
                type="area"
              />
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
            <Col xs={24}>
              <MultiMetricChart
                title="网络流量监控"
                metrics={[
                  {
                    name: '入站流量',
                    data: metrics?.network_in || [],
                    color: '#1890ff',
                    unit: 'MB/s',
                  },
                  {
                    name: '出站流量',
                    data: metrics?.network_out || [],
                    color: '#52c41a',
                    unit: 'MB/s',
                  },
                ]}
                loading={metricsLoading}
              />
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: 'nodes',
      label: '节点',
      children: (
        <DataTable
          columns={nodeColumns}
          data={nodes}
          loading={nodesLoading}
          title="集群节点"
          searchPlaceholder="搜索节点..."
          onRefresh={refetchNodes}
          onExport={(format) => {
            showSuccess('导出成功', `节点数据已导出为${format}格式`);
          }}
        />
      ),
    },
    {
      key: 'pods',
      label: 'Pods',
      children: (
        <DataTable
          columns={podColumns}
          data={pods}
          loading={podsLoading}
          title="Pod列表"
          searchPlaceholder="搜索Pod..."
          onRefresh={refetchPods}
          onExport={(format) => {
            showSuccess('导出成功', `Pod数据已导出为${format}格式`);
          }}
        />
      ),
    },
    {
      key: 'services',
      label: '服务',
      children: (
        <DataTable
          columns={serviceColumns}
          data={services}
          loading={servicesLoading}
          title="服务列表"
          searchPlaceholder="搜索服务..."
          onRefresh={refetchServices}
          onExport={(format) => {
            showSuccess('导出成功', `服务数据已导出为${format}格式`);
          }}
        />
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f7fa' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
            集群信息
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            查看和管理Kubernetes集群资源
          </p>
        </div>

        <Space>
          <Button
            icon={<ReloadOutlined />}
            loading={refreshing}
            onClick={handleRefresh}
          >
            刷新
          </Button>
          <Button icon={<SettingOutlined />}>
            设置
          </Button>
        </Space>
      </div>

      {/* 集群状态警告 */}
      {nodes.some(node => node.status !== 'Ready') && (
        <Alert
          message="集群状态异常"
          description="检测到部分节点状态异常，请及时处理"
          type="warning"
          showIcon
          closable
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* Tab内容 */}
      <Card
        style={{
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
        bodyStyle={{ padding: '0' }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          style={{ padding: '0 24px' }}
          tabBarStyle={{ marginBottom: 0 }}
        />
      </Card>
    </div>
  );
};

export default ClusterInfo;
