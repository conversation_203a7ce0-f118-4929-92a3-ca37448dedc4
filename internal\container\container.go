package container

import (
	"sync"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
	"k8s-helper/internal/service"
)

// Container 依赖注入容器
type Container struct {
	logger      *zap.Logger
	services    map[string]interface{}
	mutex       sync.RWMutex
	initialized bool
}

// NewContainer 创建新的容器
func NewContainer(logger *zap.Logger) *Container {
	return &Container{
		logger:   logger,
		services: make(map[string]interface{}),
	}
}

// Initialize 初始化容器中的所有服务
func (c *Container) Initialize() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.initialized {
		return nil
	}

	c.logger.Info("初始化依赖注入容器")

	// 注册基础服务
	c.registerBaseServices()

	// 注册业务服务
	c.registerBusinessServices()

	c.initialized = true
	c.logger.Info("依赖注入容器初始化完成")
	return nil
}

// registerBaseServices 注册基础服务
func (c *Container) registerBaseServices() {
	// 注册客户端管理器
	clientManager := service.NewClientManager(c.logger)
	c.services["clientManager"] = clientManager

	// 注册工具管理器
	toolManager := service.NewToolManager(c.logger)
	c.services["toolManager"] = toolManager

	// 注册配置解析器
	configParser := service.NewConfigParser(c.logger)
	c.services["configParser"] = configParser

	// 注册时区转换器
	tzConverter := service.NewTimeZoneConverter(c.logger)
	c.services["tzConverter"] = tzConverter

	// 注册配置缓存
	configCache := service.NewConfigCache(c.logger, 100, 30*time.Minute)
	c.services["configCache"] = configCache

	// 注册操作结果缓存
	operationCache := service.NewOperationCache(c.logger, 500, 15*time.Minute)
	c.services["operationCache"] = operationCache

	// 注册异步处理器
	asyncProcessor := service.NewAsyncProcessor(c.logger, 10, 30*time.Minute)
	c.services["asyncProcessor"] = asyncProcessor

	// 注册资源管理器
	resourceManager := service.NewResourceManager(c.logger, 50, "")
	c.services["resourceManager"] = resourceManager

	// 注册性能监控器
	performanceMonitor := service.NewPerformanceMonitor(c.logger, 1000, 1*time.Minute)
	c.services["performanceMonitor"] = performanceMonitor

	// 注册指标收集器
	metricsCollector := service.NewMetricsCollector(c.logger)
	c.services["metricsCollector"] = metricsCollector

	// 注册健康检查器
	healthChecker := service.NewHealthChecker(c.logger, metricsCollector)
	c.services["healthChecker"] = healthChecker
}

// registerBusinessServices 注册业务服务
func (c *Container) registerBusinessServices() {
	// 获取依赖
	clientManager := c.services["clientManager"].(*service.ClientManager)
	toolManager := c.services["toolManager"].(domain.ToolManager)
	configParser := c.services["configParser"].(domain.ConfigParser)
	tzConverter := c.services["tzConverter"].(domain.TimeZoneConverter)

	// 注册ETCD服务
	etcdService := service.NewETCDService(c.logger, toolManager, configParser, tzConverter, clientManager)
	c.services["etcdService"] = etcdService

	// 将客户端管理器也存储为可访问的服务
	c.services["clientManager"] = clientManager
}

// GetETCDService 获取ETCD服务
func (c *Container) GetETCDService() domain.ETCDService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	service, ok := c.services["etcdService"]
	if !ok {
		c.logger.Panic("ETCD服务未注册")
	}

	return service.(domain.ETCDService)
}

// GetToolManager 获取工具管理器
func (c *Container) GetToolManager() domain.ToolManager {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	service, ok := c.services["toolManager"]
	if !ok {
		c.logger.Panic("工具管理器未注册")
	}

	return service.(domain.ToolManager)
}

// GetConfigParser 获取配置解析器
func (c *Container) GetConfigParser() domain.ConfigParser {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	service, ok := c.services["configParser"]
	if !ok {
		c.logger.Panic("配置解析器未注册")
	}

	return service.(domain.ConfigParser)
}

// GetTimeZoneConverter 获取时区转换器
func (c *Container) GetTimeZoneConverter() domain.TimeZoneConverter {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	service, ok := c.services["tzConverter"]
	if !ok {
		c.logger.Panic("时区转换器未注册")
	}

	return service.(domain.TimeZoneConverter)
}

// GetClientManager 获取客户端管理器
func (c *Container) GetClientManager() interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	clientManager, ok := c.services["clientManager"]
	if !ok {
		c.logger.Panic("客户端管理器未注册")
	}

	return clientManager
}

// GetLogger 获取日志器
func (c *Container) GetLogger() *zap.Logger {
	return c.logger
}

// GetConfigCache 获取配置缓存
func (c *Container) GetConfigCache() *service.ConfigCache {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	svc, ok := c.services["configCache"]
	if !ok {
		c.logger.Panic("配置缓存未注册")
	}

	return svc.(*service.ConfigCache)
}

// GetOperationCache 获取操作结果缓存
func (c *Container) GetOperationCache() *service.OperationCache {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	svc, ok := c.services["operationCache"]
	if !ok {
		c.logger.Panic("操作结果缓存未注册")
	}

	return svc.(*service.OperationCache)
}

// GetAsyncProcessor 获取异步处理器
func (c *Container) GetAsyncProcessor() *service.AsyncProcessor {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	svc, ok := c.services["asyncProcessor"]
	if !ok {
		c.logger.Panic("异步处理器未注册")
	}

	return svc.(*service.AsyncProcessor)
}

// GetResourceManager 获取资源管理器
func (c *Container) GetResourceManager() *service.ResourceManager {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	svc, ok := c.services["resourceManager"]
	if !ok {
		c.logger.Panic("资源管理器未注册")
	}

	return svc.(*service.ResourceManager)
}

// GetPerformanceMonitor 获取性能监控器
func (c *Container) GetPerformanceMonitor() *service.PerformanceMonitor {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	svc, ok := c.services["performanceMonitor"]
	if !ok {
		c.logger.Panic("性能监控器未注册")
	}

	return svc.(*service.PerformanceMonitor)
}

// GetMetricsCollector 获取指标收集器
func (c *Container) GetMetricsCollector() *service.MetricsCollector {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	svc, ok := c.services["metricsCollector"]
	if !ok {
		c.logger.Panic("指标收集器未注册")
	}

	return svc.(*service.MetricsCollector)
}

// GetHealthChecker 获取健康检查器
func (c *Container) GetHealthChecker() *service.HealthChecker {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.initialized {
		c.logger.Panic("容器未初始化")
	}

	svc, ok := c.services["healthChecker"]
	if !ok {
		c.logger.Panic("健康检查器未注册")
	}

	return svc.(*service.HealthChecker)
}

// Shutdown 关闭容器
func (c *Container) Shutdown() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.logger.Info("关闭依赖注入容器")

	// 关闭性能监控器
	if performanceMonitor, ok := c.services["performanceMonitor"]; ok {
		performanceMonitor.(*service.PerformanceMonitor).Close()
	}

	// 关闭资源管理器
	if resourceManager, ok := c.services["resourceManager"]; ok {
		resourceManager.(*service.ResourceManager).Close()
	}

	// 关闭异步处理器
	if asyncProcessor, ok := c.services["asyncProcessor"]; ok {
		asyncProcessor.(*service.AsyncProcessor).Close()
	}

	// 关闭操作缓存
	if operationCache, ok := c.services["operationCache"]; ok {
		operationCache.(*service.OperationCache).Close()
	}

	// 关闭配置缓存
	if configCache, ok := c.services["configCache"]; ok {
		configCache.(*service.ConfigCache).Close()
	}

	// 清理资源
	c.services = make(map[string]interface{})
	c.initialized = false

	return nil
}
