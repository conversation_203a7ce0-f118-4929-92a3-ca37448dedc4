import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { vi } from 'vitest';

// 创建测试用的QueryClient
export const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

// 测试包装器组件
interface TestWrapperProps {
  children: React.ReactNode;
  queryClient?: QueryClient;
}

const TestWrapper: React.FC<TestWrapperProps> = ({ 
  children, 
  queryClient = createTestQueryClient() 
}) => {
  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider locale={zhCN}>
          {children}
        </ConfigProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
};

// 自定义渲染函数
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
}

export const renderWithProviders = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { queryClient, ...renderOptions } = options;
  
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper queryClient={queryClient}>
      {children}
    </TestWrapper>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// 模拟API响应
export const mockApiResponse = <T,>(data: T, delay = 0) => {
  return new Promise<T>((resolve) => {
    setTimeout(() => resolve(data), delay);
  });
};

// 模拟API错误
export const mockApiError = (message = 'API Error', status = 500, delay = 0) => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      const error = new Error(message);
      (error as any).status = status;
      reject(error);
    }, delay);
  });
};

// 模拟用户交互
export const mockUserEvent = () => {
  return {
    click: vi.fn(),
    type: vi.fn(),
    clear: vi.fn(),
    selectOptions: vi.fn(),
    upload: vi.fn(),
    hover: vi.fn(),
    unhover: vi.fn(),
    tab: vi.fn(),
    keyboard: vi.fn(),
  };
};

// 等待异步操作完成
export const waitForAsync = (ms = 0) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 模拟Kubernetes资源数据
export const mockKubernetesData = {
  pods: [
    {
      id: 'pod-1',
      name: 'test-pod-1',
      namespace: 'default',
      status: 'Running',
      node: 'node-1',
      ip: '********',
      restarts: 0,
      age: '1d',
      containers: [
        { name: 'container-1', ready: true, restarts: 0 }
      ],
    },
    {
      id: 'pod-2',
      name: 'test-pod-2',
      namespace: 'kube-system',
      status: 'Pending',
      node: 'node-2',
      ip: '********',
      restarts: 1,
      age: '2h',
      containers: [
        { name: 'container-2', ready: false, restarts: 1 }
      ],
    },
  ],
  
  services: [
    {
      id: 'service-1',
      name: 'test-service-1',
      namespace: 'default',
      type: 'ClusterIP',
      clusterIP: '*********',
      ports: [{ port: 80, targetPort: 8080, protocol: 'TCP' }],
      age: '1d',
    },
  ],
  
  deployments: [
    {
      id: 'deployment-1',
      name: 'test-deployment-1',
      namespace: 'default',
      replicas: 3,
      ready: 3,
      upToDate: 3,
      available: 3,
      age: '1d',
    },
  ],
  
  nodes: [
    {
      id: 'node-1',
      name: 'master-node',
      status: 'Ready',
      roles: ['control-plane', 'master'],
      age: '7d',
      version: 'v1.28.0',
      internalIP: '************',
      externalIP: '************',
      os: 'linux',
      arch: 'amd64',
      containerRuntime: 'containerd://1.6.6',
    },
  ],
  
  events: [
    {
      id: 'event-1',
      type: 'Normal',
      reason: 'Created',
      object: 'Pod/test-pod-1',
      message: 'Created container container-1',
      firstTime: '2024-01-01T10:00:00Z',
      lastTime: '2024-01-01T10:00:00Z',
      count: 1,
    },
  ],
};

// 模拟监控数据
export const mockMonitoringData = {
  cpu: Array.from({ length: 60 }, (_, i) => ({
    timestamp: Date.now() - (60 - i) * 1000,
    value: Math.random() * 100,
  })),
  
  memory: Array.from({ length: 60 }, (_, i) => ({
    timestamp: Date.now() - (60 - i) * 1000,
    value: Math.random() * 100,
  })),
  
  network: Array.from({ length: 60 }, (_, i) => ({
    timestamp: Date.now() - (60 - i) * 1000,
    value: Math.random() * 10,
  })),
  
  disk: Array.from({ length: 60 }, (_, i) => ({
    timestamp: Date.now() - (60 - i) * 1000,
    value: Math.random() * 5,
  })),
};

// 模拟告警数据
export const mockAlertData = [
  {
    id: 'alert-1',
    name: 'High CPU Usage',
    level: 'warning' as const,
    status: 'active' as const,
    message: 'CPU usage is above 80%',
    source: 'node-1',
    timestamp: new Date().toISOString(),
    duration: '5m',
  },
  {
    id: 'alert-2',
    name: 'Pod Restart',
    level: 'error' as const,
    status: 'active' as const,
    message: 'Pod test-pod-2 has restarted',
    source: 'test-pod-2',
    timestamp: new Date().toISOString(),
    duration: '2m',
  },
];

// 模拟端口转发数据
export const mockPortForwardData = [
  {
    id: 'pf-1',
    name: 'test-forward-1',
    namespace: 'default',
    pod: 'test-pod-1',
    localPort: 8080,
    remotePort: 80,
    status: 'running' as const,
    createdAt: new Date().toISOString(),
    connectionCount: 5,
  },
];

// 断言辅助函数
export const expectElementToBeInDocument = (element: HTMLElement | null) => {
  expect(element).toBeInTheDocument();
};

export const expectElementToHaveText = (element: HTMLElement | null, text: string) => {
  expect(element).toHaveTextContent(text);
};

export const expectElementToBeVisible = (element: HTMLElement | null) => {
  expect(element).toBeVisible();
};

// 重新导出常用的测试工具
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
