#!/bin/bash

# K8s Helper 快速构建脚本
# 用途：快速构建前后端应用，跳过测试、检查和压缩

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
DIST_DIR="$FRONTEND_DIR/dist"

log_info "🚀 K8s-Helper 快速构建模式"
log_info "项目根目录: $PROJECT_ROOT"

# 检查必要的命令
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

check_command "node"
check_command "npm"
check_command "go"

# 记录开始时间
START_TIME=$(date +%s)

# 1. 快速构建前端
log_info "📦 构建前端应用（快速模式）..."
cd "$FRONTEND_DIR"

# 检查node_modules是否存在，或者检查关键依赖
if [ ! -d "node_modules" ] || [ ! -f "node_modules/.bin/tsc" ]; then
    log_info "安装前端依赖..."
    npm ci --silent
else
    log_warning "使用现有node_modules，跳过依赖安装"
fi

# 构建前端（跳过lint和test）
log_info "编译前端应用..."
NODE_ENV=production npm run build

# 检查构建产物
if [ ! -d "$DIST_DIR" ] || [ ! -f "$DIST_DIR/index.html" ]; then
    log_error "前端构建失败"
    exit 1
fi

log_success "前端构建完成"

# 2. 快速构建后端
log_info "🔧 构建后端应用（快速模式）..."
cd "$PROJECT_ROOT"

# 跳过go mod操作（假设依赖已存在）
log_info "编译后端应用..."

# 设置构建标志
LDFLAGS="-w -s"
if [ "${PRODUCTION:-true}" = "true" ]; then
    LDFLAGS="$LDFLAGS -X main.Version=$(git describe --tags --always --dirty 2>/dev/null || echo 'dev')"
    LDFLAGS="$LDFLAGS -X main.BuildTime=$(date -u '+%Y-%m-%d_%H:%M:%S')"
    LDFLAGS="$LDFLAGS -X main.GitCommit=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
fi

# 构建当前平台的二进制文件
go build -ldflags="$LDFLAGS" -o k8s-helper .

# 检查构建产物
if [ ! -f "k8s-helper" ]; then
    log_error "后端构建失败"
    exit 1
fi

log_success "后端构建完成"

# 计算构建时间
END_TIME=$(date +%s)
BUILD_TIME=$((END_TIME - START_TIME))

# 显示构建结果
log_success "🎉 快速构建完成！"
log_info "构建时间: ${BUILD_TIME}秒"
log_info "前端构建产物: $DIST_DIR ($(du -sh "$DIST_DIR" 2>/dev/null | cut -f1))"
log_info "后端可执行文件: k8s-helper ($(ls -lh k8s-helper | awk '{print $5}'))"

# 提供使用提示
log_info "🚀 启动服务器: ./k8s-helper web --port 8080"
