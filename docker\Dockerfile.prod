# 生产环境优化的多阶段Dockerfile

# 前端构建阶段
FROM node:18-alpine AS frontend-builder

# 设置工作目录
WORKDIR /app/frontend

# 安装构建依赖
RUN apk add --no-cache git python3 make g++

# 复制package文件
COPY frontend/package*.json ./

# 安装依赖（仅生产依赖）
RUN npm ci --only=production --no-audit --no-fund

# 复制源码
COPY frontend/ ./

# 设置生产环境变量
ENV NODE_ENV=production
ENV VITE_API_BASE_URL=/api/v1

# 构建前端应用
RUN npm run build

# 验证构建产物
RUN test -f dist/index.html || (echo "Build failed: index.html not found" && exit 1)

# 后端构建阶段
FROM golang:1.21-alpine AS backend-builder

# 安装构建依赖
RUN apk add --no-cache git ca-certificates tzdata upx

# 设置工作目录
WORKDIR /app

# 复制Go模块文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download && go mod verify

# 复制源码
COPY . .

# 构建后端应用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -a -installsuffix cgo \
    -ldflags="-w -s -X main.version=${BUILD_VERSION:-dev} -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
    -o k8s-helper .

# 压缩二进制文件
RUN upx --best --lzma k8s-helper

# 验证构建产物
RUN test -x k8s-helper || (echo "Build failed: executable not found" && exit 1)

# 最终运行阶段
FROM alpine:3.19

# 安装运行时依赖
RUN apk --no-cache add \
    ca-certificates \
    tzdata \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S k8s-helper && \
    adduser -u 1001 -S k8s-helper -G k8s-helper -h /app

# 设置工作目录
WORKDIR /app

# 从构建阶段复制文件
COPY --from=backend-builder --chown=k8s-helper:k8s-helper /app/k8s-helper ./
COPY --from=backend-builder --chown=k8s-helper:k8s-helper /app/web ./web
COPY --from=frontend-builder --chown=k8s-helper:k8s-helper /app/frontend/dist ./frontend/dist

# 创建必要的目录
RUN mkdir -p /app/data /app/logs /app/config && \
    chown -R k8s-helper:k8s-helper /app

# 设置环境变量
ENV GIN_MODE=release \
    LOG_LEVEL=info \
    SERVER_HOST=0.0.0.0 \
    SERVER_PORT=8080 \
    CORS_ENABLED=true

# 切换到非root用户
USER k8s-helper

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 使用dumb-init作为PID 1
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# 启动应用
CMD ["./k8s-helper", "web", "--host", "0.0.0.0", "--port", "8080", "--cors"]
