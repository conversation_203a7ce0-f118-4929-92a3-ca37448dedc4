<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - K8s-Helper</title>
    <meta name="description" content="{{.Description}}">

    <!-- CSS Files -->
    <link rel="stylesheet" href="/static/css/main.css">

    <!-- PWA Support -->
    <link rel="manifest" href="/static/manifest.json">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">

    <!-- Additional Head Content -->
    {{block "head" .}}{{end}}
</head>
<body class="{{if .UseSidebar}}sidebar-layout{{else}}traditional-layout{{end}}">
    {{if .UseSidebar}}
    <!-- 侧边栏布局 -->
    <div class="app-container">
        <!-- 侧边栏导航 -->
        {{template "sidebar" .}}

        <!-- 主内容区域 -->
        <div class="main-wrapper">
            <!-- 顶部导航栏（简化版） -->
            <header class="top-header">
                <div class="header-content">
                    <button class="mobile-menu-toggle" onclick="toggleSidebar()">
                        <i class="icon">☰</i>
                    </button>
                    <h1 class="page-title">{{.PageTitle | default "Dashboard"}}</h1>
                    <div class="header-actions">
                        {{if .ShowUserInfo}}
                        <div class="user-info">
                            <span class="user-name">{{.UserName | default "Admin"}}</span>
                        </div>
                        {{end}}
                    </div>
                </div>
            </header>

            <!-- 主内容 -->
            <main class="main-content">
                {{block "content" .}}{{end}}
            </main>

            <!-- 底部 -->
            {{template "footer" .}}
        </div>
    </div>
    {{else}}
    <!-- 传统布局 -->
    <!-- Header -->
    {{template "header" .}}

    <!-- Main Content -->
    <main>
        {{block "content" .}}{{end}}
    </main>

    <!-- Footer -->
    {{template "footer" .}}
    {{end}}

    <!-- JavaScript Files -->
    <script src="/static/js/main.js"></script>

    <!-- Additional Scripts -->
    {{block "scripts" .}}{{end}}
</body>
</html>
