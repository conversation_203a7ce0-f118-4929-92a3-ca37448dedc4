package service

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
)

// ValidationLevel 验证级别
type ValidationLevel int

const (
	ValidationLevelError   ValidationLevel = iota // 错误级别
	ValidationLevelWarning                        // 警告级别
	ValidationLevelInfo                           // 信息级别
)

// ValidationIssue 验证问题
type ValidationIssue struct {
	Level   ValidationLevel
	Field   string
	Message string
}

// CommonValidationResult 通用验证结果
type CommonValidationResult struct {
	Valid  bool
	Issues []ValidationIssue
}

// AddError 添加错误
func (r *CommonValidationResult) AddError(field, message string) {
	r.Valid = false
	r.Issues = append(r.Issues, ValidationIssue{
		Level:   ValidationLevelError,
		Field:   field,
		Message: message,
	})
}

// AddWarning 添加警告
func (r *CommonValidationResult) AddWarning(field, message string) {
	r.Issues = append(r.Issues, ValidationIssue{
		Level:   ValidationLevelWarning,
		Field:   field,
		Message: message,
	})
}

// AddInfo 添加信息
func (r *CommonValidationResult) AddInfo(field, message string) {
	r.Issues = append(r.Issues, ValidationIssue{
		Level:   ValidationLevelInfo,
		Field:   field,
		Message: message,
	})
}

// GetErrors 获取错误列表
func (r *CommonValidationResult) GetErrors() []string {
	var errors []string
	for _, issue := range r.Issues {
		if issue.Level == ValidationLevelError {
			errors = append(errors, issue.Message)
		}
	}
	return errors
}

// GetWarnings 获取警告列表
func (r *CommonValidationResult) GetWarnings() []string {
	var warnings []string
	for _, issue := range r.Issues {
		if issue.Level == ValidationLevelWarning {
			warnings = append(warnings, issue.Message)
		}
	}
	return warnings
}

// CommonValidator 通用验证器
type CommonValidator struct {
	logger *zap.Logger
}

// NewCommonValidator 创建通用验证器
func NewCommonValidator(logger *zap.Logger) *CommonValidator {
	return &CommonValidator{
		logger: logger,
	}
}

// ValidateETCDConfig 验证ETCD配置
func (cv *CommonValidator) ValidateETCDConfig(config *domain.ETCDConfig) *CommonValidationResult {
	result := &CommonValidationResult{Valid: true}

	if config == nil {
		result.AddError("config", "ETCD配置不能为空")
		return result
	}

	// 验证必需字段
	cv.validateRequiredFields(config, result)

	// 验证服务器端点
	cv.validateServers(config.Servers, result)

	// 验证证书文件
	cv.validateCertificateFiles(config, result)

	cv.logger.Debug("ETCD配置验证完成",
		zap.Bool("valid", result.Valid),
		zap.Int("issues", len(result.Issues)))

	return result
}

// validateRequiredFields 验证必需字段
func (cv *CommonValidator) validateRequiredFields(config *domain.ETCDConfig, result *CommonValidationResult) {
	if config.Servers == "" {
		result.AddError("servers", "ETCD服务器地址不能为空")
	}

	if config.CaFile == "" {
		result.AddError("ca_file", "CA证书文件路径不能为空")
	}

	if config.CertFile == "" {
		result.AddError("cert_file", "客户端证书文件路径不能为空")
	}

	if config.KeyFile == "" {
		result.AddError("key_file", "客户端私钥文件路径不能为空")
	}
}

// validateServers 验证服务器端点
func (cv *CommonValidator) validateServers(servers string, result *CommonValidationResult) {
	if servers == "" {
		return // 已在必需字段验证中处理
	}

	// 验证服务器地址格式
	if !strings.Contains(servers, "://") {
		result.AddError("servers", fmt.Sprintf("ETCD服务器地址格式无效: %s", servers))
		return
	}

	// 验证协议
	if !strings.HasPrefix(servers, "https://") && !strings.HasPrefix(servers, "http://") {
		result.AddWarning("servers", "建议使用HTTPS协议连接ETCD服务器")
	}

	// 验证端口
	if strings.Contains(servers, ":") {
		parts := strings.Split(servers, ":")
		if len(parts) >= 3 { // protocol://host:port
			port := parts[len(parts)-1]
			if port == "" {
				result.AddWarning("servers", "端口号为空，将使用默认端口")
			}
		}
	} else {
		result.AddInfo("servers", "未指定端口，将使用默认端口")
	}
}

// validateCertificateFiles 验证证书文件
func (cv *CommonValidator) validateCertificateFiles(config *domain.ETCDConfig, result *CommonValidationResult) {
	files := map[string]string{
		"ca_file":   config.CaFile,
		"cert_file": config.CertFile,
		"key_file":  config.KeyFile,
	}

	for field, path := range files {
		if path == "" {
			continue // 已在必需字段验证中处理
		}

		cv.validateFilePath(field, path, result)
	}
}

// validateFilePath 验证文件路径
func (cv *CommonValidator) validateFilePath(field, path string, result *CommonValidationResult) {
	// 检查路径是否为绝对路径
	if !filepath.IsAbs(path) {
		result.AddWarning(field, fmt.Sprintf("建议使用绝对路径: %s", path))
	}

	// 检查文件是否存在
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		result.AddWarning(field, fmt.Sprintf("文件不存在: %s", path))
		cv.logger.Warn("证书文件不存在",
			zap.String("field", field),
			zap.String("path", path))
		return
	}

	if err != nil {
		result.AddError(field, fmt.Sprintf("无法访问文件: %s (%v)", path, err))
		return
	}

	// 检查是否为文件
	if info.IsDir() {
		result.AddError(field, fmt.Sprintf("路径指向目录而非文件: %s", path))
		return
	}

	// 检查文件权限
	cv.validateFilePermissions(field, path, result)
}

// validateFilePermissions 验证文件权限
func (cv *CommonValidator) validateFilePermissions(field, path string, result *CommonValidationResult) {
	info, err := os.Stat(path)
	if err != nil {
		return // 文件不存在或无法访问，已在其他地方处理
	}

	mode := info.Mode()

	// 检查文件权限是否过于宽松
	if mode&0077 != 0 {
		result.AddWarning(field, fmt.Sprintf("文件权限过于宽松: %s (权限: %o)", path, mode))
	}

	// 检查是否可读
	file, err := os.Open(path)
	if err != nil {
		result.AddError(field, fmt.Sprintf("无法读取文件: %s (%v)", path, err))
		return
	}
	file.Close()
}

// ValidateOutputPath 验证输出路径
func (cv *CommonValidator) ValidateOutputPath(path string, result *CommonValidationResult) {
	if path == "" {
		result.AddError("output_path", "输出路径不能为空")
		return
	}

	// 检查目录是否存在
	dir := filepath.Dir(path)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		result.AddWarning("output_path", fmt.Sprintf("输出目录不存在: %s", dir))
	}

	// 检查目录是否可写
	if err := cv.checkDirectoryWritable(dir); err != nil {
		result.AddError("output_path", fmt.Sprintf("输出目录不可写: %s (%v)", dir, err))
	}
}

// ValidateTimeout 验证超时时间
func (cv *CommonValidator) ValidateTimeout(timeout time.Duration, field string, result *CommonValidationResult) {
	if timeout <= 0 {
		result.AddWarning(field, "超时时间未设置，将使用默认值")
		return
	}

	if timeout < 30*time.Second {
		result.AddWarning(field, "超时时间过短，可能导致操作失败")
	}

	if timeout > 30*time.Minute {
		result.AddWarning(field, "超时时间过长，可能影响用户体验")
	}
}

// checkDirectoryWritable 检查目录是否可写
func (cv *CommonValidator) checkDirectoryWritable(dir string) error {
	// 尝试在目录中创建临时文件
	tempFile := filepath.Join(dir, ".write_test")
	file, err := os.Create(tempFile)
	if err != nil {
		return err
	}
	file.Close()
	os.Remove(tempFile)
	return nil
}

// ToValidationResult 转换为旧的ValidationResult格式（向后兼容）
func (r *CommonValidationResult) ToValidationResult() *ValidationResult {
	return &ValidationResult{
		Valid:    r.Valid,
		Errors:   r.GetErrors(),
		Warnings: r.GetWarnings(),
	}
}
