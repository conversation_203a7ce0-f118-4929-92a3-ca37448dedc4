import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

// WebSocket连接状态
export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error';

// WebSocket连接信息
export interface WebSocketConnection {
  id: string;
  type: 'logs' | 'status' | 'monitoring' | 'cleanup' | 'config' | 'port-forward';
  url: string;
  status: ConnectionStatus;
  lastConnected?: number;
  lastDisconnected?: number;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  reconnectInterval: number;
  error?: string;
}

// API连接状态
export interface ApiConnectionStatus {
  isOnline: boolean;
  lastCheck: number;
  responseTime?: number;
  error?: string;
}

// 连接统计信息
export interface ConnectionStats {
  totalConnections: number;
  activeConnections: number;
  failedConnections: number;
  totalReconnects: number;
  averageResponseTime: number;
  uptime: number;
}

// 连接状态接口
export interface ConnectionState {
  // API连接状态
  apiStatus: ApiConnectionStatus;
  
  // WebSocket连接
  websockets: Record<string, WebSocketConnection>;
  
  // 连接统计
  stats: ConnectionStats;
  
  // 网络状态
  isOnline: boolean;
  
  // 自动重连设置
  autoReconnect: boolean;
  
  // 连接事件历史
  connectionEvents: ConnectionEvent[];
}

// 连接事件
export interface ConnectionEvent {
  id: string;
  type: 'connect' | 'disconnect' | 'reconnect' | 'error' | 'api_check';
  connectionId?: string;
  timestamp: number;
  message: string;
  details?: Record<string, any>;
}

// 连接操作接口
export interface ConnectionActions {
  // API状态操作
  setApiStatus: (status: Partial<ApiConnectionStatus>) => void;
  checkApiConnection: () => Promise<void>;
  
  // WebSocket操作
  addWebSocketConnection: (connection: Omit<WebSocketConnection, 'id' | 'status' | 'reconnectAttempts'>) => string;
  updateWebSocketConnection: (id: string, updates: Partial<WebSocketConnection>) => void;
  removeWebSocketConnection: (id: string) => void;
  setWebSocketStatus: (id: string, status: ConnectionStatus, error?: string) => void;
  incrementReconnectAttempts: (id: string) => void;
  resetReconnectAttempts: (id: string) => void;
  
  // 统计操作
  updateStats: (updates: Partial<ConnectionStats>) => void;
  incrementConnectionCount: () => void;
  incrementFailedCount: () => void;
  incrementReconnectCount: () => void;
  updateResponseTime: (responseTime: number) => void;
  
  // 网络状态操作
  setOnlineStatus: (isOnline: boolean) => void;
  
  // 自动重连操作
  setAutoReconnect: (autoReconnect: boolean) => void;
  
  // 事件操作
  addConnectionEvent: (event: Omit<ConnectionEvent, 'id' | 'timestamp'>) => void;
  clearConnectionEvents: () => void;
  
  // 重置操作
  resetConnectionState: () => void;
}

// 初始状态
const initialState: ConnectionState = {
  apiStatus: {
    isOnline: true,
    lastCheck: Date.now(),
  },
  websockets: {},
  stats: {
    totalConnections: 0,
    activeConnections: 0,
    failedConnections: 0,
    totalReconnects: 0,
    averageResponseTime: 0,
    uptime: Date.now(),
  },
  isOnline: navigator.onLine,
  autoReconnect: true,
  connectionEvents: [],
};

// 创建连接状态store
export const useConnectionStore = create<ConnectionState & ConnectionActions>()(
  subscribeWithSelector(
    (set, get) => ({
      ...initialState,

      // API状态操作
      setApiStatus: (status: Partial<ApiConnectionStatus>) => {
        set((state) => ({
          apiStatus: { ...state.apiStatus, ...status },
        }));
      },

      checkApiConnection: async () => {
        const startTime = Date.now();
        
        try {
          // 这里可以调用一个轻量级的API端点来检查连接
          const response = await fetch('/api/v1/health', {
            method: 'GET',
            timeout: 5000,
          } as any);
          
          const responseTime = Date.now() - startTime;
          const isOnline = response.ok;
          
          get().setApiStatus({
            isOnline,
            lastCheck: Date.now(),
            responseTime,
            error: isOnline ? undefined : `HTTP ${response.status}`,
          });
          
          get().updateResponseTime(responseTime);
          
          get().addConnectionEvent({
            type: 'api_check',
            message: isOnline ? 'API连接正常' : `API连接失败: HTTP ${response.status}`,
            details: { responseTime, status: response.status },
          });
          
        } catch (error) {
          const responseTime = Date.now() - startTime;
          
          get().setApiStatus({
            isOnline: false,
            lastCheck: Date.now(),
            responseTime,
            error: error instanceof Error ? error.message : '网络错误',
          });
          
          get().addConnectionEvent({
            type: 'error',
            message: `API连接检查失败: ${error instanceof Error ? error.message : '未知错误'}`,
            details: { responseTime, error: String(error) },
          });
        }
      },

      // WebSocket操作
      addWebSocketConnection: (connection) => {
        const id = `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const newConnection: WebSocketConnection = {
          ...connection,
          id,
          status: 'disconnected',
          reconnectAttempts: 0,
        };
        
        set((state) => ({
          websockets: {
            ...state.websockets,
            [id]: newConnection,
          },
        }));
        
        get().incrementConnectionCount();
        
        get().addConnectionEvent({
          type: 'connect',
          connectionId: id,
          message: `WebSocket连接已创建: ${connection.type}`,
          details: { url: connection.url, type: connection.type },
        });
        
        return id;
      },

      updateWebSocketConnection: (id: string, updates: Partial<WebSocketConnection>) => {
        set((state) => ({
          websockets: {
            ...state.websockets,
            [id]: state.websockets[id] ? { ...state.websockets[id], ...updates } : state.websockets[id],
          },
        }));
      },

      removeWebSocketConnection: (id: string) => {
        set((state) => {
          const { [id]: removed, ...rest } = state.websockets;
          return { websockets: rest };
        });
        
        get().addConnectionEvent({
          type: 'disconnect',
          connectionId: id,
          message: 'WebSocket连接已移除',
        });
      },

      setWebSocketStatus: (id: string, status: ConnectionStatus, error?: string) => {
        const connection = get().websockets[id];
        if (!connection) return;
        
        const updates: Partial<WebSocketConnection> = { status, error };
        
        if (status === 'connected') {
          updates.lastConnected = Date.now();
        } else if (status === 'disconnected' || status === 'error') {
          updates.lastDisconnected = Date.now();
        }
        
        get().updateWebSocketConnection(id, updates);
        
        // 更新统计
        const activeCount = Object.values(get().websockets).filter(ws => ws.status === 'connected').length;
        get().updateStats({ activeConnections: activeCount });
        
        // 添加事件
        get().addConnectionEvent({
          type: status === 'connected' ? 'connect' : status === 'error' ? 'error' : 'disconnect',
          connectionId: id,
          message: `WebSocket ${connection.type} ${status === 'connected' ? '已连接' : status === 'error' ? '连接错误' : '已断开'}`,
          details: error ? { error } : undefined,
        });
      },

      incrementReconnectAttempts: (id: string) => {
        const connection = get().websockets[id];
        if (connection) {
          get().updateWebSocketConnection(id, {
            reconnectAttempts: connection.reconnectAttempts + 1,
          });
          get().incrementReconnectCount();
        }
      },

      resetReconnectAttempts: (id: string) => {
        get().updateWebSocketConnection(id, { reconnectAttempts: 0 });
      },

      // 统计操作
      updateStats: (updates: Partial<ConnectionStats>) => {
        set((state) => ({
          stats: { ...state.stats, ...updates },
        }));
      },

      incrementConnectionCount: () => {
        set((state) => ({
          stats: { ...state.stats, totalConnections: state.stats.totalConnections + 1 },
        }));
      },

      incrementFailedCount: () => {
        set((state) => ({
          stats: { ...state.stats, failedConnections: state.stats.failedConnections + 1 },
        }));
      },

      incrementReconnectCount: () => {
        set((state) => ({
          stats: { ...state.stats, totalReconnects: state.stats.totalReconnects + 1 },
        }));
      },

      updateResponseTime: (responseTime: number) => {
        set((state) => {
          const currentAvg = state.stats.averageResponseTime;
          const totalChecks = state.stats.totalConnections || 1;
          const newAvg = (currentAvg * (totalChecks - 1) + responseTime) / totalChecks;
          
          return {
            stats: { ...state.stats, averageResponseTime: newAvg },
          };
        });
      },

      // 网络状态操作
      setOnlineStatus: (isOnline: boolean) => {
        set({ isOnline });
        
        get().addConnectionEvent({
          type: isOnline ? 'connect' : 'disconnect',
          message: isOnline ? '网络连接已恢复' : '网络连接已断开',
        });
      },

      // 自动重连操作
      setAutoReconnect: (autoReconnect: boolean) => {
        set({ autoReconnect });
      },

      // 事件操作
      addConnectionEvent: (event: Omit<ConnectionEvent, 'id' | 'timestamp'>) => {
        const newEvent: ConnectionEvent = {
          ...event,
          id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
        };

        set((state) => ({
          connectionEvents: [newEvent, ...state.connectionEvents].slice(0, 100), // 最多保留100条
        }));
      },

      clearConnectionEvents: () => {
        set({ connectionEvents: [] });
      },

      // 重置操作
      resetConnectionState: () => {
        set(initialState);
      },
    })
  )
);

// 监听网络状态变化
if (typeof window !== 'undefined') {
  window.addEventListener('online', () => {
    useConnectionStore.getState().setOnlineStatus(true);
  });

  window.addEventListener('offline', () => {
    useConnectionStore.getState().setOnlineStatus(false);
  });
}

// 导出状态选择器
export const selectApiStatus = (state: ConnectionState & ConnectionActions) => state.apiStatus;
export const selectWebSockets = (state: ConnectionState & ConnectionActions) => state.websockets;
export const selectConnectionStats = (state: ConnectionState & ConnectionActions) => state.stats;
export const selectIsOnline = (state: ConnectionState & ConnectionActions) => state.isOnline;
export const selectConnectionEvents = (state: ConnectionState & ConnectionActions) => state.connectionEvents;

// 导出默认store
export default useConnectionStore;
