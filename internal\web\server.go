package web

import (
	"context"
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"k8s-helper/internal/cache"
	"k8s-helper/internal/domain"
	"k8s-helper/internal/service"
	"k8s-helper/pkg/common"
	"k8s-helper/pkg/k8s"
)

// ServerConfig Web服务器配置
type ServerConfig struct {
	Host       string
	Port       int
	TLS        bool
	CertFile   string
	KeyFile    string
	CORS       bool
	Kubeconfig string
	Logger     *zap.Logger
	DevMode    bool
}

// HTTPServer HTTP Web服务器
type HTTPServer struct {
	config             *ServerConfig
	logger             *zap.Logger
	server             *http.Server
	engine             *gin.Engine
	etcdService        domain.ETCDService
	clientset          kubernetes.Interface
	kubeconfig         string
	restConfig         *rest.Config
	metricsCollector   *service.MetricsCollector
	healthChecker      *service.HealthChecker
	performanceMonitor *service.PerformanceMonitor
	errorHandler       *service.ErrorHandler
	auditLogger        *service.AuditLogger
	wsManager          *WebSocketManager
	templateManager    *TemplateManager
	portForwardService *service.PortForwardService
	cleanupService     *service.CleanupService
	configService      *service.ConfigService
	cacheManager       *cache.CacheManager
}

// 统一错误处理方法

// handleServiceUnavailable 处理服务不可用错误
func (s *HTTPServer) handleServiceUnavailable(c *gin.Context, serviceName string) {
	HandleServiceError(c, &service.ServiceError{
		Type:        "service_unavailable",
		Message:     fmt.Sprintf("%s service not available", serviceName),
		Recoverable: false,
		Suggestions: []string{"检查服务配置", "重启服务"},
	}, s.logger)
}

// handleValidationError 处理参数验证错误
func (s *HTTPServer) handleValidationError(c *gin.Context, err error) {
	HandleServiceError(c, &service.ServiceError{
		Type:        "validation",
		Operation:   "parameter_validation",
		Message:     "请求参数验证失败",
		Cause:       err,
		Recoverable: true,
		Suggestions: []string{"检查请求参数格式", "查看API文档"},
	}, s.logger)
}

// handleInternalError 处理内部服务错误
func (s *HTTPServer) handleInternalError(c *gin.Context, err error, operation string) {
	HandleServiceError(c, &service.ServiceError{
		Type:        "internal",
		Operation:   operation,
		Message:     fmt.Sprintf("%s操作失败", operation),
		Cause:       err,
		Recoverable: false,
		Suggestions: []string{"稍后重试", "检查系统状态", "联系管理员"},
	}, s.logger)
}

// handleNotFoundError 处理资源不存在错误
func (s *HTTPServer) handleNotFoundError(c *gin.Context, resource string) {
	HandleServiceError(c, &service.ServiceError{
		Type:        "not_found",
		Operation:   "resource_lookup",
		Message:     fmt.Sprintf("请求的%s不存在", resource),
		Cause:       nil,
		Recoverable: true,
		Suggestions: []string{"检查资源名称", "确认资源是否已创建"},
	}, s.logger)
}

// NewHTTPServer 创建HTTP Web服务器
func NewHTTPServer(config *ServerConfig) *HTTPServer {
	if config == nil {
		config = &ServerConfig{
			Host: "localhost",
			Port: 8080,
		}
	}

	// 设置Gin模式
	if config.Logger != nil {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	engine := gin.New()

	// 基础中间件配置将在setupMiddlewares中完成

	// 静态文件服务将在路由注册时配置

	// 创建 Kubernetes 客户端
	clientset, _ := k8s.NewClient(config.Kubeconfig)

	// 创建 REST 配置
	restConfig, _ := k8s.GetRestConfig(config.Kubeconfig)

	// 创建WebSocket管理器
	wsManager := NewWebSocketManager(config.Logger)

	// 创建模板管理器
	templateManager := NewTemplateManager("web/templates", config.Logger, config.DevMode)

	// 创建端口转发服务
	var portForwardService *service.PortForwardService
	if clientset != nil && restConfig != nil {
		portForwardService = service.NewPortForwardService(clientset, restConfig)
	}

	// 创建资源清理服务
	var cleanupService *service.CleanupService
	if clientset != nil {
		cleanupService = service.NewCleanupService(clientset)
	}

	// 创建配置管理服务
	// 使用专门的应用配置文件路径，而不是kubeconfig路径
	appConfigPath := filepath.Join(filepath.Dir(config.Kubeconfig), "k8s-helper-config.yaml")
	configService := service.NewConfigService(appConfigPath)

	server := &HTTPServer{
		config:             config,
		logger:             config.Logger,
		engine:             engine,
		clientset:          clientset,
		kubeconfig:         config.Kubeconfig,
		restConfig:         restConfig,
		wsManager:          wsManager,
		templateManager:    templateManager,
		portForwardService: portForwardService,
		cleanupService:     cleanupService,
		configService:      configService,
	}

	// 重新配置中间件以使用server实例
	server.setupMiddlewares()

	return server
}

// setupMiddlewares 设置中间件
func (s *HTTPServer) setupMiddlewares() {
	// 添加请求ID中间件
	s.engine.Use(RequestIDMiddleware())

	// 添加访问日志中间件
	s.engine.Use(AccessLogMiddleware(s.logger))

	// 添加性能监控中间件
	s.engine.Use(func(c *gin.Context) {
		PerformanceLogMiddleware(s.logger, s.performanceMonitor)(c)
	})

	// 添加错误处理中间件
	s.engine.Use(func(c *gin.Context) {
		ErrorHandlerMiddleware(s.logger, s.errorHandler)(c)
	})

	// 添加审计日志中间件
	s.engine.Use(func(c *gin.Context) {
		AuditLogMiddleware(s.logger, s.auditLogger)(c)
	})

	// 添加CORS中间件
	if s.config.CORS {
		s.engine.Use(DevelopmentCORSMiddleware(s.logger))
		s.logger.Info("CORS中间件已启用")
	}
}

// WithETCDService 设置ETCD服务
func (s *HTTPServer) WithETCDService(etcdService domain.ETCDService) *HTTPServer {
	s.etcdService = etcdService
	return s
}

// WithMonitoringServices 设置监控服务
func (s *HTTPServer) WithMonitoringServices(
	metricsCollector *service.MetricsCollector,
	healthChecker *service.HealthChecker,
	performanceMonitor *service.PerformanceMonitor,
) *HTTPServer {
	s.metricsCollector = metricsCollector
	s.healthChecker = healthChecker
	s.performanceMonitor = performanceMonitor
	return s
}

// WithErrorHandler 设置错误处理器
func (s *HTTPServer) WithErrorHandler(errorHandler *service.ErrorHandler) *HTTPServer {
	s.errorHandler = errorHandler
	return s
}

// WithAuditLogger 设置审计日志器
func (s *HTTPServer) WithAuditLogger(auditLogger *service.AuditLogger) *HTTPServer {
	s.auditLogger = auditLogger
	return s
}

// Start 启动Web服务器
func (s *HTTPServer) Start() error {
	// 注册路由
	s.registerRoutes()

	s.server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", s.config.Host, s.config.Port),
		Handler: s.engine,
	}

	s.logger.Info("启动Web服务器", 
		zap.String("host", s.config.Host),
		zap.Int("port", s.config.Port))
		
	return s.server.ListenAndServe()
}

// Stop 停止Web服务器
func (s *HTTPServer) Stop() error {
	if s.server == nil {
		return nil
	}

	s.logger.Info("正在停止Web服务器")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return s.server.Shutdown(ctx)
}

// setupStaticFileRoutes 设置静态文件路由
func (s *HTTPServer) setupStaticFileRoutes() {
	// 获取静态文件处理器
	staticHandler := GetStaticHandler()

	// 设置静态文件路由 - 使用通配符匹配所有静态文件请求
	s.engine.Any("/assets/*filepath", gin.WrapH(staticHandler))
	s.engine.Any("/static/*filepath", gin.WrapH(staticHandler))

	// 处理特殊的静态文件
	s.engine.GET("/favicon.ico", gin.WrapH(staticHandler))
	s.engine.GET("/manifest.json", gin.WrapH(staticHandler))
	s.engine.GET("/robots.txt", gin.WrapH(staticHandler))

	s.logger.Info("静态文件路由已配置")
}

// registerRoutes 注册路由
func (s *HTTPServer) registerRoutes() {
	// API路由组
	api := s.engine.Group("/api/v1")
	{
		// ETCD相关路由
		etcd := api.Group("/etcd")
		{
			etcd.POST("/backup", s.handleETCDCreateBackup)
			etcd.GET("/backups", s.handleETCDListBackups)
			etcd.POST("/restore", s.handleETCDRestore)
			etcd.POST("/verify", s.handleETCDVerify)
			etcd.GET("/status", s.handleETCDStatus)

			// CronJob管理路由
			etcd.POST("/cronjob", s.handleETCDCreateCronJob)
			etcd.GET("/cronjobs", s.handleETCDListCronJobs)
			etcd.PUT("/cronjob/:name/suspend", s.handleETCDSuspendCronJob)
			etcd.PUT("/cronjob/:name/resume", s.handleETCDResumeCronJob)
			etcd.DELETE("/cronjob/:name", s.handleETCDDeleteCronJob)
		}

		// 健康检查相关路由
		health := api.Group("/health")
		{
			health.GET("", s.handleHealthCheck)
			health.GET("/live", s.handleLiveness)
			health.GET("/ready", s.handleReadiness)
		}

		// 监控相关路由
		monitoring := api.Group("/monitoring")
		{
			monitoring.GET("/metrics", s.handleGetMetrics)
			monitoring.GET("/health", s.handleGetHealth)
			monitoring.GET("/performance", s.handleGetPerformance)
			monitoring.GET("/operations", s.handleOperations)
			monitoring.GET("/system", s.handleSystemMetrics)
			monitoring.GET("/alerts", s.handleGetAlerts)
			monitoring.POST("/alerts", s.handleCreateAlert)
			monitoring.DELETE("/alerts/:id", s.handleDeleteAlert)
		}

		// 配置管理相关路由
		config := api.Group("/config")
		{
			config.GET("", s.handleGetConfig)
			config.GET("/:section", s.handleGetConfigSection)
			config.PUT("/:section", s.handleUpdateConfigSection)
			config.POST("/reload", s.handleReloadConfig)
			config.GET("/history", s.handleGetConfigHistory)
			config.POST("/restore/:id", s.handleRestoreConfig)
			config.POST("/validate", s.handleValidateConfig)
		}

		// 集群信息相关路由
		cluster := api.Group("/cluster")
		{
			cluster.GET("/info", s.handleClusterInfo)
			cluster.GET("/info/quick", s.handleClusterInfoQuick) // 快速响应接口
		}

		// Pod相关路由
		pods := api.Group("/pods")
		{
			pods.GET("", s.handleListPods)
			pods.GET("/:namespace", s.handleListPodsInNamespace)
			pods.GET("/:namespace/:pod", s.handleGetPod)
			pods.GET("/:namespace/:pod/logs", s.handlePodLogs)
		}

		// 端口转发相关路由
		portForward := api.Group("/port-forward")
		{
			portForward.GET("", s.handleListPortForwards)
			portForward.POST("", s.handleCreatePortForward)
			portForward.GET("/:id", s.handleGetPortForward)
			portForward.DELETE("/:id", s.handleStopPortForward)
		}

		// 资源清理相关路由
		cleanup := api.Group("/cleanup")
		{
			cleanup.GET("/namespaces", s.handleGetCleanupNamespaces)
			cleanup.POST("/scan", s.handleCleanupScan)
			cleanup.POST("/execute", s.handleCleanupExecute)
		}



		// WebSocket路由
		ws := api.Group("/ws")
		{
			ws.GET("/logs", s.handleWebSocketLogs)
			ws.GET("/status", s.handleWebSocketStatus)
		}
	}

	// API文档路由
	s.engine.GET("/api/docs", s.handleAPIDocs)
	s.engine.GET("/api/docs/ui", s.handleAPIDocsUI)
	api.GET("/docs", s.handleAPIDocs)  // 添加 /api/v1/docs 端点

	// 基础路由
	s.engine.GET("/health", s.handleHealth)
	s.engine.GET("/versions", s.handleVersions)

	// 重定向旧的UI路径到根路径
	s.engine.GET("/ui", s.handleUIRedirect)
	s.engine.GET("/ui/*any", s.handleUIRedirect)

	// 集成静态文件服务
	s.setupStaticFileRoutes()

	// 主界面路由 - 必须放在最后以支持SPA路由
	s.engine.GET("/", s.handleMainInterface)
	s.engine.NoRoute(s.handleSPARouting) // 处理所有未匹配的路由，支持前端路由
}

// handleMainInterface 处理主界面请求 - 支持React SPA
func (s *HTTPServer) handleMainInterface(c *gin.Context) {
	// 只处理根路径请求
	if c.Request.URL.Path != "/" {
		s.handleSPARouting(c)
		return
	}

	// 使用静态文件处理器服务index.html
	staticHandler := GetStaticHandler()

	// 设置SPA相关的头
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	// 代理到静态文件处理器
	staticHandler.ServeHTTP(c.Writer, c.Request)
}

// handleSPARouting 处理SPA路由 - 支持客户端路由
func (s *HTTPServer) handleSPARouting(c *gin.Context) {
	path := c.Request.URL.Path

	// 检查是否为API请求，如果是则返回404
	if strings.HasPrefix(path, "/api/") {
		c.JSON(http.StatusNotFound, gin.H{"error": "API endpoint not found"})
		return
	}

	// 检查是否为静态资源请求
	if s.isStaticFile(path) {
		// 使用静态文件处理器
		staticHandler := GetStaticHandler()
		staticHandler.ServeHTTP(c.Writer, c.Request)
		return
	}

	// 对于其他所有请求，返回index.html以支持客户端路由
	// 设置SPA相关的头
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	// 修改请求路径为根路径，让静态文件处理器返回index.html
	originalPath := c.Request.URL.Path
	c.Request.URL.Path = "/"

	staticHandler := GetStaticHandler()
	staticHandler.ServeHTTP(c.Writer, c.Request)

	// 恢复原始路径（用于日志记录）
	c.Request.URL.Path = originalPath
}

// isStaticFile 判断是否为静态文件请求
func (s *HTTPServer) isStaticFile(path string) bool {
	// 检查路径前缀
	if strings.HasPrefix(path, "/assets/") ||
		strings.HasPrefix(path, "/static/") {
		return true
	}

	// 检查文件扩展名
	staticExtensions := []string{
		".js", ".css", ".ico", ".png", ".jpg", ".jpeg", ".gif", ".svg",
		".woff", ".woff2", ".ttf", ".eot", ".map", ".json", ".txt",
	}

	for _, ext := range staticExtensions {
		if strings.HasSuffix(path, ext) {
			return true
		}
	}

	return false
}

// handleUIRedirect 处理旧UI路径的重定向
func (s *HTTPServer) handleUIRedirect(c *gin.Context) {
	// 重定向到根路径，保持URL片段
	fragment := ""
	if path := c.Param("any"); path != "" {
		// 将 /ui/xxx 转换为 /#xxx
		fragment = "#" + strings.TrimPrefix(path, "/")
	}

	c.Redirect(http.StatusMovedPermanently, "/"+fragment)
}

// handleHealth 处理健康检查
func (s *HTTPServer) handleHealth(c *gin.Context) {
	response := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
		"service":   "k8s-helper",
		"version":   "1.0.0",
	}
	
	c.JSON(http.StatusOK, response)
}

// handleVersions 处理API版本信息
func (s *HTTPServer) handleVersions(c *gin.Context) {
	response := map[string]interface{}{
		"versions": []string{"v1"},
		"default":  "v1",
		"current":  "v1",
		"service":  "k8s-helper-api",
		"timestamp": time.Now().Format(time.RFC3339),
	}
	
	c.JSON(http.StatusOK, response)
}

// handleETCDCreateBackup 处理ETCD备份请求
func (s *HTTPServer) handleETCDCreateBackup(c *gin.Context) {
	if s.etcdService == nil {
		s.handleServiceUnavailable(c, "ETCD")
		return
	}

	var req struct {
		OutputPath string `json:"output_path"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		s.handleValidationError(c, err)
		return
	}

	// 设置默认输出路径
	outputPath := req.OutputPath
	if outputPath == "" {
		outputPath = fmt.Sprintf("./etcd-backup-%s.db", time.Now().Format("20060102-150405"))
	}

	// 创建备份选项
	opts := &domain.BackupOptions{
		OutputPath:      outputPath,
		UseSDK:          true,
		FallbackToTool:  true,
		DownloadBaseURL: common.DefaultEtcdBaseURL,
		DownloadVersion: common.DefaultEtcdVersion,
		DownloadOS:      common.DefaultEtcdOS,
	}

	// 执行备份
	result, err := s.etcdService.Backup(c.Request.Context(), opts)
	if err != nil {
		s.handleInternalError(c, err, "ETCD备份")
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"path":      result.FilePath,
		"size":      result.Size,
		"timestamp": result.Timestamp,
	})
}

// handleETCDListBackups 处理ETCD备份列表请求
func (s *HTTPServer) handleETCDListBackups(c *gin.Context) {
	// 简单实现，返回空列表
	c.JSON(http.StatusOK, gin.H{
		"backups": []interface{}{},
		"total":   0,
	})
}

// ETCDRestoreRequest ETCD恢复请求结构
type ETCDRestoreRequest struct {
	SnapshotPath             string `json:"snapshot_path" binding:"required"`
	DataDir                  string `json:"data_dir,omitempty"`
	Name                     string `json:"name,omitempty"`
	InitialCluster           string `json:"initial_cluster,omitempty"`
	InitialAdvertisePeerURLs string `json:"initial_advertise_peer_urls,omitempty"`
	UseSDK                   bool   `json:"use_sdk,omitempty"`
	FallbackToTool           bool   `json:"fallback_to_tool,omitempty"`
	TimeoutSeconds           int    `json:"timeout_seconds,omitempty"`
	SkipHashCheck            bool   `json:"skip_hash_check,omitempty"`
	MarkCompacted            bool   `json:"mark_compacted,omitempty"`
}

// handleETCDRestore 处理ETCD恢复请求
func (s *HTTPServer) handleETCDRestore(c *gin.Context) {
	if s.etcdService == nil {
		s.handleServiceUnavailable(c, "ETCD")
		return
	}

	var req ETCDRestoreRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.handleValidationError(c, err)
		return
	}

	// 验证必需参数
	if req.SnapshotPath == "" {
		s.handleValidationError(c, fmt.Errorf("snapshot_path is required"))
		return
	}

	s.logger.Info("开始处理ETCD恢复请求",
		zap.String("snapshot_path", req.SnapshotPath),
		zap.String("data_dir", req.DataDir),
		zap.Bool("use_sdk", req.UseSDK))

	// 构建恢复选项
	opts := &domain.RestoreOptions{
		SnapshotPath:             req.SnapshotPath,
		DataDir:                  req.DataDir,
		Name:                     req.Name,
		InitialCluster:           req.InitialCluster,
		InitialAdvertisePeerURLs: req.InitialAdvertisePeerURLs,
		UseSDK:                   req.UseSDK,
		FallbackToTool:           req.FallbackToTool,
		SkipHashCheck:            req.SkipHashCheck,
		MarkCompacted:            req.MarkCompacted,
	}

	// 设置超时时间
	if req.TimeoutSeconds > 0 {
		opts.Timeout = time.Duration(req.TimeoutSeconds) * time.Second
	}

	// 执行恢复操作
	result, err := s.etcdService.Restore(c.Request.Context(), opts)
	if err != nil {
		s.handleInternalError(c, err, "ETCD恢复")
		return
	}

	s.logger.Info("ETCD恢复成功", zap.String("restore_id", result.RestoreID))

	c.JSON(http.StatusOK, gin.H{
		"restore_id":   result.RestoreID,
		"status":       result.Status,
		"message":      result.Message,
		"started_at":   result.StartedAt,
		"completed_at": result.CompletedAt,
		"timestamp":    time.Now().Format(time.RFC3339),
	})
}

// ETCDVerifyRequest ETCD验证请求结构
type ETCDVerifyRequest struct {
	SnapshotPath   string `json:"snapshot_path" binding:"required"`
	UseSDK         bool   `json:"use_sdk,omitempty"`
	FallbackToTool bool   `json:"fallback_to_tool,omitempty"`
	TimeoutSeconds int    `json:"timeout_seconds,omitempty"`
	DetailedVerify bool   `json:"detailed_verify,omitempty"`
}

// handleETCDVerify 处理ETCD验证请求
func (s *HTTPServer) handleETCDVerify(c *gin.Context) {
	if s.etcdService == nil {
		s.handleServiceUnavailable(c, "ETCD")
		return
	}

	var req ETCDVerifyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.handleValidationError(c, err)
		return
	}

	// 验证必需参数
	if req.SnapshotPath == "" {
		s.handleValidationError(c, fmt.Errorf("snapshot_path is required"))
		return
	}

	s.logger.Info("开始处理ETCD验证请求",
		zap.String("snapshot_path", req.SnapshotPath),
		zap.Bool("use_sdk", req.UseSDK),
		zap.Bool("detailed_verify", req.DetailedVerify))

	// 构建验证选项
	opts := &domain.VerifyOptions{
		SnapshotPath:   req.SnapshotPath,
		UseSDK:         req.UseSDK,
		FallbackToTool: req.FallbackToTool,
		DetailedVerify: req.DetailedVerify,
	}

	// 设置超时时间
	if req.TimeoutSeconds > 0 {
		opts.Timeout = time.Duration(req.TimeoutSeconds) * time.Second
	}

	// 执行验证操作
	result, err := s.etcdService.Verify(c.Request.Context(), opts)
	if err != nil {
		s.handleInternalError(c, err, "ETCD验证")
		return
	}

	s.logger.Info("ETCD验证完成",
		zap.String("backup_id", result.BackupID),
		zap.Bool("is_valid", result.IsValid))

	c.JSON(http.StatusOK, gin.H{
		"backup_id":     result.BackupID,
		"valid":         result.Valid,
		"is_valid":      result.IsValid,
		"checksum":      result.Checksum,
		"size":          result.Size,
		"compression":   result.Compression,
		"details":       result.Details,
		"duration":      result.Duration.String(),
		"message":       result.Message,
		"error_message": result.ErrorMessage,
		"method":        result.Method,
		"verified_at":   result.VerifiedAt,
		"timestamp":     time.Now().Format(time.RFC3339),
	})
}

// handleETCDStatus 处理ETCD状态请求
func (s *HTTPServer) handleETCDStatus(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "available",
		"time":   time.Now().Format(time.RFC3339),
	})
}

// handleHealthCheck 处理健康检查请求
func (s *HTTPServer) handleHealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "healthy",
		"time":   time.Now().Format(time.RFC3339),
	})
}

// handleLiveness 处理存活性检查
func (s *HTTPServer) handleLiveness(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "alive",
		"time":   time.Now().Format(time.RFC3339),
	})
}

// handleReadiness 处理就绪性检查
func (s *HTTPServer) handleReadiness(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ready",
		"time":   time.Now().Format(time.RFC3339),
	})
}

// handlePerformance 处理性能监控请求
func (s *HTTPServer) handlePerformance(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"performance": gin.H{},
		"timestamp":   time.Now().Format(time.RFC3339),
	})
}

// handleOperations 处理操作指标请求
func (s *HTTPServer) handleOperations(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"operations": gin.H{},
		"timestamp":  time.Now().Format(time.RFC3339),
	})
}

// handleSystemMetrics 处理系统指标请求
func (s *HTTPServer) handleSystemMetrics(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"system":    gin.H{},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleClusterInfo 处理集群信息请求
func (s *HTTPServer) handleClusterInfo(c *gin.Context) {
	if s.clientset == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Kubernetes client not available"})
		return
	}

	// 创建集群信息服务
	clusterService := service.NewClusterService(s.clientset)

	// 获取集群信息
	clusterInfo, err := clusterService.GetClusterInfo(c.Request.Context())
	if err != nil {
		s.logger.Error("获取集群信息失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, clusterInfo)
}

// handleClusterInfoQuick 处理集群信息快速请求（只返回基本信息）
func (s *HTTPServer) handleClusterInfoQuick(c *gin.Context) {
	if s.clientset == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Kubernetes client not available"})
		return
	}

	// 设置1秒超时的上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), 1*time.Second)
	defer cancel()

	// 只获取基本信息
	quickInfo := gin.H{
		"timestamp": time.Now().Format(time.RFC3339),
	}

	// 尝试获取服务端版本（最快的API调用）
	if version, err := s.clientset.Discovery().ServerVersion(); err == nil {
		quickInfo["server_version"] = gin.H{
			"git_version": version.GitVersion,
			"major":       version.Major,
			"minor":       version.Minor,
		}
	}

	// 尝试获取节点数量（快速统计）
	if nodes, err := s.clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{Limit: 10}); err == nil {
		readyNodes := 0
		for _, node := range nodes.Items {
			for _, condition := range node.Status.Conditions {
				if condition.Type == "Ready" && condition.Status == "True" {
					readyNodes++
					break
				}
			}
		}
		quickInfo["nodes"] = gin.H{
			"total": len(nodes.Items),
			"ready": readyNodes,
		}
	}

	c.JSON(http.StatusOK, quickInfo)
}

// handlePodLogs 处理Pod日志请求
func (s *HTTPServer) handlePodLogs(c *gin.Context) {
	if s.clientset == nil {
		s.handleServiceUnavailable(c, "Kubernetes client")
		return
	}

	namespace := c.Param("namespace")
	podName := c.Param("pod")
	container := c.Query("container")
	follow := c.Query("follow") == "true"
	timestamps := c.Query("timestamps") == "true"
	previous := c.Query("previous") == "true"
	tailLines := int64(100) // 默认显示最后100行

	// 解析tail参数
	if tail := c.Query("tail"); tail != "" {
		if parsed, err := strconv.ParseInt(tail, 10, 64); err == nil {
			tailLines = parsed
		}
	}

	// 解析since参数
	var sinceSeconds *int64
	if since := c.Query("since"); since != "" {
		if duration, err := time.ParseDuration(since); err == nil {
			seconds := int64(duration.Seconds())
			sinceSeconds = &seconds
		} else {
			s.logger.Warn("无效的since参数", zap.String("since", since), zap.Error(err))
		}
	}

	// 创建日志服务
	logsService := service.NewLogsService(s.clientset)

	// 如果是WebSocket请求（follow=true），重定向到WebSocket处理器
	if follow {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Real-time logs require WebSocket connection",
			"websocket_url": fmt.Sprintf("/api/v1/ws/logs?namespace=%s&pod=%s&container=%s", namespace, podName, container),
		})
		return
	}

	// 验证容器（如果指定了容器名）
	if container != "" {
		if err := logsService.ValidateContainer(c.Request.Context(), namespace, podName, container); err != nil {
			s.handleValidationError(c, err)
			return
		}
	}

	// 构建日志选项
	logOptions := &service.LogOptions{
		Container:    container,
		Follow:       false,
		Timestamps:   timestamps,
		TailLines:    &tailLines,
		Previous:     previous,
		SinceSeconds: sinceSeconds,
	}

	// 获取日志
	logs, err := logsService.GetLogs(c.Request.Context(), namespace, podName, logOptions)
	if err != nil {
		s.handleInternalError(c, err, "获取Pod日志")
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"namespace": namespace,
		"pod":       podName,
		"container": container,
		"logs":      logs,
		"count":     len(logs),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleListPods 处理获取所有Pod列表请求
func (s *HTTPServer) handleListPods(c *gin.Context) {
	if s.clientset == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Kubernetes client not available"})
		return
	}

	// 创建日志服务
	logsService := service.NewLogsService(s.clientset)

	// 获取所有命名空间
	namespaces, err := logsService.GetNamespaces(c.Request.Context())
	if err != nil {
		s.logger.Error("获取命名空间列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"namespaces": namespaces,
		"timestamp":  time.Now().Format(time.RFC3339),
	})
}

// handleListPodsInNamespace 处理获取指定命名空间Pod列表请求
func (s *HTTPServer) handleListPodsInNamespace(c *gin.Context) {
	if s.clientset == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Kubernetes client not available"})
		return
	}

	namespace := c.Param("namespace")

	// 创建日志服务
	logsService := service.NewLogsService(s.clientset)

	// 获取Pod列表
	pods, err := logsService.GetPods(c.Request.Context(), namespace)
	if err != nil {
		s.logger.Error("获取Pod列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"namespace": namespace,
		"pods":      pods,
		"count":     len(pods),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleGetPod 处理获取指定Pod信息请求
func (s *HTTPServer) handleGetPod(c *gin.Context) {
	if s.clientset == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Kubernetes client not available"})
		return
	}

	namespace := c.Param("namespace")
	podName := c.Param("pod")

	// 创建日志服务
	logsService := service.NewLogsService(s.clientset)

	// 获取Pod信息
	pod, err := logsService.GetPodInfo(c.Request.Context(), namespace, podName)
	if err != nil {
		s.logger.Error("获取Pod信息失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"pod":       pod,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleListPortForwards 处理获取端口转发列表请求
func (s *HTTPServer) handleListPortForwards(c *gin.Context) {
	if s.portForwardService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Port forward service not available"})
		return
	}

	forwards := s.portForwardService.List()
	c.JSON(http.StatusOK, gin.H{
		"forwards":  forwards,
		"count":     len(forwards),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleCreatePortForward 处理创建端口转发请求
func (s *HTTPServer) handleCreatePortForward(c *gin.Context) {
	if s.portForwardService == nil {
		s.handleServiceUnavailable(c, "Port forward")
		return
	}

	var req service.PortForwardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.handleValidationError(c, err)
		return
	}

	forward, err := s.portForwardService.Create(req)
	if err != nil {
		s.handleInternalError(c, err, "创建端口转发")
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"forward":   forward,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleGetPortForward 处理获取指定端口转发请求
func (s *HTTPServer) handleGetPortForward(c *gin.Context) {
	if s.portForwardService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Port forward service not available"})
		return
	}

	id := c.Param("id")
	forward, err := s.portForwardService.Get(id)
	if err != nil {
		s.handleNotFoundError(c, "端口转发")
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"forward":   forward,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleStopPortForward 处理停止端口转发请求
func (s *HTTPServer) handleStopPortForward(c *gin.Context) {
	if s.portForwardService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Port forward service not available"})
		return
	}

	id := c.Param("id")
	err := s.portForwardService.Stop(id)
	if err != nil {
		s.logger.Error("停止端口转发失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "端口转发已停止",
		"id":        id,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleGetCleanupNamespaces 处理获取清理命名空间列表请求
func (s *HTTPServer) handleGetCleanupNamespaces(c *gin.Context) {
	if s.cleanupService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Cleanup service not available"})
		return
	}

	namespaces, err := s.cleanupService.GetNamespaces(c.Request.Context())
	if err != nil {
		s.logger.Error("获取命名空间列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"namespaces": namespaces,
		"count":      len(namespaces),
		"timestamp":  time.Now().Format(time.RFC3339),
	})
}

// handleCleanupScan 处理资源清理扫描请求
func (s *HTTPServer) handleCleanupScan(c *gin.Context) {
	if s.cleanupService == nil {
		s.handleServiceUnavailable(c, "Cleanup")
		return
	}

	var req service.CleanupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.handleValidationError(c, err)
		return
	}

	// 强制设置为预览模式
	req.DryRun = true

	result, err := s.cleanupService.Execute(req)
	if err != nil {
		s.handleInternalError(c, err, "资源清理扫描")
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"result":    result,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleCleanupExecute 处理资源清理执行请求
func (s *HTTPServer) handleCleanupExecute(c *gin.Context) {
	if s.cleanupService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Cleanup service not available"})
		return
	}

	var req service.CleanupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 记录清理操作
	s.logger.Info("执行资源清理",
		zap.String("type", req.Type),
		zap.Strings("namespaces", req.Namespaces),
		zap.Bool("dry_run", req.DryRun),
		zap.String("older_than", req.OlderThan))

	result, err := s.cleanupService.Execute(req)
	if err != nil {
		s.logger.Error("资源清理执行失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"result":    result,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleGetMetrics 处理获取监控指标请求
func (s *HTTPServer) handleGetMetrics(c *gin.Context) {
	// 模拟监控指标数据
	metrics := gin.H{
		"system": gin.H{
			"cpu_usage":    "45.2%",
			"memory_usage": "68.5%",
			"disk_usage":   "32.1%",
			"network_io":   "1.2MB/s",
		},
		"kubernetes": gin.H{
			"pods_running":    42,
			"pods_pending":    3,
			"pods_failed":     1,
			"nodes_ready":     3,
			"nodes_not_ready": 0,
		},
		"application": gin.H{
			"requests_per_second": 156.7,
			"error_rate":         "0.02%",
			"response_time":      "45ms",
			"active_connections": 23,
		},
		"timestamp": time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, gin.H{
		"metrics":   metrics,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleGetHealth 处理获取健康状态请求
func (s *HTTPServer) handleGetHealth(c *gin.Context) {
	// 模拟健康检查数据
	health := gin.H{
		"overall_status": "healthy",
		"components": gin.H{
			"kubernetes_api": gin.H{
				"status":      "healthy",
				"response_time": "12ms",
				"last_check":   time.Now().Add(-30 * time.Second).Format(time.RFC3339),
			},
			"etcd": gin.H{
				"status":      "healthy",
				"response_time": "8ms",
				"last_check":   time.Now().Add(-45 * time.Second).Format(time.RFC3339),
			},
			"database": gin.H{
				"status":      "healthy",
				"response_time": "15ms",
				"last_check":   time.Now().Add(-60 * time.Second).Format(time.RFC3339),
			},
			"external_services": gin.H{
				"status":      "degraded",
				"response_time": "250ms",
				"last_check":   time.Now().Add(-90 * time.Second).Format(time.RFC3339),
				"message":     "High response time detected",
			},
		},
		"uptime": "72h 15m 30s",
		"timestamp": time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, gin.H{
		"health":    health,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleGetPerformance 处理获取性能数据请求
func (s *HTTPServer) handleGetPerformance(c *gin.Context) {
	// 模拟性能数据
	performance := gin.H{
		"cpu": gin.H{
			"current": 45.2,
			"average": 38.7,
			"peak":    78.9,
			"history": []float64{42.1, 38.5, 45.2, 41.8, 39.3, 44.7, 45.2},
		},
		"memory": gin.H{
			"current": 68.5,
			"average": 65.2,
			"peak":    82.1,
			"history": []float64{64.2, 66.8, 68.5, 67.1, 63.9, 69.2, 68.5},
		},
		"network": gin.H{
			"inbound":  "1.2MB/s",
			"outbound": "0.8MB/s",
			"total":    "2.0MB/s",
			"history": []string{"1.8MB/s", "2.1MB/s", "2.0MB/s", "1.9MB/s", "2.2MB/s", "2.0MB/s", "2.0MB/s"},
		},
		"operations": gin.H{
			"total_requests":     15672,
			"successful_requests": 15641,
			"failed_requests":    31,
			"average_response_time": "45ms",
		},
		"timestamp": time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, gin.H{
		"performance": performance,
		"timestamp":   time.Now().Format(time.RFC3339),
	})
}

// handleGetAlerts 处理获取告警列表请求
func (s *HTTPServer) handleGetAlerts(c *gin.Context) {
	// 模拟告警数据
	alerts := []gin.H{
		{
			"id":          "alert-001",
			"name":        "High CPU Usage",
			"severity":    "warning",
			"status":      "active",
			"description": "CPU usage is above 80% for more than 5 minutes",
			"created_at":  time.Now().Add(-2 * time.Hour).Format(time.RFC3339),
			"updated_at":  time.Now().Add(-30 * time.Minute).Format(time.RFC3339),
			"source":      "system",
			"tags":        []string{"cpu", "performance"},
		},
		{
			"id":          "alert-002",
			"name":        "Pod Restart Loop",
			"severity":    "critical",
			"status":      "active",
			"description": "Pod nginx-deployment-xxx is in restart loop",
			"created_at":  time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
			"updated_at":  time.Now().Add(-10 * time.Minute).Format(time.RFC3339),
			"source":      "kubernetes",
			"tags":        []string{"pod", "restart", "nginx"},
		},
		{
			"id":          "alert-003",
			"name":        "Disk Space Low",
			"severity":    "warning",
			"status":      "resolved",
			"description": "Disk usage is above 85% on node worker-1",
			"created_at":  time.Now().Add(-6 * time.Hour).Format(time.RFC3339),
			"updated_at":  time.Now().Add(-2 * time.Hour).Format(time.RFC3339),
			"resolved_at": time.Now().Add(-2 * time.Hour).Format(time.RFC3339),
			"source":      "system",
			"tags":        []string{"disk", "storage"},
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"alerts":    alerts,
		"count":     len(alerts),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleCreateAlert 处理创建告警请求
func (s *HTTPServer) handleCreateAlert(c *gin.Context) {
	var alertRequest gin.H
	if err := c.ShouldBindJSON(&alertRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 模拟创建告警
	alert := gin.H{
		"id":          fmt.Sprintf("alert-%d", time.Now().Unix()),
		"name":        alertRequest["name"],
		"severity":    alertRequest["severity"],
		"status":      "active",
		"description": alertRequest["description"],
		"created_at":  time.Now().Format(time.RFC3339),
		"updated_at":  time.Now().Format(time.RFC3339),
		"source":      "manual",
		"tags":        alertRequest["tags"],
	}

	s.logger.Info("创建告警",
		zap.String("name", alert["name"].(string)),
		zap.String("severity", alert["severity"].(string)))

	c.JSON(http.StatusCreated, gin.H{
		"alert":     alert,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleDeleteAlert 处理删除告警请求
func (s *HTTPServer) handleDeleteAlert(c *gin.Context) {
	alertID := c.Param("id")

	s.logger.Info("删除告警", zap.String("alert_id", alertID))

	c.JSON(http.StatusOK, gin.H{
		"message":   "告警已删除",
		"alert_id":  alertID,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleGetConfig 处理获取配置请求
func (s *HTTPServer) handleGetConfig(c *gin.Context) {
	if s.configService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Config service not available"})
		return
	}

	config, err := s.configService.GetConfig("")
	if err != nil {
		s.logger.Error("获取配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"config":        config,
		"version":       config.Version,
		"last_modified": config.LastModified,
		"timestamp":     time.Now().Format(time.RFC3339),
	})
}

// handleGetConfigSection 处理获取配置部分请求
func (s *HTTPServer) handleGetConfigSection(c *gin.Context) {
	if s.configService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Config service not available"})
		return
	}

	section := c.Param("section")
	configSection, err := s.configService.GetConfigSection(section)
	if err != nil {
		s.logger.Error("获取配置部分失败", zap.String("section", section), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"section":   section,
		"config":    configSection,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleUpdateConfigSection 处理更新配置部分请求
func (s *HTTPServer) handleUpdateConfigSection(c *gin.Context) {
	if s.configService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Config service not available"})
		return
	}

	section := c.Param("section")
	var updateRequest service.ConfigUpdateRequest
	if err := c.ShouldBindJSON(&updateRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证配置（如果需要）
	if updateRequest.Validate {
		if err := s.configService.ValidateConfig(updateRequest.Content); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "配置验证失败: " + err.Error()})
			return
		}
	}

	// 更新配置
	err := s.configService.UpdateConfig(section, updateRequest.Content)
	if err != nil {
		s.logger.Error("更新配置失败", zap.String("section", section), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	s.logger.Info("配置已更新", zap.String("section", section))

	c.JSON(http.StatusOK, gin.H{
		"status":    "updated",
		"section":   section,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleReloadConfig 处理重载配置请求
func (s *HTTPServer) handleReloadConfig(c *gin.Context) {
	if s.configService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Config service not available"})
		return
	}

	err := s.configService.ReloadConfig()
	if err != nil {
		s.logger.Error("重载配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	s.logger.Info("配置已重载")

	c.JSON(http.StatusOK, gin.H{
		"status":    "reloaded",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleGetConfigHistory 处理获取配置历史请求
func (s *HTTPServer) handleGetConfigHistory(c *gin.Context) {
	if s.configService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Config service not available"})
		return
	}

	limit := 20
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	history, err := s.configService.GetConfigHistory(limit)
	if err != nil {
		s.logger.Error("获取配置历史失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"history":   history,
		"count":     len(history),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleRestoreConfig 处理恢复配置请求
func (s *HTTPServer) handleRestoreConfig(c *gin.Context) {
	if s.configService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Config service not available"})
		return
	}

	backupID := c.Param("id")
	err := s.configService.RestoreConfig(backupID)
	if err != nil {
		s.logger.Error("恢复配置失败", zap.String("backup_id", backupID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	s.logger.Info("配置已恢复", zap.String("backup_id", backupID))

	c.JSON(http.StatusOK, gin.H{
		"status":    "restored",
		"backup_id": backupID,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleValidateConfig 处理验证配置请求
func (s *HTTPServer) handleValidateConfig(c *gin.Context) {
	if s.configService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Config service not available"})
		return
	}

	var content interface{}
	if err := c.ShouldBindJSON(&content); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := s.configService.ValidateConfig(content)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"valid":     false,
			"error":     err.Error(),
			"timestamp": time.Now().Format(time.RFC3339),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":     true,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleWebSocketLogs 处理WebSocket日志连接
func (s *HTTPServer) handleWebSocketLogs(c *gin.Context) {
	s.wsManager.HandleWebSocketConnection(c, "logs")
}

// handleWebSocketStatus 处理WebSocket状态连接
func (s *HTTPServer) handleWebSocketStatus(c *gin.Context) {
	s.wsManager.HandleWebSocketConnection(c, "status")
}

// ETCDCronJobCreateRequest ETCD CronJob创建请求结构
type ETCDCronJobCreateRequest struct {
	Name               string `json:"name" binding:"required"`
	Namespace          string `json:"namespace,omitempty"`
	Schedule           string `json:"schedule" binding:"required"`
	TimeZone           string `json:"time_zone,omitempty"`
	LocalTime          bool   `json:"local_time,omitempty"`
	Image              string `json:"image,omitempty"`
	BackupPath         string `json:"backup_path,omitempty"`
	BackupRetain       int    `json:"backup_retain,omitempty"`
	BackupPVCName      string `json:"backup_pvc_name,omitempty"`
	ETCDCertSecretName string `json:"etcd_cert_secret_name,omitempty"`
}

// handleETCDCreateCronJob 处理创建ETCD CronJob请求
func (s *HTTPServer) handleETCDCreateCronJob(c *gin.Context) {
	if s.etcdService == nil {
		s.logger.Error("ETCD服务不可用")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "ETCD service not available"})
		return
	}

	var req ETCDCronJobCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		s.logger.Error("解析ETCD CronJob创建请求失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证必需参数
	if req.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "name is required"})
		return
	}
	if req.Schedule == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "schedule is required"})
		return
	}

	s.logger.Info("开始创建ETCD CronJob",
		zap.String("name", req.Name),
		zap.String("namespace", req.Namespace),
		zap.String("schedule", req.Schedule))

	// 构建CronJob选项
	opts := &domain.CronJobOptions{
		Name:               req.Name,
		Namespace:          req.Namespace,
		Schedule:           req.Schedule,
		TimeZone:           req.TimeZone,
		LocalTime:          req.LocalTime,
		Image:              req.Image,
		BackupPath:         req.BackupPath,
		BackupRetain:       req.BackupRetain,
		BackupPVCName:      req.BackupPVCName,
		ETCDCertSecretName: req.ETCDCertSecretName,
	}

	// 创建CronJob
	err := s.etcdService.CreateCronJob(c.Request.Context(), opts)
	if err != nil {
		s.logger.Error("创建ETCD CronJob失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	s.logger.Info("ETCD CronJob创建成功", zap.String("name", req.Name))

	c.JSON(http.StatusCreated, gin.H{
		"message":   "CronJob created successfully",
		"name":      req.Name,
		"namespace": req.Namespace,
		"schedule":  req.Schedule,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleETCDListCronJobs 处理列出ETCD CronJobs请求
func (s *HTTPServer) handleETCDListCronJobs(c *gin.Context) {
	if s.etcdService == nil {
		s.logger.Error("ETCD服务不可用")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "ETCD service not available"})
		return
	}

	// 获取命名空间参数
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	s.logger.Info("开始列出ETCD CronJobs", zap.String("namespace", namespace))

	// 列出CronJobs
	cronJobs, err := s.etcdService.ListCronJobs(c.Request.Context(), namespace)
	if err != nil {
		s.logger.Error("列出ETCD CronJobs失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	s.logger.Info("ETCD CronJobs列出成功",
		zap.String("namespace", namespace),
		zap.Int("count", len(cronJobs)))

	c.JSON(http.StatusOK, gin.H{
		"cronjobs":  cronJobs,
		"namespace": namespace,
		"count":     len(cronJobs),
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleETCDSuspendCronJob 处理暂停ETCD CronJob请求
func (s *HTTPServer) handleETCDSuspendCronJob(c *gin.Context) {
	if s.etcdService == nil {
		s.logger.Error("ETCD服务不可用")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "ETCD service not available"})
		return
	}

	// 获取路径参数
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "name parameter is required"})
		return
	}

	// 获取命名空间参数
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	s.logger.Info("开始暂停ETCD CronJob",
		zap.String("name", name),
		zap.String("namespace", namespace))

	// 暂停CronJob
	err := s.etcdService.SuspendCronJob(c.Request.Context(), namespace, name)
	if err != nil {
		s.logger.Error("暂停ETCD CronJob失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	s.logger.Info("ETCD CronJob暂停成功",
		zap.String("name", name),
		zap.String("namespace", namespace))

	c.JSON(http.StatusOK, gin.H{
		"message":   "CronJob suspended successfully",
		"name":      name,
		"namespace": namespace,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleETCDResumeCronJob 处理恢复ETCD CronJob请求
func (s *HTTPServer) handleETCDResumeCronJob(c *gin.Context) {
	if s.etcdService == nil {
		s.logger.Error("ETCD服务不可用")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "ETCD service not available"})
		return
	}

	// 获取路径参数
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "name parameter is required"})
		return
	}

	// 获取命名空间参数
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	s.logger.Info("开始恢复ETCD CronJob",
		zap.String("name", name),
		zap.String("namespace", namespace))

	// 恢复CronJob
	err := s.etcdService.ResumeCronJob(c.Request.Context(), namespace, name)
	if err != nil {
		s.logger.Error("恢复ETCD CronJob失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	s.logger.Info("ETCD CronJob恢复成功",
		zap.String("name", name),
		zap.String("namespace", namespace))

	c.JSON(http.StatusOK, gin.H{
		"message":   "CronJob resumed successfully",
		"name":      name,
		"namespace": namespace,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleETCDDeleteCronJob 处理删除ETCD CronJob请求
func (s *HTTPServer) handleETCDDeleteCronJob(c *gin.Context) {
	if s.etcdService == nil {
		s.logger.Error("ETCD服务不可用")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "ETCD service not available"})
		return
	}

	// 获取路径参数
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "name parameter is required"})
		return
	}

	// 获取命名空间参数
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	s.logger.Info("开始删除ETCD CronJob",
		zap.String("name", name),
		zap.String("namespace", namespace))

	// 删除CronJob
	err := s.etcdService.DeleteCronJob(c.Request.Context(), namespace, name)
	if err != nil {
		s.logger.Error("删除ETCD CronJob失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	s.logger.Info("ETCD CronJob删除成功",
		zap.String("name", name),
		zap.String("namespace", namespace))

	c.JSON(http.StatusOK, gin.H{
		"message":   "CronJob deleted successfully",
		"name":      name,
		"namespace": namespace,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}
