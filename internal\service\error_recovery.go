package service

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
)

// RecoveryStrategy 恢复策略
type RecoveryStrategy string

const (
	RecoveryStrategyRetry     RecoveryStrategy = "retry"
	RecoveryStrategyFallback  RecoveryStrategy = "fallback"
	RecoveryStrategySkip      RecoveryStrategy = "skip"
	RecoveryStrategyAbort     RecoveryStrategy = "abort"
)

// RecoveryAction 恢复动作
type RecoveryAction struct {
	Strategy    RecoveryStrategy
	MaxRetries  int
	RetryDelay  time.Duration
	FallbackFn  func(ctx context.Context) error
	Description string
}

// RecoveryConfig 恢复配置
type RecoveryConfig struct {
	Enabled           bool                            `yaml:"enabled"`
	MaxRetries        int                             `yaml:"max_retries"`
	RetryDelay        time.Duration                   `yaml:"retry_delay"`
	BackoffMultiplier float64                         `yaml:"backoff_multiplier"`
	MaxRetryDelay     time.Duration                   `yaml:"max_retry_delay"`
	ErrorActions      map[ErrorCode]*RecoveryAction   `yaml:"error_actions"`
}

// DefaultRecoveryConfig 默认恢复配置
func DefaultRecoveryConfig() *RecoveryConfig {
	return &RecoveryConfig{
		Enabled:           true,
		MaxRetries:        3,
		RetryDelay:        1 * time.Second,
		BackoffMultiplier: 2.0,
		MaxRetryDelay:     30 * time.Second,
		ErrorActions: map[ErrorCode]*RecoveryAction{
			ErrorCodeETCDConnectionFailed: {
				Strategy:    RecoveryStrategyRetry,
				MaxRetries:  5,
				RetryDelay:  2 * time.Second,
				Description: "ETCD连接失败时重试",
			},
			ErrorCodeETCDTimeoutError: {
				Strategy:    RecoveryStrategyRetry,
				MaxRetries:  3,
				RetryDelay:  5 * time.Second,
				Description: "ETCD超时时重试",
			},
			ErrorCodeBackupToolNotFound: {
				Strategy:    RecoveryStrategyFallback,
				MaxRetries:  1,
				RetryDelay:  0,
				Description: "备份工具未找到时回退到SDK",
			},
			ErrorCodeBackupSDKFailed: {
				Strategy:    RecoveryStrategyFallback,
				MaxRetries:  1,
				RetryDelay:  0,
				Description: "SDK备份失败时回退到工具",
			},
			ErrorCodeNetworkConnectionFailed: {
				Strategy:    RecoveryStrategyRetry,
				MaxRetries:  3,
				RetryDelay:  3 * time.Second,
				Description: "网络连接失败时重试",
			},
			ErrorCodeSystemDiskSpaceInsufficient: {
				Strategy:    RecoveryStrategyAbort,
				MaxRetries:  0,
				RetryDelay:  0,
				Description: "磁盘空间不足时中止操作",
			},
		},
	}
}

// ErrorRecoveryManager 错误恢复管理器
type ErrorRecoveryManager struct {
	logger *zap.Logger
	config *RecoveryConfig
}

// NewErrorRecoveryManager 创建错误恢复管理器
func NewErrorRecoveryManager(logger *zap.Logger, config *RecoveryConfig) *ErrorRecoveryManager {
	if config == nil {
		config = DefaultRecoveryConfig()
	}

	return &ErrorRecoveryManager{
		logger: logger,
		config: config,
	}
}

// RecoveryResult 恢复结果
type RecoveryResult struct {
	Success      bool
	Attempts     int
	TotalTime    time.Duration
	Strategy     RecoveryStrategy
	FinalError   error
	RecoveredBy  string
}

// AttemptRecovery 尝试错误恢复
func (erm *ErrorRecoveryManager) AttemptRecovery(ctx context.Context, err error, operation func(ctx context.Context) error) *RecoveryResult {
	if !erm.config.Enabled {
		return &RecoveryResult{
			Success:    false,
			Attempts:   0,
			Strategy:   RecoveryStrategyAbort,
			FinalError: err,
		}
	}

	startTime := time.Now()
	
	// 分析错误并获取恢复策略
	strategy, action := erm.getRecoveryStrategy(err)
	
	erm.logger.Info("开始错误恢复",
		zap.String("strategy", string(strategy)),
		zap.String("error", err.Error()),
		zap.Int("max_retries", action.MaxRetries))

	switch strategy {
	case RecoveryStrategyRetry:
		return erm.retryOperation(ctx, operation, action, startTime)
	case RecoveryStrategyFallback:
		return erm.fallbackOperation(ctx, action, startTime)
	case RecoveryStrategySkip:
		return erm.skipOperation(startTime)
	case RecoveryStrategyAbort:
		return erm.abortOperation(err, startTime)
	default:
		return erm.abortOperation(err, startTime)
	}
}

// getRecoveryStrategy 获取恢复策略
func (erm *ErrorRecoveryManager) getRecoveryStrategy(err error) (RecoveryStrategy, *RecoveryAction) {
	// 检查是否为增强的服务错误
	if enhancedErr, ok := err.(*EnhancedServiceError); ok {
		if action, exists := erm.config.ErrorActions[enhancedErr.ErrorCode]; exists {
			return action.Strategy, action
		}
	}

	// 默认策略
	return RecoveryStrategyAbort, &RecoveryAction{
		Strategy:    RecoveryStrategyAbort,
		MaxRetries:  0,
		RetryDelay:  0,
		Description: "默认中止策略",
	}
}

// retryOperation 重试操作
func (erm *ErrorRecoveryManager) retryOperation(ctx context.Context, operation func(ctx context.Context) error, action *RecoveryAction, startTime time.Time) *RecoveryResult {
	var lastErr error
	delay := action.RetryDelay

	for attempt := 1; attempt <= action.MaxRetries; attempt++ {
		erm.logger.Info("重试操作",
			zap.Int("attempt", attempt),
			zap.Int("max_retries", action.MaxRetries),
			zap.Duration("delay", delay))

		// 等待重试延迟
		if delay > 0 {
			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return &RecoveryResult{
					Success:    false,
					Attempts:   attempt - 1,
					TotalTime:  time.Since(startTime),
					Strategy:   RecoveryStrategyRetry,
					FinalError: ctx.Err(),
				}
			}
		}

		// 执行操作
		if err := operation(ctx); err != nil {
			lastErr = err
			erm.logger.Warn("重试失败",
				zap.Int("attempt", attempt),
				zap.Error(err))

			// 计算下次重试延迟（指数退避）
			delay = time.Duration(float64(delay) * erm.config.BackoffMultiplier)
			if delay > erm.config.MaxRetryDelay {
				delay = erm.config.MaxRetryDelay
			}
		} else {
			// 成功
			erm.logger.Info("重试成功",
				zap.Int("attempt", attempt),
				zap.Duration("total_time", time.Since(startTime)))

			return &RecoveryResult{
				Success:     true,
				Attempts:    attempt,
				TotalTime:   time.Since(startTime),
				Strategy:    RecoveryStrategyRetry,
				RecoveredBy: "retry",
			}
		}
	}

	// 所有重试都失败了
	return &RecoveryResult{
		Success:    false,
		Attempts:   action.MaxRetries,
		TotalTime:  time.Since(startTime),
		Strategy:   RecoveryStrategyRetry,
		FinalError: lastErr,
	}
}

// fallbackOperation 回退操作
func (erm *ErrorRecoveryManager) fallbackOperation(ctx context.Context, action *RecoveryAction, startTime time.Time) *RecoveryResult {
	if action.FallbackFn == nil {
		return &RecoveryResult{
			Success:    false,
			Attempts:   0,
			TotalTime:  time.Since(startTime),
			Strategy:   RecoveryStrategyFallback,
			FinalError: fmt.Errorf("没有配置回退函数"),
		}
	}

	erm.logger.Info("执行回退操作", zap.String("description", action.Description))

	if err := action.FallbackFn(ctx); err != nil {
		return &RecoveryResult{
			Success:    false,
			Attempts:   1,
			TotalTime:  time.Since(startTime),
			Strategy:   RecoveryStrategyFallback,
			FinalError: err,
		}
	}

	return &RecoveryResult{
		Success:     true,
		Attempts:    1,
		TotalTime:   time.Since(startTime),
		Strategy:    RecoveryStrategyFallback,
		RecoveredBy: "fallback",
	}
}

// skipOperation 跳过操作
func (erm *ErrorRecoveryManager) skipOperation(startTime time.Time) *RecoveryResult {
	erm.logger.Info("跳过操作")
	
	return &RecoveryResult{
		Success:     true,
		Attempts:    0,
		TotalTime:   time.Since(startTime),
		Strategy:    RecoveryStrategySkip,
		RecoveredBy: "skip",
	}
}

// abortOperation 中止操作
func (erm *ErrorRecoveryManager) abortOperation(err error, startTime time.Time) *RecoveryResult {
	erm.logger.Error("中止操作", zap.Error(err))
	
	return &RecoveryResult{
		Success:    false,
		Attempts:   0,
		TotalTime:  time.Since(startTime),
		Strategy:   RecoveryStrategyAbort,
		FinalError: err,
	}
}

// BackupRecoveryWrapper 备份操作恢复包装器
type BackupRecoveryWrapper struct {
	backupService *ETCDBackupService
	recoveryMgr   *ErrorRecoveryManager
	logger        *zap.Logger
}

// NewBackupRecoveryWrapper 创建备份恢复包装器
func NewBackupRecoveryWrapper(backupService *ETCDBackupService, recoveryMgr *ErrorRecoveryManager, logger *zap.Logger) *BackupRecoveryWrapper {
	return &BackupRecoveryWrapper{
		backupService: backupService,
		recoveryMgr:   recoveryMgr,
		logger:        logger,
	}
}

// BackupWithRecovery 带恢复机制的备份
func (brw *BackupRecoveryWrapper) BackupWithRecovery(ctx context.Context, opts *domain.BackupOptions) (*domain.BackupResult, error) {
	var result *domain.BackupResult

	operation := func(ctx context.Context) error {
		var err error
		result, err = brw.backupService.Backup(ctx, opts)
		return err
	}

	recoveryResult := brw.recoveryMgr.AttemptRecovery(ctx, nil, operation)

	if recoveryResult.Success {
		brw.logger.Info("备份操作成功",
			zap.String("recovery_strategy", string(recoveryResult.Strategy)),
			zap.Int("attempts", recoveryResult.Attempts),
			zap.Duration("total_time", recoveryResult.TotalTime))
		return result, nil
	}

	brw.logger.Error("备份操作最终失败",
		zap.String("recovery_strategy", string(recoveryResult.Strategy)),
		zap.Int("attempts", recoveryResult.Attempts),
		zap.Duration("total_time", recoveryResult.TotalTime),
		zap.Error(recoveryResult.FinalError))

	return nil, recoveryResult.FinalError
}