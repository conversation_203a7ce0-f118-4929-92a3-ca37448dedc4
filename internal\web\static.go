//go:generate go run ../../scripts/generate.go

//go:build !dev
// +build !dev

package web

import (
	"fmt"
	"io/fs"
	"net/http"
	"net/http/httptest"
	"log"
	"os"
	"path/filepath"

	"github.com/vearutop/statigz"
	"github.com/vearutop/statigz/brotli"
)

// EmbeddedFS 外部嵌入文件系统的接口
// 这个变量将在main包中设置
var EmbeddedFS fs.FS

// staticHandler 缓存的静态文件处理器
var staticHandler http.Handler

// GetStaticHandler 返回生产环境的静态文件处理器
// 实现了高性能的压缩文件服务，支持 gzip 和 brotli 压缩
func GetStaticHandler() http.Handler {
	// 使用单例模式，避免重复初始化
	if staticHandler != nil {
		return staticHandler
	}

	log.Println("[INFO] Initializing production static file handler with embedded files")

	// 尝试使用嵌入的文件系统
	var distFS fs.FS

	if EmbeddedFS != nil {
		// 使用嵌入的文件系统
		distFS = EmbeddedFS
		log.Println("[INFO] Using embedded static files")
	} else {
		log.Printf("[WARN] No embedded files available, falling back to filesystem")
		// 回退到文件系统
		distPath := filepath.Join("frontend", "dist")
		if _, err := os.Stat(distPath); os.IsNotExist(err) {
			log.Printf("[ERROR] Frontend dist directory not found: %s", distPath)
			return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				http.Error(w, "Static files not available: frontend not built", http.StatusInternalServerError)
			})
		}
		distFS = os.DirFS(distPath)
		log.Println("[INFO] Using filesystem static files")
	}

	// 验证文件系统是否支持 ReadDir
	readDirFS, ok := distFS.(fs.ReadDirFS)
	if !ok {
		log.Printf("[ERROR] Filesystem does not support ReadDir")
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			http.Error(w, "Static files filesystem error", http.StatusInternalServerError)
		})
	}

	// 创建 statigz 文件服务器
	// 移除 EncodeOnInit 以避免启动时的长时间压缩
	handler := statigz.FileServer(
		readDirFS,
		// 启用 brotli 压缩编码
		brotli.AddEncoding,
		// 按需压缩，而不是初始化时压缩所有文件
		// statigz.EncodeOnInit, // 移除这个选项
	)

	log.Println("[INFO] Production static file handler initialized successfully")
	log.Println("[INFO] Supported encodings: gzip, brotli")
	log.Println("[INFO] On-demand compression enabled for better startup performance")

	// 包装处理器以添加自定义缓存头和日志
	staticHandler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 设置缓存头
		SetCacheHeaders(w, r.URL.Path)

		// 添加安全头
		w.Header().Set("X-Content-Type-Options", "nosniff")
		w.Header().Set("X-Frame-Options", "DENY")
		w.Header().Set("X-XSS-Protection", "1; mode=block")

		// 记录静态文件请求（仅在调试模式下）
		if r.URL.Query().Get("debug") == "1" {
			log.Printf("[DEBUG] Static file request: %s", r.URL.Path)
		}

		// 特殊处理 HEAD 请求
		// statigz 库可能不正确处理 HEAD 请求，我们将其转换为 GET 请求处理
		if r.Method == http.MethodHead {
			// 创建 GET 请求的副本
			getRequest := r.Clone(r.Context())
			getRequest.Method = http.MethodGet

			// 使用响应记录器捕获响应头和状态码
			recorder := httptest.NewRecorder()

			// 调用 statigz 处理器处理 GET 请求
			handler.ServeHTTP(recorder, getRequest)

			// 复制头部信息到实际响应
			for key, values := range recorder.Header() {
				for _, value := range values {
					w.Header().Add(key, value)
				}
			}

			// 设置状态码但不写入响应体
			w.WriteHeader(recorder.Code)
			return
		}

		// 调用 statigz 处理器
		handler.ServeHTTP(w, r)
	})

	return staticHandler
}

// GetEmbeddedFileInfo 获取静态文件的信息（用于调试和监控）
func GetEmbeddedFileInfo() (map[string]interface{}, error) {
	info := make(map[string]interface{})

	// 获取前端构建目录
	distPath := filepath.Join("frontend", "dist")
	distFS := os.DirFS(distPath)
	
	// 统计文件数量和大小
	var fileCount int
	var totalSize int64

	err := fs.WalkDir(distFS, ".", func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		
		if !d.IsDir() {
			fileCount++
			if fileInfo, err := d.Info(); err == nil {
				totalSize += fileInfo.Size()
			}
		}
		
		return nil
	})
	
	if err != nil {
		return nil, fmt.Errorf("failed to walk embedded files: %w", err)
	}
	
	info["file_count"] = fileCount
	info["total_size"] = totalSize
	info["compression_enabled"] = true
	info["encodings"] = []string{"gzip", "brotli"}
	
	return info, nil
}

// ValidateEmbeddedFiles 验证静态文件是否完整
func ValidateEmbeddedFiles() error {
	// 检查关键文件是否存在
	requiredFiles := []string{
		"index.html",
	}

	distPath := filepath.Join("frontend", "dist")
	distFS := os.DirFS(distPath)
	
	for _, file := range requiredFiles {
		if _, err := fs.Stat(distFS, file); err != nil {
			return fmt.Errorf("required file %s not found in embedded files: %w", file, err)
		}
	}
	
	log.Println("[INFO] Embedded files validation passed")
	return nil
}
