// 组件开发模板
// 复制此文件作为新组件的起点

import React from 'react';
import type { ComponentProps } from '@/types';

// 组件Props接口定义
interface Props extends ComponentProps {
  // 必需属性
  title: string;
  
  // 可选属性
  description?: string;
  loading?: boolean;
  disabled?: boolean;
  
  // 事件处理器
  onClick?: () => void;
  onSubmit?: (data: any) => void;
  
  // 子组件
  children?: React.ReactNode;
}

/**
 * 组件描述
 * 
 * @param props 组件属性
 * @returns JSX元素
 * 
 * @example
 * ```tsx
 * <ComponentTemplate 
 *   title="示例标题"
 *   description="示例描述"
 *   onClick={() => console.log('clicked')}
 * />
 * ```
 */
export const ComponentTemplate: React.FC<Props> = ({
  title,
  description,
  loading = false,
  disabled = false,
  onClick,
  onSubmit,
  children,
  className,
  style,
  ...restProps
}) => {
  // 内部状态
  const [internalState, setInternalState] = React.useState<string>('');
  
  // 副作用
  React.useEffect(() => {
    // 组件挂载时的逻辑
    return () => {
      // 组件卸载时的清理逻辑
    };
  }, []);
  
  // 事件处理函数
  const handleClick = React.useCallback(() => {
    if (disabled || loading) return;
    onClick?.();
  }, [disabled, loading, onClick]);
  
  const handleSubmit = React.useCallback((data: any) => {
    if (disabled || loading) return;
    onSubmit?.(data);
  }, [disabled, loading, onSubmit]);
  
  // 计算属性
  const computedClassName = React.useMemo(() => {
    const classes = ['component-template'];
    if (loading) classes.push('loading');
    if (disabled) classes.push('disabled');
    if (className) classes.push(className);
    return classes.join(' ');
  }, [loading, disabled, className]);
  
  // 渲染
  return (
    <div 
      className={computedClassName}
      style={style}
      {...restProps}
    >
      <h2>{title}</h2>
      {description && <p>{description}</p>}
      
      {loading && <div>加载中...</div>}
      
      <button 
        onClick={handleClick}
        disabled={disabled || loading}
      >
        点击按钮
      </button>
      
      {children}
    </div>
  );
};

// 默认导出
export default ComponentTemplate;

// 组件的显示名称（用于调试）
ComponentTemplate.displayName = 'ComponentTemplate';
