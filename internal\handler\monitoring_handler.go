package handler

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.uber.org/zap"
	"k8s-helper/internal/service"
)

// MonitoringHandler 监控处理器
type MonitoringHandler struct {
	logger             *zap.Logger
	metricsCollector   *service.MetricsCollector
	healthChecker      *service.HealthChecker
	performanceMonitor *service.PerformanceMonitor
}

// NewMonitoringHandler 创建监控处理器
func NewMonitoringHandler(
	logger *zap.Logger,
	metricsCollector *service.MetricsCollector,
	healthChecker *service.HealthChecker,
	performanceMonitor *service.PerformanceMonitor,
) *MonitoringHandler {
	return &MonitoringHandler{
		logger:             logger,
		metricsCollector:   metricsCollector,
		healthChecker:      healthChecker,
		performanceMonitor: performanceMonitor,
	}
}

// RegisterRoutes 注册监控路由
func (mh *MonitoringHandler) RegisterRoutes(mux *http.ServeMux) {
	// Prometheus指标端点
	mux.Handle("/metrics", promhttp.HandlerFor(
		mh.metricsCollector.GetRegistry(),
		promhttp.HandlerOpts{
			EnableOpenMetrics: true,
		},
	))

	// 健康检查端点
	mux.HandleFunc("/health", mh.handleHealth)
	mux.HandleFunc("/health/live", mh.handleLiveness)
	mux.HandleFunc("/health/ready", mh.handleReadiness)

	// 性能监控端点
	mux.HandleFunc("/monitoring/performance", mh.handlePerformance)
	mux.HandleFunc("/monitoring/operations", mh.handleOperations)
	mux.HandleFunc("/monitoring/system", mh.handleSystem)

	// 管理端点
	mux.HandleFunc("/monitoring/reset", mh.handleReset)

	mh.logger.Info("监控路由已注册")
}

// handleHealth 处理健康检查请求
func (mh *MonitoringHandler) handleHealth(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()

	health := mh.healthChecker.Check(ctx)

	// 设置响应状态码
	statusCode := http.StatusOK
	switch health.Status {
	case service.HealthStatusUnhealthy:
		statusCode = http.StatusServiceUnavailable
	case service.HealthStatusDegraded:
		statusCode = http.StatusPartialContent
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(health); err != nil {
		mh.logger.Error("编码健康检查响应失败", zap.Error(err))
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	mh.logger.Debug("健康检查请求处理完成",
		zap.String("status", string(health.Status)),
		zap.Int("statusCode", statusCode))
}

// handleLiveness 处理存活性检查
func (mh *MonitoringHandler) handleLiveness(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 简单的存活性检查，只要服务在运行就返回200
	response := map[string]interface{}{
		"status":    "alive",
		"timestamp": time.Now(),
		"message":   "Service is running",
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// handleReadiness 处理就绪性检查
func (mh *MonitoringHandler) handleReadiness(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 检查服务是否就绪（可以处理请求）
	isReady := mh.healthChecker.IsHealthy()

	response := map[string]interface{}{
		"status":    "ready",
		"timestamp": time.Now(),
		"ready":     isReady,
	}

	statusCode := http.StatusOK
	if !isReady {
		statusCode = http.StatusServiceUnavailable
		response["status"] = "not_ready"
		response["message"] = "Service is not ready to handle requests"
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// handlePerformance 处理性能监控请求
func (mh *MonitoringHandler) handlePerformance(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	report := mh.performanceMonitor.GetPerformanceReport()

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(report); err != nil {
		mh.logger.Error("编码性能报告失败", zap.Error(err))
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// handleOperations 处理操作指标请求
func (mh *MonitoringHandler) handleOperations(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取查询参数
	query := r.URL.Query()
	limitStr := query.Get("limit")
	errorOnly := query.Get("errors_only") == "true"

	var limit int
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	var operations interface{}

	if errorOnly {
		operations = mh.performanceMonitor.GetErrorOperations(limit)
	} else {
		allOps := mh.performanceMonitor.GetOperationMetrics()
		if limit > 0 && len(allOps) > limit {
			// 简化处理，实际可以按需求排序
			limitedOps := make(map[string]*service.OperationMetric)
			count := 0
			for name, metric := range allOps {
				if count >= limit {
					break
				}
				limitedOps[name] = metric
				count++
			}
			operations = limitedOps
		} else {
			operations = allOps
		}
	}

	response := map[string]interface{}{
		"timestamp":  time.Now(),
		"operations": operations,
		"filters": map[string]interface{}{
			"limit":       limit,
			"errors_only": errorOnly,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		mh.logger.Error("编码操作指标失败", zap.Error(err))
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// handleSystem 处理系统指标请求
func (mh *MonitoringHandler) handleSystem(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 获取查询参数
	query := r.URL.Query()
	historyStr := query.Get("history")
	currentOnly := query.Get("current_only") == "true"

	var historyLimit int
	if historyStr != "" {
		if h, err := strconv.Atoi(historyStr); err == nil && h > 0 {
			historyLimit = h
		}
	}

	response := map[string]interface{}{
		"timestamp": time.Now(),
	}

	if currentOnly {
		response["current"] = mh.performanceMonitor.GetCurrentSystemMetric()
	} else {
		systemMetrics := mh.performanceMonitor.GetSystemMetrics()
		if historyLimit > 0 && len(systemMetrics) > historyLimit {
			// 获取最近的N条记录
			start := len(systemMetrics) - historyLimit
			systemMetrics = systemMetrics[start:]
		}
		response["history"] = systemMetrics
		response["current"] = mh.performanceMonitor.GetCurrentSystemMetric()
	}

	response["filters"] = map[string]interface{}{
		"history_limit": historyLimit,
		"current_only":  currentOnly,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		mh.logger.Error("编码系统指标失败", zap.Error(err))
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// handleReset 处理重置指标请求
func (mh *MonitoringHandler) handleReset(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求体
	var resetRequest struct {
		ResetPerformance bool `json:"reset_performance"`
		ResetMetrics     bool `json:"reset_metrics"`
	}

	if err := json.NewDecoder(r.Body).Decode(&resetRequest); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	response := map[string]interface{}{
		"timestamp": time.Now(),
		"reset":     make(map[string]bool),
	}

	if resetRequest.ResetPerformance {
		mh.performanceMonitor.ResetMetrics()
		response["reset"].(map[string]bool)["performance"] = true
		mh.logger.Info("性能指标已重置")
	}

	// 注意：Prometheus指标通常不需要重置，因为它们是累积的
	// 如果需要重置，需要重新创建MetricsCollector
	if resetRequest.ResetMetrics {
		response["reset"].(map[string]bool)["metrics"] = true
		mh.logger.Info("Prometheus指标重置请求已接收（注意：Prometheus指标通常不重置）")
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		mh.logger.Error("编码重置响应失败", zap.Error(err))
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// HealthResponse 健康检查响应格式
type HealthResponse struct {
	Status     string                 `json:"status"`
	Timestamp  time.Time              `json:"timestamp"`
	Duration   string                 `json:"duration"`
	Version    string                 `json:"version"`
	Components map[string]interface{} `json:"components"`
	Summary    map[string]interface{} `json:"summary"`
}

// MetricsResponse 指标响应格式
type MetricsResponse struct {
	Timestamp time.Time   `json:"timestamp"`
	Data      interface{} `json:"data"`
	Filters   interface{} `json:"filters,omitempty"`
}
