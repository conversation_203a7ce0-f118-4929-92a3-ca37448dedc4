#!/bin/bash

# --- Configuration ---
# Etcd data directory used by kubeadm
ETCD_DATA_DIR="/var/lib/etcd"
# Directory where etcd certificates are stored by kubeadm
ETCD_PKI_DIR="/etc/kubernetes/pki/etcd"
# Directory to store backups
BACKUP_BASE_DIR="/opt/etcd-backups"
# Etcd endpoint (usually localhost on a control-plane node)
ETCD_ENDPOINT="https://127.0.0.1:2379"

# --- Certificate Configuration ---
# Default certificates - PRIORITIZING server.crt/server.key as requested
ETCD_CACERT_DEFAULT="$ETCD_PKI_DIR/ca.crt"
ETCD_CERT_DEFAULT="$ETCD_PKI_DIR/server.crt"
ETCD_KEY_DEFAULT="$ETCD_PKI_DIR/server.key"
# Fallback certificates (apiserver client certs)
ETCD_CERT_FALLBACK="$ETCD_PKI_DIR/apiserver-etcd-client.crt"
ETCD_KEY_FALLBACK="$ETCD_PKI_DIR/apiserver-etcd-client.key"

# Variables to hold the actual paths used
ETCD_CACERT=""
ETCD_CERT=""
ETCD_KEY=""

# Download URLs for etcd tools
DOWNLOAD_URL_AMD64="https://wutong-paas.obs.cn-east-3.myhuaweicloud.com/kubeadm-ansible/etcd/etcd-v3.5.15-linux-amd64.tar.gz"
DOWNLOAD_URL_ARM64="https://wutong-paas.obs.cn-east-3.myhuaweicloud.com/kubeadm-ansible/etcd/etcd-v3.5.15-linux-arm64.tar.gz"

# Variables to hold the command path
ETCDCTL_CMD=""
DOWNLOAD_DIR="/tmp/etcd_download"
INSTALL_DIR="/usr/local/bin" # Standard location for user-installed binaries

# --- Helper Functions ---

# Function to print messages
log_info() {
    echo "[INFO] $1"
}

log_warn() {
    echo "[WARN] $1"
}

log_error() {
    echo "[ERROR] $1" >&2
}

# Function to check for root privileges
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限才能运行。"
        exit 1
    fi
}

# Function to check prerequisites for backup and determine cert paths
check_backup_prereqs() {
    log_info "正在检查备份所需的证书..."

    # Check CA certificate
    if [ -f "$ETCD_CACERT_DEFAULT" ]; then
        ETCD_CACERT="$ETCD_CACERT_DEFAULT"
        log_info "找到 CA 证书: $ETCD_CACERT"
    else
        log_error "找不到必需的 Etcd CA 证书: $ETCD_CACERT_DEFAULT"
        exit 1
    fi

    # Check for preferred cert/key (server.crt/key)
    if [ -f "$ETCD_CERT_DEFAULT" ] && [ -f "$ETCD_KEY_DEFAULT" ]; then
        ETCD_CERT="$ETCD_CERT_DEFAULT"
        ETCD_KEY="$ETCD_KEY_DEFAULT"
        log_info "找到首选证书/密钥: $ETCD_CERT / $ETCD_KEY"
    # Check for fallback cert/key (apiserver-etcd-client.crt/key)
    elif [ -f "$ETCD_CERT_FALLBACK" ] && [ -f "$ETCD_KEY_FALLBACK" ]; then
        log_warn "未找到首选证书/密钥 ($ETCD_CERT_DEFAULT / $ETCD_KEY_DEFAULT)。"
        ETCD_CERT="$ETCD_CERT_FALLBACK"
        ETCD_KEY="$ETCD_KEY_FALLBACK"
        log_info "使用备选证书/密钥: $ETCD_CERT / $ETCD_KEY"
    else
        log_error "找不到任何可用的 Etcd 证书/密钥对。"
        log_error "检查路径: $ETCD_CERT_DEFAULT / $ETCD_KEY_DEFAULT"
        log_error "或检查路径: $ETCD_CERT_FALLBACK / $ETCD_KEY_FALLBACK"
        exit 1
    fi
}


# Function to find or download etcdctl/etcdutl
find_or_download_etcd_tools() {
    log_info "正在检查 etcdctl 或 etcdutl..."
    if command -v etcdctl &> /dev/null; then
        ETCDCTL_CMD=$(command -v etcdctl)
        log_info "找到 etcdctl: $ETCDCTL_CMD"
        # Check etcdctl version compatibility if needed (optional)
        # etcdctl_version=$($ETCDCTL_CMD version | grep "etcdctl version" | awk '{print $3}')
        # log_info "etcdctl 版本: $etcdctl_version"
        return 0
    elif command -v etcdutl &> /dev/null; then
        ETCDCTL_CMD=$(command -v etcdutl)
        log_info "找到 etcdutl: $ETCDCTL_CMD"
        # etcdutl_version=$($ETCDCTL_CMD version | grep "etcdutl version" | awk '{print $3}') # Adjust command if needed
        # log_info "etcdutl 版本: $etcdutl_version"
        return 0
    else
        log_warn "系统中未找到 etcdctl 或 etcdutl。"
        read -p "是否尝试下载并安装 etcd v3.5.15? (y/N): " confirm_download
        if [[ ! "$confirm_download" =~ ^[Yy]$ ]]; then
            log_error "用户取消下载。脚本无法继续。"
            exit 1
        fi

        # Determine architecture
        ARCH=$(uname -m)
        local download_url=""
        local tar_filename=""

        log_info "检测到系统架构: $ARCH"
        if [[ "$ARCH" == "x86_64" ]] || [[ "$ARCH" == "amd64" ]]; then
            download_url="$DOWNLOAD_URL_AMD64"
            tar_filename=$(basename "$download_url")
        elif [[ "$ARCH" == "aarch64" ]] || [[ "$ARCH" == "arm64" ]]; then
            download_url="$DOWNLOAD_URL_ARM64"
            tar_filename=$(basename "$download_url")
        else
            log_error "不支持的系统架构: $ARCH"
            exit 1
        fi

        log_info "将从 $download_url 下载..."
        mkdir -p "$DOWNLOAD_DIR"
        if command -v wget &> /dev/null; then
            wget -q -O "$DOWNLOAD_DIR/$tar_filename" "$download_url"
        elif command -v curl &> /dev/null; then
            curl -s -L -o "$DOWNLOAD_DIR/$tar_filename" "$download_url"
        else
            log_error "未找到 wget 或 curl。无法下载 etcd 工具。"
            rmdir "$DOWNLOAD_DIR" # Clean up empty dir
            exit 1
        fi

        if [ $? -ne 0 ] || [ ! -f "$DOWNLOAD_DIR/$tar_filename" ]; then
            log_error "下载失败: $tar_filename"
            rm -rf "$DOWNLOAD_DIR"
            exit 1
        fi
        log_info "下载完成: $DOWNLOAD_DIR/$tar_filename"

        log_info "正在解压..."
        # Extract specific binaries (etcdctl, etcdutl) into the download dir first
        # Tar structure might be like etcd-vX.Y.Z-linux-ARCH/etcdctl
        # Use find within tar listing to locate binaries robustly
        local tar_content_path=""
        tar_content_path=$(tar -tzf "$DOWNLOAD_DIR/$tar_filename" | grep -E '(etcdctl|etcdutl)$' | head -n 1)

        if [ -z "$tar_content_path" ]; then
             log_error "在归档文件中未找到 etcdctl 或 etcdutl。"
             rm -rf "$DOWNLOAD_DIR"
             exit 1
        fi

        local basedir_in_tar=$(dirname "$tar_content_path")
        local strip_components_count=$(echo "$basedir_in_tar" | awk -F/ '{print NF}')

        log_info "从 $basedir_in_tar 中提取..."
        tar -xzf "$DOWNLOAD_DIR/$tar_filename" -C "$DOWNLOAD_DIR" --strip-components="$strip_components_count" "$tar_content_path"

        if [ $? -ne 0 ]; then
             log_error "解压失败: $tar_filename"
             rm -rf "$DOWNLOAD_DIR"
             exit 1
        fi

        local extracted_binary="$DOWNLOAD_DIR/$(basename "$tar_content_path")"

        if [ -f "$extracted_binary" ]; then
            log_info "找到解压后的 $(basename "$extracted_binary")，正在尝试安装到 $INSTALL_DIR..."
            install -m 755 "$extracted_binary" "$INSTALL_DIR/"
            if [ $? -ne 0 ]; then
                log_error "安装 $(basename "$extracted_binary") 到 $INSTALL_DIR 失败。请检查权限。"
                rm -rf "$DOWNLOAD_DIR"
                exit 1
            fi
            ETCDCTL_CMD="$INSTALL_DIR/$(basename "$extracted_binary")"
            log_info "$(basename "$extracted_binary") 安装成功: $ETCDCTL_CMD"
        else
             log_error "在下载的归档文件中未找到 etcdctl 或 etcdutl 的有效文件。"
             rm -rf "$DOWNLOAD_DIR"
             exit 1
        fi

        log_info "清理下载文件..."
        rm -rf "$DOWNLOAD_DIR"
        # Verify the command is now available
        if ! command -v "$(basename $ETCDCTL_CMD)" &> /dev/null; then
             log_error "安装后仍然无法找到命令 $(basename $ETCDCTL_CMD)。请检查 $INSTALL_DIR 是否在 PATH 中。"
             # Try using the full path directly anyway
             if [ ! -x "$ETCDCTL_CMD" ]; then
                  log_error "无法执行 $ETCDCTL_CMD。"
                  exit 1
             fi
        fi
        return 0
    fi
}

# --- Action Functions ---

# Function to perform backup
perform_backup() {
    log_info "开始执行备份操作..."
    check_backup_prereqs || exit 1 # Check certs and set paths
    find_or_download_etcd_tools || exit 1 # Check tools

    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_BASE_DIR"
    if [ $? -ne 0 ]; then
        log_error "创建备份目录 $BACKUP_BASE_DIR 失败。请检查权限。"
        exit 1
    fi

    local backup_filename="etcd-snapshot-$(date +%Y%m%d_%H%M%S).db"
    local backup_filepath="$BACKUP_BASE_DIR/$backup_filename"

    log_info "准备将 etcd 数据备份到: $backup_filepath"
    log_info "使用端点: $ETCD_ENDPOINT"
    log_info "使用 CA: $ETCD_CACERT"
    log_info "使用证书: $ETCD_CERT"
    log_info "使用密钥: $ETCD_KEY"

    # Run etcdctl snapshot save command
    ETCDCTL_API=3 "$ETCDCTL_CMD" snapshot save "$backup_filepath" \
        --endpoints="$ETCD_ENDPOINT" \
        --cacert="$ETCD_CACERT" \
        --cert="$ETCD_CERT" \
        --key="$ETCD_KEY"

    if [ $? -eq 0 ]; then
        log_info "备份成功完成: $backup_filepath"
        # Optionally display snapshot status
        log_info "验证备份文件状态:"
        ETCDCTL_API=3 "$ETCDCTL_CMD" snapshot status "$backup_filepath" -w table
    else
        log_error "Etcd 备份失败。"
        # Clean up potentially incomplete backup file
        rm -f "$backup_filepath"
        exit 1
    fi
}

# Function to perform restore
perform_restore() {
    log_info "开始执行还原操作..."
    # Restore doesn't need certs, but needs the tool
    find_or_download_etcd_tools || exit 1 # Check tools

    # Prompt user for the backup file path
    read -p "请输入要还原的 etcd 备份文件完整路径: " backup_file_path

    # Validate backup file existence
    if [ -z "$backup_file_path" ]; then
        log_error "未提供备份文件路径。"
        exit 1
    fi
    if [ ! -f "$backup_file_path" ]; then
        log_error "指定的备份文件不存在: $backup_file_path"
        exit 1
    fi

     # Verify snapshot integrity before restore (optional but recommended)
    log_info "正在验证备份文件 $backup_file_path ..."
    ETCDCTL_API=3 "$ETCDCTL_CMD" snapshot status "$backup_file_path"
    if [ $? -ne 0 ]; then
        log_error "备份文件验证失败。请检查文件是否有效或损坏。"
        exit 1
    fi
    log_info "备份文件验证通过。"


    log_info "将从文件 $backup_file_path 还原。"
    local restore_data_dir_temp="${ETCD_DATA_DIR}.restore-temp-$(date +%s)"
    log_info "目标数据目录 (临时): $restore_data_dir_temp"
    log_info "最终数据目录: $ETCD_DATA_DIR"


    # Check if the FINAL target data directory exists and is not empty
    if [ -d "$ETCD_DATA_DIR" ] && [ "$(ls -A "$ETCD_DATA_DIR")" ]; then
        log_error "目标目录 $ETCD_DATA_DIR 已存在且非空。"
        log_error "为了安全起见，还原操作要求目标目录为空或不存在，或者您需要手动处理。"
        read -p "是否要备份当前 $ETCD_DATA_DIR 到 ${ETCD_DATA_DIR}.bak 并继续还原? (y/N): " confirm_backup_old
        if [[ "$confirm_backup_old" =~ ^[Yy]$ ]]; then
            local backup_old_dir="${ETCD_DATA_DIR}.bak-$(date +%Y%m%d_%H%M%S)"
            log_info "正在备份当前数据到 $backup_old_dir ..."
            mv "$ETCD_DATA_DIR" "$backup_old_dir"
            if [ $? -ne 0 ]; then
                log_error "备份旧数据目录失败！还原中止。"
                exit 1
            fi
             log_info "旧数据已备份到 $backup_old_dir"
             # Now ETCD_DATA_DIR does not exist, proceed to restore
        else
             log_error "请先手动备份或清空 $ETCD_DATA_DIR，然后重试。"
             exit 1
        fi
    elif [ -d "$ETCD_DATA_DIR" ] && [ ! "$(ls -A "$ETCD_DATA_DIR")" ]; then
         log_info "目标目录 $ETCD_DATA_DIR 存在但为空，将继续执行还原。"
         # No action needed, restore will use it
         rmdir "$ETCD_DATA_DIR" # snapshot restore expects the dir not to exist or creates it
         if [ $? -ne 0 ] && [ -d "$ETCD_DATA_DIR" ]; then # Check if rmdir failed
              log_error "无法移除空的 $ETCD_DATA_DIR。请检查权限。"
              exit 1
         fi

    elif [ ! -e "$ETCD_DATA_DIR" ]; then
         log_info "目标目录 $ETCD_DATA_DIR 不存在，还原过程将创建它。"
         # No action needed, restore will create it
    fi

    # It's safer to restore to a temporary directory first, then move it
    log_info "正在将快照还原到临时目录 $restore_data_dir_temp ..."
    ETCDCTL_API=3 "$ETCDCTL_CMD" snapshot restore "$backup_file_path" --data-dir "$restore_data_dir_temp"

    if [ $? -eq 0 ]; then
        log_info "数据成功还原到临时目录: $restore_data_dir_temp"

        # Move the restored data to the final location
        log_info "正在将还原的数据移动到最终位置: $ETCD_DATA_DIR"
        mv "$restore_data_dir_temp" "$ETCD_DATA_DIR"
        if [ $? -ne 0 ]; then
             log_error "将还原的数据从 $restore_data_dir_temp 移动到 $ETCD_DATA_DIR 失败。"
             log_error "还原的数据仍在 $restore_data_dir_temp 中，请手动处理。"
             exit 1
        fi

        log_info "数据移动成功。"
        log_warn "重要提示：还原操作已完成，但您必须执行以下操作："
        log_warn "1. 确保 $ETCD_DATA_DIR 的所有权和权限正确 (通常是 etcd:etcd)。请运行: chown -R etcd:etcd $ETCD_DATA_DIR"
        log_warn "   (注意: 'etcd' 用户和组可能需要根据你的系统调整)"
        log_warn "2. 如果 etcd 作为静态 Pod 运行 (kubeadm 默认)，kubelet 应该会自动检测并重启它。请确认 Pod 状态。"
        log_warn "   如果作为 systemd 服务运行，请使用 'systemctl start etcd' 手动启动。"
        log_warn "3. 监控 etcd 和 Kubernetes API Server 的日志以确保集群恢复正常。"
    else
        log_error "Etcd 还原到临时目录 $restore_data_dir_temp 失败。"
        log_error "请检查错误信息。临时目录可能包含部分还原的数据，请手动清理。"
        rm -rf "$restore_data_dir_temp" # Attempt cleanup
        exit 1
    fi
}

# --- Main Script Logic ---

check_root

# Display menu
echo "======================================"
echo "  Kubeadm Etcd 数据管理脚本"
echo "======================================"
echo "请选择操作:"
echo "  1) 备份 Etcd 数据"
echo "  2) 还原 Etcd 数据"
echo "======================================"
read -p "输入选项 (1 或 2): " choice

# Process user choice
case $choice in
    1)
        log_info "选择了备份操作。"
        perform_backup
        ;;
    2)
        log_info "选择了还原操作。"
        log_warn "!!! 警告: 还原操作是危险的，并且应该在 etcd 服务完全停止后执行 !!!"
        log_warn "!!! 此脚本仅执行数据文件还原，不负责停止/启动 etcd 服务 !!!"
        log_warn "!!! 强烈建议在执行前已停止所有控制平面节点的 etcd 实例 !!!"
        read -p "您确定要继续吗? (y/N): " confirm_restore
        if [[ "$confirm_restore" =~ ^[Yy]$ ]]; then
            perform_restore
        else
            log_info "还原操作已取消。"
            exit 0
        fi
        ;;
    *)
        log_error "无效选项: $choice"
        exit 1
        ;;
esac

exit 0