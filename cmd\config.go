package cmd

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"k8s-helper/internal/service"
	"k8s-helper/pkg/cmdhelp"
	"k8s-helper/pkg/common"
	"k8s-helper/pkg/config"
)

// configCmd 代表 config 命令
var configCmd = cmdhelp.ConfigHelp.BuildCommand(nil, nil)

// configInitCmd 代表 config init 命令
var configInitCmd = &cobra.Command{
	Use:   "init",
	Short: "生成示例配置文件",
	Long: `在指定位置生成示例配置文件。

如果不指定路径，将在默认位置创建配置文件。
如果配置文件已存在，将询问是否覆盖。

使用示例：
  k8s-helper config init                           # 在默认位置创建配置文件
  k8s-helper config init --output /path/to/config # 在指定位置创建配置文件`,
	RunE: runConfigInit,
}

// configShowCmd 代表 config show 命令
var configShowCmd = &cobra.Command{
	Use:   "show",
	Short: "显示当前配置",
	Long: `显示当前生效的配置信息。

配置优先级：命令行参数 > 环境变量 > 配置文件 > 默认值

使用示例：
  k8s-helper config show                           # 显示当前配置
  k8s-helper config show --config /path/to/config # 显示指定配置文件的内容`,
	RunE: runConfigShow,
}

// configPathCmd 代表 config path 命令
var configPathCmd = &cobra.Command{
	Use:   "path",
	Short: "显示配置文件路径",
	Long: `显示当前使用的配置文件路径。

使用示例：
  k8s-helper config path                           # 显示默认配置文件路径`,
	RunE: runConfigPath,
}

// configReloadCmd 代表 config reload 命令
var configReloadCmd = &cobra.Command{
	Use:   "reload [command]",
	Short: "配置热重载管理",
	Long:  "管理配置热重载，包括启动监听、立即重载等操作",
}

// configReloadStartCmd 代表 config reload start acket
var configReloadStartCmd = &cobra.Command{
	Use:   "start",
	Short: "启动配置热重载监听",
	Long: `启动配置热重载监听，在配置文件发生变化时自动重载。

使用示例：
  k8s-helper config reload start --config /path/to/config.yaml
  k8s-helper config reload start --verbose`,
	RunE: runConfigReloadStart,
}

// configReloadNowCmd 代表 config reload now 命令
var configReloadNowCmd = &cobra.Command{
	Use:   "now",
	Short: "立即重载配置",
	Long: `立即重载配置文件，不等待文件变化。

使用示例：
  k8s-helper config reload now --config /path/to/config.yaml`,
	RunE: runConfigReloadNow,
}

// configReloadStatsCmd 代表 config reload stats 命令
var configReloadStatsCmd = &cobra.Command{
	Use:   "stats",
	Short: "显示配置重载统计信息",
	Long: `显示配置重载统计信息，包括重载次数、错误次数等。

使用示例：
  k8s-helper config reload stats`,
	RunE: runConfigReloadStats,
}

// configReloadEventsCmd 代表 config reload events 命令
var configReloadEventsCmd = &cobra.Command{
	Use:   "events",
	Short: "显示配置重载事件历史",
	Long: `显示配置重载事件历史，包括成功、失败等事件。

使用示例：
  k8s-helper config reload events --limit 50`,
	RunE: runConfigReloadEvents,
}

var (
	// 配置文件输出路径
	configOutput string
	// 指定的配置文件路径
	configFile string
	// 重载配置文件路径
	reloadConfigFile string
	// 事件显示限制
	eventLimit int
)

func init() {
	// 添加子命令
	configCmd.AddCommand(configInitCmd)
	configCmd.AddCommand(configShowCmd)
	configCmd.AddCommand(configPathCmd)
	
	// 添加 reload 子命令
	configCmd.AddCommand(configReloadCmd)
	
	// 添加 reload 子命令
	configReloadCmd.AddCommand(configReloadStartCmd)
	configReloadCmd.AddCommand(configReloadNowCmd)
	configReloadCmd.AddCommand(configReloadStatsCmd)
	configReloadCmd.AddCommand(configReloadEventsCmd)
	
	// init 命令标志
	configInitCmd.Flags().StringVarP(&configOutput, "output", "o", "",
		"配置文件输出路径（默认为 ~/.k8s-helper.yaml）")

	// show 命令标志
	configShowCmd.Flags().StringVarP(&configFile, "config", "c", "",
		"指定配置文件路径")
	
	// reload start 命令标志
	configReloadStartCmd.Flags().StringVarP(&reloadConfigFile, "config", "c", "",
		"指定配置文件路径")
	
	// reload now 命令标志
	configReloadNowCmd.Flags().StringVarP(&reloadConfigFile, "config", "c", "",
		"指定配置文件路径")
	
	// reload events 命令标志
	configReloadEventsCmd.Flags().IntVar(&eventLimit, "limit", 100,
		"事件显示限制数量")
}

// runConfigInit 执行配置初始化
func runConfigInit(cmd *cobra.Command, args []string) error {
	// 确定配置文件路径
	configPath := configOutput
	if configPath == "" {
		configPath = config.GetDefaultConfigPath()
	}

	// 检查文件是否已存在
	if common.FileExists(configPath) {
		if !force {
			if !common.ConfirmAction(fmt.Sprintf("配置文件 %s 已存在，是否覆盖？", configPath)) {
				fmt.Println("操作已取消")
				return nil
			}
		}
	}

	// 生成示例配置文件
	if err := config.GenerateExampleConfig(configPath); err != nil {
		return fmt.Errorf("生成配置文件失败: %w", err)
	}

	common.PrintSuccess(fmt.Sprintf("配置文件已生成: %s", configPath))
	fmt.Println("\n您可以编辑此文件来自定义配置。")
	fmt.Println("配置优先级：命令行参数 > 环境变量 > 配置文件 > 默认值")

	return nil
}

// runConfigShow 显示当前配置
func runConfigShow(cmd *cobra.Command, args []string) error {
	// 加载配置
	cfg, err := config.LoadConfig(configFile)
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 从环境变量合并配置
	cfg.MergeFromEnv()

	// 显示配置信息
	fmt.Println("=== 当前配置 ===")
	fmt.Println()

	// 全局配置
	fmt.Println("🌐 全局配置:")
	fmt.Printf("  Kubeconfig: %s\n", getConfigValue(cfg.Global.Kubeconfig, "默认路径"))
	fmt.Printf("  命名空间: %s\n", cfg.Global.Namespace)
	fmt.Printf("  详细输出: %v\n", cfg.Global.Verbose)
	fmt.Printf("  强制模式: %v\n", cfg.Global.Force)
	fmt.Println()

	// ETCD 配置
	fmt.Println("💾 ETCD 配置:")
	fmt.Printf("  下载基础 URL: %s\n", cfg.ETCD.BaseURL)
	fmt.Printf("  版本: %s\n", cfg.ETCD.Version)
	fmt.Printf("  操作系统: %s\n", cfg.ETCD.OS)
	fmt.Printf("  数据目录: %s\n", cfg.ETCD.DataDir)
	fmt.Printf("  备份目录: %s\n", cfg.ETCD.BackupDir)
	fmt.Printf("  API Server 配置: %s\n", cfg.ETCD.APIServerManifest)
	fmt.Println()

	// 日志配置
	fmt.Println("📋 日志配置:")
	fmt.Printf("  级别: %s\n", cfg.Logging.Level)
	fmt.Printf("  格式: %s\n", cfg.Logging.Format)
	fmt.Printf("  文件: %s\n", getConfigValue(cfg.Logging.File, "标准输出"))
	fmt.Printf("  颜色: %v\n", cfg.Logging.Color)
	fmt.Println()

	// 清理配置
	fmt.Println("🧹 清理配置:")
	fmt.Printf("  默认时间阈值: %s\n", cfg.Cleanup.DefaultOlderThan)
	fmt.Printf("  默认干运行: %v\n", cfg.Cleanup.DefaultDryRun)
	fmt.Printf("  并发工作线程: %d\n", cfg.Cleanup.ConcurrentWorkers)

	// 显示配置文件路径
	configPath := configFile
	if configPath == "" {
		configPath = config.GetDefaultConfigPath()
	}
	fmt.Printf("\n📁 配置文件路径: %s\n", configPath)

	// 检查配置文件是否存在
	if !common.FileExists(configPath) {
		fmt.Println("   (配置文件不存在，使用默认配置)")
	}

	return nil
}

// runConfigPath 显示配置文件路径
func runConfigPath(cmd *cobra.Command, args []string) error {
	configPath := config.GetDefaultConfigPath()
	fmt.Println(configPath)

	if verbose {
		if common.FileExists(configPath) {
			common.PrintInfo("配置文件存在")
		} else {
			common.PrintWarning("配置文件不存在")
			fmt.Println("使用 'k8s-helper config init' 创建配置文件")
		}
	}

	return nil
}

// runConfigReloadStart 执行配置重载启动
func runConfigReloadStart(cmd *cobra.Command, args []string) error {
	// 确定配置文件路径
	configPath := reloadConfigFile
	if configPath == "" {
		configPath = config.GetDefaultConfigPath()
	}
	
	// 检查配置文件是否存在
	if !common.FileExists(configPath) {
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}
	
	// 加载初始配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}
	
	// 初始化日志
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()
	
	// 创建配置重载器
	reloader, err := service.NewConfigReloader(configPath, cfg, logger)
	if err != nil {
		return fmt.Errorf("创建配置重载器失败: %w", err)
	}
	
	// 注册处理器
	// 这里需要根据实际的服务来注册相应的处理器
	// 示例：
	// reloader.RegisterHandler(service.NewLoggingConfigReloadHandler(logger))
	
	// 启动监听
	if err := reloader.Start(); err != nil {
		return fmt.Errorf("启动配置监听失败: %w", err)
	}
	
	common.PrintSuccess(fmt.Sprintf("配置热重载监听已启动: %s", configPath))
	
	// 保持程序运行
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	
	<-sigChan
	reloader.Stop()
	
	common.PrintInfo("配置热重载监听已停止")
	return nil
}

// runConfigReloadNow 立即重载配置
func runConfigReloadNow(cmd *cobra.Command, args []string) error {
	// 确定配置文件路径
	configPath := reloadConfigFile
	if configPath == "" {
		configPath = config.GetDefaultConfigPath()
	}
	
	// 检查配置文件是否存在
	if !common.FileExists(configPath) {
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}
	
	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}
	
	// 初始化日志
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()
	
	// 创建配置重载器
	reloader, err := service.NewConfigReloader(configPath, cfg, logger)
	if err != nil {
		return fmt.Errorf("创建配置重载器失败: %w", err)
	}
	
	// 立即重载
	if err := reloader.ReloadNow(); err != nil {
		return fmt.Errorf("配置重载失败: %w", err)
	}
	
	common.PrintSuccess("配置重载完成")
	return nil
}

// runConfigReloadStats 显示配置重载统计信息
func runConfigReloadStats(cmd *cobra.Command, args []string) error {
	// 这里需要访问已运行的配置重载器实例
	// 由于命令行的限制，我们只能显示静态信息
	fmt.Println("配置重载统计信息:")
	fmt.Println("  功能待完善 - 需要在运行时访问配置重载器实例")
	return nil
}

// runConfigReloadEvents 显示配置重载事件历史
func runConfigReloadEvents(cmd *cobra.Command, args []string) error {
	// 这里需要访问已运行的配置重载器实例
	// 由于命令行的限制，我们只能显示静态信息
	fmt.Println("配置重载事件历史:")
	fmt.Println("  功能待完善 - 需要在运行时访问配置重载器实例")
	return nil
}

// getConfigValue 获取配置值，如果为空则返回默认描述
func getConfigValue(value, defaultDesc string) string {
	if value == "" {
		return fmt.Sprintf("(%s)", defaultDesc)
	}
	return value
}
