package service

import (
	"context"
	"crypto/md5"
	"crypto/sha256"
	"fmt"
	"hash"
	"io"
	"os"
	"sync"
	"time"

	"go.uber.org/zap"
)

// HashType 哈希类型
type HashType string

const (
	HashTypeMD5    HashType = "md5"
	HashTypeSHA256 HashType = "sha256"
)

// HashRequest 哈希计算请求
type HashRequest struct {
	ID       string
	FilePath string
	HashType HashType
	Priority int // 优先级，数字越大优先级越高
	Callback func(result *HashResult)
}

// HashResult 哈希计算结果
type HashResult struct {
	ID        string
	FilePath  string
	HashType  HashType
	Hash      string
	FileSize  int64
	Duration  time.Duration
	Error     error
	Timestamp time.Time
}

// AsyncHasherConfig 异步哈希计算器配置
type AsyncHasherConfig struct {
	WorkerCount int           // 工作协程数量
	QueueSize   int           // 队列大小
	BufferSize  int           // 读取缓冲区大小
	Timeout     time.Duration // 单个文件计算超时
	EnableStats bool          // 是否启用统计
	EnableCache bool          // 是否启用结果缓存
	CacheTTL    time.Duration // 缓存TTL
}

// DefaultAsyncHasherConfig 默认异步哈希计算器配置
func DefaultAsyncHasherConfig() *AsyncHasherConfig {
	return &AsyncHasherConfig{
		WorkerCount: 4,
		QueueSize:   100,
		BufferSize:  64 * 1024, // 64KB
		Timeout:     5 * time.Minute,
		EnableStats: true,
		EnableCache: true,
		CacheTTL:    30 * time.Minute,
	}
}

// AsyncHasher 异步哈希计算器
type AsyncHasher struct {
	config      *AsyncHasherConfig
	logger      *zap.Logger
	requestChan chan *HashRequest
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup

	// 结果缓存
	cache      map[string]*HashResult
	cacheMutex sync.RWMutex

	// 统计信息
	totalRequests  int64
	completedTasks int64
	failedTasks    int64
	totalDuration  time.Duration
	startTime      time.Time
	statsMutex     sync.RWMutex
}

// NewAsyncHasher 创建异步哈希计算器
func NewAsyncHasher(config *AsyncHasherConfig, logger *zap.Logger) *AsyncHasher {
	if config == nil {
		config = DefaultAsyncHasherConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	ah := &AsyncHasher{
		config:      config,
		logger:      logger,
		requestChan: make(chan *HashRequest, config.QueueSize),
		ctx:         ctx,
		cancel:      cancel,
		cache:       make(map[string]*HashResult),
		startTime:   time.Now(),
	}

	// 启动工作协程
	ah.startWorkers()

	return ah
}

// CalculateHash 异步计算文件哈希
func (ah *AsyncHasher) CalculateHash(filePath string, hashType HashType, callback func(result *HashResult)) (string, error) {
	return ah.CalculateHashWithPriority(filePath, hashType, 0, callback)
}

// CalculateHashWithPriority 异步计算文件哈希（带优先级）
func (ah *AsyncHasher) CalculateHashWithPriority(filePath string, hashType HashType, priority int, callback func(result *HashResult)) (string, error) {
	// 生成请求ID
	requestID := fmt.Sprintf("%s_%s_%d", filePath, hashType, time.Now().UnixNano())

	// 检查缓存
	if ah.config.EnableCache {
		if result := ah.getCachedResult(filePath, hashType); result != nil {
			ah.logger.Debug("哈希缓存命中",
				zap.String("path", filePath),
				zap.String("hashType", string(hashType)))

			// 异步调用回调
			if callback != nil {
				go callback(result)
			}
			return result.Hash, nil
		}
	}

	// 创建请求
	request := &HashRequest{
		ID:       requestID,
		FilePath: filePath,
		HashType: hashType,
		Priority: priority,
		Callback: callback,
	}

	// 更新统计
	ah.statsMutex.Lock()
	ah.totalRequests++
	ah.statsMutex.Unlock()

	// 发送到队列
	select {
	case ah.requestChan <- request:
		ah.logger.Debug("哈希计算请求已提交",
			zap.String("id", requestID),
			zap.String("path", filePath),
			zap.String("hashType", string(hashType)))
		return requestID, nil
	default:
		return "", fmt.Errorf("哈希计算队列已满")
	}
}

// CalculateHashSync 同步计算文件哈希
func (ah *AsyncHasher) CalculateHashSync(filePath string, hashType HashType) (*HashResult, error) {
	// 检查缓存
	if ah.config.EnableCache {
		if result := ah.getCachedResult(filePath, hashType); result != nil {
			return result, nil
		}
	}

	// 直接计算
	result := ah.calculateFileHash(filePath, hashType)
	return result, result.Error
}

// GetStats 获取统计信息
func (ah *AsyncHasher) GetStats() map[string]interface{} {
	ah.statsMutex.RLock()
	defer ah.statsMutex.RUnlock()

	var avgDuration time.Duration
	if ah.completedTasks > 0 {
		avgDuration = ah.totalDuration / time.Duration(ah.completedTasks)
	}

	ah.cacheMutex.RLock()
	cacheSize := len(ah.cache)
	ah.cacheMutex.RUnlock()

	return map[string]interface{}{
		"totalRequests":  ah.totalRequests,
		"completedTasks": ah.completedTasks,
		"failedTasks":    ah.failedTasks,
		"queueLength":    len(ah.requestChan),
		"queueCapacity":  ah.config.QueueSize,
		"workerCount":    ah.config.WorkerCount,
		"cacheSize":      cacheSize,
		"avgDuration":    avgDuration.String(),
		"uptime":         time.Since(ah.startTime).String(),
	}
}

// Close 关闭异步哈希计算器
func (ah *AsyncHasher) Close() error {
	ah.logger.Info("关闭异步哈希计算器")

	// 取消上下文
	ah.cancel()

	// 关闭请求通道
	close(ah.requestChan)

	// 等待所有工作协程结束
	ah.wg.Wait()

	// 清空缓存
	ah.cacheMutex.Lock()
	ah.cache = make(map[string]*HashResult)
	ah.cacheMutex.Unlock()

	ah.logger.Info("异步哈希计算器已关闭")
	return nil
}

// startWorkers 启动工作协程
func (ah *AsyncHasher) startWorkers() {
	for i := 0; i < ah.config.WorkerCount; i++ {
		ah.wg.Add(1)
		go ah.worker(i)
	}

	ah.logger.Info("异步哈希计算器已启动",
		zap.Int("workerCount", ah.config.WorkerCount),
		zap.Int("queueSize", ah.config.QueueSize))
}

// worker 工作协程
func (ah *AsyncHasher) worker(workerID int) {
	defer ah.wg.Done()

	ah.logger.Debug("哈希计算工作协程启动", zap.Int("workerID", workerID))

	for {
		select {
		case <-ah.ctx.Done():
			ah.logger.Debug("哈希计算工作协程收到停止信号", zap.Int("workerID", workerID))
			return
		case request, ok := <-ah.requestChan:
			if !ok {
				ah.logger.Debug("哈希计算请求通道已关闭", zap.Int("workerID", workerID))
				return
			}
			ah.processRequest(workerID, request)
		}
	}
}

// processRequest 处理请求
func (ah *AsyncHasher) processRequest(workerID int, request *HashRequest) {
	startTime := time.Now()

	ah.logger.Debug("开始处理哈希计算请求",
		zap.Int("workerID", workerID),
		zap.String("id", request.ID),
		zap.String("path", request.FilePath))

	// 计算哈希
	result := ah.calculateFileHash(request.FilePath, request.HashType)
	result.ID = request.ID

	duration := time.Since(startTime)
	result.Duration = duration

	// 更新统计
	ah.statsMutex.Lock()
	if result.Error == nil {
		ah.completedTasks++
	} else {
		ah.failedTasks++
	}
	ah.totalDuration += duration
	ah.statsMutex.Unlock()

	// 缓存结果
	if ah.config.EnableCache && result.Error == nil {
		ah.setCachedResult(result)
	}

	// 调用回调
	if request.Callback != nil {
		go func() {
			defer func() {
				if r := recover(); r != nil {
					ah.logger.Error("哈希计算回调panic",
						zap.String("id", request.ID),
						zap.Any("panic", r))
				}
			}()
			request.Callback(result)
		}()
	}

	ah.logger.Debug("哈希计算请求处理完成",
		zap.Int("workerID", workerID),
		zap.String("id", request.ID),
		zap.Duration("duration", duration),
		zap.Bool("success", result.Error == nil))
}

// calculateFileHash 计算文件哈希
func (ah *AsyncHasher) calculateFileHash(filePath string, hashType HashType) *HashResult {
	result := &HashResult{
		FilePath:  filePath,
		HashType:  hashType,
		Timestamp: time.Now(),
	}

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		result.Error = fmt.Errorf("打开文件失败: %w", err)
		return result
	}
	defer file.Close()

	// 获取文件信息
	info, err := file.Stat()
	if err != nil {
		result.Error = fmt.Errorf("获取文件信息失败: %w", err)
		return result
	}
	result.FileSize = info.Size()

	// 创建哈希计算器
	var hasher hash.Hash
	switch hashType {
	case HashTypeMD5:
		hasher = md5.New()
	case HashTypeSHA256:
		hasher = sha256.New()
	default:
		result.Error = fmt.Errorf("不支持的哈希类型: %s", hashType)
		return result
	}

	// 创建带缓冲的读取器
	buffer := make([]byte, ah.config.BufferSize)

	// 计算哈希
	for {
		n, err := file.Read(buffer)
		if n > 0 {
			hasher.Write(buffer[:n])
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			result.Error = fmt.Errorf("读取文件失败: %w", err)
			return result
		}
	}

	// 获取哈希值
	result.Hash = fmt.Sprintf("%x", hasher.Sum(nil))
	return result
}

// getCachedResult 获取缓存结果
func (ah *AsyncHasher) getCachedResult(filePath string, hashType HashType) *HashResult {
	ah.cacheMutex.RLock()
	defer ah.cacheMutex.RUnlock()

	key := fmt.Sprintf("%s_%s", filePath, hashType)
	result, exists := ah.cache[key]
	if !exists {
		return nil
	}

	// 检查缓存是否过期
	if ah.config.CacheTTL > 0 && time.Since(result.Timestamp) > ah.config.CacheTTL {
		return nil
	}

	// 检查文件是否被修改
	info, err := os.Stat(filePath)
	if err != nil {
		return nil
	}

	// 简单检查：如果文件大小或修改时间变化，认为文件已修改
	if info.Size() != result.FileSize {
		return nil
	}

	return result
}

// setCachedResult 设置缓存结果
func (ah *AsyncHasher) setCachedResult(result *HashResult) {
	ah.cacheMutex.Lock()
	defer ah.cacheMutex.Unlock()

	key := fmt.Sprintf("%s_%s", result.FilePath, result.HashType)
	ah.cache[key] = result
}
