{{template "layout/base.html" .}}

{{define "content"}}
<div class="container">
    <!-- Dashboard Module -->
    <div id="module-dashboard" class="module active">
        {{template "dashboard" .}}
    </div>

    <!-- Cluster Info Module -->
    <div id="module-cluster-info" class="module">
        {{template "cluster-info" .}}
    </div>

    <!-- ETCD Module -->
    <div id="module-etcd" class="module">
        {{template "etcd" .}}
    </div>

    <!-- Pod Logs Module -->
    <div id="module-logs" class="module">
        {{template "logs" .}}
    </div>

    <!-- Port Forward Module -->
    <div id="module-port-forward" class="module">
        {{template "port-forward" .}}
    </div>

    <!-- Cleanup Module -->
    <div id="module-cleanup" class="module">
        {{template "cleanup" .}}
    </div>

    <!-- Monitoring Module -->
    <div id="module-monitoring" class="module">
        {{template "monitoring" .}}
    </div>

    <!-- Configuration Module -->
    <div id="module-config" class="module">
        {{template "config" .}}
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化WebSocket连接
        if (window.K8sHelper) {
            window.K8sHelper.initializeWebSocket();
        }
        
        // 加载初始数据
        setTimeout(() => {
            if (typeof loadDashboardData === 'function') {
                loadDashboardData();
            }
        }, 1000);
    });
</script>
{{end}}
