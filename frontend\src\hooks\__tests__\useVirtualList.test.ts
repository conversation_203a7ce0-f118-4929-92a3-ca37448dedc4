import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useVirtualList, useFixedSizeList, useDynamicSizeList } from '../useVirtualList';

// 模拟数据
const mockItems = Array.from({ length: 1000 }, (_, i) => ({
  id: i,
  name: `Item ${i}`,
  value: Math.random() * 100,
}));

describe('useVirtualList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确计算可见范围', () => {
    const options = {
      itemHeight: 50,
      containerHeight: 300,
      overscan: 2,
    };

    const { result } = renderHook(() => useVirtualList(mockItems, options));

    // 初始状态下应该显示前几项
    expect(result.current.virtualItems.length).toBeGreaterThan(0);
    expect(result.current.virtualItems[0].index).toBe(0);
    
    // 总高度应该等于项目数量 * 项目高度
    expect(result.current.totalHeight).toBe(mockItems.length * options.itemHeight);
  });

  it('应该正确处理滚动事件', () => {
    const options = {
      itemHeight: 50,
      containerHeight: 300,
      overscan: 2,
    };

    const { result } = renderHook(() => useVirtualList(mockItems, options));

    // 模拟滚动事件
    const mockScrollEvent = {
      currentTarget: {
        scrollTop: 500,
      },
    } as React.UIEvent<HTMLDivElement>;

    act(() => {
      result.current.handleScroll(mockScrollEvent);
    });

    expect(result.current.scrollTop).toBe(500);
    expect(result.current.isScrolling).toBe(true);

    // 等待滚动延迟结束
    await new Promise(resolve => setTimeout(resolve, 200));

    expect(result.current.isScrolling).toBe(false);
  });

  it('应该支持滚动到指定索引', () => {
    const options = {
      itemHeight: 50,
      containerHeight: 300,
    };

    const { result } = renderHook(() => useVirtualList(mockItems, options));

    // 滚动到索引10
    act(() => {
      result.current.scrollToIndex(10, 'start');
    });

    expect(result.current.scrollTop).toBe(10 * options.itemHeight);

    // 滚动到索引20，居中对齐
    act(() => {
      result.current.scrollToIndex(20, 'center');
    });

    const expectedScrollTop = 20 * options.itemHeight - options.containerHeight / 2 + options.itemHeight / 2;
    expect(result.current.scrollTop).toBe(expectedScrollTop);
  });

  it('应该正确返回可见项目数据', () => {
    const options = {
      itemHeight: 50,
      containerHeight: 300,
      overscan: 1,
    };

    const { result } = renderHook(() => useVirtualList(mockItems, options));

    expect(result.current.visibleItems.length).toBeGreaterThan(0);
    
    // 每个可见项目都应该包含数据
    result.current.visibleItems.forEach((item, index) => {
      expect(item.data).toBeDefined();
      expect(item.data.id).toBe(item.index);
      expect(item.data.name).toBe(`Item ${item.index}`);
    });
  });

  it('应该正确处理overscan', () => {
    const options = {
      itemHeight: 50,
      containerHeight: 300,
      overscan: 5,
    };

    const { result } = renderHook(() => useVirtualList(mockItems, options));

    // 计算预期的可见项目数量
    const visibleCount = Math.ceil(options.containerHeight / options.itemHeight);
    const expectedCount = visibleCount + options.overscan * 2;

    expect(result.current.virtualItems.length).toBeLessThanOrEqual(expectedCount + 1);
  });

  it('应该返回正确的样式对象', () => {
    const options = {
      itemHeight: 50,
      containerHeight: 300,
    };

    const { result } = renderHook(() => useVirtualList(mockItems, options));

    expect(result.current.containerStyle).toEqual({
      height: 300,
      overflow: 'auto',
    });

    expect(result.current.contentStyle).toEqual({
      height: mockItems.length * options.itemHeight,
      position: 'relative',
    });
  });
});

describe('useFixedSizeList', () => {
  it('应该是useVirtualList的简化版本', () => {
    const itemHeight = 40;
    const containerHeight = 400;
    const overscan = 3;

    const { result } = renderHook(() => 
      useFixedSizeList(mockItems, itemHeight, containerHeight, overscan)
    );

    expect(result.current.totalHeight).toBe(mockItems.length * itemHeight);
    expect(result.current.containerStyle.height).toBe(containerHeight);
    expect(result.current.virtualItems.length).toBeGreaterThan(0);
  });
});

describe('useDynamicSizeList', () => {
  const getItemHeight = (index: number, item: any) => {
    // 模拟动态高度：偶数索引高度为50，奇数索引高度为80
    return index % 2 === 0 ? 50 : 80;
  };

  it('应该正确计算动态高度的项目元数据', () => {
    const containerHeight = 400;
    const overscan = 2;

    const { result } = renderHook(() => 
      useDynamicSizeList(mockItems.slice(0, 10), getItemHeight, containerHeight, overscan)
    );

    expect(result.current.totalHeight).toBeGreaterThan(0);
    expect(result.current.virtualItems.length).toBeGreaterThan(0);

    // 检查第一个项目的高度
    const firstItem = result.current.virtualItems[0];
    expect(firstItem.size).toBe(50); // 索引0是偶数，高度应该是50
  });

  it('应该正确处理滚动事件', () => {
    const containerHeight = 400;

    const { result } = renderHook(() => 
      useDynamicSizeList(mockItems.slice(0, 20), getItemHeight, containerHeight)
    );

    // 模拟滚动事件
    const mockScrollEvent = {
      currentTarget: {
        scrollTop: 200,
      },
    } as React.UIEvent<HTMLDivElement>;

    act(() => {
      result.current.handleScroll(mockScrollEvent);
    });

    expect(result.current.scrollTop).toBe(200);
    expect(result.current.isScrolling).toBe(true);
  });

  it('应该支持滚动到指定索引', () => {
    const containerHeight = 400;

    const { result } = renderHook(() => 
      useDynamicSizeList(mockItems.slice(0, 20), getItemHeight, containerHeight)
    );

    // 滚动到索引5
    act(() => {
      result.current.scrollToIndex(5);
    });

    // 计算前5个项目的总高度
    let expectedScrollTop = 0;
    for (let i = 0; i < 5; i++) {
      expectedScrollTop += getItemHeight(i, mockItems[i]);
    }

    expect(result.current.scrollTop).toBe(expectedScrollTop);
  });

  it('应该正确返回可见项目', () => {
    const containerHeight = 400;

    const { result } = renderHook(() => 
      useDynamicSizeList(mockItems.slice(0, 10), getItemHeight, containerHeight)
    );

    expect(result.current.visibleItems.length).toBeGreaterThan(0);
    
    // 每个可见项目都应该有正确的数据和尺寸
    result.current.visibleItems.forEach((item) => {
      expect(item.data).toBeDefined();
      expect(item.size).toBeGreaterThan(0);
      expect(item.start).toBeGreaterThanOrEqual(0);
      expect(item.end).toBeGreaterThan(item.start);
    });
  });

  it('应该处理空数据', () => {
    const containerHeight = 400;

    const { result } = renderHook(() => 
      useDynamicSizeList([], getItemHeight, containerHeight)
    );

    expect(result.current.totalHeight).toBe(0);
    expect(result.current.virtualItems).toHaveLength(0);
    expect(result.current.visibleItems).toHaveLength(0);
  });

  it('应该正确处理边界情况', () => {
    const containerHeight = 400;
    const singleItem = [mockItems[0]];

    const { result } = renderHook(() => 
      useDynamicSizeList(singleItem, getItemHeight, containerHeight)
    );

    expect(result.current.totalHeight).toBe(getItemHeight(0, singleItem[0]));
    expect(result.current.virtualItems).toHaveLength(1);
    expect(result.current.visibleItems).toHaveLength(1);
  });
});
