import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Space,
  Button,
  Alert,
  Divider
} from 'antd';
import {
  ReloadOutlined,
  SettingOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons';
import { PodSelector } from '@/components/PodSelector';
import { LogViewer } from '@/components/LogViewer';
import { useAppState, useNotifications } from '@/hooks';
import type { Pod } from '@/types/api';

/**
 * 日志管理页面
 *
 * 功能特性：
 * - Pod选择器
 * - 实时日志查看
 * - 日志搜索和过滤
 * - 全屏模式
 * - URL参数支持
 */
const LogsManagement: React.FC = () => {
  const [selectedNamespace, setSelectedNamespace] = useState<string>('');
  const [selectedPod, setSelectedPod] = useState<string>('');
  const [selectedContainer, setSelectedContainer] = useState<string>('');
  const [selectedPodInfo, setSelectedPodInfo] = useState<Pod | undefined>();
  const [isFullscreen, setIsFullscreen] = useState(false);

  const { setPageTitle } = useAppState();
  const { showSuccess, showWarning } = useNotifications();

  // 设置页面标题
  useEffect(() => {
    setPageTitle('日志查看 - K8s-Helper');
  }, [setPageTitle]);

  // 从URL参数初始化选择
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const namespace = urlParams.get('namespace');
    const pod = urlParams.get('pod');
    const container = urlParams.get('container');

    if (namespace) setSelectedNamespace(namespace);
    if (pod) setSelectedPod(pod);
    if (container) setSelectedContainer(container);
  }, []);

  // 更新URL参数
  const updateUrlParams = (namespace: string, pod: string, container: string) => {
    const url = new URL(window.location.href);
    if (namespace) url.searchParams.set('namespace', namespace);
    else url.searchParams.delete('namespace');

    if (pod) url.searchParams.set('pod', pod);
    else url.searchParams.delete('pod');

    if (container) url.searchParams.set('container', container);
    else url.searchParams.delete('container');

    window.history.replaceState({}, '', url.toString());
  };

  // 处理命名空间变化
  const handleNamespaceChange = (namespace: string) => {
    setSelectedNamespace(namespace);
    setSelectedPod('');
    setSelectedContainer('');
    setSelectedPodInfo(undefined);
    updateUrlParams(namespace, '', '');
  };

  // 处理Pod变化
  const handlePodChange = (pod: string, podInfo?: Pod) => {
    setSelectedPod(pod);
    setSelectedPodInfo(podInfo);
    setSelectedContainer('');
    updateUrlParams(selectedNamespace, pod, '');

    if (pod && podInfo) {
      showSuccess('Pod已选择', `已选择Pod: ${podInfo.namespace}/${pod}`);
    }
  };

  // 处理容器变化
  const handleContainerChange = (container: string) => {
    setSelectedContainer(container);
    updateUrlParams(selectedNamespace, selectedPod, container);

    if (container) {
      showSuccess('容器已选择', `已选择容器: ${container}`);
    }
  };

  // 切换全屏模式
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 检查是否可以查看日志
  const canViewLogs = selectedNamespace && selectedPod && selectedContainer;

  return (
    <div style={{
      padding: isFullscreen ? '12px' : '24px',
      minHeight: '100vh',
      backgroundColor: '#f5f7fa'
    }}>
      {/* 页面头部 */}
      {!isFullscreen && (
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <div>
            <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
              日志查看
            </h1>
            <p style={{ margin: '8px 0 0 0', color: '#666' }}>
              实时查看Pod容器日志
            </p>
          </div>

          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => window.location.reload()}
            >
              刷新页面
            </Button>
            <Button
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              onClick={toggleFullscreen}
            >
              {isFullscreen ? '退出全屏' : '全屏模式'}
            </Button>
            <Button icon={<SettingOutlined />}>
              设置
            </Button>
          </Space>
        </div>
      )}

      {/* 使用说明 */}
      {!canViewLogs && !isFullscreen && (
        <Alert
          message="使用说明"
          description="请先选择命名空间、Pod和容器，然后即可查看实时日志。支持日志搜索、过滤和导出功能。"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      <Row gutter={[16, 16]}>
        {/* Pod选择器 */}
        <Col xs={24} lg={isFullscreen ? 6 : 8}>
          <PodSelector
            selectedNamespace={selectedNamespace}
            selectedPod={selectedPod}
            selectedContainer={selectedContainer}
            onNamespaceChange={handleNamespaceChange}
            onPodChange={handlePodChange}
            onContainerChange={handleContainerChange}
          />

          {/* Pod详细信息 */}
          {selectedPodInfo && !isFullscreen && (
            <div style={{ marginTop: '16px' }}>
              <Divider orientation="left" style={{ fontSize: '14px' }}>
                Pod信息
              </Divider>
              <div style={{
                padding: '12px',
                backgroundColor: 'white',
                borderRadius: '6px',
                fontSize: '12px'
              }}>
                <div><strong>状态:</strong> {selectedPodInfo.status}</div>
                <div><strong>节点:</strong> {selectedPodInfo.node}</div>
                <div><strong>IP:</strong> {selectedPodInfo.ip}</div>
                <div><strong>重启次数:</strong> {selectedPodInfo.restarts}</div>
                <div><strong>运行时间:</strong> {selectedPodInfo.age}</div>
                <div><strong>容器数量:</strong> {selectedPodInfo.containers.length}</div>
              </div>
            </div>
          )}
        </Col>

        {/* 日志查看器 */}
        <Col xs={24} lg={isFullscreen ? 18 : 16}>
          {canViewLogs ? (
            <LogViewer
              namespace={selectedNamespace}
              pod={selectedPod}
              container={selectedContainer}
              height={isFullscreen ? window.innerHeight - 100 : 700}
            />
          ) : (
            <div style={{
              height: 600,
              backgroundColor: 'white',
              borderRadius: '6px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              color: '#8c8c8c',
              fontSize: '16px'
            }}>
              请选择Pod和容器以查看日志
            </div>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default LogsManagement;
