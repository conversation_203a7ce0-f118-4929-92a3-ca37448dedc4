{{define "dashboard"}}
<div class="module-content">
    <div class="dashboard-overview">
        <!-- 核心指标卡片网格 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-header">
                    <h4>系统健康</h4>
                    <span class="metric-icon">💚</span>
                </div>
                <div id="health-result" class="metric-value">检查中...</div>
                <div class="metric-actions">
                    <button class="btn btn-secondary btn-sm" onclick="checkHealth()">检查状态</button>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <h4>系统指标</h4>
                    <span class="metric-icon">📊</span>
                </div>
                <div id="system-metrics" class="metric-value">加载中...</div>
                <div class="metric-actions">
                    <button class="btn btn-secondary btn-sm" onclick="loadSystemMetrics()">刷新指标</button>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <h4>WebSocket连接</h4>
                    <span class="metric-icon">🔗</span>
                </div>
                <div id="ws-connections" class="metric-value">0</div>
                <div class="metric-actions">
                    <button class="btn btn-secondary btn-sm" onclick="reconnectWebSocket()">重新连接</button>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <h4>ETCD备份</h4>
                    <span class="metric-icon">💾</span>
                </div>
                <div id="backup-count" class="metric-value">0</div>
                <div class="metric-actions">
                    <button class="btn btn-secondary btn-sm" onclick="switchModule('etcd')">管理备份</button>
                </div>
            </div>
        </div>
        
        <!-- 仪表板功能区域 -->
        <div class="dashboard-sections">
            <!-- 快速操作区域 -->
            <div class="section">
                <h3>快速操作</h3>
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="switchModule('cluster-info')">
                        <i class="icon">🔍</i> 查看集群信息
                    </button>
                    <button class="btn btn-primary" onclick="switchModule('logs')">
                        <i class="icon">📋</i> 查看Pod日志
                    </button>
                    <button class="btn btn-primary" onclick="switchModule('etcd')">
                        <i class="icon">💾</i> ETCD管理
                    </button>
                    <button class="btn btn-primary" onclick="switchModule('monitoring')">
                        <i class="icon">📈</i> 监控告警
                    </button>
                </div>
            </div>
            
            <!-- 系统状态区域 -->
            <div class="section">
                <h3>系统状态</h3>
                <div id="system-status" class="status-display">
                    <div class="status-item">
                        <span class="status-label">Kubernetes API:</span>
                        <span class="status-value" id="k8s-api-status">检查中...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">ETCD状态:</span>
                        <span class="status-value" id="etcd-status">检查中...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">最后更新:</span>
                        <span class="status-value" id="last-update">--</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最近活动区域 -->
        <div class="section recent-activities">
            <h3>最近活动</h3>
            <div id="recent-activities-list" class="activities-list">
                <div class="activity-item">
                    <span class="activity-time">刚刚</span>
                    <span class="activity-desc">系统启动完成</span>
                    <span class="activity-status success">成功</span>
                </div>
                <div class="activity-item">
                    <span class="activity-time">1分钟前</span>
                    <span class="activity-desc">WebSocket连接建立</span>
                    <span class="activity-status success">成功</span>
                </div>
                <div class="activity-item">
                    <span class="activity-time">5分钟前</span>
                    <span class="activity-desc">健康检查完成</span>
                    <span class="activity-status success">成功</span>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}
