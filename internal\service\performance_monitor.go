package service

import (
	"runtime"
	"sync"
	"time"

	"go.uber.org/zap"
)

// OperationMetric 操作指标
type OperationMetric struct {
	Name         string
	Count        int64
	TotalTime    time.Duration
	MinTime      time.Duration
	MaxTime      time.Duration
	AvgTime      time.Duration
	LastExecuted time.Time
	ErrorCount   int64
}

// SystemMetric 系统指标
type SystemMetric struct {
	Timestamp    time.Time
	MemoryAlloc  int64
	MemoryTotal  int64
	GoroutineNum int
	CPUUsage     float64
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	logger          *zap.Logger
	operations      map[string]*OperationMetric
	systemMetrics   []*SystemMetric
	mutex           sync.RWMutex
	maxHistorySize  int
	collectInterval time.Duration
	collectTicker   *time.Ticker
	stopCollect     chan struct{}
	lastCPUTime     time.Time
	lastCPUUsage    float64
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor(logger *zap.Logger, maxHistorySize int, collectInterval time.Duration) *PerformanceMonitor {
	monitor := &PerformanceMonitor{
		logger:          logger,
		operations:      make(map[string]*OperationMetric),
		systemMetrics:   make([]*SystemMetric, 0, maxHistorySize),
		maxHistorySize:  maxHistorySize,
		collectInterval: collectInterval,
		stopCollect:     make(chan struct{}),
	}

	// 启动系统指标收集
	monitor.startCollection()

	return monitor
}

// RecordOperation 记录操作执行
func (pm *PerformanceMonitor) RecordOperation(operationName string, duration time.Duration, success bool) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	metric, exists := pm.operations[operationName]
	if !exists {
		metric = &OperationMetric{
			Name:    operationName,
			MinTime: duration,
			MaxTime: duration,
		}
		pm.operations[operationName] = metric
	}

	// 更新统计信息
	metric.Count++
	metric.TotalTime += duration
	metric.LastExecuted = time.Now()

	if !success {
		metric.ErrorCount++
	}

	// 更新最小/最大时间
	if duration < metric.MinTime {
		metric.MinTime = duration
	}
	if duration > metric.MaxTime {
		metric.MaxTime = duration
	}

	// 计算平均时间
	metric.AvgTime = metric.TotalTime / time.Duration(metric.Count)

	pm.logger.Debug("记录操作指标",
		zap.String("operation", operationName),
		zap.Duration("duration", duration),
		zap.Bool("success", success),
		zap.Int64("totalCount", metric.Count))
}

// RecordBackupOperation 记录备份操作
func (pm *PerformanceMonitor) RecordBackupOperation(duration time.Duration, success bool, backupSize int64) {
	pm.RecordOperation("backup", duration, success)

	// 记录额外的备份特定指标
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 可以在这里添加备份大小等特定指标的记录
	pm.logger.Info("备份操作完成",
		zap.Duration("duration", duration),
		zap.Bool("success", success),
		zap.Int64("backupSize", backupSize))
}

// RecordRestoreOperation 记录恢复操作
func (pm *PerformanceMonitor) RecordRestoreOperation(duration time.Duration, success bool, restoredSize int64) {
	pm.RecordOperation("restore", duration, success)

	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.logger.Info("恢复操作完成",
		zap.Duration("duration", duration),
		zap.Bool("success", success),
		zap.Int64("restoredSize", restoredSize))
}

// RecordValidationOperation 记录验证操作
func (pm *PerformanceMonitor) RecordValidationOperation(validationType string, duration time.Duration, success bool) {
	operationName := "validation_" + validationType
	pm.RecordOperation(operationName, duration, success)
}

// RecordCronJobOperation 记录CronJob操作
func (pm *PerformanceMonitor) RecordCronJobOperation(operation string, duration time.Duration, success bool) {
	operationName := "cronjob_" + operation
	pm.RecordOperation(operationName, duration, success)
}

// GetOperationMetrics 获取操作指标
func (pm *PerformanceMonitor) GetOperationMetrics() map[string]*OperationMetric {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 创建副本以避免并发访问问题
	metrics := make(map[string]*OperationMetric)
	for name, metric := range pm.operations {
		metricCopy := *metric
		metrics[name] = &metricCopy
	}

	return metrics
}

// GetSystemMetrics 获取系统指标历史
func (pm *PerformanceMonitor) GetSystemMetrics() []*SystemMetric {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 创建副本
	metrics := make([]*SystemMetric, len(pm.systemMetrics))
	copy(metrics, pm.systemMetrics)

	return metrics
}

// GetCurrentSystemMetric 获取当前系统指标
func (pm *PerformanceMonitor) GetCurrentSystemMetric() *SystemMetric {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return &SystemMetric{
		Timestamp:    time.Now(),
		MemoryAlloc:  int64(m.Alloc),
		MemoryTotal:  int64(m.TotalAlloc),
		GoroutineNum: runtime.NumGoroutine(),
		CPUUsage:     pm.getCPUUsage(),
	}
}

// getCPUUsage 获取CPU使用率
func (pm *PerformanceMonitor) getCPUUsage() float64 {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 获取当前时间和CPU时间
	now := time.Now()

	// 如果是第一次调用，初始化并返回0
	if pm.lastCPUTime.IsZero() {
		pm.lastCPUTime = now
		pm.lastCPUUsage = 0.0
		return 0.0
	}

	// 计算时间间隔
	timeDelta := now.Sub(pm.lastCPUTime).Seconds()
	if timeDelta <= 0 {
		return pm.lastCPUUsage
	}

	// 获取运行时统计信息
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 使用GC暂停时间作为CPU使用率的近似指标
	// 这是一个简化的实现，实际生产环境可能需要更精确的方法
	gcPauseTotal := float64(m.PauseTotalNs) / 1e9 // 转换为秒

	// 计算CPU使用率（基于GC暂停时间的近似值）
	cpuUsage := (gcPauseTotal / timeDelta) * 100
	if cpuUsage > 100 {
		cpuUsage = 100
	}
	if cpuUsage < 0 {
		cpuUsage = 0
	}

	// 更新记录
	pm.lastCPUTime = now
	pm.lastCPUUsage = cpuUsage

	return cpuUsage
}

// startCollection 启动系统指标收集
func (pm *PerformanceMonitor) startCollection() {
	pm.collectTicker = time.NewTicker(pm.collectInterval)

	go func() {
		for {
			select {
			case <-pm.collectTicker.C:
				pm.collectSystemMetrics()
			case <-pm.stopCollect:
				pm.collectTicker.Stop()
				return
			}
		}
	}()
}

// collectSystemMetrics 收集系统指标
func (pm *PerformanceMonitor) collectSystemMetrics() {
	metric := pm.GetCurrentSystemMetric()

	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 添加新指标
	pm.systemMetrics = append(pm.systemMetrics, metric)

	// 保持历史大小限制
	if len(pm.systemMetrics) > pm.maxHistorySize {
		pm.systemMetrics = pm.systemMetrics[1:]
	}

	pm.logger.Debug("收集系统指标",
		zap.Int64("memoryAlloc", metric.MemoryAlloc),
		zap.Int("goroutines", metric.GoroutineNum))
}

// GetPerformanceReport 获取性能报告
func (pm *PerformanceMonitor) GetPerformanceReport() map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 操作统计
	operationStats := make(map[string]interface{})
	totalOperations := int64(0)
	totalErrors := int64(0)

	for name, metric := range pm.operations {
		totalOperations += metric.Count
		totalErrors += metric.ErrorCount

		operationStats[name] = map[string]interface{}{
			"count":        metric.Count,
			"totalTime":    metric.TotalTime.String(),
			"avgTime":      metric.AvgTime.String(),
			"minTime":      metric.MinTime.String(),
			"maxTime":      metric.MaxTime.String(),
			"errorCount":   metric.ErrorCount,
			"errorRate":    float64(metric.ErrorCount) / float64(metric.Count),
			"lastExecuted": metric.LastExecuted,
		}
	}

	// 系统统计
	var currentMemory, maxMemory int64
	var maxGoroutines int

	if len(pm.systemMetrics) > 0 {
		latest := pm.systemMetrics[len(pm.systemMetrics)-1]
		currentMemory = latest.MemoryAlloc

		for _, metric := range pm.systemMetrics {
			if metric.MemoryAlloc > maxMemory {
				maxMemory = metric.MemoryAlloc
			}
			if metric.GoroutineNum > maxGoroutines {
				maxGoroutines = metric.GoroutineNum
			}
		}
	}

	return map[string]interface{}{
		"summary": map[string]interface{}{
			"totalOperations": totalOperations,
			"totalErrors":     totalErrors,
			"errorRate":       float64(totalErrors) / float64(totalOperations),
			"operationTypes":  len(pm.operations),
		},
		"operations": operationStats,
		"system": map[string]interface{}{
			"currentMemory":  currentMemory,
			"maxMemory":      maxMemory,
			"maxGoroutines":  maxGoroutines,
			"metricsHistory": len(pm.systemMetrics),
		},
		"config": map[string]interface{}{
			"maxHistorySize":  pm.maxHistorySize,
			"collectInterval": pm.collectInterval.String(),
		},
	}
}

// ResetMetrics 重置指标
func (pm *PerformanceMonitor) ResetMetrics() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.operations = make(map[string]*OperationMetric)
	pm.systemMetrics = make([]*SystemMetric, 0, pm.maxHistorySize)

	pm.logger.Info("性能指标已重置")
}

// GetTopOperations 获取耗时最长的操作
func (pm *PerformanceMonitor) GetTopOperations(limit int) []*OperationMetric {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	operations := make([]*OperationMetric, 0, len(pm.operations))
	for _, metric := range pm.operations {
		metricCopy := *metric
		operations = append(operations, &metricCopy)
	}

	// 按平均时间排序
	for i := 0; i < len(operations)-1; i++ {
		for j := i + 1; j < len(operations); j++ {
			if operations[i].AvgTime < operations[j].AvgTime {
				operations[i], operations[j] = operations[j], operations[i]
			}
		}
	}

	if limit > 0 && limit < len(operations) {
		operations = operations[:limit]
	}

	return operations
}

// GetErrorOperations 获取错误率最高的操作
func (pm *PerformanceMonitor) GetErrorOperations(limit int) []*OperationMetric {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	operations := make([]*OperationMetric, 0)
	for _, metric := range pm.operations {
		if metric.ErrorCount > 0 {
			metricCopy := *metric
			operations = append(operations, &metricCopy)
		}
	}

	// 按错误率排序
	for i := 0; i < len(operations)-1; i++ {
		for j := i + 1; j < len(operations); j++ {
			errorRateI := float64(operations[i].ErrorCount) / float64(operations[i].Count)
			errorRateJ := float64(operations[j].ErrorCount) / float64(operations[j].Count)
			if errorRateI < errorRateJ {
				operations[i], operations[j] = operations[j], operations[i]
			}
		}
	}

	if limit > 0 && limit < len(operations) {
		operations = operations[:limit]
	}

	return operations
}

// Close 关闭性能监控器
func (pm *PerformanceMonitor) Close() {
	close(pm.stopCollect)

	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.operations = make(map[string]*OperationMetric)
	pm.systemMetrics = make([]*SystemMetric, 0)

	pm.logger.Info("性能监控器已关闭")
}
