import React, { useState } from 'react';
import { 
  Card, 
  Table, 
  Tag, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select, 
  InputNumber,
  Switch,
  Tooltip,
  Badge,
  Popconfirm,
  message
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  BellOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { DataTable, StatusTag, ActionButtons } from '@/components/DataTable';

const { Option } = Select;
const { TextArea } = Input;

// 告警级别
export type AlertLevel = 'info' | 'warning' | 'error' | 'critical';

// 告警状态
export type AlertStatus = 'active' | 'resolved' | 'silenced' | 'acknowledged';

// 告警项
export interface Alert {
  id: string;
  name: string;
  level: AlertLevel;
  status: AlertStatus;
  message: string;
  source: string;
  timestamp: string;
  duration?: string;
  labels?: Record<string, string>;
  annotations?: Record<string, string>;
}

// 告警规则
export interface AlertRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  level: AlertLevel;
  metric: string;
  condition: 'gt' | 'lt' | 'eq' | 'ne';
  threshold: number;
  duration: number;
  labels?: Record<string, string>;
  annotations?: Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

// 告警管理器属性
export interface AlertManagerProps {
  alerts?: Alert[];
  rules?: AlertRule[];
  loading?: boolean;
  onAlertAcknowledge?: (alertId: string) => void;
  onAlertResolve?: (alertId: string) => void;
  onAlertSilence?: (alertId: string, duration: number) => void;
  onRuleCreate?: (rule: Omit<AlertRule, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onRuleUpdate?: (ruleId: string, rule: Partial<AlertRule>) => void;
  onRuleDelete?: (ruleId: string) => void;
  onRuleToggle?: (ruleId: string, enabled: boolean) => void;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 告警管理组件
 * 
 * 功能特性：
 * - 告警列表展示
 * - 告警状态管理
 * - 告警规则配置
 * - 告警操作功能
 */
export const AlertManager: React.FC<AlertManagerProps> = ({
  alerts = [],
  rules = [],
  loading = false,
  onAlertAcknowledge,
  onAlertResolve,
  onAlertSilence,
  onRuleCreate,
  onRuleUpdate,
  onRuleDelete,
  onRuleToggle,
  className,
  style,
}) => {
  const [activeTab, setActiveTab] = useState<'alerts' | 'rules'>('alerts');
  const [ruleModalVisible, setRuleModalVisible] = useState(false);
  const [editingRule, setEditingRule] = useState<AlertRule | null>(null);
  const [silenceModalVisible, setSilenceModalVisible] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null);
  const [form] = Form.useForm();
  const [silenceForm] = Form.useForm();

  // 告警级别配置
  const levelConfig = {
    info: { color: 'blue', text: '信息', icon: <BellOutlined /> },
    warning: { color: 'orange', text: '警告', icon: <WarningOutlined /> },
    error: { color: 'red', text: '错误', icon: <ExclamationCircleOutlined /> },
    critical: { color: 'red', text: '严重', icon: <ExclamationCircleOutlined /> },
  };

  // 告警状态配置
  const statusConfig = {
    active: { color: 'red', text: '活跃' },
    resolved: { color: 'green', text: '已解决' },
    silenced: { color: 'gray', text: '已静默' },
    acknowledged: { color: 'blue', text: '已确认' },
  };

  // 告警表格列定义
  const alertColumns: ColumnsType<Alert> = [
    {
      title: '告警名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Alert) => (
        <div>
          <div style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: '8px' }}>
            {levelConfig[record.level].icon}
            {name}
          </div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
            {record.source}
          </div>
        </div>
      ),
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: AlertLevel) => (
        <Tag color={levelConfig[level].color}>
          {levelConfig[level].text}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: AlertStatus) => (
        <Badge 
          status={status === 'active' ? 'error' : status === 'resolved' ? 'success' : 'default'}
          text={statusConfig[status].text}
        />
      ),
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 160,
      render: (timestamp: string) => new Date(timestamp).toLocaleString(),
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record: Alert) => (
        <ActionButtons
          actions={[
            {
              key: 'acknowledge',
              label: '确认',
              icon: <CheckOutlined />,
              disabled: record.status !== 'active',
              onClick: () => onAlertAcknowledge?.(record.id),
            },
            {
              key: 'resolve',
              label: '解决',
              disabled: record.status === 'resolved',
              onClick: () => onAlertResolve?.(record.id),
            },
            {
              key: 'silence',
              label: '静默',
              disabled: record.status === 'silenced',
              onClick: () => {
                setSelectedAlert(record);
                setSilenceModalVisible(true);
              },
            },
          ]}
        />
      ),
    },
  ];

  // 规则表格列定义
  const ruleColumns: ColumnsType<AlertRule> = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: AlertRule) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
            {record.description}
          </div>
        </div>
      ),
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: AlertLevel) => (
        <Tag color={levelConfig[level].color}>
          {levelConfig[level].text}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 80,
      render: (enabled: boolean) => (
        <Tag color={enabled ? 'green' : 'default'}>
          {enabled ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '条件',
      key: 'condition',
      width: 200,
      render: (_, record: AlertRule) => (
        <code style={{ fontSize: '12px' }}>
          {record.metric} {record.condition} {record.threshold}
        </code>
      ),
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration: number) => `${duration}s`,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record: AlertRule) => (
        <ActionButtons
          actions={[
            {
              key: 'toggle',
              label: record.enabled ? '禁用' : '启用',
              onClick: () => onRuleToggle?.(record.id, !record.enabled),
            },
            {
              key: 'edit',
              label: '编辑',
              icon: <EditOutlined />,
              onClick: () => handleEditRule(record),
            },
            {
              key: 'delete',
              label: '删除',
              icon: <DeleteOutlined />,
              danger: true,
              onClick: () => handleDeleteRule(record.id),
            },
          ]}
        />
      ),
    },
  ];

  // 处理创建规则
  const handleCreateRule = () => {
    setEditingRule(null);
    form.resetFields();
    setRuleModalVisible(true);
  };

  // 处理编辑规则
  const handleEditRule = (rule: AlertRule) => {
    setEditingRule(rule);
    form.setFieldsValue(rule);
    setRuleModalVisible(true);
  };

  // 处理删除规则
  const handleDeleteRule = (ruleId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个告警规则吗？',
      onOk: () => onRuleDelete?.(ruleId),
    });
  };

  // 处理规则保存
  const handleRuleSave = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingRule) {
        onRuleUpdate?.(editingRule.id, values);
      } else {
        onRuleCreate?.(values);
      }
      
      setRuleModalVisible(false);
      message.success(editingRule ? '规则更新成功' : '规则创建成功');
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理告警静默
  const handleAlertSilence = async () => {
    try {
      const values = await silenceForm.validateFields();
      
      if (selectedAlert) {
        onAlertSilence?.(selectedAlert.id, values.duration);
        setSilenceModalVisible(false);
        message.success('告警已静默');
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <div className={className} style={style}>
      {/* Tab切换 */}
      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Button 
            type={activeTab === 'alerts' ? 'primary' : 'default'}
            onClick={() => setActiveTab('alerts')}
          >
            告警列表 ({alerts.length})
          </Button>
          <Button 
            type={activeTab === 'rules' ? 'primary' : 'default'}
            onClick={() => setActiveTab('rules')}
          >
            告警规则 ({rules.length})
          </Button>
        </Space>
      </div>

      {/* 告警列表 */}
      {activeTab === 'alerts' && (
        <DataTable
          columns={alertColumns}
          data={alerts}
          loading={loading}
          title="告警列表"
          searchPlaceholder="搜索告警..."
        />
      )}

      {/* 告警规则 */}
      {activeTab === 'rules' && (
        <DataTable
          columns={ruleColumns}
          data={rules}
          loading={loading}
          title="告警规则"
          searchPlaceholder="搜索规则..."
          extra={
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleCreateRule}
            >
              创建规则
            </Button>
          }
        />
      )}

      {/* 规则编辑模态框 */}
      <Modal
        title={editingRule ? '编辑告警规则' : '创建告警规则'}
        open={ruleModalVisible}
        onCancel={() => setRuleModalVisible(false)}
        onOk={handleRuleSave}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="请输入规则名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="规则描述"
          >
            <TextArea placeholder="请输入规则描述" rows={3} />
          </Form.Item>

          <Form.Item
            name="level"
            label="告警级别"
            rules={[{ required: true, message: '请选择告警级别' }]}
          >
            <Select placeholder="请选择告警级别">
              <Option value="info">信息</Option>
              <Option value="warning">警告</Option>
              <Option value="error">错误</Option>
              <Option value="critical">严重</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="metric"
            label="监控指标"
            rules={[{ required: true, message: '请输入监控指标' }]}
          >
            <Input placeholder="例如：cpu_usage_percent" />
          </Form.Item>

          <Form.Item
            name="condition"
            label="条件"
            rules={[{ required: true, message: '请选择条件' }]}
          >
            <Select placeholder="请选择条件">
              <Option value="gt">大于 (&gt;)</Option>
              <Option value="lt">小于 (&lt;)</Option>
              <Option value="eq">等于 (=)</Option>
              <Option value="ne">不等于 (≠)</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="threshold"
            label="阈值"
            rules={[{ required: true, message: '请输入阈值' }]}
          >
            <InputNumber 
              placeholder="请输入阈值" 
              style={{ width: '100%' }}
              min={0}
            />
          </Form.Item>

          <Form.Item
            name="duration"
            label="持续时间(秒)"
            rules={[{ required: true, message: '请输入持续时间' }]}
          >
            <InputNumber 
              placeholder="请输入持续时间" 
              style={{ width: '100%' }}
              min={1}
            />
          </Form.Item>

          <Form.Item
            name="enabled"
            label="启用规则"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>

      {/* 静默模态框 */}
      <Modal
        title="静默告警"
        open={silenceModalVisible}
        onCancel={() => setSilenceModalVisible(false)}
        onOk={handleAlertSilence}
      >
        <Form form={silenceForm} layout="vertical">
          <Form.Item
            name="duration"
            label="静默时长(分钟)"
            rules={[{ required: true, message: '请输入静默时长' }]}
            initialValue={60}
          >
            <Select>
              <Option value={15}>15分钟</Option>
              <Option value={30}>30分钟</Option>
              <Option value={60}>1小时</Option>
              <Option value={240}>4小时</Option>
              <Option value={1440}>24小时</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AlertManager;
