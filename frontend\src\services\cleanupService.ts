import { apiClient } from './api';
import type {
  CleanupScanRequest,
  CleanupScanResponse,
  CleanupExecuteRequest,
  CleanupExecuteResponse,
} from '@/types/api';

/**
 * 资源清理服务类
 * 
 * 提供资源清理相关的API操作：
 * - 资源扫描
 * - 清理执行
 * - 清理历史
 */
export class CleanupService {
  private readonly basePath = '/cleanup';

  // ============ 资源清理管理 ============

  /**
   * 获取可清理的命名空间列表
   * @returns 命名空间列表
   */
  async getCleanupNamespaces(): Promise<string[]> {
    return apiClient.get<string[]>(`${this.basePath}/namespaces`);
  }

  /**
   * 扫描可清理的资源
   * @param request 扫描请求
   * @returns 扫描结果
   */
  async scanResources(request: CleanupScanRequest): Promise<CleanupScanResponse> {
    return apiClient.post<CleanupScanResponse>(`${this.basePath}/scan`, request);
  }

  /**
   * 执行资源清理
   * @param request 清理请求
   * @returns 清理结果
   */
  async executeCleanup(request: CleanupExecuteRequest): Promise<CleanupExecuteResponse> {
    return apiClient.post<CleanupExecuteResponse>(`${this.basePath}/execute`, request);
  }

  // ============ 工具方法 ============

  /**
   * 解析时间阈值
   * @param threshold 时间阈值字符串 (如: "7d", "1h", "30m")
   * @returns 毫秒数
   */
  parseTimeThreshold(threshold: string): number {
    const match = threshold.match(/^(\d+)([dhms])$/);
    if (!match) return 0;

    const value = parseInt(match[1]);
    const unit = match[2];

    const multipliers: Record<string, number> = {
      's': 1000,           // 秒
      'm': 60 * 1000,      // 分钟
      'h': 60 * 60 * 1000, // 小时
      'd': 24 * 60 * 60 * 1000, // 天
    };

    return value * (multipliers[unit] || 0);
  }

  /**
   * 格式化时间阈值
   * @param milliseconds 毫秒数
   * @returns 格式化的时间字符串
   */
  formatTimeThreshold(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}天`;
    if (hours > 0) return `${hours}小时`;
    if (minutes > 0) return `${minutes}分钟`;
    return `${seconds}秒`;
  }

  /**
   * 计算资源节省百分比
   * @param scanResponse 扫描响应
   * @returns 节省百分比
   */
  calculateSavingsPercentage(scanResponse: CleanupScanResponse): number {
    if (scanResponse.total_resources === 0) return 0;
    return (scanResponse.cleanable_resources.length / scanResponse.total_resources) * 100;
  }

  /**
   * 获取资源类型颜色
   * @param resourceType 资源类型
   * @returns 颜色值
   */
  getResourceTypeColor(resourceType: string): string {
    const colorMap: Record<string, string> = {
      'Pod': '#1890ff',
      'Service': '#52c41a',
      'Deployment': '#722ed1',
      'ConfigMap': '#faad14',
      'Secret': '#ff4d4f',
      'PersistentVolume': '#13c2c2',
      'PersistentVolumeClaim': '#eb2f96',
    };
    
    return colorMap[resourceType] || '#d9d9d9';
  }

  /**
   * 获取安全级别颜色
   * @param safeToDelete 是否安全删除
   * @returns 颜色值
   */
  getSafetyColor(safeToDelete: boolean): string {
    return safeToDelete ? '#52c41a' : '#ff4d4f';
  }

  /**
   * 格式化资源大小
   * @param bytes 字节数
   * @returns 格式化的大小字符串
   */
  formatResourceSize(bytes?: number): string {
    if (!bytes) return 'N/A';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 获取常用的时间阈值选项
   * @returns 时间阈值选项列表
   */
  getTimeThresholdOptions(): Array<{ label: string; value: string }> {
    return [
      { label: '1小时', value: '1h' },
      { label: '6小时', value: '6h' },
      { label: '12小时', value: '12h' },
      { label: '1天', value: '1d' },
      { label: '3天', value: '3d' },
      { label: '7天', value: '7d' },
      { label: '14天', value: '14d' },
      { label: '30天', value: '30d' },
    ];
  }

  /**
   * 获取常用的资源类型选项
   * @returns 资源类型选项列表
   */
  getResourceTypeOptions(): Array<{ label: string; value: string }> {
    return [
      { label: 'Pod', value: 'Pod' },
      { label: 'Service', value: 'Service' },
      { label: 'Deployment', value: 'Deployment' },
      { label: 'ConfigMap', value: 'ConfigMap' },
      { label: 'Secret', value: 'Secret' },
      { label: 'PersistentVolume', value: 'PersistentVolume' },
      { label: 'PersistentVolumeClaim', value: 'PersistentVolumeClaim' },
      { label: 'Job', value: 'Job' },
      { label: 'CronJob', value: 'CronJob' },
    ];
  }
}

// 创建并导出清理服务实例
export const cleanupService = new CleanupService();

export default cleanupService;
