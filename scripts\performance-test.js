#!/usr/bin/env node

/**
 * K8s Helper 性能基准测试脚本
 * 用于测试React重构版本的性能指标
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:8080',
  testDuration: 60000, // 60秒
  concurrentUsers: 10,
  iterations: 5,
  outputDir: './test-results',
};

// 性能指标收集器
class PerformanceCollector {
  constructor() {
    this.metrics = {
      loadTime: [],
      firstContentfulPaint: [],
      largestContentfulPaint: [],
      firstInputDelay: [],
      cumulativeLayoutShift: [],
      memoryUsage: [],
      networkRequests: [],
    };
  }

  async collectMetrics(page) {
    // 收集Web Vitals指标
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const metrics = {};
        
        // 收集性能指标
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'paint') {
              if (entry.name === 'first-contentful-paint') {
                metrics.firstContentfulPaint = entry.startTime;
              }
            }
            if (entry.entryType === 'largest-contentful-paint') {
              metrics.largestContentfulPaint = entry.startTime;
            }
            if (entry.entryType === 'first-input') {
              metrics.firstInputDelay = entry.processingStart - entry.startTime;
            }
            if (entry.entryType === 'layout-shift') {
              if (!entry.hadRecentInput) {
                metrics.cumulativeLayoutShift = (metrics.cumulativeLayoutShift || 0) + entry.value;
              }
            }
          }
        });

        observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });

        // 收集导航时间
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
          metrics.loadTime = navigation.loadEventEnd - navigation.fetchStart;
        }

        // 收集内存使用情况
        if (performance.memory) {
          metrics.memoryUsage = {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit,
          };
        }

        // 收集网络请求
        const resources = performance.getEntriesByType('resource');
        metrics.networkRequests = resources.length;

        setTimeout(() => resolve(metrics), 3000);
      });
    });

    return vitals;
  }

  addMetrics(metrics) {
    Object.keys(metrics).forEach(key => {
      if (this.metrics[key] && metrics[key] !== undefined) {
        if (Array.isArray(this.metrics[key])) {
          this.metrics[key].push(metrics[key]);
        }
      }
    });
  }

  getAverages() {
    const averages = {};
    Object.keys(this.metrics).forEach(key => {
      if (Array.isArray(this.metrics[key]) && this.metrics[key].length > 0) {
        const values = this.metrics[key].filter(v => typeof v === 'number');
        if (values.length > 0) {
          averages[key] = values.reduce((a, b) => a + b, 0) / values.length;
        }
      }
    });
    return averages;
  }
}

// 单页面性能测试
async function testPagePerformance(url, testName) {
  console.log(`🧪 Testing ${testName}...`);
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const collector = new PerformanceCollector();
  
  try {
    for (let i = 0; i < TEST_CONFIG.iterations; i++) {
      const page = await browser.newPage();
      
      // 设置视口
      await page.setViewport({ width: 1920, height: 1080 });
      
      // 开始性能监控
      await page.goto(url, { waitUntil: 'networkidle2' });
      
      // 收集指标
      const metrics = await collector.collectMetrics(page);
      collector.addMetrics(metrics);
      
      await page.close();
      
      console.log(`  Iteration ${i + 1}/${TEST_CONFIG.iterations} completed`);
    }
  } catch (error) {
    console.error(`❌ Error testing ${testName}:`, error.message);
  } finally {
    await browser.close();
  }

  return collector.getAverages();
}

// 并发用户测试
async function testConcurrentUsers() {
  console.log(`🚀 Testing ${TEST_CONFIG.concurrentUsers} concurrent users...`);
  
  const promises = [];
  const startTime = Date.now();
  
  for (let i = 0; i < TEST_CONFIG.concurrentUsers; i++) {
    promises.push(simulateUser(i));
  }
  
  const results = await Promise.all(promises);
  const endTime = Date.now();
  
  return {
    totalTime: endTime - startTime,
    users: TEST_CONFIG.concurrentUsers,
    averageResponseTime: results.reduce((a, b) => a + b.averageResponseTime, 0) / results.length,
    errors: results.reduce((a, b) => a + b.errors, 0),
    successRate: (results.reduce((a, b) => a + b.successes, 0) / (TEST_CONFIG.concurrentUsers * 10)) * 100,
  };
}

// 模拟单个用户行为
async function simulateUser(userId) {
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();
  const results = { successes: 0, errors: 0, responseTimes: [] };
  
  try {
    // 模拟用户操作序列
    const actions = [
      () => page.goto(`${TEST_CONFIG.baseUrl}/`),
      () => page.goto(`${TEST_CONFIG.baseUrl}/dashboard`),
      () => page.goto(`${TEST_CONFIG.baseUrl}/cluster-info`),
      () => page.goto(`${TEST_CONFIG.baseUrl}/monitoring`),
      () => page.goto(`${TEST_CONFIG.baseUrl}/etcd`),
      () => page.goto(`${TEST_CONFIG.baseUrl}/port-forward`),
      () => page.goto(`${TEST_CONFIG.baseUrl}/logs`),
      () => page.goto(`${TEST_CONFIG.baseUrl}/cleanup`),
      () => page.goto(`${TEST_CONFIG.baseUrl}/config`),
      () => page.goto(`${TEST_CONFIG.baseUrl}/`),
    ];

    for (const action of actions) {
      const startTime = Date.now();
      try {
        await action();
        await page.waitForSelector('body', { timeout: 5000 });
        const responseTime = Date.now() - startTime;
        results.responseTimes.push(responseTime);
        results.successes++;
      } catch (error) {
        results.errors++;
        console.log(`❌ User ${userId} error:`, error.message);
      }
    }
  } catch (error) {
    console.error(`❌ User ${userId} failed:`, error.message);
  } finally {
    await browser.close();
  }

  return {
    userId,
    successes: results.successes,
    errors: results.errors,
    averageResponseTime: results.responseTimes.length > 0 
      ? results.responseTimes.reduce((a, b) => a + b, 0) / results.responseTimes.length 
      : 0,
  };
}

// 生成测试报告
function generateReport(results) {
  const report = {
    timestamp: new Date().toISOString(),
    testConfig: TEST_CONFIG,
    results: results,
    summary: {
      overallScore: calculateOverallScore(results),
      recommendations: generateRecommendations(results),
    },
  };

  // 确保输出目录存在
  if (!fs.existsSync(TEST_CONFIG.outputDir)) {
    fs.mkdirSync(TEST_CONFIG.outputDir, { recursive: true });
  }

  // 保存详细报告
  const reportPath = path.join(TEST_CONFIG.outputDir, `performance-report-${Date.now()}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  // 生成Markdown报告
  const markdownReport = generateMarkdownReport(report);
  const markdownPath = path.join(TEST_CONFIG.outputDir, `performance-report-${Date.now()}.md`);
  fs.writeFileSync(markdownPath, markdownReport);

  console.log(`📊 Reports saved to:`);
  console.log(`  JSON: ${reportPath}`);
  console.log(`  Markdown: ${markdownPath}`);

  return report;
}

// 计算总体评分
function calculateOverallScore(results) {
  let score = 100;
  
  // 根据加载时间扣分
  if (results.homepage?.loadTime > 3000) score -= 20;
  else if (results.homepage?.loadTime > 2000) score -= 10;
  
  // 根据FCP扣分
  if (results.homepage?.firstContentfulPaint > 2000) score -= 15;
  else if (results.homepage?.firstContentfulPaint > 1500) score -= 8;
  
  // 根据并发测试结果扣分
  if (results.concurrentUsers?.successRate < 95) score -= 25;
  else if (results.concurrentUsers?.successRate < 98) score -= 10;
  
  return Math.max(0, score);
}

// 生成优化建议
function generateRecommendations(results) {
  const recommendations = [];
  
  if (results.homepage?.loadTime > 2000) {
    recommendations.push('考虑优化资源加载，使用代码分割和懒加载');
  }
  
  if (results.homepage?.firstContentfulPaint > 1500) {
    recommendations.push('优化关键渲染路径，减少首屏渲染时间');
  }
  
  if (results.concurrentUsers?.successRate < 98) {
    recommendations.push('提高服务器并发处理能力，优化数据库查询');
  }
  
  if (results.homepage?.cumulativeLayoutShift > 0.1) {
    recommendations.push('减少布局偏移，为图片和动态内容预留空间');
  }
  
  return recommendations;
}

// 生成Markdown报告
function generateMarkdownReport(report) {
  return `# 性能测试报告

## 测试概览
- **测试时间**: ${report.timestamp}
- **测试配置**: ${report.testConfig.iterations}次迭代，${report.testConfig.concurrentUsers}个并发用户
- **总体评分**: ${report.summary.overallScore}/100

## 页面性能指标

### 首页性能
- **加载时间**: ${Math.round(report.results.homepage?.loadTime || 0)}ms
- **首次内容绘制**: ${Math.round(report.results.homepage?.firstContentfulPaint || 0)}ms
- **最大内容绘制**: ${Math.round(report.results.homepage?.largestContentfulPaint || 0)}ms
- **累积布局偏移**: ${(report.results.homepage?.cumulativeLayoutShift || 0).toFixed(3)}

## 并发用户测试
- **并发用户数**: ${report.results.concurrentUsers?.users || 0}
- **成功率**: ${(report.results.concurrentUsers?.successRate || 0).toFixed(1)}%
- **平均响应时间**: ${Math.round(report.results.concurrentUsers?.averageResponseTime || 0)}ms
- **错误数**: ${report.results.concurrentUsers?.errors || 0}

## 优化建议
${report.summary.recommendations.map(rec => `- ${rec}`).join('\n')}

---
*报告生成时间: ${new Date().toLocaleString()}*
`;
}

// 主测试函数
async function runPerformanceTests() {
  console.log('🚀 Starting K8s Helper Performance Tests...\n');
  
  const results = {};
  
  try {
    // 测试主要页面
    results.homepage = await testPagePerformance(`${TEST_CONFIG.baseUrl}/`, 'Homepage');
    results.dashboard = await testPagePerformance(`${TEST_CONFIG.baseUrl}/dashboard`, 'Dashboard');
    results.monitoring = await testPagePerformance(`${TEST_CONFIG.baseUrl}/monitoring`, 'Monitoring');
    
    // 并发用户测试
    results.concurrentUsers = await testConcurrentUsers();
    
    // 生成报告
    const report = generateReport(results);
    
    console.log('\n✅ Performance tests completed!');
    console.log(`📊 Overall Score: ${report.summary.overallScore}/100`);
    
    if (report.summary.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.summary.recommendations.forEach(rec => console.log(`  - ${rec}`));
    }
    
  } catch (error) {
    console.error('❌ Performance tests failed:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runPerformanceTests();
}

module.exports = { runPerformanceTests, testPagePerformance, testConcurrentUsers };
