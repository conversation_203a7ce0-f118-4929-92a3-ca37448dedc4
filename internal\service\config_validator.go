package service

import (
	"go.uber.org/zap"
	"k8s-helper/internal/domain"
)

// ValidationResult 验证结果
type ValidationResult struct {
	Valid    bool
	Errors   []string
	Warnings []string
}

// ConfigValidator 配置验证器
type ConfigValidator struct {
	logger          *zap.Logger
	errorHandler    *ErrorHandler
	commonValidator *CommonValidator
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator(logger *zap.Logger) *ConfigValidator {
	return &ConfigValidator{
		logger:          logger,
		errorHandler:    NewErrorHandler(logger),
		commonValidator: NewCommonValidator(logger),
	}
}

// ValidateETCDConfig 验证ETCD配置
func (cv *ConfigValidator) ValidateETCDConfig(config *domain.ETCDConfig) *ValidationResult {
	// 使用通用验证器进行验证
	commonResult := cv.commonValidator.ValidateETCDConfig(config)

	// 转换为旧格式以保持向后兼容
	return commonResult.ToValidationResult()
}

// ValidateBackupOptions 验证备份选项
func (cv *ConfigValidator) ValidateBackupOptions(opts *domain.BackupOptions) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	if opts == nil {
		result.Valid = false
		result.Errors = append(result.Errors, "备份选项不能为空")
		return result
	}

	// 使用通用验证器创建结果
	commonResult := &CommonValidationResult{Valid: true}

	// 验证输出路径
	cv.commonValidator.ValidateOutputPath(opts.OutputPath, commonResult)

	// 验证超时时间
	cv.commonValidator.ValidateTimeout(opts.Timeout, "timeout", commonResult)

	// 验证ETCD配置
	if opts.ETCDConfig != nil {
		etcdResult := cv.commonValidator.ValidateETCDConfig(opts.ETCDConfig)
		commonResult.Issues = append(commonResult.Issues, etcdResult.Issues...)
		if !etcdResult.Valid {
			commonResult.Valid = false
		}
	}

	// 转换为旧格式
	result.Valid = commonResult.Valid
	result.Errors = commonResult.GetErrors()
	result.Warnings = commonResult.GetWarnings()

	return result
}

// ValidateRestoreOptions 验证恢复选项
func (cv *ConfigValidator) ValidateRestoreOptions(opts *domain.RestoreOptions) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	if opts == nil {
		result.Valid = false
		result.Errors = append(result.Errors, "恢复选项不能为空")
		return result
	}

	// 使用通用验证器创建结果
	commonResult := &CommonValidationResult{Valid: true}

	// 验证快照路径
	if opts.SnapshotPath == "" {
		commonResult.AddError("snapshot_path", "快照路径不能为空")
	} else {
		cv.commonValidator.validateFilePath("snapshot_path", opts.SnapshotPath, commonResult)
	}

	// 验证数据目录
	if opts.DataDir == "" {
		commonResult.AddError("data_dir", "数据目录不能为空")
	}

	// 验证节点名称
	if opts.Name == "" {
		commonResult.AddError("name", "节点名称不能为空")
	}

	// 验证超时时间
	cv.commonValidator.ValidateTimeout(opts.Timeout, "timeout", commonResult)

	// 转换为旧格式
	result.Valid = commonResult.Valid
	result.Errors = commonResult.GetErrors()
	result.Warnings = commonResult.GetWarnings()

	return result
}

// ValidateVerifyOptions 验证验证选项
func (cv *ConfigValidator) ValidateVerifyOptions(opts *domain.VerifyOptions) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	if opts == nil {
		result.Valid = false
		result.Errors = append(result.Errors, "验证选项不能为空")
		return result
	}

	// 使用通用验证器创建结果
	commonResult := &CommonValidationResult{Valid: true}

	// 验证快照路径
	if opts.SnapshotPath == "" {
		commonResult.AddError("snapshot_path", "快照路径不能为空")
	} else {
		cv.commonValidator.validateFilePath("snapshot_path", opts.SnapshotPath, commonResult)
	}

	// 验证超时时间
	cv.commonValidator.ValidateTimeout(opts.Timeout, "timeout", commonResult)

	// 转换为旧格式
	result.Valid = commonResult.Valid
	result.Errors = commonResult.GetErrors()
	result.Warnings = commonResult.GetWarnings()

	return result
}
