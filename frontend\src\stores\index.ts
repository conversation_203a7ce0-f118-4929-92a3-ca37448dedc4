// 状态管理导出文件
// 这里将导出所有Zustand stores

// 全局应用状态
export { default as useAppStore } from './appStore';
export type {
  AppState,
  AppActions,
  AppNotification,
  AppSettings,
  Theme
} from './appStore';

export { default as useUserStore } from './userStore';
export type {
  UserState,
  UserActions,
  UserInfo,
  UserPreferences,
  UserSession,
  RecentPage,
  UserAction
} from './userStore';

// 连接状态
export { default as useConnectionStore } from './connectionStore';
export type {
  ConnectionState,
  ConnectionActions,
  WebSocketConnection,
  ApiConnectionStatus,
  ConnectionStats,
  ConnectionEvent,
  ConnectionStatus
} from './connectionStore';

// 状态选择器导出
export {
  selectTheme,
  selectIsDarkMode,
  selectSidebarCollapsed,
  selectPageLoading,
  selectGlobalLoading,
  selectGlobalError,
  selectNotifications,
  selectSettings,
} from './appStore';

export {
  selectUserInfo,
  selectIsAuthenticated,
  selectPreferences,
  selectRecentPages,
  selectFavorites,
  selectActionHistory,
} from './userStore';

export {
  selectApiStatus,
  selectWebSockets,
  selectConnectionStats,
  selectIsOnline,
  selectConnectionEvents,
} from './connectionStore';

// 业务状态（待实现）
// export { useClusterStore } from './clusterStore';
// export { useETCDStore } from './etcdStore';
// export { useLogsStore } from './logsStore';
// export { useConfigStore } from './configStore';
// export { useMonitoringStore } from './monitoringStore';

// UI状态（待实现）
// export { useModalStore } from './modalStore';
// export { useNotificationStore } from './notificationStore';
