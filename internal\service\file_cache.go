package service

import (
	"crypto/md5"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"sync"
	"time"

	"go.uber.org/zap"
)

// FileCacheEntry 文件缓存条目
type FileCacheEntry struct {
	Content    []byte
	Hash       string
	ModTime    time.Time
	Size       int64
	CachedAt   time.Time
	AccessedAt time.Time
	HitCount   int64
	Metadata   map[string]interface{}
}

// IsExpired 检查缓存是否过期
func (e *FileCacheEntry) IsExpired(ttl time.Duration) bool {
	if ttl <= 0 {
		return false // 永不过期
	}
	return time.Since(e.CachedAt) > ttl
}

// IsStale 检查缓存是否过时（文件已修改）
func (e *FileCacheEntry) IsStale(filePath string) bool {
	info, err := os.Stat(filePath)
	if err != nil {
		return true // 文件不存在或无法访问，认为过时
	}

	return !info.ModTime().Equal(e.ModTime) || info.Size() != e.Size
}

// Touch 更新访问时间和命中次数
func (e *FileCacheEntry) Touch() {
	e.AccessedAt = time.Now()
	e.HitCount++
}

// FileCacheConfig 文件缓存配置
type FileCacheConfig struct {
	MaxSize         int           // 最大缓存条目数
	MaxMemory       int64         // 最大内存使用（字节）
	DefaultTTL      time.Duration // 默认TTL
	CleanupInterval time.Duration // 清理间隔
	EnableStats     bool          // 是否启用统计
}

// DefaultFileCacheConfig 默认文件缓存配置
func DefaultFileCacheConfig() *FileCacheConfig {
	return &FileCacheConfig{
		MaxSize:         500,
		MaxMemory:       50 * 1024 * 1024, // 50MB
		DefaultTTL:      10 * time.Minute,
		CleanupInterval: 2 * time.Minute,
		EnableStats:     true,
	}
}

// FileCache 文件内容缓存
type FileCache struct {
	config        *FileCacheConfig
	logger        *zap.Logger
	cache         map[string]*FileCacheEntry
	mutex         sync.RWMutex
	currentMemory int64
	totalRequests int64
	totalHits     int64
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
}

// NewFileCache 创建文件缓存
func NewFileCache(config *FileCacheConfig, logger *zap.Logger) *FileCache {
	if config == nil {
		config = DefaultFileCacheConfig()
	}

	fc := &FileCache{
		config:      config,
		logger:      logger,
		cache:       make(map[string]*FileCacheEntry),
		stopCleanup: make(chan struct{}),
	}

	// 启动定期清理
	if config.CleanupInterval > 0 {
		fc.startCleanup()
	}

	return fc
}

// ReadFile 读取文件内容（带缓存）
func (fc *FileCache) ReadFile(filePath string) ([]byte, error) {
	fc.mutex.Lock()
	defer fc.mutex.Unlock()

	if fc.config.EnableStats {
		fc.totalRequests++
	}

	// 检查缓存
	if entry, exists := fc.cache[filePath]; exists {
		// 检查缓存是否过期或过时
		if !entry.IsExpired(fc.config.DefaultTTL) && !entry.IsStale(filePath) {
			entry.Touch()
			if fc.config.EnableStats {
				fc.totalHits++
			}

			fc.logger.Debug("文件缓存命中",
				zap.String("path", filePath),
				zap.Int64("hitCount", entry.HitCount))

			return entry.Content, nil
		}

		// 缓存过期或过时，删除旧条目
		fc.removeEntry(filePath)
	}

	// 缓存未命中，读取文件
	fc.logger.Debug("文件缓存未命中，读取文件", zap.String("path", filePath))

	content, err := fc.readFileFromDisk(filePath)
	if err != nil {
		return nil, err
	}

	// 添加到缓存
	fc.addToCache(filePath, content)

	return content, nil
}

// ReadFileWithHash 读取文件内容并计算哈希
func (fc *FileCache) ReadFileWithHash(filePath string, hashType string) ([]byte, string, error) {
	content, err := fc.ReadFile(filePath)
	if err != nil {
		return nil, "", err
	}

	// 检查缓存中是否已有哈希
	fc.mutex.RLock()
	entry, exists := fc.cache[filePath]
	fc.mutex.RUnlock()

	if exists && entry.Hash != "" {
		return content, entry.Hash, nil
	}

	// 计算哈希
	hash, err := fc.calculateHash(content, hashType)
	if err != nil {
		return content, "", err
	}

	// 更新缓存中的哈希
	fc.mutex.Lock()
	if entry, exists := fc.cache[filePath]; exists {
		entry.Hash = hash
	}
	fc.mutex.Unlock()

	return content, hash, nil
}

// InvalidateFile 使文件缓存失效
func (fc *FileCache) InvalidateFile(filePath string) {
	fc.mutex.Lock()
	defer fc.mutex.Unlock()

	fc.removeEntry(filePath)
	fc.logger.Debug("文件缓存已失效", zap.String("path", filePath))
}

// Clear 清空所有缓存
func (fc *FileCache) Clear() {
	fc.mutex.Lock()
	defer fc.mutex.Unlock()

	size := len(fc.cache)
	fc.cache = make(map[string]*FileCacheEntry)
	fc.currentMemory = 0
	fc.totalRequests = 0
	fc.totalHits = 0

	fc.logger.Info("文件缓存已清空", zap.Int("clearedEntries", size))
}

// GetStats 获取缓存统计信息
func (fc *FileCache) GetStats() map[string]interface{} {
	fc.mutex.RLock()
	defer fc.mutex.RUnlock()

	var hitRate float64
	if fc.totalRequests > 0 {
		hitRate = float64(fc.totalHits) / float64(fc.totalRequests)
	}

	return map[string]interface{}{
		"size":          len(fc.cache),
		"maxSize":       fc.config.MaxSize,
		"currentMemory": fc.currentMemory,
		"maxMemory":     fc.config.MaxMemory,
		"totalRequests": fc.totalRequests,
		"totalHits":     fc.totalHits,
		"hitRate":       hitRate,
		"ttl":           fc.config.DefaultTTL.String(),
	}
}

// Close 关闭文件缓存
func (fc *FileCache) Close() error {
	if fc.stopCleanup != nil {
		close(fc.stopCleanup)
	}

	fc.Clear()
	fc.logger.Info("文件缓存已关闭")
	return nil
}

// readFileFromDisk 从磁盘读取文件
func (fc *FileCache) readFileFromDisk(filePath string) ([]byte, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	info, err := file.Stat()
	if err != nil {
		return nil, fmt.Errorf("获取文件信息失败: %w", err)
	}

	// 检查文件大小是否超过内存限制
	if info.Size() > fc.config.MaxMemory/2 {
		return nil, fmt.Errorf("文件过大，无法缓存: %d bytes", info.Size())
	}

	content, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("读取文件内容失败: %w", err)
	}

	return content, nil
}

// addToCache 添加到缓存
func (fc *FileCache) addToCache(filePath string, content []byte) {
	info, err := os.Stat(filePath)
	if err != nil {
		fc.logger.Warn("获取文件信息失败", zap.String("path", filePath), zap.Error(err))
		return
	}

	contentSize := int64(len(content))

	// 检查内存限制
	if fc.currentMemory+contentSize > fc.config.MaxMemory {
		fc.evictLRU(contentSize)
	}

	// 检查条目数量限制
	if len(fc.cache) >= fc.config.MaxSize {
		fc.evictLRU(0)
	}

	now := time.Now()
	entry := &FileCacheEntry{
		Content:    content,
		Hash:       "",
		ModTime:    info.ModTime(),
		Size:       info.Size(),
		CachedAt:   now,
		AccessedAt: now,
		HitCount:   0,
		Metadata:   make(map[string]interface{}),
	}

	fc.cache[filePath] = entry
	fc.currentMemory += contentSize

	fc.logger.Debug("文件已添加到缓存",
		zap.String("path", filePath),
		zap.Int64("size", contentSize),
		zap.Int("cacheSize", len(fc.cache)))
}

// removeEntry 删除缓存条目
func (fc *FileCache) removeEntry(filePath string) {
	if entry, exists := fc.cache[filePath]; exists {
		fc.currentMemory -= int64(len(entry.Content))
		delete(fc.cache, filePath)
	}
}

// evictLRU 驱逐最近最少使用的条目
func (fc *FileCache) evictLRU(needSpace int64) {
	if len(fc.cache) == 0 {
		return
	}

	// 找到最旧的条目
	var oldestPath string
	var oldestTime time.Time
	var found bool

	for path, entry := range fc.cache {
		if !found {
			oldestPath = path
			oldestTime = entry.AccessedAt
			found = true
		} else if entry.AccessedAt.Before(oldestTime) {
			oldestPath = path
			oldestTime = entry.AccessedAt
		}
	}

	if found {
		fc.removeEntry(oldestPath)
		fc.logger.Debug("驱逐LRU缓存条目", zap.String("path", oldestPath))

		// 如果还需要更多空间，继续驱逐
		if needSpace > 0 && fc.currentMemory+needSpace > fc.config.MaxMemory && len(fc.cache) > 0 {
			fc.evictLRU(needSpace)
		}
	}
}

// calculateHash 计算内容哈希
func (fc *FileCache) calculateHash(content []byte, hashType string) (string, error) {
	switch hashType {
	case "md5":
		hash := md5.Sum(content)
		return fmt.Sprintf("%x", hash), nil
	case "sha256":
		hash := sha256.Sum256(content)
		return fmt.Sprintf("%x", hash), nil
	default:
		// 默认使用SHA256
		hash := sha256.Sum256(content)
		return fmt.Sprintf("%x", hash), nil
	}
}

// startCleanup 启动定期清理
func (fc *FileCache) startCleanup() {
	fc.cleanupTicker = time.NewTicker(fc.config.CleanupInterval)

	go func() {
		for {
			select {
			case <-fc.cleanupTicker.C:
				fc.cleanup()
			case <-fc.stopCleanup:
				fc.cleanupTicker.Stop()
				return
			}
		}
	}()
}

// cleanup 清理过期缓存
func (fc *FileCache) cleanup() {
	fc.mutex.Lock()
	defer fc.mutex.Unlock()

	expiredPaths := make([]string, 0)

	for path, entry := range fc.cache {
		if entry.IsExpired(fc.config.DefaultTTL) || entry.IsStale(path) {
			expiredPaths = append(expiredPaths, path)
		}
	}

	for _, path := range expiredPaths {
		fc.removeEntry(path)
	}

	if len(expiredPaths) > 0 {
		fc.logger.Debug("清理过期文件缓存",
			zap.Int("expired", len(expiredPaths)),
			zap.Int("remaining", len(fc.cache)))
	}
}
