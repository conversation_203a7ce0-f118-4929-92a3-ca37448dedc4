package etcd

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.etcd.io/etcd/etcdutl/v3/snapshot"
	"go.uber.org/zap"
)

// RestoreOptions 恢复选项
type RestoreOptions struct {
	SnapshotPath             string        // 快照文件路径
	DataDir                  string        // 数据目录路径
	Name                     string        // 节点名称
	InitialCluster           string        // 初始集群配置
	InitialAdvertisePeerURLs string        // 初始广播对等URL
	Timeout                  time.Duration // 超时时间
	SkipHashCheck            bool          // 是否跳过哈希检查
	MarkCompacted            bool          // 是否标记为压缩
	RevisionBump             uint64        // 版本号增量
}

// DefaultRestoreOptions 返回默认恢复选项
func DefaultRestoreOptions() *RestoreOptions {
	return &RestoreOptions{
		Timeout:       5 * time.Minute,
		SkipHashCheck: false,
		MarkCompacted: false,
		RevisionBump:  0,
	}
}

// RestoreResult 恢复结果
type RestoreResult struct {
	DataDir      string        // 恢复的数据目录
	Duration     time.Duration // 恢复耗时
	SnapshotSize int64         // 快照文件大小
	Success      bool          // 是否成功
}

// RestoreWithSDK 使用SDK进行恢复操作
func (c *Client) RestoreWithSDK(ctx context.Context, opts *RestoreOptions) (*RestoreResult, error) {
	if opts == nil {
		opts = DefaultRestoreOptions()
	}

	startTime := time.Now()

	c.logger.Info("开始使用SDK执行etcd恢复",
		zap.String("snapshot", opts.SnapshotPath),
		zap.String("dataDir", opts.DataDir),
		zap.String("name", opts.Name))

	// 验证恢复选项
	if err := c.validateRestoreOptions(opts); err != nil {
		return nil, NewSDKError("restore", "恢复选项验证失败", err)
	}

	// 设置超时
	if opts.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, opts.Timeout)
		defer cancel()
	}

	// 获取快照文件大小
	snapshotInfo, err := os.Stat(opts.SnapshotPath)
	if err != nil {
		return nil, NewSDKError("restore", "获取快照文件信息失败", err)
	}

	// 创建快照管理器
	manager := snapshot.NewV3(c.logger)

	// 解析PeerURLs
	var peerURLs []string
	if opts.InitialAdvertisePeerURLs != "" {
		peerURLs = strings.Split(opts.InitialAdvertisePeerURLs, ",")
	}

	// 配置恢复参数
	restoreConfig := snapshot.RestoreConfig{
		SnapshotPath:   opts.SnapshotPath,
		Name:           opts.Name,
		OutputDataDir:  opts.DataDir,
		PeerURLs:       peerURLs,
		InitialCluster: opts.InitialCluster,
		SkipHashCheck:  opts.SkipHashCheck,
		MarkCompacted:  opts.MarkCompacted,
		RevisionBump:   opts.RevisionBump,
	}

	c.logger.Info("执行SDK恢复操作",
		zap.String("config", fmt.Sprintf("%+v", restoreConfig)))

	// 执行恢复
	if err := manager.Restore(restoreConfig); err != nil {
		return nil, NewSDKError("restore", "SDK恢复操作失败", err)
	}

	duration := time.Since(startTime)

	result := &RestoreResult{
		DataDir:      opts.DataDir,
		Duration:     duration,
		SnapshotSize: snapshotInfo.Size(),
		Success:      true,
	}

	c.logger.Info("SDK恢复操作完成",
		zap.String("dataDir", result.DataDir),
		zap.Duration("duration", result.Duration),
		zap.Int64("snapshotSize", result.SnapshotSize))

	return result, nil
}

// RestoreWithOptions 使用选项执行恢复
func (c *Client) RestoreWithOptions(ctx context.Context, opts *RestoreOptions) (*RestoreResult, error) {
	if opts == nil {
		opts = DefaultRestoreOptions()
	}

	// 验证恢复选项
	if err := c.validateRestoreOptions(opts); err != nil {
		return nil, err
	}

	// 确保数据目录的父目录存在
	parentDir := filepath.Dir(opts.DataDir)
	if err := os.MkdirAll(parentDir, 0755); err != nil {
		return nil, NewSDKError("restore", fmt.Sprintf("创建数据目录父目录失败: %s", parentDir), err)
	}

	// 执行恢复
	result, err := c.RestoreWithSDK(ctx, opts)
	if err != nil {
		return nil, err
	}

	// 验证恢复结果
	if err := c.verifyRestoreResult(result); err != nil {
		c.logger.Warn("恢复结果验证失败", zap.Error(err))
		// 不返回错误，只记录警告
	}

	return result, nil
}

// validateRestoreOptions 验证恢复选项
func (c *Client) validateRestoreOptions(opts *RestoreOptions) error {
	// 验证快照文件路径
	if opts.SnapshotPath == "" {
		return NewSDKError("validate", "快照文件路径不能为空", ErrSnapshotInvalid)
	}

	// 检查快照文件是否存在
	if _, err := os.Stat(opts.SnapshotPath); os.IsNotExist(err) {
		return NewSDKError("validate", fmt.Sprintf("快照文件不存在: %s", opts.SnapshotPath), ErrSnapshotInvalid)
	}

	// 验证数据目录路径
	if opts.DataDir == "" {
		return NewSDKError("validate", "数据目录路径不能为空", ErrDataDirInvalid)
	}

	// 验证节点名称
	if opts.Name == "" {
		return NewSDKError("validate", "节点名称不能为空", ErrInvalidConfig)
	}

	// 验证初始集群配置
	if opts.InitialCluster == "" {
		return NewSDKError("validate", "初始集群配置不能为空", ErrInvalidConfig)
	}

	// 验证初始广播对等URL
	if opts.InitialAdvertisePeerURLs == "" {
		return NewSDKError("validate", "初始广播对等URL不能为空", ErrInvalidConfig)
	}

	return nil
}

// verifyRestoreResult 验证恢复结果
func (c *Client) verifyRestoreResult(result *RestoreResult) error {
	// 检查数据目录是否存在
	if _, err := os.Stat(result.DataDir); os.IsNotExist(err) {
		return NewSDKError("verify", fmt.Sprintf("恢复后数据目录不存在: %s", result.DataDir), ErrDataDirInvalid)
	}

	// 检查数据目录是否为空
	entries, err := os.ReadDir(result.DataDir)
	if err != nil {
		return NewSDKError("verify", fmt.Sprintf("读取数据目录失败: %s", result.DataDir), err)
	}

	if len(entries) == 0 {
		return NewSDKError("verify", fmt.Sprintf("恢复后数据目录为空: %s", result.DataDir), ErrDataDirInvalid)
	}

	c.logger.Info("恢复结果验证通过",
		zap.String("dataDir", result.DataDir),
		zap.Int("entries", len(entries)))

	return nil
}

// RestoreManager 恢复管理器
type RestoreManager struct {
	client *Client
	logger *zap.Logger
}

// NewRestoreManager 创建恢复管理器
func NewRestoreManager(client *Client) *RestoreManager {
	return &RestoreManager{
		client: client,
		logger: client.logger,
	}
}

// PerformRestore 执行恢复操作
func (rm *RestoreManager) PerformRestore(ctx context.Context, snapshotPath, dataDir, name, initialCluster, initialAdvertisePeerURLs string) (*RestoreResult, error) {
	opts := &RestoreOptions{
		SnapshotPath:             snapshotPath,
		DataDir:                  dataDir,
		Name:                     name,
		InitialCluster:           initialCluster,
		InitialAdvertisePeerURLs: initialAdvertisePeerURLs,
		Timeout:                  5 * time.Minute,
		SkipHashCheck:            false,
		MarkCompacted:            false,
	}

	return rm.client.RestoreWithOptions(ctx, opts)
}

// GenerateRestoreConfig 生成恢复配置
func GenerateRestoreConfig(name, dataDir string) (string, string) {
	// 生成默认的初始集群配置
	initialCluster := fmt.Sprintf("%s=http://localhost:2380", name)

	// 生成默认的初始广播对等URL
	initialAdvertisePeerURLs := "http://localhost:2380"

	return initialCluster, initialAdvertisePeerURLs
}
