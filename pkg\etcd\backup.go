package etcd

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"k8s-helper/pkg/common"
)

// BackupOptions 备份选项
type BackupOptions struct {
	OutputPath      string        // 输出路径
	Timeout         time.Duration // 超时时间
	VerifyIntegrity bool          // 是否验证完整性
	CreateDir       bool          // 是否创建目录
}

// DefaultBackupOptions 返回默认备份选项
func DefaultBackupOptions() *BackupOptions {
	return &BackupOptions{
		Timeout:         5 * time.Minute,
		VerifyIntegrity: true,
		CreateDir:       true,
	}
}

// BackupWithOptions 使用选项执行备份
func (c *Client) BackupWithOptions(ctx context.Context, opts *BackupOptions) (*BackupResult, error) {
	if opts == nil {
		opts = DefaultBackupOptions()
	}

	// 验证输出路径
	if opts.OutputPath == "" {
		return nil, NewSDKError("backup", "输出路径不能为空", ErrInvalidConfig)
	}

	// 创建目录（如果需要）
	if opts.CreateDir {
		dir := filepath.Dir(opts.OutputPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return nil, NewSDKError("backup", fmt.Sprintf("创建目录失败: %s", dir), err)
		}
	}

	// 设置超时
	if opts.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, opts.Timeout)
		defer cancel()
	}

	// 执行备份
	result, err := c.Backup(ctx, opts.OutputPath)
	if err != nil {
		return nil, err
	}

	// 验证完整性（如果需要）
	if opts.VerifyIntegrity {
		if err := c.verifyBackupFile(opts.OutputPath); err != nil {
			c.logger.Warn("备份文件完整性验证失败", zap.Error(err))
			// 不返回错误，只是警告
		}
	}

	return result, nil
}

// verifyBackupFile 验证备份文件
func (c *Client) verifyBackupFile(filePath string) error {
	// 检查文件是否存在
	info, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("备份文件不存在: %w", err)
	}

	// 检查文件大小
	if info.Size() == 0 {
		return fmt.Errorf("备份文件为空")
	}

	c.logger.Info("备份文件验证通过",
		zap.String("file", filePath),
		zap.Int64("size", info.Size()))

	return nil
}

// GenerateBackupPath 生成备份文件路径
func GenerateBackupPath(baseDir, prefix string) string {
	timestamp := time.Now().Format(common.BackupTimeFormat)
	filename := fmt.Sprintf("%s-%s.db", prefix, timestamp)
	return filepath.Join(baseDir, filename)
}

// BackupManager 备份管理器
type BackupManager struct {
	client    *Client
	logger    *zap.Logger
	baseDir   string
	retention int // 保留的备份数量
}

// NewBackupManager 创建备份管理器
func NewBackupManager(client *Client, baseDir string, retention int) *BackupManager {
	return &BackupManager{
		client:    client,
		logger:    client.logger,
		baseDir:   baseDir,
		retention: retention,
	}
}

// CreateBackup 创建备份
func (bm *BackupManager) CreateBackup(ctx context.Context, prefix string) (*BackupResult, error) {
	// 确保备份目录存在
	if err := os.MkdirAll(bm.baseDir, 0755); err != nil {
		return nil, fmt.Errorf("创建备份目录失败: %w", err)
	}

	// 生成备份文件路径
	backupPath := GenerateBackupPath(bm.baseDir, prefix)

	// 执行备份
	opts := &BackupOptions{
		OutputPath:      backupPath,
		Timeout:         5 * time.Minute,
		VerifyIntegrity: true,
		CreateDir:       false, // 目录已经创建
	}

	result, err := bm.client.BackupWithOptions(ctx, opts)
	if err != nil {
		return nil, err
	}

	// 清理旧备份
	if bm.retention > 0 {
		if err := bm.cleanupOldBackups(prefix); err != nil {
			bm.logger.Warn("清理旧备份失败", zap.Error(err))
		}
	}

	return result, nil
}

// cleanupOldBackups 清理旧备份
func (bm *BackupManager) cleanupOldBackups(prefix string) error {
	pattern := filepath.Join(bm.baseDir, fmt.Sprintf("%s-*.db", prefix))
	matches, err := filepath.Glob(pattern)
	if err != nil {
		return err
	}

	// 如果备份数量超过保留数量，删除最旧的
	if len(matches) > bm.retention {
		// 按修改时间排序
		type fileInfo struct {
			path    string
			modTime time.Time
		}

		files := make([]fileInfo, 0, len(matches))
		for _, match := range matches {
			info, err := os.Stat(match)
			if err != nil {
				continue
			}
			files = append(files, fileInfo{
				path:    match,
				modTime: info.ModTime(),
			})
		}

		// 简单排序（冒泡排序）
		for i := 0; i < len(files)-1; i++ {
			for j := 0; j < len(files)-1-i; j++ {
				if files[j].modTime.After(files[j+1].modTime) {
					files[j], files[j+1] = files[j+1], files[j]
				}
			}
		}

		// 删除最旧的文件
		toDelete := len(files) - bm.retention
		for i := 0; i < toDelete; i++ {
			if err := os.Remove(files[i].path); err != nil {
				bm.logger.Warn("删除旧备份失败",
					zap.String("file", files[i].path),
					zap.Error(err))
			} else {
				bm.logger.Info("删除旧备份", zap.String("file", files[i].path))
			}
		}
	}

	return nil
}

// ListBackups 列出备份文件
func (bm *BackupManager) ListBackups(prefix string) ([]string, error) {
	pattern := filepath.Join(bm.baseDir, fmt.Sprintf("%s-*.db", prefix))
	return filepath.Glob(pattern)
}
