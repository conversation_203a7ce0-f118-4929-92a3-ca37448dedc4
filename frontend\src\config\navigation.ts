import React from 'react';
import {
  DashboardOutlined,
  DatabaseOutlined,
  ClusterOutlined,
  FileTextOutlined,
  ApiOutlined,
  DeleteOutlined,
  MonitorOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { ROUTES } from '@/utils/constants';

// 导航菜单项类型定义
export interface NavigationItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  path?: string;
  children?: NavigationItem[];
  permission?: string; // 权限控制预留
  badge?: string | number; // 徽章显示
  disabled?: boolean; // 是否禁用
  hidden?: boolean; // 是否隐藏
}

// 主导航菜单配置
export const navigationConfig: NavigationItem[] = [
  {
    key: ROUTES.DASHBOARD,
    label: '仪表板',
    icon: React.createElement(DashboardOutlined),
    path: ROUTES.DASHBOARD,
    permission: 'dashboard:view',
  },
  {
    key: 'etcd',
    label: 'ETCD管理',
    icon: React.createElement(DatabaseOutlined),
    permission: 'etcd:view',
    children: [
      {
        key: ROUTES.ETCD,
        label: '概览',
        path: ROUTES.ETCD,
        permission: 'etcd:overview',
      },
      {
        key: ROUTES.ETCD_BACKUP,
        label: '备份管理',
        path: ROUTES.ETCD_BACKUP,
        permission: 'etcd:backup',
      },
      {
        key: ROUTES.ETCD_RESTORE,
        label: '恢复管理',
        path: ROUTES.ETCD_RESTORE,
        permission: 'etcd:restore',
      },
      {
        key: ROUTES.ETCD_CRONJOBS,
        label: '定时任务',
        path: ROUTES.ETCD_CRONJOBS,
        permission: 'etcd:cronjobs',
      },
    ],
  },
  {
    key: ROUTES.CLUSTER,
    label: '集群信息',
    icon: React.createElement(ClusterOutlined),
    path: ROUTES.CLUSTER,
    permission: 'cluster:view',
  },
  {
    key: ROUTES.LOGS,
    label: '日志管理',
    icon: React.createElement(FileTextOutlined),
    path: ROUTES.LOGS,
    permission: 'logs:view',
  },
  {
    key: ROUTES.PORT_FORWARD,
    label: '端口转发',
    icon: React.createElement(ApiOutlined),
    path: ROUTES.PORT_FORWARD,
    permission: 'port-forward:view',
  },
  {
    key: ROUTES.CLEANUP,
    label: '资源清理',
    icon: React.createElement(DeleteOutlined),
    path: ROUTES.CLEANUP,
    permission: 'cleanup:view',
  },
  {
    key: ROUTES.MONITORING,
    label: '监控告警',
    icon: React.createElement(MonitorOutlined),
    path: ROUTES.MONITORING,
    permission: 'monitoring:view',
  },
  {
    key: ROUTES.CONFIG,
    label: '配置管理',
    icon: React.createElement(SettingOutlined),
    path: ROUTES.CONFIG,
    permission: 'config:view',
  },
];

// 面包屑映射配置
export const breadcrumbMap: Record<string, string> = {
  [ROUTES.DASHBOARD]: '仪表板',
  [ROUTES.ETCD]: 'ETCD管理',
  [ROUTES.ETCD_BACKUP]: '备份管理',
  [ROUTES.ETCD_RESTORE]: '恢复管理',
  [ROUTES.ETCD_CRONJOBS]: '定时任务',
  [ROUTES.CLUSTER]: '集群信息',
  [ROUTES.LOGS]: '日志管理',
  [ROUTES.PORT_FORWARD]: '端口转发',
  [ROUTES.CLEANUP]: '资源清理',
  [ROUTES.MONITORING]: '监控告警',
  [ROUTES.CONFIG]: '配置管理',
};

// 页面标题映射配置
export const pageTitleMap: Record<string, string> = {
  [ROUTES.DASHBOARD]: 'K8s-Helper - 仪表板',
  [ROUTES.ETCD]: 'K8s-Helper - ETCD管理',
  [ROUTES.ETCD_BACKUP]: 'K8s-Helper - 备份管理',
  [ROUTES.ETCD_RESTORE]: 'K8s-Helper - 恢复管理',
  [ROUTES.ETCD_CRONJOBS]: 'K8s-Helper - 定时任务',
  [ROUTES.CLUSTER]: 'K8s-Helper - 集群信息',
  [ROUTES.LOGS]: 'K8s-Helper - 日志管理',
  [ROUTES.PORT_FORWARD]: 'K8s-Helper - 端口转发',
  [ROUTES.CLEANUP]: 'K8s-Helper - 资源清理',
  [ROUTES.MONITORING]: 'K8s-Helper - 监控告警',
  [ROUTES.CONFIG]: 'K8s-Helper - 配置管理',
};

// 权限检查函数（预留）
export const hasPermission = (permission: string): boolean => {
  // TODO: 实现权限检查逻辑
  // 这里可以集成用户权限系统
  return true;
};

// 过滤导航菜单（根据权限）
export const filterNavigationByPermission = (
  items: NavigationItem[],
  checkPermission: (permission: string) => boolean = hasPermission
): NavigationItem[] => {
  return items
    .filter(item => {
      // 检查是否隐藏
      if (item.hidden) return false;
      
      // 检查权限
      if (item.permission && !checkPermission(item.permission)) {
        return false;
      }
      
      return true;
    })
    .map(item => {
      // 递归过滤子菜单
      if (item.children) {
        const filteredChildren = filterNavigationByPermission(item.children, checkPermission);
        return {
          ...item,
          children: filteredChildren.length > 0 ? filteredChildren : undefined,
        };
      }
      
      return item;
    });
};

// 获取当前路由对应的菜单项
export const getCurrentMenuItem = (pathname: string): NavigationItem | null => {
  const findMenuItem = (items: NavigationItem[]): NavigationItem | null => {
    for (const item of items) {
      if (item.path === pathname) {
        return item;
      }
      
      if (item.children) {
        const found = findMenuItem(item.children);
        if (found) return found;
      }
    }
    return null;
  };
  
  return findMenuItem(navigationConfig);
};

// 获取面包屑路径
export const getBreadcrumbItems = (pathname: string) => {
  const breadcrumbs = [
    {
      title: '首页',
      href: ROUTES.DASHBOARD,
    },
  ];

  if (pathname === ROUTES.DASHBOARD) {
    return breadcrumbs;
  }

  // 处理ETCD相关路由
  if (pathname.startsWith('/etcd')) {
    breadcrumbs.push({
      title: 'ETCD管理',
      href: ROUTES.ETCD,
    });

    if (pathname !== ROUTES.ETCD) {
      const currentPageTitle = breadcrumbMap[pathname];
      if (currentPageTitle) {
        breadcrumbs.push({
          title: currentPageTitle,
        });
      }
    }
  } else {
    // 其他路由
    const currentPageTitle = breadcrumbMap[pathname];
    if (currentPageTitle) {
      breadcrumbs.push({
        title: currentPageTitle,
      });
    }
  }

  return breadcrumbs;
};
