# K8s-Helper Makefile - Frontend & Backend Integration

# Project variables
BINARY_NAME=k8s-helper
VERSION=$(shell git describe --tags --always --dirty 2>/dev/null || echo 'dev')
BUILD_TIME=$(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT=$(shell git rev-parse --short HEAD 2>/dev/null || echo 'unknown')

# Directories
BUILD_DIR=build
FRONTEND_DIR=frontend
DIST_DIR=$(FRONTEND_DIR)/dist
SCRIPTS_DIR=scripts

# Go variables
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOMOD=$(GOCMD) mod
GOGENERATE=$(GOCMD) generate

# Node variables
NPM=npm
NODE=node

# Build flags
LDFLAGS_BASE=-w -s
LDFLAGS_VERSION=-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)
LDFLAGS_DEV=-ldflags "$(LDFLAGS_BASE)"
LDFLAGS_PROD=-ldflags "$(LDFLAGS_BASE) $(LDFLAGS_VERSION)"

# Colors for output
RED=\033[0;31m
GREEN=\033[0;32m
YELLOW=\033[1;33m
BLUE=\033[0;34m
NC=\033[0m # No Color

# Default target
.PHONY: all
all: help

# Help - show available targets
.PHONY: help
help:
	@echo "$(BLUE)K8s-Helper Development Automation$(NC)"
	@echo ""
	@echo "$(GREEN)Development Commands:$(NC)"
	@echo "  $(YELLOW)dev$(NC)              - Start development environment (frontend + backend)"
	@echo "  $(YELLOW)dev-frontend$(NC)     - Start only frontend development server"
	@echo "  $(YELLOW)dev-backend$(NC)      - Start only backend in development mode"
	@echo ""
	@echo "$(GREEN)Build Commands:$(NC)"
	@echo "  $(YELLOW)build$(NC)            - Production build (frontend + backend)"
	@echo "  $(YELLOW)build-fast$(NC)       - Fast production build (skip tests, lint, compression)"
	@echo "  $(YELLOW)build-frontend$(NC)   - Build only frontend"
	@echo "  $(YELLOW)build-backend$(NC)    - Build only backend"
	@echo "  $(YELLOW)build-dev$(NC)        - Development build"
	@echo "  $(YELLOW)build-prod$(NC)       - Production build with compression"
	@echo ""
	@echo "$(GREEN)Utility Commands:$(NC)"
	@echo "  $(YELLOW)clean$(NC)            - Clean all build artifacts"
	@echo "  $(YELLOW)deps$(NC)             - Install all dependencies"
	@echo "  $(YELLOW)test$(NC)             - Run all tests"
	@echo "  $(YELLOW)lint$(NC)             - Run code linting"
	@echo "  $(YELLOW)fmt$(NC)              - Format code"
	@echo ""
	@echo "$(GREEN)Examples:$(NC)"
	@echo "  make dev              # Start development environment"
	@echo "  make build-prod       # Production build with compression"
	@echo "  make clean build      # Clean and rebuild"

# Development environment - start frontend and backend concurrently
.PHONY: dev
dev: deps-check
	@echo "$(GREEN)Starting development environment...$(NC)"
	@echo "$(BLUE)Frontend will be available at: http://localhost:5173$(NC)"
	@echo "$(BLUE)Backend will be available at: http://localhost:8080$(NC)"
	@echo "$(YELLOW)Press Ctrl+C to stop both servers$(NC)"
	@$(MAKE) -j2 dev-frontend dev-backend

# Start frontend development server
.PHONY: dev-frontend
dev-frontend:
	@echo "$(GREEN)Starting frontend development server...$(NC)"
	@cd $(FRONTEND_DIR) && $(NPM) run dev

# Start backend in development mode
.PHONY: dev-backend
dev-backend: deps-go
	@echo "$(GREEN)Starting backend in development mode...$(NC)"
	@$(GOBUILD) -tags=dev $(LDFLAGS_DEV) -o $(BINARY_NAME)-dev .
	@./$(BINARY_NAME)-dev web --port 8080 --cors

# Production build - complete build with compression
.PHONY: build
build: build-prod

# Fast production build - skip tests, lint, and compression
.PHONY: build-fast
build-fast:
	@echo "$(GREEN)Starting fast production build...$(NC)"
	@echo "$(YELLOW)Skipping: tests, lint, compression, dependency checks$(NC)"
	@if [ -f $(SCRIPTS_DIR)/build-fast.sh ]; then \
		chmod +x $(SCRIPTS_DIR)/build-fast.sh && $(SCRIPTS_DIR)/build-fast.sh; \
	elif [ -f $(SCRIPTS_DIR)/build.sh ]; then \
		chmod +x $(SCRIPTS_DIR)/build.sh && $(SCRIPTS_DIR)/build.sh --fast --production; \
	else \
		$(MAKE) build-frontend-fast && $(MAKE) build-backend-prod; \
	fi
	@echo "$(GREEN)Fast production build completed!$(NC)"

# Production build with compression
.PHONY: build-prod
build-prod: deps
	@echo "$(GREEN)Starting production build...$(NC)"
	@if command -v powershell >/dev/null 2>&1; then \
		powershell -ExecutionPolicy Bypass -File $(SCRIPTS_DIR)/build-simple.ps1 -Production; \
	elif [ -f $(SCRIPTS_DIR)/build.sh ]; then \
		chmod +x $(SCRIPTS_DIR)/build.sh && $(SCRIPTS_DIR)/build.sh --production; \
	else \
		$(MAKE) build-frontend && $(MAKE) build-backend-prod; \
	fi
	@echo "$(GREEN)Production build completed!$(NC)"

# Development build
.PHONY: build-dev
build-dev: deps
	@echo "$(GREEN)Starting development build...$(NC)"
	@$(MAKE) build-frontend
	@$(MAKE) build-backend-dev
	@echo "$(GREEN)Development build completed!$(NC)"

# Build frontend only
.PHONY: build-frontend
build-frontend: deps-frontend
	@echo "$(GREEN)Building frontend...$(NC)"
	@cd $(FRONTEND_DIR) && $(NPM) run build
	@echo "$(GREEN)Frontend build completed!$(NC)"

# Fast frontend build - skip dependency check if node_modules exists
.PHONY: build-frontend-fast
build-frontend-fast:
	@echo "$(GREEN)Building frontend (fast mode)...$(NC)"
	@if [ ! -d "$(FRONTEND_DIR)/node_modules" ]; then \
		echo "$(YELLOW)Installing frontend dependencies...$(NC)"; \
		cd $(FRONTEND_DIR) && $(NPM) ci; \
	else \
		echo "$(YELLOW)Using existing node_modules...$(NC)"; \
	fi
	@cd $(FRONTEND_DIR) && $(NPM) run build
	@echo "$(GREEN)Frontend fast build completed!$(NC)"

# Build backend only (production)
.PHONY: build-backend
build-backend: build-backend-prod

# Build backend in production mode
.PHONY: build-backend-prod
build-backend-prod: deps-go
	@echo "$(GREEN)Building backend (production)...$(NC)"
	@$(GOGENERATE) ./internal/web
	@CGO_ENABLED=0 $(GOBUILD) $(LDFLAGS_PROD) -o $(BINARY_NAME) .
	@echo "$(GREEN)Backend production build completed!$(NC)"

# Build backend in development mode
.PHONY: build-backend-dev
build-backend-dev: deps-go
	@echo "$(GREEN)Building backend (development)...$(NC)"
	@$(GOBUILD) -tags=dev $(LDFLAGS_DEV) -o $(BINARY_NAME)-dev .
	@echo "$(GREEN)Backend development build completed!$(NC)"

# Install all dependencies
.PHONY: deps
deps: deps-go deps-frontend

# Install Go dependencies
.PHONY: deps-go
deps-go:
	@echo "$(GREEN)Installing Go dependencies...$(NC)"
	@$(GOMOD) download
	@$(GOMOD) tidy

# Install frontend dependencies
.PHONY: deps-frontend
deps-frontend: deps-check
	@echo "$(GREEN)Installing frontend dependencies...$(NC)"
	@cd $(FRONTEND_DIR) && $(NPM) ci

# Check if required tools are available
.PHONY: deps-check
deps-check:
	@echo "$(GREEN)Checking dependencies...$(NC)"
	@command -v $(GOCMD) >/dev/null 2>&1 || { echo "$(RED)Go is not installed$(NC)"; exit 1; }
	@command -v $(NODE) >/dev/null 2>&1 || { echo "$(RED)Node.js is not installed$(NC)"; exit 1; }
	@command -v $(NPM) >/dev/null 2>&1 || { echo "$(RED)npm is not installed$(NC)"; exit 1; }
	@echo "$(GREEN)All required tools are available$(NC)"

# Run all tests
.PHONY: test
test: test-backend test-frontend

# Run backend tests
.PHONY: test-backend
test-backend:
	@echo "$(GREEN)Running backend tests...$(NC)"
	@$(GOTEST) -v ./...

# Run frontend tests
.PHONY: test-frontend
test-frontend:
	@echo "$(GREEN)Running frontend tests...$(NC)"
	@cd $(FRONTEND_DIR) && $(NPM) run test:run

# Run linting
.PHONY: lint
lint: lint-backend lint-frontend

# Run backend linting
.PHONY: lint-backend
lint-backend:
	@echo "$(GREEN)Running backend linting...$(NC)"
	@$(GOCMD) vet ./...
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "$(YELLOW)golangci-lint not found, skipping advanced linting$(NC)"; \
	fi

# Run frontend linting
.PHONY: lint-frontend
lint-frontend:
	@echo "$(GREEN)Running frontend linting...$(NC)"
	@cd $(FRONTEND_DIR) && $(NPM) run lint

# Format code
.PHONY: fmt
fmt: fmt-backend fmt-frontend

# Format backend code
.PHONY: fmt-backend
fmt-backend:
	@echo "$(GREEN)Formatting backend code...$(NC)"
	@$(GOCMD) fmt ./...

# Format frontend code
.PHONY: fmt-frontend
fmt-frontend:
	@echo "$(GREEN)Formatting frontend code...$(NC)"
	@cd $(FRONTEND_DIR) && $(NPM) run lint:fix

# Clean all build artifacts
.PHONY: clean
clean:
	@echo "$(GREEN)Cleaning build artifacts...$(NC)"
	@$(GOCLEAN)
	@rm -f $(BINARY_NAME) $(BINARY_NAME)-dev $(BINARY_NAME)-prod
	@rm -f $(BINARY_NAME).exe $(BINARY_NAME)-dev.exe $(BINARY_NAME)-prod.exe
	@rm -f *-test.exe *-test
	@rm -rf $(BUILD_DIR)
	@if [ -d $(DIST_DIR) ]; then \
		echo "$(GREEN)Cleaning frontend build...$(NC)"; \
		rm -rf $(DIST_DIR); \
	fi
	@echo "$(GREEN)Clean completed!$(NC)"

# Show version information
.PHONY: version
version:
	@echo "$(BLUE)K8s-Helper Version Information:$(NC)"
	@echo "  Version: $(VERSION)"
	@echo "  Build Time: $(BUILD_TIME)"
	@echo "  Git Commit: $(GIT_COMMIT)"

# Show project status
.PHONY: status
status:
	@echo "$(BLUE)Project Status:$(NC)"
	@echo "  Frontend Directory: $(FRONTEND_DIR)"
	@echo "  Frontend Built: $$(if [ -d $(DIST_DIR) ]; then echo '$(GREEN)Yes$(NC)'; else echo '$(RED)No$(NC)'; fi)"
	@echo "  Backend Binary: $$(if [ -f $(BINARY_NAME) ]; then echo '$(GREEN)Yes$(NC)'; else echo '$(RED)No$(NC)'; fi)"
	@echo "  Dev Binary: $$(if [ -f $(BINARY_NAME)-dev ]; then echo '$(GREEN)Yes$(NC)'; else echo '$(RED)No$(NC)'; fi)"
	@if [ -d $(DIST_DIR) ]; then \
		echo "  Frontend Size: $$(du -sh $(DIST_DIR) 2>/dev/null | cut -f1)"; \
	fi
	@if [ -f $(BINARY_NAME) ]; then \
		echo "  Backend Size: $$(ls -lh $(BINARY_NAME) 2>/dev/null | awk '{print $$5}')"; \
	fi

# Quick development setup
.PHONY: setup
setup: deps-check deps
	@echo "$(GREEN)Development environment setup completed!$(NC)"
	@echo "$(BLUE)Next steps:$(NC)"
	@echo "  make dev              # Start development environment"
	@echo "  make build-prod       # Build for production"

# Watch mode for backend development
.PHONY: watch-backend
watch-backend:
	@echo "$(GREEN)Watching backend files for changes...$(NC)"
	@echo "$(YELLOW)Install 'entr' for file watching: apt-get install entr or brew install entr$(NC)"
	@if command -v entr >/dev/null 2>&1; then \
		find . -name "*.go" -not -path "./vendor/*" | entr -r sh -c 'make build-backend-dev && echo "$(GREEN)Backend rebuilt!$(NC)"'; \
	else \
		echo "$(RED)entr not found. Please install it for file watching.$(NC)"; \
	fi

# Serve production build locally for testing
.PHONY: serve
serve: build-prod
	@echo "$(GREEN)Starting production server...$(NC)"
	@echo "$(BLUE)Server will be available at: http://localhost:8080$(NC)"
	@./$(BINARY_NAME) web --port 8080

# Docker build (if Dockerfile exists)
.PHONY: docker
docker:
	@if [ -f Dockerfile ]; then \
		echo "$(GREEN)Building Docker image...$(NC)"; \
		docker build -t $(BINARY_NAME):$(VERSION) .; \
		docker build -t $(BINARY_NAME):latest .; \
		echo "$(GREEN)Docker build completed!$(NC)"; \
	else \
		echo "$(RED)Dockerfile not found$(NC)"; \
	fi

# Show disk usage of build artifacts
.PHONY: size
size:
	@echo "$(BLUE)Build Artifacts Size:$(NC)"
	@if [ -d $(DIST_DIR) ]; then echo "  Frontend: $$(du -sh $(DIST_DIR) | cut -f1)"; fi
	@if [ -f $(BINARY_NAME) ]; then echo "  Backend: $$(ls -lh $(BINARY_NAME) | awk '{print $$5}')"; fi
	@if [ -f $(BINARY_NAME)-dev ]; then echo "  Backend (dev): $$(ls -lh $(BINARY_NAME)-dev | awk '{print $$5}')"; fi
