package web

import "net/http"

// StaticHandler 定义静态文件服务接口
// 这个接口确保开发环境和生产环境的实现保持一致
type StaticHandler interface {
	// ServeHTTP 处理HTTP请求，提供静态文件服务
	ServeHTTP(w http.ResponseWriter, r *http.Request)
}

// GetStaticHandler 获取静态文件处理器
// 根据构建标签返回不同的实现：
// - 生产环境（默认）：返回嵌入式静态文件处理器
// - 开发环境（-tags=dev）：返回代理到前端开发服务器的处理器
//
// 返回值实现了 http.Handler 接口，可以直接用于 Gin 路由或标准 HTTP 服务器
// 这个函数的具体实现在 static.go（生产环境）和 static_dev.go（开发环境）中
// 通过 Go 的构建标签机制，编译时只会包含对应环境的实现

// StaticFileConfig 静态文件服务配置
type StaticFileConfig struct {
	// EnableCompression 是否启用压缩
	EnableCompression bool
	
	// EnableCaching 是否启用缓存
	EnableCaching bool
	
	// CacheMaxAge 缓存最大时间（秒）
	CacheMaxAge int
	
	// CompressionLevel 压缩级别（1-9）
	CompressionLevel int
}

// DefaultStaticConfig 默认静态文件配置
var DefaultStaticConfig = StaticFileConfig{
	EnableCompression: true,
	EnableCaching:     true,
	CacheMaxAge:       31536000, // 1年
	CompressionLevel:  6,        // 平衡压缩率和性能
}

// IsStaticFile 判断请求路径是否为静态文件
// 用于路由处理逻辑中区分静态文件请求和SPA路由请求
func IsStaticFile(path string) bool {
	// 静态文件扩展名列表
	staticExts := []string{
		".js", ".css", ".html", ".htm",
		".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico", ".webp",
		".woff", ".woff2", ".ttf", ".eot",
		".json", ".xml", ".txt",
		".map", // source map文件
	}
	
	for _, ext := range staticExts {
		if len(path) >= len(ext) && path[len(path)-len(ext):] == ext {
			return true
		}
	}
	
	// 检查是否为静态资源目录下的文件
	staticPaths := []string{
		"/static/",
		"/assets/",
		"/public/",
		"/dist/",
	}
	
	for _, prefix := range staticPaths {
		if len(path) >= len(prefix) && path[:len(prefix)] == prefix {
			return true
		}
	}
	
	return false
}

// SetCacheHeaders 设置缓存头
// 根据文件类型设置不同的缓存策略
func SetCacheHeaders(w http.ResponseWriter, path string) {
	if path == "/" || path == "/index.html" {
		// HTML文件不缓存，确保SPA路由更新能及时生效
		w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
		w.Header().Set("Pragma", "no-cache")
		w.Header().Set("Expires", "0")
	} else if IsStaticFile(path) {
		// 静态资源长期缓存
		w.Header().Set("Cache-Control", "public, max-age=31536000") // 1年
		w.Header().Set("Vary", "Accept-Encoding")
	}
}
