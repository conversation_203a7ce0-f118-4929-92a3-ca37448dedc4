package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
)

// TaskStatus 任务状态
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusCancelled TaskStatus = "cancelled"
)

// ProgressInfo 进度信息
type ProgressInfo struct {
	Current    int64
	Total      int64
	Percentage float64
	Message    string
	UpdatedAt  time.Time
}

// TaskResult 任务结果
type TaskResult struct {
	Success bool
	Data    interface{}
	Error   error
}

// AsyncTask 异步任务
type AsyncTask struct {
	ID          string
	Type        string
	Status      TaskStatus
	Progress    *ProgressInfo
	Result      *TaskResult
	CreatedAt   time.Time
	StartedAt   *time.Time
	CompletedAt *time.Time
	Context     context.Context
	Cancel      context.CancelFunc
	Metadata    map[string]interface{}
}

// TaskHandler 任务处理函数
type TaskHandler func(ctx context.Context, task *AsyncTask, progressCallback func(*ProgressInfo)) *TaskResult

// AsyncProcessor 异步处理器
type AsyncProcessor struct {
	logger        *zap.Logger
	tasks         map[string]*AsyncTask
	handlers      map[string]TaskHandler
	mutex         sync.RWMutex
	workerPool    chan struct{}
	maxWorkers    int
	taskTimeout   time.Duration
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
}

// NewAsyncProcessor 创建异步处理器
func NewAsyncProcessor(logger *zap.Logger, maxWorkers int, taskTimeout time.Duration) *AsyncProcessor {
	processor := &AsyncProcessor{
		logger:      logger,
		tasks:       make(map[string]*AsyncTask),
		handlers:    make(map[string]TaskHandler),
		workerPool:  make(chan struct{}, maxWorkers),
		maxWorkers:  maxWorkers,
		taskTimeout: taskTimeout,
		stopCleanup: make(chan struct{}),
	}

	// 启动定期清理
	processor.startCleanup()

	return processor
}

// RegisterHandler 注册任务处理器
func (ap *AsyncProcessor) RegisterHandler(taskType string, handler TaskHandler) {
	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	ap.handlers[taskType] = handler
	ap.logger.Info("注册异步任务处理器", zap.String("taskType", taskType))
}

// SubmitBackupTask 提交备份任务
func (ap *AsyncProcessor) SubmitBackupTask(opts *domain.BackupOptions) (string, error) {
	metadata := map[string]interface{}{
		"backup_path": opts.OutputPath,
		"endpoints":   opts.ETCDConfig.Servers,
	}

	return ap.SubmitTask("backup", metadata)
}

// SubmitRestoreTask 提交恢复任务
func (ap *AsyncProcessor) SubmitRestoreTask(opts *domain.RestoreOptions) (string, error) {
	metadata := map[string]interface{}{
		"snapshot_path": opts.SnapshotPath,
		"data_dir":      opts.DataDir,
		"name":          opts.Name,
	}

	return ap.SubmitTask("restore", metadata)
}

// SubmitSnapshotValidationTask 提交快照验证任务
func (ap *AsyncProcessor) SubmitSnapshotValidationTask(snapshotPath string) (string, error) {
	metadata := map[string]interface{}{
		"snapshot_path": snapshotPath,
	}

	return ap.SubmitTask("snapshot_validation", metadata)
}

// SubmitTask 提交任务
func (ap *AsyncProcessor) SubmitTask(taskType string, metadata map[string]interface{}) (string, error) {
	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	// 检查处理器是否存在
	if _, exists := ap.handlers[taskType]; !exists {
		return "", fmt.Errorf("未找到任务类型 %s 的处理器", taskType)
	}

	// 生成任务ID
	taskID := ap.generateTaskID(taskType)

	// 创建任务上下文
	ctx, cancel := context.WithTimeout(context.Background(), ap.taskTimeout)

	// 创建任务
	task := &AsyncTask{
		ID:        taskID,
		Type:      taskType,
		Status:    TaskStatusPending,
		Progress:  &ProgressInfo{},
		CreatedAt: time.Now(),
		Context:   ctx,
		Cancel:    cancel,
		Metadata:  metadata,
	}

	// 存储任务
	ap.tasks[taskID] = task

	// 异步执行任务
	go ap.executeTask(task)

	ap.logger.Info("提交异步任务",
		zap.String("taskID", taskID),
		zap.String("taskType", taskType))

	return taskID, nil
}

// executeTask 执行任务
func (ap *AsyncProcessor) executeTask(task *AsyncTask) {
	// 获取工作池槽位
	ap.workerPool <- struct{}{}
	defer func() { <-ap.workerPool }()

	// 更新任务状态
	ap.updateTaskStatus(task.ID, TaskStatusRunning)
	now := time.Now()
	task.StartedAt = &now

	ap.logger.Info("开始执行异步任务",
		zap.String("taskID", task.ID),
		zap.String("taskType", task.Type))

	// 获取处理器
	ap.mutex.RLock()
	handler, exists := ap.handlers[task.Type]
	ap.mutex.RUnlock()

	if !exists {
		ap.completeTask(task, &TaskResult{
			Success: false,
			Error:   fmt.Errorf("未找到任务处理器: %s", task.Type),
		})
		return
	}

	// 创建进度回调
	progressCallback := func(progress *ProgressInfo) {
		ap.updateTaskProgress(task.ID, progress)
	}

	// 执行任务
	result := handler(task.Context, task, progressCallback)

	// 完成任务
	ap.completeTask(task, result)
}

// updateTaskStatus 更新任务状态
func (ap *AsyncProcessor) updateTaskStatus(taskID string, status TaskStatus) {
	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	if task, exists := ap.tasks[taskID]; exists {
		task.Status = status
		ap.logger.Debug("更新任务状态",
			zap.String("taskID", taskID),
			zap.String("status", string(status)))
	}
}

// updateTaskProgress 更新任务进度
func (ap *AsyncProcessor) updateTaskProgress(taskID string, progress *ProgressInfo) {
	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	if task, exists := ap.tasks[taskID]; exists {
		progress.UpdatedAt = time.Now()
		task.Progress = progress

		ap.logger.Debug("更新任务进度",
			zap.String("taskID", taskID),
			zap.Float64("percentage", progress.Percentage),
			zap.String("message", progress.Message))
	}
}

// completeTask 完成任务
func (ap *AsyncProcessor) completeTask(task *AsyncTask, result *TaskResult) {
	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	now := time.Now()
	task.CompletedAt = &now
	task.Result = result

	if result.Success {
		task.Status = TaskStatusCompleted
	} else {
		task.Status = TaskStatusFailed
	}

	// 取消上下文
	task.Cancel()

	ap.logger.Info("任务执行完成",
		zap.String("taskID", task.ID),
		zap.String("status", string(task.Status)),
		zap.Bool("success", result.Success),
		zap.Duration("duration", now.Sub(*task.StartedAt)))
}

// GetTask 获取任务信息
func (ap *AsyncProcessor) GetTask(taskID string) (*AsyncTask, error) {
	ap.mutex.RLock()
	defer ap.mutex.RUnlock()

	task, exists := ap.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}

	return task, nil
}

// ListTasks 列出所有任务
func (ap *AsyncProcessor) ListTasks() []*AsyncTask {
	ap.mutex.RLock()
	defer ap.mutex.RUnlock()

	tasks := make([]*AsyncTask, 0, len(ap.tasks))
	for _, task := range ap.tasks {
		tasks = append(tasks, task)
	}

	return tasks
}

// CancelTask 取消任务
func (ap *AsyncProcessor) CancelTask(taskID string) error {
	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	task, exists := ap.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	if task.Status == TaskStatusCompleted || task.Status == TaskStatusFailed {
		return fmt.Errorf("任务已完成，无法取消: %s", taskID)
	}

	task.Status = TaskStatusCancelled
	task.Cancel()

	ap.logger.Info("取消任务", zap.String("taskID", taskID))
	return nil
}

// generateTaskID 生成任务ID
func (ap *AsyncProcessor) generateTaskID(taskType string) string {
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("%s_%d", taskType, timestamp)
}

// startCleanup 启动定期清理
func (ap *AsyncProcessor) startCleanup() {
	ap.cleanupTicker = time.NewTicker(10 * time.Minute)

	go func() {
		for {
			select {
			case <-ap.cleanupTicker.C:
				ap.cleanup()
			case <-ap.stopCleanup:
				ap.cleanupTicker.Stop()
				return
			}
		}
	}()
}

// cleanup 清理完成的任务
func (ap *AsyncProcessor) cleanup() {
	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	now := time.Now()
	cleanupThreshold := 1 * time.Hour // 清理1小时前完成的任务
	cleanedTasks := make([]string, 0)

	for taskID, task := range ap.tasks {
		if task.CompletedAt != nil && now.Sub(*task.CompletedAt) > cleanupThreshold {
			cleanedTasks = append(cleanedTasks, taskID)
		}
	}

	for _, taskID := range cleanedTasks {
		delete(ap.tasks, taskID)
	}

	if len(cleanedTasks) > 0 {
		ap.logger.Info("清理完成的任务",
			zap.Int("cleaned", len(cleanedTasks)),
			zap.Int("remaining", len(ap.tasks)))
	}
}

// GetStats 获取处理器统计信息
func (ap *AsyncProcessor) GetStats() map[string]interface{} {
	ap.mutex.RLock()
	defer ap.mutex.RUnlock()

	statusCount := make(map[TaskStatus]int)
	for _, task := range ap.tasks {
		statusCount[task.Status]++
	}

	return map[string]interface{}{
		"totalTasks":   len(ap.tasks),
		"maxWorkers":   ap.maxWorkers,
		"taskTimeout":  ap.taskTimeout.String(),
		"statusCount":  statusCount,
		"handlerCount": len(ap.handlers),
	}
}

// Close 关闭异步处理器
func (ap *AsyncProcessor) Close() {
	close(ap.stopCleanup)

	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	// 取消所有运行中的任务
	for _, task := range ap.tasks {
		if task.Status == TaskStatusRunning || task.Status == TaskStatusPending {
			task.Cancel()
		}
	}

	ap.tasks = make(map[string]*AsyncTask)
	ap.logger.Info("异步处理器已关闭")
}
