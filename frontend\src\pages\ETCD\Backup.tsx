import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Switch,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Progress
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FileZipOutlined,
  FileOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  useETCDBackups,
  useCreateETCDBackup,
  useAppState,
  useNotifications
} from '@/hooks';
import type { ETCDBackupItem, ETCDBackupRequest } from '@/types/api';

/**
 * ETCD备份管理页面
 *
 * 功能特性：
 * - 备份列表展示
 * - 创建新备份
 * - 备份文件下载
 * - 备份验证
 * - 备份删除
 */
const ETCDBackup: React.FC = () => {
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createForm] = Form.useForm();

  const { setPageTitle } = useAppState();
  const { showSuccess, showError } = useNotifications();

  // 获取备份列表
  const {
    data: backups = [],
    isLoading,
    refetch
  } = useETCDBackups();

  // 创建备份
  const createBackupMutation = useCreateETCDBackup({
    onSuccess: () => {
      showSuccess('备份创建成功', '备份任务已开始执行');
      setCreateModalVisible(false);
      createForm.resetFields();
      refetch();
    },
    onError: (error: any) => {
      showError('备份创建失败', error.message || '创建备份时发生错误');
    },
  });

  // 设置页面标题
  useEffect(() => {
    setPageTitle('ETCD备份 - K8s-Helper');
  }, [setPageTitle]);

  // 处理创建备份
  const handleCreateBackup = async (values: any) => {
    const request: ETCDBackupRequest = {
      output_path: values.output_path,
      compress: values.compress || false,
      description: values.description,
    };

    createBackupMutation.mutate(request);
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化时间
  const formatTime = (timeStr: string): string => {
    return new Date(timeStr).toLocaleString('zh-CN');
  };

  // 表格列定义
  const columns: ColumnsType<ETCDBackupItem> = [
    {
      title: '备份ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id: string) => (
        <Tooltip title={id}>
          <code style={{ fontSize: '12px' }}>
            {id.substring(0, 8)}...
          </code>
        </Tooltip>
      ),
    },
    {
      title: '文件路径',
      dataIndex: 'path',
      key: 'path',
      ellipsis: true,
      render: (path: string, record: ETCDBackupItem) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            {record.compressed ? <FileZipOutlined /> : <FileOutlined />}
            <span>{path}</span>
          </div>
          {record.description && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
              {record.description}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
      render: (size: number) => formatFileSize(size),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      render: (time: string) => formatTime(time),
    },
    {
      title: '状态',
      dataIndex: 'valid',
      key: 'valid',
      width: 80,
      render: (valid: boolean) => (
        <Tag
          color={valid ? 'green' : 'red'}
          icon={valid ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
        >
          {valid ? '有效' : '无效'}
        </Tag>
      ),
    },
    {
      title: '压缩',
      dataIndex: 'compressed',
      key: 'compressed',
      width: 80,
      render: (compressed: boolean) => (
        <Tag color={compressed ? 'blue' : 'default'}>
          {compressed ? '已压缩' : '未压缩'}
        </Tag>
      ),
    },
    {
      title: '校验和',
      dataIndex: 'checksum',
      key: 'checksum',
      width: 120,
      render: (checksum: string) => (
        <Tooltip title={checksum}>
          <code style={{ fontSize: '12px' }}>
            {checksum.substring(0, 8)}...
          </code>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: ETCDBackupItem) => (
        <Space size="small">
          <Tooltip title="下载备份">
            <Button
              type="text"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => handleDownloadBackup(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个备份吗？"
            description="删除后无法恢复"
            onConfirm={() => handleDeleteBackup(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除备份">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理下载备份
  const handleDownloadBackup = (backup: ETCDBackupItem) => {
    // 这里应该调用下载API
    message.info(`开始下载备份: ${backup.path}`);
  };

  // 处理删除备份
  const handleDeleteBackup = (backupId: string) => {
    // 这里应该调用删除API
    message.success('备份删除成功');
    refetch();
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <h2 style={{ margin: 0 }}>备份管理</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            管理ETCD数据库备份文件
          </p>
        </div>

        <Space>
          <Button
            icon={<ReloadOutlined />}
            loading={isLoading}
            onClick={() => refetch()}
          >
            刷新
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建备份
          </Button>
        </Space>
      </div>

      {/* 备份列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={backups}
          loading={isLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>

      {/* 创建备份模态框 */}
      <Modal
        title="创建ETCD备份"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        onOk={() => createForm.submit()}
        confirmLoading={createBackupMutation.isPending}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateBackup}
        >
          <Form.Item
            name="output_path"
            label="备份文件路径"
            rules={[
              { required: true, message: '请输入备份文件路径' },
              {
                pattern: /^\/.*\.db$/,
                message: '路径必须以/开头，以.db结尾'
              }
            ]}
          >
            <Input
              placeholder="/backup/etcd-backup-$(date +%Y%m%d-%H%M%S).db"
              addonBefore="文件路径"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="备份描述"
          >
            <Input.TextArea
              placeholder="请输入备份描述信息（可选）"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="compress"
            label="压缩选项"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="启用压缩"
              unCheckedChildren="不压缩"
            />
          </Form.Item>

          <div style={{
            padding: '12px',
            backgroundColor: '#f6f8fa',
            borderRadius: '6px',
            marginTop: '16px'
          }}>
            <h4 style={{ margin: '0 0 8px 0' }}>备份说明：</h4>
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              <li>备份过程中ETCD服务不会中断</li>
              <li>建议在业务低峰期进行备份操作</li>
              <li>启用压缩可以减少备份文件大小</li>
              <li>备份文件将保存在指定的路径中</li>
            </ul>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default ETCDBackup;
