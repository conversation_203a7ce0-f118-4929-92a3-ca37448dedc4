package cmd

import (
	"context"
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"k8s-helper/pkg/cmdhelp"
	"k8s-helper/pkg/common"
	"k8s-helper/pkg/k8s"
)

// infoCmd 代表 info 命令
var infoCmd = cmdhelp.InfoHelp.BuildCommand(runInfoCommand, nil)

var (
	// 仅显示节点信息
	nodesOnly bool
)

func init() {
	infoCmd.Flags().BoolVar(&nodesOnly, "nodes-only", common.DefaultNodesOnly, "仅显示节点信息")
}

// runInfoCommand 执行 info 命令的主要逻辑
func runInfoCommand(cmd *cobra.Command, args []string) error {
	// 创建 Kubernetes 客户端
	clientset, err := k8s.NewClient(kubeconfig)
	if err != nil {
		return fmt.Errorf("创建 Kubernetes 客户端失败: %w", err)
	}

	ctx := context.Background()

	// 如果不是仅显示节点，则显示服务端版本信息
	if !nodesOnly {
		if err := displayServerVersion(ctx, clientset); err != nil {
			return fmt.Errorf("获取服务端版本信息失败: %w", err)
		}
		fmt.Println()
	}

	// 显示节点信息
	if err := displayNodesInfo(ctx, clientset); err != nil {
		return fmt.Errorf("获取节点信息失败: %w", err)
	}

	return nil
}

// displayServerVersion 显示 Kubernetes 服务端版本信息
func displayServerVersion(ctx context.Context, clientset *kubernetes.Clientset) error {
	version, err := clientset.Discovery().ServerVersion()
	if err != nil {
		return err
	}

	fmt.Println("=== Kubernetes 服务端信息 ===")
	fmt.Printf("版本: %s\n", version.String())
	fmt.Printf("Git 版本: %s\n", version.GitVersion)
	fmt.Printf("构建日期: %s\n", version.BuildDate)
	fmt.Printf("Go 版本: %s\n", version.GoVersion)
	fmt.Printf("平台: %s\n", version.Platform)

	if verbose {
		common.PrintInfo(fmt.Sprintf("服务端详细信息获取完成，版本: %s", version.GitVersion))
	}

	return nil
}

// displayNodesInfo 显示集群节点信息
func displayNodesInfo(ctx context.Context, clientset *kubernetes.Clientset) error {
	nodes, err := clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}

	fmt.Println("=== 集群节点信息 ===")
	if len(nodes.Items) == 0 {
		fmt.Println("未找到任何节点")
		return nil
	}

	// 打印表头
	fmt.Printf("%-20s %-10s %-15s %-15s %-10s\n",
		"节点名称", "状态", "角色", "内部IP", "版本")
	fmt.Println(strings.Repeat("-", 80))

	// 遍历并显示每个节点的信息
	for _, node := range nodes.Items {
		// 获取节点状态
		status := "Unknown"
		for _, condition := range node.Status.Conditions {
			if condition.Type == "Ready" {
				if condition.Status == "True" {
					status = "Ready"
				} else {
					status = "NotReady"
				}
				break
			}
		}

		// 获取节点角色
		roles := getNodeRoles(node.Labels)
		if roles == "" {
			roles = "<none>"
		}

		// 获取内部 IP
		internalIP := getNodeInternalIP(node.Status.Addresses)
		if internalIP == "" {
			internalIP = "<unknown>"
		}

		// 获取 kubelet 版本
		version := node.Status.NodeInfo.KubeletVersion

		fmt.Printf("%-20s %-10s %-15s %-15s %-10s\n",
			node.Name, status, roles, internalIP, version)
	}

	fmt.Printf("\n总计: %d 个节点\n", len(nodes.Items))

	if verbose {
		common.PrintSuccess(fmt.Sprintf("成功获取 %d 个节点的信息", len(nodes.Items)))
	}

	return nil
}

// getNodeRoles 从节点标签中提取角色信息
func getNodeRoles(labels map[string]string) string {
	var roles []string

	// 检查常见的角色标签
	roleLabels := []string{
		"node-role.kubernetes.io/master",
		"node-role.kubernetes.io/control-plane",
		"node-role.kubernetes.io/worker",
		"node-role.kubernetes.io/etcd",
	}

	for _, roleLabel := range roleLabels {
		if _, exists := labels[roleLabel]; exists {
			// 提取角色名称（标签的最后一部分）
			parts := strings.Split(roleLabel, "/")
			if len(parts) > 1 {
				roles = append(roles, parts[len(parts)-1])
			}
		}
	}

	return strings.Join(roles, ",")
}

// getNodeInternalIP 从节点地址列表中获取内部 IP
func getNodeInternalIP(addresses []v1.NodeAddress) string {
	for _, addr := range addresses {
		if addr.Type == v1.NodeInternalIP {
			return addr.Address
		}
	}
	return ""
}
