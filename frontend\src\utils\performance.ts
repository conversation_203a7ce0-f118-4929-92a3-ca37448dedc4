// 性能指标类型
export interface PerformanceMetrics {
  // 页面加载性能
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  
  // 资源性能
  resourceCount: number;
  resourceSize: number;
  
  // 内存使用
  memoryUsage?: {
    used: number;
    total: number;
    limit: number;
  };
  
  // 网络信息
  connection?: {
    effectiveType: string;
    downlink: number;
    rtt: number;
  };
}

// 性能监控类
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];

  private constructor() {
    this.initializeObservers();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // 初始化性能观察器
  private initializeObservers(): void {
    // 观察导航性能
    if ('PerformanceObserver' in window) {
      try {
        // LCP观察器
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          this.metrics.largestContentfulPaint = lastEntry.startTime;
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);

        // FID观察器
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);

        // CLS观察器
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          this.metrics.cumulativeLayoutShift = clsValue;
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (error) {
        console.warn('Performance observers not supported:', error);
      }
    }
  }

  // 收集基础性能指标
  public collectBasicMetrics(): void {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        this.metrics.loadTime = navigation.loadEventEnd - navigation.navigationStart;
        this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.navigationStart;
      }

      // FCP
      const paintEntries = performance.getEntriesByType('paint');
      const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        this.metrics.firstContentfulPaint = fcpEntry.startTime;
      }

      // 资源性能
      const resourceEntries = performance.getEntriesByType('resource');
      this.metrics.resourceCount = resourceEntries.length;
      this.metrics.resourceSize = resourceEntries.reduce((total, entry: any) => {
        return total + (entry.transferSize || 0);
      }, 0);
    }

    // 内存使用情况
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.memoryUsage = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
      };
    }

    // 网络信息
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.metrics.connection = {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
      };
    }
  }

  // 获取性能指标
  public getMetrics(): Partial<PerformanceMetrics> {
    this.collectBasicMetrics();
    return { ...this.metrics };
  }

  // 记录自定义性能标记
  public mark(name: string): void {
    if ('performance' in window && performance.mark) {
      performance.mark(name);
    }
  }

  // 测量两个标记之间的时间
  public measure(name: string, startMark: string, endMark?: string): number {
    if ('performance' in window && performance.measure) {
      performance.measure(name, startMark, endMark);
      const measures = performance.getEntriesByName(name, 'measure');
      return measures.length > 0 ? measures[measures.length - 1].duration : 0;
    }
    return 0;
  }

  // 清理观察器
  public cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// 性能监控实例
export const performanceMonitor = PerformanceMonitor.getInstance();

// 页面性能监控Hook
import { useEffect } from 'react';

export const usePagePerformance = (pageName: string) => {
  useEffect(() => {
    performanceMonitor.mark(`${pageName}-start`);

    return () => {
      performanceMonitor.mark(`${pageName}-end`);
      const duration = performanceMonitor.measure(
        `${pageName}-duration`,
        `${pageName}-start`,
        `${pageName}-end`
      );

      console.log(`Page ${pageName} render time:`, duration);
    };
  }, [pageName]);
};

// 错误监控
export class ErrorMonitor {
  private static instance: ErrorMonitor;
  private errors: Array<{
    message: string;
    stack?: string;
    timestamp: number;
    url: string;
    userAgent: string;
  }> = [];

  private constructor() {
    this.initializeErrorHandlers();
  }

  public static getInstance(): ErrorMonitor {
    if (!ErrorMonitor.instance) {
      ErrorMonitor.instance = new ErrorMonitor();
    }
    return ErrorMonitor.instance;
  }

  private initializeErrorHandlers(): void {
    // 全局错误处理
    window.addEventListener('error', (event) => {
      this.logError({
        message: event.message,
        stack: event.error?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      });
    });

    // Promise错误处理
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      });
    });
  }

  private logError(error: any): void {
    this.errors.push(error);
    console.error('Error logged:', error);
    
    // 这里可以添加错误上报逻辑
    // this.reportError(error);
  }

  public getErrors(): any[] {
    return [...this.errors];
  }

  public clearErrors(): void {
    this.errors = [];
  }
}

// 错误监控实例
export const errorMonitor = ErrorMonitor.getInstance();

// 工具函数
export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatDuration = (ms: number): string => {
  if (ms < 1000) return `${Math.round(ms)}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
};

// 性能评分
export const getPerformanceScore = (metrics: Partial<PerformanceMetrics>): number => {
  let score = 100;
  
  // LCP评分 (理想 < 2.5s)
  if (metrics.largestContentfulPaint) {
    if (metrics.largestContentfulPaint > 4000) score -= 30;
    else if (metrics.largestContentfulPaint > 2500) score -= 15;
  }
  
  // FID评分 (理想 < 100ms)
  if (metrics.firstInputDelay) {
    if (metrics.firstInputDelay > 300) score -= 25;
    else if (metrics.firstInputDelay > 100) score -= 10;
  }
  
  // CLS评分 (理想 < 0.1)
  if (metrics.cumulativeLayoutShift) {
    if (metrics.cumulativeLayoutShift > 0.25) score -= 25;
    else if (metrics.cumulativeLayoutShift > 0.1) score -= 10;
  }
  
  // 加载时间评分 (理想 < 3s)
  if (metrics.loadTime) {
    if (metrics.loadTime > 5000) score -= 20;
    else if (metrics.loadTime > 3000) score -= 10;
  }
  
  return Math.max(0, score);
};
