{{define "sidebar"}}
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <i class="icon">🏠</i>
            <span class="sidebar-title">K8s-Helper</span>
        </div>
        <button class="sidebar-toggle" id="sidebar-toggle" onclick="toggleSidebar()">
            <i class="icon">☰</i>
        </button>
    </div>
    
    <nav class="sidebar-nav">
        <ul class="nav-list">
            <li class="nav-item">
                <a href="#" class="nav-link active" data-module="dashboard" onclick="switchModule('dashboard')">
                    <i class="nav-icon">📊</i>
                    <span class="nav-text">Dashboard</span>
                    <span class="nav-label">概览</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-module="cluster-info" onclick="switchModule('cluster-info')">
                    <i class="nav-icon">🔍</i>
                    <span class="nav-text">Cluster Info</span>
                    <span class="nav-label">集群信息</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-module="etcd" onclick="switchModule('etcd')">
                    <i class="nav-icon">💾</i>
                    <span class="nav-text">ETCD</span>
                    <span class="nav-label">数据管理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-module="logs" onclick="switchModule('logs')">
                    <i class="nav-icon">📋</i>
                    <span class="nav-text">Pod Logs</span>
                    <span class="nav-label">日志查看</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-module="port-forward" onclick="switchModule('port-forward')">
                    <i class="nav-icon">🔗</i>
                    <span class="nav-text">Port Forward</span>
                    <span class="nav-label">端口转发</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-module="cleanup" onclick="switchModule('cleanup')">
                    <i class="nav-icon">🧹</i>
                    <span class="nav-text">Cleanup</span>
                    <span class="nav-label">资源清理</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-module="monitoring" onclick="switchModule('monitoring')">
                    <i class="nav-icon">📈</i>
                    <span class="nav-text">Monitoring</span>
                    <span class="nav-label">监控告警</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-module="config" onclick="switchModule('config')">
                    <i class="nav-icon">⚙️</i>
                    <span class="nav-text">Configuration</span>
                    <span class="nav-label">配置管理</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <div class="sidebar-footer">
        <div class="connection-status">
            <div class="status-item">
                <span id="ws-status" class="status unknown">WebSocket</span>
                <span class="status-indicator" id="ws-indicator"></span>
            </div>
            <div class="status-item">
                <span id="health-status" class="status unknown">系统状态</span>
                <span class="status-indicator" id="health-indicator"></span>
            </div>
        </div>
    </div>
</div>

<!-- 移动端遮罩层 -->
<div class="sidebar-overlay" id="sidebar-overlay" onclick="closeSidebar()"></div>
{{end}}
