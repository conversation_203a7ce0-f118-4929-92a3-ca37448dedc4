import React from 'react';
import { Badge, Tooltip } from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  CloseCircleOutlined, 
  QuestionCircleOutlined 
} from '@ant-design/icons';

// 状态类型
export type StatusType = 'healthy' | 'warning' | 'error' | 'unknown';

// 状态指示器大小
export type StatusSize = 'small' | 'default' | 'large';

// 状态指示器属性
export interface StatusIndicatorProps {
  status: StatusType;
  size?: StatusSize;
  showText?: boolean;
  text?: string;
  tooltip?: string;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 状态指示器组件
 * 
 * 功能特性：
 * - 显示不同状态的视觉指示
 * - 支持多种大小
 * - 支持文本显示
 * - 支持工具提示
 * - 支持自定义样式
 */
export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'default',
  showText = false,
  text,
  tooltip,
  className,
  style,
}) => {
  // 状态配置
  const statusConfig = {
    healthy: {
      color: '#52c41a',
      icon: <CheckCircleOutlined />,
      text: '健康',
      badgeStatus: 'success' as const,
    },
    warning: {
      color: '#faad14',
      icon: <ExclamationCircleOutlined />,
      text: '警告',
      badgeStatus: 'warning' as const,
    },
    error: {
      color: '#ff4d4f',
      icon: <CloseCircleOutlined />,
      text: '错误',
      badgeStatus: 'error' as const,
    },
    unknown: {
      color: '#d9d9d9',
      icon: <QuestionCircleOutlined />,
      text: '未知',
      badgeStatus: 'default' as const,
    },
  };

  // 大小配置
  const sizeConfig = {
    small: {
      fontSize: '12px',
      dotSize: 6,
    },
    default: {
      fontSize: '14px',
      dotSize: 8,
    },
    large: {
      fontSize: '16px',
      dotSize: 10,
    },
  };

  const config = statusConfig[status];
  const sizeStyle = sizeConfig[size];
  const displayText = text || config.text;

  // 渲染带图标的指示器
  const renderWithIcon = () => {
    const iconElement = React.cloneElement(config.icon, {
      style: {
        color: config.color,
        fontSize: sizeStyle.fontSize,
      },
    });

    return (
      <span
        className={className}
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: '4px',
          ...style,
        }}
      >
        {iconElement}
        {showText && (
          <span style={{ 
            color: config.color, 
            fontSize: sizeStyle.fontSize,
            fontWeight: 500,
          }}>
            {displayText}
          </span>
        )}
      </span>
    );
  };

  // 渲染徽章样式的指示器
  const renderWithBadge = () => {
    return (
      <Badge
        status={config.badgeStatus}
        text={showText ? displayText : undefined}
        className={className}
        style={style}
      />
    );
  };

  // 选择渲染方式
  const indicator = showText ? renderWithBadge() : renderWithIcon();

  // 如果有工具提示，包装在Tooltip中
  if (tooltip) {
    return (
      <Tooltip title={tooltip}>
        {indicator}
      </Tooltip>
    );
  }

  return indicator;
};

// 状态指示器列表组件
export interface StatusIndicatorListProps {
  items: Array<{
    label: string;
    status: StatusType;
    count?: number;
    tooltip?: string;
  }>;
  size?: StatusSize;
  layout?: 'horizontal' | 'vertical';
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 状态指示器列表组件
 * 
 * 用于显示多个状态指示器的列表
 */
export const StatusIndicatorList: React.FC<StatusIndicatorListProps> = ({
  items,
  size = 'default',
  layout = 'horizontal',
  className,
  style,
}) => {
  const isHorizontal = layout === 'horizontal';

  return (
    <div
      className={className}
      style={{
        display: 'flex',
        flexDirection: isHorizontal ? 'row' : 'column',
        gap: isHorizontal ? '16px' : '8px',
        alignItems: isHorizontal ? 'center' : 'flex-start',
        ...style,
      }}
    >
      {items.map((item, index) => (
        <div
          key={index}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <StatusIndicator
            status={item.status}
            size={size}
            tooltip={item.tooltip}
          />
          <span style={{ fontSize: size === 'small' ? '12px' : '14px' }}>
            {item.label}
            {item.count !== undefined && (
              <span style={{ 
                marginLeft: '4px', 
                color: '#8c8c8c',
                fontWeight: 'normal',
              }}>
                ({item.count})
              </span>
            )}
          </span>
        </div>
      ))}
    </div>
  );
};

// 状态统计组件
export interface StatusStatsProps {
  stats: Record<StatusType, number>;
  total?: number;
  size?: StatusSize;
  showPercentage?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 状态统计组件
 * 
 * 显示各种状态的统计数据
 */
export const StatusStats: React.FC<StatusStatsProps> = ({
  stats,
  total,
  size = 'default',
  showPercentage = false,
  className,
  style,
}) => {
  const totalCount = total || Object.values(stats).reduce((sum, count) => sum + count, 0);

  const items = (Object.keys(stats) as StatusType[])
    .filter(status => stats[status] > 0)
    .map(status => {
      const count = stats[status];
      const percentage = totalCount > 0 ? Math.round((count / totalCount) * 100) : 0;
      
      return {
        label: showPercentage ? `${percentage}%` : count.toString(),
        status,
        tooltip: `${status}: ${count}${showPercentage ? ` (${percentage}%)` : ''}`,
      };
    });

  return (
    <StatusIndicatorList
      items={items}
      size={size}
      layout="horizontal"
      className={className}
      style={style}
    />
  );
};

export default StatusIndicator;
