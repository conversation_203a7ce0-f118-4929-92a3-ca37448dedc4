package cmdhelp

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"
)

// CommandHelp 命令帮助信息结构
type CommandHelp struct {
	Use             string            // 命令使用格式
	Short           string            // 简短描述
	Long            string            // 详细描述
	Examples        []string          // 使用示例
	ValidArgs       []string          // 有效参数列表
	ArgDescriptions map[string]string // 参数描述
}

// ErrorConfig 错误配置
type ErrorConfig struct {
	RequiredArgs    int                  // 必需参数数量
	MinArgs         int                  // 最少参数数量
	MaxArgs         int                  // 最多参数数量
	ErrorMessage    string               // 基础错误信息
	ShowExamples    bool                 // 是否显示示例
	ShowValidArgs   bool                 // 是否显示有效参数
	CustomValidator func([]string) error // 自定义验证器
}

// BuildCommand 构建带有统一错误处理的命令
func (h *CommandHelp) BuildCommand(runE func(*cobra.Command, []string) error, errorConfig *ErrorConfig) *cobra.Command {
	cmd := &cobra.Command{
		Use:   h.Use,
		Short: h.Short,
		Long:  h.buildLongDescription(),
		Args:  h.buildArgsValidator(errorConfig),
		RunE:  runE,
	}
	return cmd
}

// buildLongDescription 构建详细描述
func (h *CommandHelp) buildLongDescription() string {
	var parts []string

	if h.Long != "" {
		parts = append(parts, h.Long)
	}

	if len(h.ValidArgs) > 0 && len(h.ArgDescriptions) > 0 {
		parts = append(parts, h.buildValidArgsSection())
	}

	if len(h.Examples) > 0 {
		parts = append(parts, h.buildExamplesSection())
	}

	return strings.Join(parts, "\n\n")
}

// buildValidArgsSection 构建有效参数部分
func (h *CommandHelp) buildValidArgsSection() string {
	if len(h.ValidArgs) == 0 {
		return ""
	}

	var lines []string
	lines = append(lines, "支持的参数：")

	for _, arg := range h.ValidArgs {
		if desc, exists := h.ArgDescriptions[arg]; exists {
			lines = append(lines, fmt.Sprintf("  %-12s - %s", arg, desc))
		} else {
			lines = append(lines, fmt.Sprintf("  %s", arg))
		}
	}

	return strings.Join(lines, "\n")
}

// buildExamplesSection 构建示例部分
func (h *CommandHelp) buildExamplesSection() string {
	if len(h.Examples) == 0 {
		return ""
	}

	var lines []string
	lines = append(lines, "使用示例：")

	for _, example := range h.Examples {
		lines = append(lines, fmt.Sprintf("  %s", example))
	}

	return strings.Join(lines, "\n")
}

// buildArgsValidator 构建参数验证器
func (h *CommandHelp) buildArgsValidator(config *ErrorConfig) cobra.PositionalArgs {
	if config == nil {
		return cobra.NoArgs
	}

	return func(cmd *cobra.Command, args []string) error {
		// 自定义验证器优先
		if config.CustomValidator != nil {
			return config.CustomValidator(args)
		}

		// 精确参数数量验证
		if config.RequiredArgs > 0 {
			if len(args) != config.RequiredArgs {
				return h.buildArgError(config, args)
			}
			return nil
		}

		// 最少参数验证
		if config.MinArgs > 0 && len(args) < config.MinArgs {
			return h.buildArgError(config, args)
		}

		// 最多参数验证
		if config.MaxArgs > 0 && len(args) > config.MaxArgs {
			return h.buildArgError(config, args)
		}

		return nil
	}
}

// buildArgError 构建参数错误信息
func (h *CommandHelp) buildArgError(config *ErrorConfig, args []string) error {
	var parts []string

	// 基础错误信息
	if config.ErrorMessage != "" {
		parts = append(parts, config.ErrorMessage)
	} else {
		parts = append(parts, h.getDefaultErrorMessage(config, len(args)))
	}

	// 显示有效参数
	if config.ShowValidArgs && len(h.ValidArgs) > 0 {
		parts = append(parts, "", h.buildValidArgsSection())
	}

	// 显示关键示例（最多3个）
	if config.ShowExamples && len(h.Examples) > 0 {
		parts = append(parts, "", h.buildKeyExamplesSection())
	}

	// 帮助提示
	parts = append(parts, "", fmt.Sprintf("使用 '%s -h' 查看完整帮助信息", h.getCommandName()))

	return fmt.Errorf(strings.Join(parts, "\n"))
}

// buildKeyExamplesSection 构建关键示例部分（简化版）
func (h *CommandHelp) buildKeyExamplesSection() string {
	if len(h.Examples) == 0 {
		return ""
	}

	var lines []string
	lines = append(lines, "使用示例：")

	// 最多显示3个示例
	maxExamples := 3
	if len(h.Examples) < maxExamples {
		maxExamples = len(h.Examples)
	}

	for i := 0; i < maxExamples; i++ {
		lines = append(lines, fmt.Sprintf("  %s", h.Examples[i]))
	}

	return strings.Join(lines, "\n")
}

// getDefaultErrorMessage 获取默认错误信息
func (h *CommandHelp) getDefaultErrorMessage(config *ErrorConfig, actualArgs int) string {
	if config.RequiredArgs > 0 {
		if actualArgs == 0 {
			return "缺少必需的参数"
		}
		return fmt.Sprintf("参数数量不正确，需要 %d 个参数，实际提供了 %d 个", config.RequiredArgs, actualArgs)
	}

	if config.MinArgs > 0 {
		return fmt.Sprintf("参数数量不足，至少需要 %d 个参数", config.MinArgs)
	}

	return "参数错误"
}

// getCommandName 获取命令名称（用于帮助提示）
func (h *CommandHelp) getCommandName() string {
	parts := strings.Fields(h.Use)
	if len(parts) > 0 {
		return parts[0]
	}
	return "command"
}

// NewCommandHelp 创建新的命令帮助
func NewCommandHelp(use, short, long string) *CommandHelp {
	return &CommandHelp{
		Use:             use,
		Short:           short,
		Long:            long,
		Examples:        make([]string, 0),
		ValidArgs:       make([]string, 0),
		ArgDescriptions: make(map[string]string),
	}
}

// AddExample 添加使用示例
func (h *CommandHelp) AddExample(example string) *CommandHelp {
	h.Examples = append(h.Examples, example)
	return h
}

// AddExamples 批量添加使用示例
func (h *CommandHelp) AddExamples(examples ...string) *CommandHelp {
	h.Examples = append(h.Examples, examples...)
	return h
}

// AddValidArg 添加有效参数
func (h *CommandHelp) AddValidArg(arg, description string) *CommandHelp {
	h.ValidArgs = append(h.ValidArgs, arg)
	h.ArgDescriptions[arg] = description
	return h
}

// AddValidArgs 批量添加有效参数
func (h *CommandHelp) AddValidArgs(args map[string]string) *CommandHelp {
	for arg, desc := range args {
		h.AddValidArg(arg, desc)
	}
	return h
}

// ValidateCleanupType 验证cleanup命令的清理类型
func ValidateCleanupType(args []string) error {
	if len(args) != 1 {
		return CleanupHelp.buildArgError(CleanupErrorConfig, args)
	}

	validTypes := []string{"pods", "jobs", "failed-pods", "pending-pods", "all"}
	cleanupType := args[0]

	for _, validType := range validTypes {
		if cleanupType == validType {
			return nil
		}
	}

	return fmt.Errorf("无效的清理类型 '%s'\n\n%s\n\n使用 'k8s-helper cleanup -h' 查看完整帮助信息",
		cleanupType, CleanupHelp.buildValidArgsSection())
}

// SimpleArgsValidator 创建简单的参数验证器
func SimpleArgsValidator(requiredArgs int, errorMsg string) func([]string) error {
	return func(args []string) error {
		if len(args) != requiredArgs {
			if requiredArgs == 1 {
				return fmt.Errorf("%s\n\n使用 '-h' 查看完整帮助信息", errorMsg)
			}
			return fmt.Errorf("%s\n\n使用 '-h' 查看完整帮助信息", errorMsg)
		}
		return nil
	}
}

// MinArgsValidator 创建最少参数验证器
func MinArgsValidator(minArgs int, errorMsg string) func([]string) error {
	return func(args []string) error {
		if len(args) < minArgs {
			return fmt.Errorf("%s\n\n使用 '-h' 查看完整帮助信息", errorMsg)
		}
		return nil
	}
}
