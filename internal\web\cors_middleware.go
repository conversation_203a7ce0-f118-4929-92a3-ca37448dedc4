package web

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CORSConfig CORS配置
type CORSConfig struct {
	AllowOrigins     []string
	AllowMethods     []string
	AllowHeaders     []string
	ExposeHeaders    []string
	AllowCredentials bool
	MaxAge           int
}

// DefaultCORSConfig 默认CORS配置
func DefaultCORSConfig() *CORSConfig {
	return &CORSConfig{
		AllowOrigins: []string{"*"},
		AllowMethods: []string{
			http.MethodGet,
			http.MethodPost,
			http.MethodPut,
			http.MethodPatch,
			http.MethodDelete,
			http.MethodHead,
			http.MethodOptions,
		},
		AllowHeaders: []string{
			"Origin",
			"Content-Length",
			"Content-Type",
			"Authorization",
			"Accept",
			"X-Requested-With",
			"X-Request-ID",
		},
		ExposeHeaders: []string{
			"Content-Length",
			"X-Request-ID",
		},
		AllowCredentials: false,
		MaxAge:           86400, // 24小时
	}
}

// DevelopmentCORSConfig 开发环境CORS配置
func DevelopmentCORSConfig() *CORSConfig {
	config := DefaultCORSConfig()
	config.AllowOrigins = []string{
		"http://localhost:3000",  // React开发服务器
		"http://localhost:5173",  // Vite开发服务器默认端口
		"http://localhost:5174",  // Vite备用端口
		"http://localhost:8080",  // 后端服务器
		"http://localhost:8081",  // 备用端口
		"http://127.0.0.1:3000",
		"http://127.0.0.1:5173",
		"http://127.0.0.1:5174",
		"http://127.0.0.1:8080",
		"http://127.0.0.1:8081",
	}
	config.AllowCredentials = true
	// 添加更多开发环境需要的头部
	config.AllowHeaders = append(config.AllowHeaders,
		"X-CSRF-Token",
		"X-Forwarded-For",
		"X-Real-IP",
		"Cache-Control",
	)
	return config
}

// ProductionCORSConfig 生产环境CORS配置
func ProductionCORSConfig(allowedOrigins []string) *CORSConfig {
	config := DefaultCORSConfig()
	if len(allowedOrigins) > 0 {
		config.AllowOrigins = allowedOrigins
	} else {
		// 生产环境默认不允许所有来源
		config.AllowOrigins = []string{}
	}
	config.AllowCredentials = true
	return config
}

// CORSMiddleware 创建CORS中间件
func CORSMiddleware(config *CORSConfig, logger *zap.Logger) gin.HandlerFunc {
	if config == nil {
		config = DefaultCORSConfig()
	}

	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// 检查是否允许该来源
		if len(config.AllowOrigins) > 0 && !isOriginAllowed(origin, config.AllowOrigins) {
			if logger != nil {
				logger.Warn("CORS: Origin not allowed",
					zap.String("origin", origin),
					zap.String("method", c.Request.Method),
					zap.String("path", c.Request.URL.Path))
			}
			c.AbortWithStatus(http.StatusForbidden)
			return
		}

		// 设置CORS头
		setCORSHeaders(c, config, origin)

		// 处理预检请求
		if c.Request.Method == http.MethodOptions {
			if logger != nil {
				logger.Debug("CORS: Handling preflight request",
					zap.String("origin", origin),
					zap.String("path", c.Request.URL.Path))
			}
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// isOriginAllowed 检查来源是否被允许
func isOriginAllowed(origin string, allowedOrigins []string) bool {
	if origin == "" {
		return true // 允许同源请求
	}

	for _, allowed := range allowedOrigins {
		if allowed == "*" {
			return true
		}
		if allowed == origin {
			return true
		}
		// 支持通配符匹配
		if strings.HasSuffix(allowed, "*") {
			prefix := strings.TrimSuffix(allowed, "*")
			if strings.HasPrefix(origin, prefix) {
				return true
			}
		}
	}
	return false
}

// setCORSHeaders 设置CORS响应头
func setCORSHeaders(c *gin.Context, config *CORSConfig, origin string) {
	// Access-Control-Allow-Origin
	if len(config.AllowOrigins) == 1 && config.AllowOrigins[0] == "*" {
		c.Header("Access-Control-Allow-Origin", "*")
	} else if origin != "" && isOriginAllowed(origin, config.AllowOrigins) {
		c.Header("Access-Control-Allow-Origin", origin)
	}

	// Access-Control-Allow-Methods
	if len(config.AllowMethods) > 0 {
		c.Header("Access-Control-Allow-Methods", strings.Join(config.AllowMethods, ", "))
	}

	// Access-Control-Allow-Headers
	if len(config.AllowHeaders) > 0 {
		c.Header("Access-Control-Allow-Headers", strings.Join(config.AllowHeaders, ", "))
	}

	// Access-Control-Expose-Headers
	if len(config.ExposeHeaders) > 0 {
		c.Header("Access-Control-Expose-Headers", strings.Join(config.ExposeHeaders, ", "))
	}

	// Access-Control-Allow-Credentials
	if config.AllowCredentials {
		c.Header("Access-Control-Allow-Credentials", "true")
	}

	// Access-Control-Max-Age
	if config.MaxAge > 0 {
		c.Header("Access-Control-Max-Age", fmt.Sprintf("%d", config.MaxAge))
	}
}

// SimpleCORSMiddleware 简单的CORS中间件（用于快速启用CORS）
func SimpleCORSMiddleware() gin.HandlerFunc {
	return CORSMiddleware(DefaultCORSConfig(), nil)
}

// DevelopmentCORSMiddleware 开发环境CORS中间件
func DevelopmentCORSMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return CORSMiddleware(DevelopmentCORSConfig(), logger)
}

// ProductionCORSMiddleware 生产环境CORS中间件
func ProductionCORSMiddleware(allowedOrigins []string, logger *zap.Logger) gin.HandlerFunc {
	return CORSMiddleware(ProductionCORSConfig(allowedOrigins), logger)
}
