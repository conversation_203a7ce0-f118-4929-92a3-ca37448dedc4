import React, { Suspense } from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { Spin } from 'antd';
import { AppLayout } from '@/components';
import { ROUTES } from '@/utils/constants';
import {
  DashboardSkeleton,
  TableSkeleton,
  DetailSkeleton,
  ChartSkeleton
} from '@/components/SkeletonLoader';

// 创建带有错误边界和骨架屏的懒加载组件
const createLazyComponent = (
  importFn: () => Promise<{ default: React.ComponentType<any> }>,
  fallback?: React.ComponentType,
  preload?: boolean
) => {
  const LazyComponent = React.lazy(importFn);

  // 预加载组件
  if (preload) {
    importFn();
  }

  return (props: any) => (
    <Suspense
      fallback={
        fallback ? React.createElement(fallback) : (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '400px'
          }}>
            <Spin size="large" tip="页面加载中..." />
          </div>
        )
      }
    >
      <LazyComponent {...props} />
    </Suspense>
  );
};

// 页面懒加载组件 - 使用优化的加载策略
const Dashboard = createLazyComponent(
  () => import('@/pages/Dashboard'),
  DashboardSkeleton,
  true // 预加载Dashboard
);
const NotFound = createLazyComponent(() => import('@/pages/NotFound'));

// ETCD管理相关页面
const ETCDManagement = createLazyComponent(
  () => import('@/pages/ETCD'),
  TableSkeleton
);
const ETCDOverview = createLazyComponent(
  () => import('@/pages/ETCD/Overview'),
  ChartSkeleton
);
const ETCDBackup = createLazyComponent(
  () => import('@/pages/ETCD/Backup'),
  TableSkeleton
);
const ETCDRestore = createLazyComponent(
  () => import('@/pages/ETCD/Restore'),
  TableSkeleton
);
const ETCDCronJobs = createLazyComponent(
  () => import('@/pages/ETCD/CronJobs'),
  TableSkeleton
);

// 其他功能页面
const ClusterInfo = createLazyComponent(
  () => import('@/pages/ClusterInfo'),
  DetailSkeleton
);
const LogsManagement = createLazyComponent(
  () => import('@/pages/Logs'),
  TableSkeleton
);
const PortForward = createLazyComponent(
  () => import('@/pages/PortForward'),
  TableSkeleton
);
const ResourceCleanup = createLazyComponent(
  () => import('@/pages/Cleanup'),
  TableSkeleton
);
const Monitoring = createLazyComponent(
  () => import('@/pages/Monitoring'),
  ChartSkeleton
);
const ConfigManagement = createLazyComponent(
  () => import('@/pages/Config'),
  DetailSkeleton
);

// 加载中组件
const PageLoading: React.FC = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '200px' 
  }}>
    <Spin size="large" tip="页面加载中..." />
  </div>
);

// 路由配置
export const router = createBrowserRouter([
  {
    path: '/',
    element: <AppLayout />,
    errorElement: <NotFound />,
    children: [
      // 默认重定向到仪表板
      {
        index: true,
        element: <Navigate to={ROUTES.DASHBOARD} replace />,
      },
      
      // 仪表板
      {
        path: 'dashboard',
        element: (
          <Suspense fallback={<PageLoading />}>
            <Dashboard />
          </Suspense>
        ),
      },
      
      // ETCD管理模块
      {
        path: 'etcd',
        element: (
          <Suspense fallback={<PageLoading />}>
            <ETCDManagement />
          </Suspense>
        ),
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<PageLoading />}>
                <ETCDOverview />
              </Suspense>
            ),
          },
          {
            path: 'backup',
            element: (
              <Suspense fallback={<PageLoading />}>
                <ETCDBackup />
              </Suspense>
            ),
          },
          {
            path: 'restore',
            element: (
              <Suspense fallback={<PageLoading />}>
                <ETCDRestore />
              </Suspense>
            ),
          },
          {
            path: 'cronjobs',
            element: (
              <Suspense fallback={<PageLoading />}>
                <ETCDCronJobs />
              </Suspense>
            ),
          },
        ],
      },
      
      // 集群信息
      {
        path: 'cluster',
        element: (
          <Suspense fallback={<PageLoading />}>
            <ClusterInfo />
          </Suspense>
        ),
      },
      
      // 日志管理
      {
        path: 'logs',
        element: (
          <Suspense fallback={<PageLoading />}>
            <LogsManagement />
          </Suspense>
        ),
      },
      
      // 端口转发
      {
        path: 'port-forward',
        element: (
          <Suspense fallback={<PageLoading />}>
            <PortForward />
          </Suspense>
        ),
      },
      
      // 资源清理
      {
        path: 'cleanup',
        element: (
          <Suspense fallback={<PageLoading />}>
            <ResourceCleanup />
          </Suspense>
        ),
      },
      
      // 监控告警
      {
        path: 'monitoring',
        element: (
          <Suspense fallback={<PageLoading />}>
            <Monitoring />
          </Suspense>
        ),
      },
      
      // 配置管理
      {
        path: 'config',
        element: (
          <Suspense fallback={<PageLoading />}>
            <ConfigManagement />
          </Suspense>
        ),
      },
    ],
  },
  
  // 404页面
  {
    path: '*',
    element: (
      <Suspense fallback={<PageLoading />}>
        <NotFound />
      </Suspense>
    ),
  },
]);

export default router;
