import { useCallback } from 'react';
import { useAppStore, selectTheme, selectIsDarkMode, selectSidebarCollapsed, selectGlobalLoading, selectGlobalError, selectNotifications, selectSettings } from '@/stores';
import type { Theme, AppNotification, AppSettings } from '@/stores';

/**
 * 应用状态管理Hook
 * 
 * 提供应用全局状态的访问和操作方法
 */
export const useAppState = () => {
  // 状态选择器
  const theme = useAppStore(selectTheme);
  const isDarkMode = useAppStore(selectIsDarkMode);
  const sidebarCollapsed = useAppStore(selectSidebarCollapsed);
  const sidebarWidth = useAppStore(state => state.sidebarWidth);
  const pageLoading = useAppStore(state => state.pageLoading);
  const pageTitle = useAppStore(state => state.pageTitle);
  const globalLoading = useAppStore(selectGlobalLoading);
  const loadingMessage = useAppStore(state => state.loadingMessage);
  const globalError = useAppStore(selectGlobalError);
  const notifications = useAppStore(selectNotifications);
  const settings = useAppStore(selectSettings);

  // 操作方法
  const setTheme = useAppStore(state => state.setTheme);
  const toggleTheme = useAppStore(state => state.toggleTheme);
  const setSidebarCollapsed = useAppStore(state => state.setSidebarCollapsed);
  const toggleSidebar = useAppStore(state => state.toggleSidebar);
  const setPageTitle = useAppStore(state => state.setPageTitle);
  const setGlobalLoading = useAppStore(state => state.setGlobalLoading);
  const setGlobalError = useAppStore(state => state.setGlobalError);
  const clearGlobalError = useAppStore(state => state.clearGlobalError);
  const addNotification = useAppStore(state => state.addNotification);
  const removeNotification = useAppStore(state => state.removeNotification);
  const updateSettings = useAppStore(state => state.updateSettings);

  return {
    // 状态
    theme,
    isDarkMode,
    sidebarCollapsed,
    sidebarWidth,
    pageLoading,
    pageTitle,
    globalLoading,
    loadingMessage,
    globalError,
    notifications,
    settings,

    // 操作
    setTheme,
    toggleTheme,
    setSidebarCollapsed,
    toggleSidebar,
    setPageTitle,
    setGlobalLoading,
    setGlobalError,
    clearGlobalError,
    addNotification,
    removeNotification,
    updateSettings,
  };
};

/**
 * 主题管理Hook
 */
export const useTheme = () => {
  const theme = useAppStore(selectTheme);
  const isDarkMode = useAppStore(selectIsDarkMode);
  const setTheme = useAppStore(state => state.setTheme);
  const toggleTheme = useAppStore(state => state.toggleTheme);

  const applyTheme = useCallback((newTheme: Theme) => {
    setTheme(newTheme);
    
    // 应用到document
    const root = document.documentElement;
    if (newTheme === 'dark' || (newTheme === 'auto' && isDarkMode)) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [setTheme, isDarkMode]);

  return {
    theme,
    isDarkMode,
    setTheme: applyTheme,
    toggleTheme,
  };
};

/**
 * 侧边栏管理Hook
 */
export const useSidebar = () => {
  const collapsed = useAppStore(selectSidebarCollapsed);
  const setCollapsed = useAppStore(state => state.setSidebarCollapsed);
  const toggle = useAppStore(state => state.toggleSidebar);
  const width = useAppStore(state => state.sidebarWidth);
  const setWidth = useAppStore(state => state.setSidebarWidth);

  return {
    collapsed,
    width,
    setCollapsed,
    setWidth,
    toggle,
  };
};

/**
 * 全局加载状态Hook
 */
export const useGlobalLoading = () => {
  const loading = useAppStore(selectGlobalLoading);
  const message = useAppStore(state => state.loadingMessage);
  const setLoading = useAppStore(state => state.setGlobalLoading);

  const showLoading = useCallback((message?: string) => {
    setLoading(true, message);
  }, [setLoading]);

  const hideLoading = useCallback(() => {
    setLoading(false);
  }, [setLoading]);

  return {
    loading,
    message,
    showLoading,
    hideLoading,
  };
};

/**
 * 全局错误管理Hook
 */
export const useGlobalError = () => {
  const error = useAppStore(selectGlobalError);
  const setError = useAppStore(state => state.setGlobalError);
  const clearError = useAppStore(state => state.clearGlobalError);

  const showError = useCallback((errorMessage: string) => {
    setError(errorMessage);
  }, [setError]);

  return {
    error,
    showError,
    clearError,
  };
};

/**
 * 通知管理Hook
 */
export const useNotifications = () => {
  const notifications = useAppStore(selectNotifications);
  const addNotification = useAppStore(state => state.addNotification);
  const removeNotification = useAppStore(state => state.removeNotification);
  const markAsRead = useAppStore(state => state.markNotificationAsRead);
  const clearAll = useAppStore(state => state.clearAllNotifications);

  // 便捷方法
  const showSuccess = useCallback((title: string, message: string, duration = 3000) => {
    addNotification({
      type: 'success',
      title,
      message,
      duration,
    });
  }, [addNotification]);

  const showError = useCallback((title: string, message: string, duration = 5000) => {
    addNotification({
      type: 'error',
      title,
      message,
      duration,
    });
  }, [addNotification]);

  const showWarning = useCallback((title: string, message: string, duration = 4000) => {
    addNotification({
      type: 'warning',
      title,
      message,
      duration,
    });
  }, [addNotification]);

  const showInfo = useCallback((title: string, message: string, duration = 3000) => {
    addNotification({
      type: 'info',
      title,
      message,
      duration,
    });
  }, [addNotification]);

  // 获取未读通知数量
  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    unreadCount,
    addNotification,
    removeNotification,
    markAsRead,
    clearAll,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
};

/**
 * 应用设置Hook
 */
export const useAppSettings = () => {
  const settings = useAppStore(selectSettings);
  const updateSettings = useAppStore(state => state.updateSettings);
  const resetSettings = useAppStore(state => state.resetSettings);

  const updateSetting = useCallback(<K extends keyof AppSettings>(
    key: K,
    value: AppSettings[K]
  ) => {
    updateSettings({ [key]: value });
  }, [updateSettings]);

  return {
    settings,
    updateSettings,
    updateSetting,
    resetSettings,
  };
};

export default useAppState;
