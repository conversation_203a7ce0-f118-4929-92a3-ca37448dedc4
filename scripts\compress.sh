#!/bin/bash

# 静态文件压缩脚本
# 用途：对前端构建产物进行gzip和brotli预压缩，优化生产环境性能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[COMPRESS]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[COMPRESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[COMPRESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[COMPRESS]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_warning "$1 命令未找到，跳过 $2 压缩"
        return 1
    fi
    return 0
}

# 获取文件大小（字节）
get_file_size() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        stat -f%z "$1" 2>/dev/null || echo 0
    else
        # Linux
        stat -c%s "$1" 2>/dev/null || echo 0
    fi
}

# 格式化文件大小
format_size() {
    local size=$1
    if [ $size -gt 1048576 ]; then
        echo "$(echo "scale=2; $size/1048576" | bc)MB"
    elif [ $size -gt 1024 ]; then
        echo "$(echo "scale=2; $size/1024" | bc)KB"
    else
        echo "${size}B"
    fi
}

# 压缩单个文件
compress_file() {
    local file="$1"
    local original_size=$(get_file_size "$file")
    
    if [ $original_size -eq 0 ]; then
        log_warning "跳过空文件: $file"
        return
    fi
    
    # 跳过已经很小的文件（小于1KB）
    if [ $original_size -lt 1024 ]; then
        log_info "跳过小文件: $file ($(format_size $original_size))"
        return
    fi
    
    local compressed_count=0
    
    # Gzip压缩
    if check_command "gzip" "gzip"; then
        if gzip -9 -k -f "$file" 2>/dev/null; then
            local gzip_size=$(get_file_size "${file}.gz")
            local gzip_ratio=$(echo "scale=1; (1-$gzip_size/$original_size)*100" | bc 2>/dev/null || echo "0")
            log_info "Gzip: $file -> $(format_size $gzip_size) (压缩率: ${gzip_ratio}%)"
            compressed_count=$((compressed_count + 1))
        else
            log_warning "Gzip压缩失败: $file"
        fi
    fi
    
    # Brotli压缩
    if check_command "brotli" "brotli"; then
        if brotli -9 -k -f "$file" 2>/dev/null; then
            local brotli_size=$(get_file_size "${file}.br")
            local brotli_ratio=$(echo "scale=1; (1-$brotli_size/$original_size)*100" | bc 2>/dev/null || echo "0")
            log_info "Brotli: $file -> $(format_size $brotli_size) (压缩率: ${brotli_ratio}%)"
            compressed_count=$((compressed_count + 1))
        else
            log_warning "Brotli压缩失败: $file"
        fi
    fi
    
    if [ $compressed_count -eq 0 ]; then
        log_warning "无法压缩文件: $file (没有可用的压缩工具)"
    fi
}

# 主函数
main() {
    local dist_dir="${1:-frontend/dist}"
    
    # 获取项目根目录
    local project_root="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    local full_dist_path="$project_root/$dist_dir"
    
    log_info "开始压缩静态文件..."
    log_info "目标目录: $full_dist_path"
    
    # 检查目录是否存在
    if [ ! -d "$full_dist_path" ]; then
        log_error "目录不存在: $full_dist_path"
        exit 1
    fi
    
    # 检查压缩工具
    local has_gzip=false
    local has_brotli=false
    
    if check_command "gzip" "gzip"; then
        has_gzip=true
    fi
    
    if check_command "brotli" "brotli"; then
        has_brotli=true
    fi
    
    if [ "$has_gzip" = false ] && [ "$has_brotli" = false ]; then
        log_error "没有找到压缩工具 (gzip 或 brotli)"
        log_error "请安装压缩工具："
        log_error "  Ubuntu/Debian: sudo apt-get install gzip brotli"
        log_error "  CentOS/RHEL: sudo yum install gzip brotli"
        log_error "  macOS: brew install brotli"
        exit 1
    fi
    
    # 统计信息
    local total_files=0
    local compressed_files=0
    local original_total_size=0
    local compressed_total_size=0
    
    # 需要压缩的文件扩展名
    local extensions=("js" "css" "html" "json" "txt" "xml" "svg" "map")
    
    log_info "支持的文件类型: ${extensions[*]}"
    
    # 遍历并压缩文件
    for ext in "${extensions[@]}"; do
        while IFS= read -r -d '' file; do
            if [ -f "$file" ]; then
                total_files=$((total_files + 1))
                original_size=$(get_file_size "$file")
                original_total_size=$((original_total_size + original_size))
                
                compress_file "$file"
                
                # 检查是否生成了压缩文件
                if [ -f "${file}.gz" ] || [ -f "${file}.br" ]; then
                    compressed_files=$((compressed_files + 1))
                    
                    # 计算最小压缩文件大小
                    local min_compressed_size=$original_size
                    if [ -f "${file}.gz" ]; then
                        local gz_size=$(get_file_size "${file}.gz")
                        if [ $gz_size -lt $min_compressed_size ]; then
                            min_compressed_size=$gz_size
                        fi
                    fi
                    if [ -f "${file}.br" ]; then
                        local br_size=$(get_file_size "${file}.br")
                        if [ $br_size -lt $min_compressed_size ]; then
                            min_compressed_size=$br_size
                        fi
                    fi
                    
                    compressed_total_size=$((compressed_total_size + min_compressed_size))
                else
                    compressed_total_size=$((compressed_total_size + original_size))
                fi
            fi
        done < <(find "$full_dist_path" -name "*.${ext}" -type f -print0)
    done
    
    # 显示统计信息
    log_success "压缩完成！"
    log_info "统计信息："
    log_info "  处理文件: $total_files"
    log_info "  压缩文件: $compressed_files"
    log_info "  原始大小: $(format_size $original_total_size)"
    log_info "  压缩大小: $(format_size $compressed_total_size)"
    
    if [ $original_total_size -gt 0 ]; then
        local total_ratio=$(echo "scale=1; (1-$compressed_total_size/$original_total_size)*100" | bc 2>/dev/null || echo "0")
        log_info "  总压缩率: ${total_ratio}%"
        local saved_size=$((original_total_size - compressed_total_size))
        log_info "  节省空间: $(format_size $saved_size)"
    fi
    
    # 列出生成的压缩文件
    local gz_count=$(find "$full_dist_path" -name "*.gz" -type f | wc -l)
    local br_count=$(find "$full_dist_path" -name "*.br" -type f | wc -l)
    
    log_info "生成的压缩文件："
    log_info "  Gzip文件: $gz_count"
    log_info "  Brotli文件: $br_count"
}

# 显示帮助信息
show_help() {
    echo "静态文件压缩脚本"
    echo ""
    echo "用法: $0 [目录]"
    echo ""
    echo "参数:"
    echo "  目录    要压缩的目录路径 (默认: frontend/dist)"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 压缩 frontend/dist 目录"
    echo "  $0 build/static       # 压缩 build/static 目录"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
