import React, { useRef, useEffect } from 'react';
import { Card, Empty, Spin } from 'antd';
import * as echarts from 'echarts';

// 图表数据点
export interface ChartDataPoint {
  timestamp: number;
  value: number;
  label?: string;
}

// 资源图表属性
export interface ResourceChartProps {
  title: string;
  data: ChartDataPoint[];
  loading?: boolean;
  height?: number;
  type?: 'line' | 'area' | 'bar';
  color?: string;
  unit?: string;
  max?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 资源使用图表组件
 * 
 * 功能特性：
 * - 支持线图、面积图、柱状图
 * - 实时数据更新
 * - 响应式设计
 * - 自定义样式
 * - 数据格式化
 */
export const ResourceChart: React.FC<ResourceChartProps> = ({
  title,
  data,
  loading = false,
  height = 300,
  type = 'line',
  color = '#1890ff',
  unit = '%',
  max = 100,
  showGrid = true,
  showLegend = false,
  className,
  style,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current) return;

    chartInstance.current = echarts.init(chartRef.current);
    
    return () => {
      chartInstance.current?.dispose();
    };
  }, []);

  // 更新图表数据
  useEffect(() => {
    if (!chartInstance.current || loading || !data.length) return;

    const option: echarts.EChartsOption = {
      title: {
        text: title,
        left: 'left',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal',
          color: '#666',
        },
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const point = params[0];
          const time = new Date(point.data[0]).toLocaleTimeString();
          return `${time}<br/>${point.seriesName}: ${point.data[1]}${unit}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
        show: showGrid,
      },
      xAxis: {
        type: 'time',
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#e8e8e8',
          },
        },
        axisLabel: {
          color: '#666',
          formatter: (value: number) => {
            return new Date(value).toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
            });
          },
        },
        splitLine: {
          show: showGrid,
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      yAxis: {
        type: 'value',
        max: max,
        min: 0,
        axisLine: {
          lineStyle: {
            color: '#e8e8e8',
          },
        },
        axisLabel: {
          color: '#666',
          formatter: `{value}${unit}`,
        },
        splitLine: {
          show: showGrid,
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      series: [
        {
          name: title,
          type: type === 'area' ? 'line' : type,
          data: data.map(point => [point.timestamp, point.value]),
          smooth: true,
          symbol: 'none',
          lineStyle: {
            color: color,
            width: 2,
          },
          areaStyle: type === 'area' ? {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: color + '40', // 透明度
                },
                {
                  offset: 1,
                  color: color + '10',
                },
              ],
            },
          } : undefined,
          itemStyle: {
            color: color,
          },
        },
      ],
      legend: {
        show: showLegend,
        bottom: 0,
      },
    };

    chartInstance.current.setOption(option, true);
  }, [data, loading, title, type, color, unit, max, showGrid, showLegend]);

  // 响应式处理
  useEffect(() => {
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height 
        }}>
          <Spin size="large" />
        </div>
      );
    }

    if (!data.length) {
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height 
        }}>
          <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    }

    return (
      <div 
        ref={chartRef} 
        style={{ width: '100%', height }}
      />
    );
  };

  return (
    <div className={className} style={style}>
      {renderContent()}
    </div>
  );
};

// 多指标图表组件
export interface MultiMetricChartProps {
  title: string;
  metrics: Array<{
    name: string;
    data: ChartDataPoint[];
    color: string;
    unit?: string;
  }>;
  loading?: boolean;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
}

export const MultiMetricChart: React.FC<MultiMetricChartProps> = ({
  title,
  metrics,
  loading = false,
  height = 300,
  className,
  style,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current) return;

    chartInstance.current = echarts.init(chartRef.current);
    
    return () => {
      chartInstance.current?.dispose();
    };
  }, []);

  // 更新图表数据
  useEffect(() => {
    if (!chartInstance.current || loading || !metrics.length) return;

    const option: echarts.EChartsOption = {
      title: {
        text: title,
        left: 'left',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal',
          color: '#666',
        },
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const time = new Date(params[0].data[0]).toLocaleTimeString();
          let content = `${time}<br/>`;
          params.forEach((param: any) => {
            const metric = metrics.find(m => m.name === param.seriesName);
            const unit = metric?.unit || '%';
            content += `${param.seriesName}: ${param.data[1]}${unit}<br/>`;
          });
          return content;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'time',
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#e8e8e8',
          },
        },
        axisLabel: {
          color: '#666',
          formatter: (value: number) => {
            return new Date(value).toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
            });
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#e8e8e8',
          },
        },
        axisLabel: {
          color: '#666',
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      series: metrics.map(metric => ({
        name: metric.name,
        type: 'line',
        data: metric.data.map(point => [point.timestamp, point.value]),
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: metric.color,
          width: 2,
        },
        itemStyle: {
          color: metric.color,
        },
      })),
      legend: {
        show: true,
        bottom: 0,
        data: metrics.map(m => m.name),
      },
    };

    chartInstance.current.setOption(option, true);
  }, [metrics, loading, title]);

  // 响应式处理
  useEffect(() => {
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height 
        }}>
          <Spin size="large" />
        </div>
      );
    }

    if (!metrics.length || !metrics.some(m => m.data.length)) {
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height 
        }}>
          <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    }

    return (
      <div 
        ref={chartRef} 
        style={{ width: '100%', height }}
      />
    );
  };

  return (
    <Card title={title} className={className} style={style}>
      {renderContent()}
    </Card>
  );
};

export default ResourceChart;
