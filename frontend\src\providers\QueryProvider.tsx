import React from 'react';
import { QueryClient, QueryClientProvider, DefaultOptions } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useAppStore } from '@/stores';
import type { ErrorResponse } from '@/types';

// 默认查询选项
const defaultQueryOptions: DefaultOptions = {
  queries: {
    // 数据被认为是新鲜的时间（5分钟）
    staleTime: 5 * 60 * 1000,
    // 数据在缓存中保留的时间（10分钟）
    gcTime: 10 * 60 * 1000,
    // 智能重试策略
    retry: (failureCount, error) => {
      // 对于某些错误不重试
      if (error && typeof error === 'object' && 'code' in error) {
        const errorCode = (error as ErrorResponse).code;
        const noRetryErrors = ['UNAUTHORIZED', 'FORBIDDEN', 'NOT_FOUND'];
        if (noRetryErrors.includes(errorCode)) {
          return false;
        }
      }
      // 最多重试3次
      return failureCount < 3;
    },
    // 重试延迟（指数退避）
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // 窗口重新获得焦点时重新获取数据
    refetchOnWindowFocus: false,
    // 网络重新连接时重新获取数据
    refetchOnReconnect: true,
    // 组件挂载时重新获取数据
    refetchOnMount: true,
    // 全局错误处理
    onError: (error) => {
      console.error('Query error:', error);

      // 添加全局错误通知
      const { addNotification } = useAppStore.getState();
      if (error && typeof error === 'object' && 'message' in error) {
        addNotification({
          type: 'error',
          title: '请求失败',
          message: (error as ErrorResponse).message || '未知错误',
          duration: 5000,
        });
      }
    },
  },
  mutations: {
    // 变更重试策略
    retry: (failureCount, error) => {
      // 变更操作通常不重试，除非是网络错误
      if (error && typeof error === 'object' && 'code' in error) {
        const errorCode = (error as ErrorResponse).code;
        if (errorCode === 'NETWORK_ERROR') {
          return failureCount < 2;
        }
      }
      return false;
    },
    // 变更重试延迟
    retryDelay: 1000,
    // 全局变更错误处理
    onError: (error) => {
      console.error('Mutation error:', error);

      const { addNotification } = useAppStore.getState();
      if (error && typeof error === 'object' && 'message' in error) {
        addNotification({
          type: 'error',
          title: '操作失败',
          message: (error as ErrorResponse).message || '未知错误',
          duration: 5000,
        });
      }
    },
  },
};

// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: defaultQueryOptions,
});

interface QueryProviderProps {
  children: React.ReactNode;
}

export const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* 开发环境下显示 React Query DevTools */}
      {import.meta.env.DEV && (
        <ReactQueryDevtools
          initialIsOpen={false}
          position="bottom-right"
          buttonPosition="bottom-right"
        />
      )}
    </QueryClientProvider>
  );
};

// 导出 queryClient 实例，供其他地方使用
export { queryClient };

// 查询键工厂函数
export const queryKeys = {
  // 集群相关
  cluster: {
    all: ['cluster'] as const,
    info: () => [...queryKeys.cluster.all, 'info'] as const,
    health: () => [...queryKeys.cluster.all, 'health'] as const,
    nodes: () => [...queryKeys.cluster.all, 'nodes'] as const,
    pods: () => [...queryKeys.cluster.all, 'pods'] as const,
    services: () => [...queryKeys.cluster.all, 'services'] as const,
    metrics: () => [...queryKeys.cluster.all, 'metrics'] as const,
  },
  
  // ETCD 相关
  etcd: {
    all: ['etcd'] as const,
    status: () => [...queryKeys.etcd.all, 'status'] as const,
    backups: () => [...queryKeys.etcd.all, 'backups'] as const,
    backup: (id: string) => [...queryKeys.etcd.backups(), id] as const,
    cronjobs: () => [...queryKeys.etcd.all, 'cronjobs'] as const,
  },

  // 系统相关
  system: {
    all: ['system'] as const,
    info: () => [...queryKeys.system.all, 'info'] as const,
    metrics: () => [...queryKeys.system.all, 'metrics'] as const,
    config: () => [...queryKeys.system.all, 'config'] as const,
  },

  // 监控相关
  monitoring: {
    all: ['monitoring'] as const,
    metrics: (timeRange?: string) => [...queryKeys.monitoring.all, 'metrics', timeRange] as const,
    alerts: () => [...queryKeys.monitoring.all, 'alerts'] as const,
    rules: () => [...queryKeys.monitoring.all, 'rules'] as const,
  },

  // Pod 相关
  pods: {
    all: ['pods'] as const,
    list: (namespace?: string) => 
      namespace 
        ? [...queryKeys.pods.all, 'list', namespace] as const
        : [...queryKeys.pods.all, 'list'] as const,
    detail: (namespace: string, name: string) => 
      [...queryKeys.pods.all, 'detail', namespace, name] as const,
    logs: (namespace: string, pod: string, container?: string) =>
      [...queryKeys.pods.all, 'logs', namespace, pod, container] as const,
  },
  
  // 配置相关
  config: {
    all: ['config'] as const,
    sections: () => [...queryKeys.config.all, 'sections'] as const,
    section: (section: string) => [...queryKeys.config.sections(), section] as const,
    history: () => [...queryKeys.config.all, 'history'] as const,
  },
  
  // 端口转发相关
  portForward: {
    all: ['portForward'] as const,
    list: () => [...queryKeys.portForward.all, 'list'] as const,
    detail: (id: string) => [...queryKeys.portForward.all, 'detail', id] as const,
  },

  // 资源清理相关
  cleanup: {
    all: ['cleanup'] as const,
    scan: () => [...queryKeys.cleanup.all, 'scan'] as const,
    rules: () => [...queryKeys.cleanup.all, 'rules'] as const,
    tasks: () => [...queryKeys.cleanup.all, 'tasks'] as const,
  },
} as const;

// 查询选项工厂
export const queryOptions = {
  // 实时数据（短缓存）
  realtime: {
    staleTime: 10 * 1000, // 10秒
    gcTime: 30 * 1000, // 30秒
    refetchInterval: 10 * 1000, // 10秒自动刷新
  },

  // 快速数据（中等缓存）
  fast: {
    staleTime: 30 * 1000, // 30秒
    gcTime: 2 * 60 * 1000, // 2分钟
    refetchInterval: 30 * 1000, // 30秒自动刷新
  },

  // 静态数据（长缓存）
  static: {
    staleTime: 10 * 60 * 1000, // 10分钟
    gcTime: 30 * 60 * 1000, // 30分钟
    refetchInterval: false, // 不自动刷新
  },

  // 一次性数据（不缓存）
  once: {
    staleTime: 0,
    gcTime: 0,
    refetchInterval: false,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  },
};

export default QueryProvider;
