{{define "header"}}
<div class="header">
    <div class="container">
        <h1>{{.AppName | default "K8s-Helper"}}</h1>
        <p class="subtitle">{{.Subtitle | default "Kubernetes 集群管理助手"}}</p>
        
        {{if .ShowConnectionStatus}}
        <div class="connection-status">
            <span id="ws-status" class="status unknown">WebSocket: 未连接</span>
            <span id="health-status" class="status unknown">系统: 检查中</span>
        </div>
        {{end}}
    </div>
</div>
{{end}}
