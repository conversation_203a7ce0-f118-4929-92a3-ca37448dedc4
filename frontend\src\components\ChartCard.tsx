import React from 'react';
import { Card, Empty, Spin, Button, Dropdown, Space } from 'antd';
import { 
  MoreOutlined, 
  ReloadOutlined, 
  FullscreenOutlined,
  DownloadOutlined 
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

// 图表卡片属性
export interface ChartCardProps {
  title: string;
  loading?: boolean;
  error?: string;
  children?: React.ReactNode;
  extra?: React.ReactNode;
  actions?: ChartAction[];
  height?: number;
  className?: string;
  style?: React.CSSProperties;
  onRefresh?: () => void;
  onFullscreen?: () => void;
  onExport?: (format: 'png' | 'svg' | 'pdf') => void;
}

// 图表操作
export interface ChartAction {
  key: string;
  label: string;
  icon?: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
}

/**
 * 图表卡片组件
 * 
 * 功能特性：
 * - 统一的图表容器样式
 * - 加载和错误状态处理
 * - 内置常用操作（刷新、全屏、导出）
 * - 支持自定义操作
 * - 响应式高度设置
 */
export const ChartCard: React.FC<ChartCardProps> = ({
  title,
  loading = false,
  error,
  children,
  extra,
  actions = [],
  height = 300,
  className,
  style,
  onRefresh,
  onFullscreen,
  onExport,
}) => {
  // 构建默认操作菜单
  const buildDefaultActions = (): MenuProps['items'] => {
    const items: MenuProps['items'] = [];

    if (onRefresh) {
      items.push({
        key: 'refresh',
        label: '刷新',
        icon: <ReloadOutlined />,
        onClick: onRefresh,
      });
    }

    if (onFullscreen) {
      items.push({
        key: 'fullscreen',
        label: '全屏',
        icon: <FullscreenOutlined />,
        onClick: onFullscreen,
      });
    }

    if (onExport) {
      items.push({
        key: 'export',
        label: '导出',
        icon: <DownloadOutlined />,
        children: [
          {
            key: 'export-png',
            label: 'PNG图片',
            onClick: () => onExport('png'),
          },
          {
            key: 'export-svg',
            label: 'SVG矢量图',
            onClick: () => onExport('svg'),
          },
          {
            key: 'export-pdf',
            label: 'PDF文档',
            onClick: () => onExport('pdf'),
          },
        ],
      });
    }

    // 添加自定义操作
    actions.forEach(action => {
      items.push({
        key: action.key,
        label: action.label,
        icon: action.icon,
        onClick: action.onClick,
        disabled: action.disabled,
      });
    });

    return items;
  };

  // 渲染操作菜单
  const renderActions = () => {
    const menuItems = buildDefaultActions();
    
    if (menuItems.length === 0) {
      return null;
    }

    return (
      <Dropdown
        menu={{ items: menuItems }}
        trigger={['click']}
        placement="bottomRight"
      >
        <Button
          type="text"
          icon={<MoreOutlined />}
          size="small"
          style={{ color: '#8c8c8c' }}
        />
      </Dropdown>
    );
  };

  // 渲染卡片标题
  const renderTitle = () => {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <span>{title}</span>
        <Space>
          {extra}
          {renderActions()}
        </Space>
      </div>
    );
  };

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: height,
          }}
        >
          <Spin size="large" />
        </div>
      );
    }

    if (error) {
      return (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: height,
          }}
        >
          <Empty
            description={error}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            {onRefresh && (
              <Button type="primary" icon={<ReloadOutlined />} onClick={onRefresh}>
                重试
              </Button>
            )}
          </Empty>
        </div>
      );
    }

    if (!children) {
      return (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: height,
          }}
        >
          <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      );
    }

    return (
      <div style={{ height: height, position: 'relative' }}>
        {children}
      </div>
    );
  };

  return (
    <Card
      title={renderTitle()}
      className={className}
      style={{
        ...style,
      }}
      bodyStyle={{
        padding: '16px',
      }}
    >
      {renderContent()}
    </Card>
  );
};

// 图表网格组件
export interface ChartGridProps {
  charts: Array<ChartCardProps & { span?: number }>;
  gutter?: [number, number];
  loading?: boolean;
}

/**
 * 图表网格组件
 * 
 * 用于批量显示图表卡片，支持响应式布局
 */
export const ChartGrid: React.FC<ChartGridProps> = ({
  charts,
  gutter = [16, 16],
  loading = false,
}) => {
  return (
    <div style={{ display: 'grid', gap: `${gutter[1]}px ${gutter[0]}px` }}>
      {charts.map((chart, index) => {
        const { span, ...chartProps } = chart;
        return (
          <div
            key={index}
            style={{
              gridColumn: span ? `span ${span}` : 'span 1',
            }}
          >
            <ChartCard {...chartProps} loading={loading} />
          </div>
        );
      })}
    </div>
  );
};

export default ChartCard;
