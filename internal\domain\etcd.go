package domain

import (
	"context"
	"time"
)

// ETCDConfig ETCD连接配置
type ETCDConfig struct {
	Servers  string `yaml:"servers"`
	CaFile   string `yaml:"ca_file"`
	CertFile string `yaml:"cert_file"`
	KeyFile  string `yaml:"key_file"`
}

// BackupOptions 备份选项
type BackupOptions struct {
	OutputPath     string
	UseSDK         bool
	FallbackToTool bool
	Timeout        time.Duration
	ETCDConfig     *ETCDConfig

	// ETCD工具下载配置
	DownloadBaseURL string
	DownloadVersion string
	DownloadOS      string
}

// RestoreOptions 恢复选项
type RestoreOptions struct {
	SnapshotPath             string
	DataDir                  string
	Name                     string
	InitialCluster           string
	InitialAdvertisePeerURLs string
	UseSDK                   bool
	FallbackToTool           bool
	Timeout                  time.Duration
	SkipHashCheck            bool
	MarkCompacted            bool
}

// VerifyOptions 验证选项
type VerifyOptions struct {
	SnapshotPath   string
	UseSDK         bool
	FallbackToTool bool
	Timeout        time.Duration
	DetailedVerify bool
}

// CronJobOptions CronJob创建选项
type CronJobOptions struct {
	Name               string
	Namespace          string
	Schedule           string
	TimeZone           string
	LocalTime          bool
	Image              string
	BackupPath         string
	BackupRetain       int
	BackupPVCName      string
	ETCDCertSecretName string
	ETCDConfig         *ETCDConfig
}

// BackupResult 备份结果
type BackupResult struct {
	BackupID  string        `json:"backup_id"`
	Status    string        `json:"status"`
	Message   string        `json:"message"`
	CreatedAt time.Time     `json:"created_at"`
	Size      int64         `json:"size"`
	Path      string        `json:"path"`
	FilePath  string        `json:"file_path"` // 兼容字段
	Duration  time.Duration `json:"duration"`
	Version   string        `json:"version"`
	Timestamp time.Time     `json:"timestamp"`
	Method    string        `json:"method"`
}

// RestoreResult 恢复结果
type RestoreResult struct {
	RestoreID    string        `json:"restore_id"`
	Status       string        `json:"status"`
	Message      string        `json:"message"`
	StartedAt    time.Time     `json:"started_at"`
	CompletedAt  *time.Time    `json:"completed_at,omitempty"`
	DataDir      string        `json:"data_dir"` // 兼容字段
	Duration     time.Duration `json:"duration"`
	SnapshotSize int64         `json:"snapshot_size"`
	Timestamp    time.Time     `json:"timestamp"`
	Method       string        `json:"method"`
	Success      bool          `json:"success"`
}

// VerifyResult 验证结果
type VerifyResult struct {
	BackupID     string                 `json:"backup_id"`
	IsValid      bool                   `json:"is_valid"`
	Valid        bool                   `json:"valid"` // 兼容字段
	Details      map[string]interface{} `json:"details"`
	Duration     time.Duration          `json:"duration"`
	ErrorMessage string                 `json:"error_message"`
	Message      string                 `json:"message"`
	Timestamp    time.Time              `json:"timestamp"`
	VerifiedAt   time.Time              `json:"verified_at"`
	Method       string                 `json:"method"`
	Checksum     string                 `json:"checksum"`
	Size         int64                  `json:"size"`
	Compression  string                 `json:"compression"`
}

// SnapshotInfo 快照信息
type SnapshotInfo struct {
	Path          string
	Size          int64
	ModTime       time.Time
	IsValid       bool
	VerifyMessage string
}

// ETCDService ETCD服务接口
type ETCDService interface {
	// Backup 执行备份操作
	Backup(ctx context.Context, opts *BackupOptions) (*BackupResult, error)

	// Restore 执行恢复操作
	Restore(ctx context.Context, opts *RestoreOptions) (*RestoreResult, error)

	// Verify 验证快照文件
	Verify(ctx context.Context, opts *VerifyOptions) (*VerifyResult, error)

	// CreateCronJob 创建备份CronJob
	CreateCronJob(ctx context.Context, opts *CronJobOptions) error

	// ListCronJobs 列出CronJob
	ListCronJobs(ctx context.Context, namespace string) ([]CronJobInfo, error)

	// DeleteCronJob 删除CronJob
	DeleteCronJob(ctx context.Context, namespace, name string) error

	// SuspendCronJob 暂停CronJob
	SuspendCronJob(ctx context.Context, namespace, name string) error

	// ResumeCronJob 恢复CronJob
	ResumeCronJob(ctx context.Context, namespace, name string) error
}

// CronJobInfo CronJob信息
type CronJobInfo struct {
	Name             string
	Namespace        string
	Schedule         string
	OriginalSchedule string
	TimeZone         string
	Suspend          bool
	LastRun          *time.Time
	NextRun          *time.Time
}

// ToolManager 工具管理接口
type ToolManager interface {
	// EnsureTools 确保工具可用（使用默认配置）
	EnsureTools(ctx context.Context) error

	// EnsureToolsWithConfig 确保工具可用（使用自定义配置）
	EnsureToolsWithConfig(ctx context.Context, baseURL, version, os string) error

	// GetBackupRestoreTool 获取备份恢复工具路径
	GetBackupRestoreTool() (string, error)

	// GetVerifyTool 获取验证工具路径
	GetVerifyTool() (string, error)

	// DownloadTools 下载工具
	DownloadTools(ctx context.Context, baseURL, version, os string) error
}

// ConfigParser 配置解析接口
type ConfigParser interface {
	// ParseAPIServerConfig 解析API Server配置
	ParseAPIServerConfig(manifestPath string) (*ETCDConfig, error)

	// ValidateConfig 验证配置
	ValidateConfig(config *ETCDConfig) error
}

// TimeZoneConverter 时区转换接口
type TimeZoneConverter interface {
	// ConvertCronScheduleToUTC 转换Cron表达式到UTC
	ConvertCronScheduleToUTC(schedule, timezone string) (*ConversionResult, error)

	// SupportsTimeZone 检查是否支持时区
	SupportsTimeZone(version string) bool
}

// ConversionResult 转换结果
type ConversionResult struct {
	ConvertedSchedule string
	ConversionApplied bool
	OriginalSchedule  string
	OriginalTimezone  string
	ConvertedTimezone string
}

// Config 应用配置
type Config struct {
	Global     GlobalConfig     `yaml:"global"`
	Logging    LoggingConfig    `yaml:"logging"`
	ETCD       ETCDConfig       `yaml:"etcd"`
	Cleanup    CleanupConfig    `yaml:"cleanup"`
	Monitoring MonitoringConfig `yaml:"monitoring"`
	API        APIConfig        `yaml:"api"`
}

// GlobalConfig 全局配置
type GlobalConfig struct {
	Namespace  string `yaml:"namespace"`
	Kubeconfig string `yaml:"kubeconfig"`
	Verbose    bool   `yaml:"verbose"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	Color  bool   `yaml:"color"`
}

// CleanupConfig 清理配置
type CleanupConfig struct {
	DefaultOlderThan  string `yaml:"default_older_than"`
	DefaultDryRun     bool   `yaml:"default_dry_run"`
	ConcurrentWorkers int    `yaml:"concurrent_workers"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled             bool          `yaml:"enabled"`
	Port                int           `yaml:"port"`
	Host                string        `yaml:"host"`
	MetricsInterval     time.Duration `yaml:"metrics_interval"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval"`
	EnablePprof         bool          `yaml:"enable_pprof"`
}

// APIConfig API配置
type APIConfig struct {
	Enabled         bool          `yaml:"enabled"`
	Port            int           `yaml:"port"`
	Host            string        `yaml:"host"`
	ReadTimeout     time.Duration `yaml:"read_timeout"`
	WriteTimeout    time.Duration `yaml:"write_timeout"`
	IdleTimeout     time.Duration `yaml:"idle_timeout"`
	EnableCORS      bool          `yaml:"enable_cors"`
	EnableAuth      bool          `yaml:"enable_auth"`
	EnableRateLimit bool          `yaml:"enable_rate_limit"`
	DefaultVersion  string        `yaml:"default_version"`
}

// ETCDStatus ETCD状态
type ETCDStatus struct {
	ClusterID string `json:"cluster_id"`
	MemberID  string `json:"member_id"`
	Version   string `json:"version"`
	DBSize    int64  `json:"db_size"`
	Leader    string `json:"leader"`
	Healthy   bool   `json:"healthy"`
}

// ClusterInfo 集群信息
type ClusterInfo struct {
	ClusterID   string   `json:"cluster_id"`
	MemberCount int      `json:"member_count"`
	Members     []string `json:"members"`
	Leader      string   `json:"leader"`
	Version     string   `json:"version"`
	IsHealthy   bool     `json:"is_healthy"`
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status    string                 `json:"status"`
	Component string                 `json:"component"`
	Message   string                 `json:"message"`
	Details   map[string]interface{} `json:"details"`
	Timestamp time.Time              `json:"timestamp"`
}

// NetworkStats 网络统计
type NetworkStats struct {
	BytesSent     int64 `json:"bytes_sent"`
	BytesReceived int64 `json:"bytes_received"`
	PacketsSent   int64 `json:"packets_sent"`
	PacketsRecv   int64 `json:"packets_received"`
}

// ResourceStats 资源统计
type ResourceStats struct {
	MemoryUsage    int64         `json:"memory_usage"`
	CPUUsage       float64       `json:"cpu_usage"`
	GoroutineCount int64         `json:"goroutine_count"`
	DiskUsage      int64         `json:"disk_usage"`
	NetworkStats   *NetworkStats `json:"network_stats"`
	Timestamp      time.Time     `json:"timestamp"`
}

// API Handler需要的额外类型定义

// BackupRequest 备份请求
type BackupRequest struct {
	OutputPath      string            `json:"output_path"`
	UseSDK          bool              `json:"use_sdk"`
	FallbackToTool  bool              `json:"fallback_to_tool"`
	Timeout         time.Duration     `json:"timeout"`
	ETCDConfig      *ETCDConfig       `json:"etcd_config"`
	DownloadBaseURL string            `json:"download_base_url"`
	DownloadVersion string            `json:"download_version"`
	DownloadOS      string            `json:"download_os"`
	Metadata        map[string]string `json:"metadata"`
}

// BackupInfo 备份信息
type BackupInfo struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Path        string            `json:"path"`
	Size        int64             `json:"size"`
	CreatedAt   time.Time         `json:"created_at"`
	Status      string            `json:"status"`
	Version     string            `json:"version"`
	Checksum    string            `json:"checksum"`
	Metadata    map[string]string `json:"metadata"`
	Compression string            `json:"compression"`
}

// RestoreRequest 恢复请求
type RestoreRequest struct {
	BackupID                 string            `json:"backup_id"`
	BackupPath               string            `json:"backup_path"`
	DataDir                  string            `json:"data_dir"`
	Name                     string            `json:"name"`
	InitialCluster           string            `json:"initial_cluster"`
	InitialAdvertisePeerURLs string            `json:"initial_advertise_peer_urls"`
	UseSDK                   bool              `json:"use_sdk"`
	FallbackToTool           bool              `json:"fallback_to_tool"`
	Timeout                  time.Duration     `json:"timeout"`
	SkipHashCheck            bool              `json:"skip_hash_check"`
	MarkCompacted            bool              `json:"mark_compacted"`
	Metadata                 map[string]string `json:"metadata"`
}

// RestoreStatus 恢复状态
type RestoreStatus struct {
	RestoreID   string     `json:"restore_id"`
	Status      string     `json:"status"`
	Progress    int        `json:"progress"`
	Message     string     `json:"message"`
	StartedAt   time.Time  `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Error       string     `json:"error,omitempty"`
}

// ETCDHealth ETCD健康状态
type ETCDHealth struct {
	Status    string    `json:"status"`
	Version   string    `json:"version"`
	Leader    string    `json:"leader"`
	Members   []string  `json:"members"`
	DbSize    int64     `json:"db_size"`
	CheckedAt time.Time `json:"checked_at"`
	Errors    []string  `json:"errors,omitempty"`
}
