package service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"go.uber.org/zap"
)

// FileEvent 文件事件类型
type FileEvent int

const (
	FileCreated FileEvent = iota
	FileModified
	FileDeleted
	FileRenamed
)

// String 返回事件类型的字符串表示
func (e FileEvent) String() string {
	switch e {
	case FileCreated:
		return "created"
	case FileModified:
		return "modified"
	case FileDeleted:
		return "deleted"
	case FileRenamed:
		return "renamed"
	default:
		return "unknown"
	}
}

// FileEventInfo 文件事件信息
type FileEventInfo struct {
	Path      string
	Event     FileEvent
	Timestamp time.Time
	Size      int64
	ModTime   time.Time
}

// FileEventHandler 文件事件处理器
type FileEventHandler func(event *FileEventInfo)

// WatchedFile 被监听的文件信息
type WatchedFile struct {
	Path     string
	ModTime  time.Time
	Size     int64
	Exists   bool
	Handlers []FileEventHandler
}

// FileWatcherConfig 文件监听器配置
type FileWatcherConfig struct {
	PollInterval    time.Duration // 轮询间隔
	MaxWatchedFiles int           // 最大监听文件数
	EnableStats     bool          // 是否启用统计
}

// DefaultFileWatcherConfig 默认文件监听器配置
func DefaultFileWatcherConfig() *FileWatcherConfig {
	return &FileWatcherConfig{
		PollInterval:    2 * time.Second,
		MaxWatchedFiles: 1000,
		EnableStats:     true,
	}
}

// FileWatcher 文件监听器
type FileWatcher struct {
	config       *FileWatcherConfig
	logger       *zap.Logger
	watchedFiles map[string]*WatchedFile
	mutex        sync.RWMutex
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup

	// 统计信息
	totalEvents  int64
	totalWatched int64
	startTime    time.Time
}

// NewFileWatcher 创建文件监听器
func NewFileWatcher(config *FileWatcherConfig, logger *zap.Logger) *FileWatcher {
	if config == nil {
		config = DefaultFileWatcherConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	fw := &FileWatcher{
		config:       config,
		logger:       logger,
		watchedFiles: make(map[string]*WatchedFile),
		ctx:          ctx,
		cancel:       cancel,
		startTime:    time.Now(),
	}

	// 启动监听循环
	fw.start()

	return fw
}

// WatchFile 监听文件
func (fw *FileWatcher) WatchFile(filePath string, handler FileEventHandler) error {
	fw.mutex.Lock()
	defer fw.mutex.Unlock()

	// 检查监听文件数量限制
	if len(fw.watchedFiles) >= fw.config.MaxWatchedFiles {
		return fmt.Errorf("已达到最大监听文件数量限制: %d", fw.config.MaxWatchedFiles)
	}

	// 获取文件的绝对路径
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return fmt.Errorf("获取绝对路径失败: %w", err)
	}

	// 获取文件初始状态
	var modTime time.Time
	var size int64
	var exists bool

	if info, err := os.Stat(absPath); err == nil {
		modTime = info.ModTime()
		size = info.Size()
		exists = true
	} else if !os.IsNotExist(err) {
		return fmt.Errorf("获取文件状态失败: %w", err)
	}

	// 检查是否已在监听
	if watchedFile, fileExists := fw.watchedFiles[absPath]; fileExists {
		// 添加新的处理器
		watchedFile.Handlers = append(watchedFile.Handlers, handler)
		fw.logger.Debug("为已监听文件添加处理器", zap.String("path", absPath))
	} else {
		// 创建新的监听条目
		watchedFile := &WatchedFile{
			Path:     absPath,
			ModTime:  modTime,
			Size:     size,
			Exists:   exists,
			Handlers: []FileEventHandler{handler},
		}

		fw.watchedFiles[absPath] = watchedFile
		fw.totalWatched++

		fw.logger.Info("开始监听文件",
			zap.String("path", absPath),
			zap.Int("totalWatched", len(fw.watchedFiles)))
	}

	return nil
}

// UnwatchFile 停止监听文件
func (fw *FileWatcher) UnwatchFile(filePath string) error {
	fw.mutex.Lock()
	defer fw.mutex.Unlock()

	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return fmt.Errorf("获取绝对路径失败: %w", err)
	}

	if _, exists := fw.watchedFiles[absPath]; !exists {
		return fmt.Errorf("文件未在监听列表中: %s", absPath)
	}

	delete(fw.watchedFiles, absPath)
	fw.logger.Info("停止监听文件",
		zap.String("path", absPath),
		zap.Int("remaining", len(fw.watchedFiles)))

	return nil
}

// GetWatchedFiles 获取所有监听的文件
func (fw *FileWatcher) GetWatchedFiles() []string {
	fw.mutex.RLock()
	defer fw.mutex.RUnlock()

	files := make([]string, 0, len(fw.watchedFiles))
	for path := range fw.watchedFiles {
		files = append(files, path)
	}

	return files
}

// GetStats 获取监听器统计信息
func (fw *FileWatcher) GetStats() map[string]interface{} {
	fw.mutex.RLock()
	defer fw.mutex.RUnlock()

	return map[string]interface{}{
		"watchedFiles":    len(fw.watchedFiles),
		"maxWatchedFiles": fw.config.MaxWatchedFiles,
		"totalEvents":     fw.totalEvents,
		"totalWatched":    fw.totalWatched,
		"pollInterval":    fw.config.PollInterval.String(),
		"uptime":          time.Since(fw.startTime).String(),
	}
}

// Close 关闭文件监听器
func (fw *FileWatcher) Close() error {
	fw.logger.Info("关闭文件监听器")

	// 取消上下文
	fw.cancel()

	// 等待所有goroutine结束
	fw.wg.Wait()

	// 清空监听列表
	fw.mutex.Lock()
	fw.watchedFiles = make(map[string]*WatchedFile)
	fw.mutex.Unlock()

	fw.logger.Info("文件监听器已关闭")
	return nil
}

// start 启动监听循环
func (fw *FileWatcher) start() {
	fw.wg.Add(1)
	go fw.watchLoop()
}

// watchLoop 监听循环
func (fw *FileWatcher) watchLoop() {
	defer fw.wg.Done()

	ticker := time.NewTicker(fw.config.PollInterval)
	defer ticker.Stop()

	fw.logger.Info("文件监听器已启动",
		zap.Duration("pollInterval", fw.config.PollInterval))

	for {
		select {
		case <-fw.ctx.Done():
			fw.logger.Debug("文件监听器收到停止信号")
			return
		case <-ticker.C:
			fw.checkFiles()
		}
	}
}

// checkFiles 检查所有监听的文件
func (fw *FileWatcher) checkFiles() {
	fw.mutex.Lock()
	defer fw.mutex.Unlock()

	for path, watchedFile := range fw.watchedFiles {
		fw.checkFile(path, watchedFile)
	}
}

// checkFile 检查单个文件
func (fw *FileWatcher) checkFile(path string, watchedFile *WatchedFile) {
	info, err := os.Stat(path)

	if err != nil {
		if os.IsNotExist(err) {
			// 文件被删除
			if watchedFile.Exists {
				fw.fireEvent(path, watchedFile, FileDeleted, 0, time.Time{})
				watchedFile.Exists = false
			}
		} else {
			fw.logger.Warn("检查文件状态失败",
				zap.String("path", path),
				zap.Error(err))
		}
		return
	}

	// 文件存在
	if !watchedFile.Exists {
		// 文件被创建
		fw.fireEvent(path, watchedFile, FileCreated, info.Size(), info.ModTime())
		watchedFile.Exists = true
		watchedFile.ModTime = info.ModTime()
		watchedFile.Size = info.Size()
		return
	}

	// 检查文件是否被修改
	if !info.ModTime().Equal(watchedFile.ModTime) || info.Size() != watchedFile.Size {
		fw.fireEvent(path, watchedFile, FileModified, info.Size(), info.ModTime())
		watchedFile.ModTime = info.ModTime()
		watchedFile.Size = info.Size()
	}
}

// fireEvent 触发事件
func (fw *FileWatcher) fireEvent(path string, watchedFile *WatchedFile, event FileEvent, size int64, modTime time.Time) {
	eventInfo := &FileEventInfo{
		Path:      path,
		Event:     event,
		Timestamp: time.Now(),
		Size:      size,
		ModTime:   modTime,
	}

	fw.totalEvents++

	fw.logger.Debug("文件事件",
		zap.String("path", path),
		zap.String("event", event.String()),
		zap.Int64("size", size))

	// 异步调用所有处理器
	for _, handler := range watchedFile.Handlers {
		go func(h FileEventHandler) {
			defer func() {
				if r := recover(); r != nil {
					fw.logger.Error("文件事件处理器panic",
						zap.String("path", path),
						zap.Any("panic", r))
				}
			}()
			h(eventInfo)
		}(handler)
	}
}
