{{define "logs"}}
<div id="logs" class="tab-content">
    <div class="card">
        <div class="card-header">
            <h3>实时日志查看</h3>
            <div class="connection-status">
                <span id="log-ws-status" class="status unknown">WebSocket: 未连接</span>
            </div>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label for="pod-namespace" class="form-label">命名空间:</label>
                <input type="text" id="pod-namespace" class="form-control" 
                       placeholder="default" value="default">
            </div>
            <div class="form-group">
                <label for="pod-name" class="form-label">Pod 名称:</label>
                <input type="text" id="pod-name" class="form-control" 
                       placeholder="my-pod">
            </div>
            <div class="form-group">
                <label for="container-name" class="form-label">容器名称 (可选):</label>
                <input type="text" id="container-name" class="form-control" 
                       placeholder="留空使用默认容器">
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="startLogStream()">开始实时日志</button>
                <button class="btn btn-secondary" onclick="stopLogStream()">停止日志</button>
                <button class="btn btn-secondary" onclick="clearLogs()">清空日志</button>
            </div>
            <div id="logs-container" class="logs-container">
                <div id="logs-output" class="logs-output"></div>
            </div>
        </div>
    </div>
</div>
{{end}}
