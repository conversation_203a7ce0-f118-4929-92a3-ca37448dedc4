package service

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
	"k8s-helper/pkg/etcd"
	"k8s-helper/pkg/common"
)

// ToolManager 工具管理器接口
type ToolManager interface {
	IsToolAvailable(toolName string) bool
	EnsureTools(ctx context.Context) error
	EnsureToolsWithConfig(ctx context.Context, baseURL, version, os string) error
	GetBackupRestoreTool() (string, error)
	GetVerifyTool() (string, error)
	DownloadTools(ctx context.Context, baseURL, version, os string) error
}

// ETCDBackupService ETCD备份服务实现
type ETCDBackupService struct {
	logger        *zap.Logger
	toolManager   ToolManager
	clientManager *ClientManager
	auditLogger   *AuditLogger
	errorHandler  *ErrorHandler
}

// NewBackupService 创建备份服务
func NewBackupService(logger *zap.Logger, toolManager ToolManager, clientManager *ClientManager) *ETCDBackupService {
	// 创建审计日志记录器
	auditLogger, err := NewAuditLogger(DefaultAuditConfig())
	if err != nil {
		logger.Warn("创建审计日志记录器失败", zap.Error(err))
		auditLogger = nil
	}

	// 创建错误处理器
	errorHandler := NewErrorHandler(logger)

	return &ETCDBackupService{
		logger:        logger,
		toolManager:   toolManager,
		clientManager: clientManager,
		auditLogger:   auditLogger,
		errorHandler:  errorHandler,
	}
}

// Backup 执行备份操作
func (s *ETCDBackupService) Backup(ctx context.Context, opts *domain.BackupOptions) (*domain.BackupResult, error) {
	startTime := time.Now()
	s.logger.Info("开始执行备份",
		zap.String("output_path", opts.OutputPath),
		zap.Bool("use_sdk", opts.UseSDK))

	// 记录审计日志 - 备份开始
	if s.auditLogger != nil {
		details := map[string]interface{}{
			"output_path": opts.OutputPath,
			"use_sdk":     opts.UseSDK,
			"timeout":     opts.Timeout.String(),
		}
		s.auditLogger.LogBackupOperation(ctx, AuditEventBackupStarted, "backup_start", details, 0, nil)
	}

	// 选择备份方法
	var result *domain.BackupResult
	var err error

	if opts.UseSDK {
		result, err = s.backupWithSDK(ctx, opts)
	} else {
		result, err = s.backupWithTool(ctx, opts)
	}

	duration := time.Since(startTime)

	// 记录审计日志 - 备份完成或失败
	if s.auditLogger != nil {
		details := map[string]interface{}{
			"output_path": opts.OutputPath,
			"use_sdk":     opts.UseSDK,
			"method":      "SDK",
		}
		if !opts.UseSDK {
			details["method"] = "Tool"
		}
		if result != nil {
			details["backup_size"] = result.Size
			details["backup_path"] = result.Path
		}

		if err != nil {
			s.auditLogger.LogBackupOperation(ctx, AuditEventBackupFailed, "backup_execution", details, duration, err)
		} else {
			s.auditLogger.LogBackupOperation(ctx, AuditEventBackupCompleted, "backup_execution", details, duration, nil)
		}
	}

	return result, err
}

// backupWithSDK 使用SDK进行备份
func (s *ETCDBackupService) backupWithSDK(ctx context.Context, opts *domain.BackupOptions) (*domain.BackupResult, error) {
	s.logger.Info("使用SDK进行备份",
		zap.String("output_path", opts.OutputPath),
		zap.String("servers", opts.ETCDConfig.Servers))

	// 创建ETCD客户端配置
	config := &etcd.Config{
		Endpoints: etcd.ParseEndpoints(opts.ETCDConfig.Servers),
		CertFile:  opts.ETCDConfig.CertFile,
		KeyFile:   opts.ETCDConfig.KeyFile,
		CAFile:    opts.ETCDConfig.CaFile,
		Timeout:   30 * time.Second,
	}

	// 创建ETCD客户端
	client, err := etcd.NewClient(config, s.logger)
	if err != nil {
		return nil, fmt.Errorf("创建ETCD客户端失败: %w", err)
	}
	defer client.Close()

	// 测试连接
	if err := client.TestConnection(ctx); err != nil {
		return nil, fmt.Errorf("ETCD连接测试失败: %w", err)
	}

	// 生成输出路径（如果未指定）
	if opts.OutputPath == "" {
		if err := s.generateOutputPath(opts); err != nil {
			return nil, fmt.Errorf("生成输出路径失败: %w", err)
		}
	}

	// 创建备份选项
	backupOpts := &etcd.BackupOptions{
		OutputPath:      opts.OutputPath,
		Timeout:         opts.Timeout,
		VerifyIntegrity: true,
		CreateDir:       true,
	}

	// 执行备份
	startTime := time.Now()
	result, err := client.BackupWithOptions(ctx, backupOpts)
	if err != nil {
		return nil, fmt.Errorf("SDK备份失败: %w", err)
	}

	// 转换结果格式
	domainResult := &domain.BackupResult{
		Status:    "completed",
		Path:      result.FilePath,
		Size:      result.Size,
		Duration:  result.Duration,
		Method:    "SDK",
		CreatedAt: startTime,
		Message:   "Backup completed successfully using SDK",
	}

	s.logger.Info("SDK备份完成",
		zap.String("path", domainResult.Path),
		zap.Int64("size", domainResult.Size),
		zap.Duration("duration", domainResult.Duration))

	return domainResult, nil
}

// generateOutputPath 生成输出路径
func (s *ETCDBackupService) generateOutputPath(opts *domain.BackupOptions) error {
	// 确保备份目录存在
	if err := common.CreateDirIfNotExists(common.DefaultEtcdBackupDir); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	// 生成时间戳
	timestamp := common.GetCurrentTimestamp(common.BackupTimeFormat)

	// 生成文件名
	filename := fmt.Sprintf("etcd-backup-%s.db", timestamp)
	opts.OutputPath = filepath.Join(common.DefaultEtcdBackupDir, filename)

	s.logger.Info("生成备份文件路径", zap.String("path", opts.OutputPath))
	return nil
}

// buildBackupCommand 构建备份命令参数
func (s *ETCDBackupService) buildBackupCommand(toolPath string, opts *domain.BackupOptions) ([]string, error) {
	toolName := filepath.Base(toolPath)

	switch toolName {
	case "etcdutl":
		return s.buildEtcdutlBackupCommand(opts), nil
	case "etcdctl":
		return s.buildEtcdctlBackupCommand(opts), nil
	default:
		return nil, fmt.Errorf("不支持的ETCD工具: %s", toolName)
	}
}

// buildEtcdutlBackupCommand 构建etcdutl备份命令
func (s *ETCDBackupService) buildEtcdutlBackupCommand(opts *domain.BackupOptions) []string {
	args := []string{"snapshot", "save", opts.OutputPath}

	// etcdutl 使用 --endpoints 参数
	if opts.ETCDConfig.Servers != "" {
		args = append(args, "--endpoints", opts.ETCDConfig.Servers)
	}

	// 添加证书参数
	if opts.ETCDConfig.CaFile != "" {
		args = append(args, "--cacert", opts.ETCDConfig.CaFile)
	}
	if opts.ETCDConfig.CertFile != "" {
		args = append(args, "--cert", opts.ETCDConfig.CertFile)
	}
	if opts.ETCDConfig.KeyFile != "" {
		args = append(args, "--key", opts.ETCDConfig.KeyFile)
	}

	return args
}

// buildEtcdctlBackupCommand 构建etcdctl备份命令
func (s *ETCDBackupService) buildEtcdctlBackupCommand(opts *domain.BackupOptions) []string {
	args := []string{"snapshot", "save", opts.OutputPath}

	// etcdctl 使用 --endpoints 参数
	if opts.ETCDConfig.Servers != "" {
		args = append(args, "--endpoints", opts.ETCDConfig.Servers)
	}

	// 添加证书参数
	if opts.ETCDConfig.CaFile != "" {
		args = append(args, "--cacert", opts.ETCDConfig.CaFile)
	}
	if opts.ETCDConfig.CertFile != "" {
		args = append(args, "--cert", opts.ETCDConfig.CertFile)
	}
	if opts.ETCDConfig.KeyFile != "" {
		args = append(args, "--key", opts.ETCDConfig.KeyFile)
	}

	return args
}

// buildRestoreCommand 构建恢复命令参数
func (s *ETCDBackupService) buildRestoreCommand(toolPath string, opts *domain.RestoreOptions) ([]string, error) {
	toolName := filepath.Base(toolPath)

	switch toolName {
	case "etcdutl":
		return s.buildEtcdutlRestoreCommand(opts), nil
	case "etcdctl":
		return s.buildEtcdctlRestoreCommand(opts), nil
	default:
		return nil, fmt.Errorf("不支持的ETCD工具: %s", toolName)
	}
}

// buildEtcdutlRestoreCommand 构建etcdutl恢复命令
func (s *ETCDBackupService) buildEtcdutlRestoreCommand(opts *domain.RestoreOptions) []string {
	args := []string{"snapshot", "restore", opts.SnapshotPath}

	// 添加数据目录
	if opts.DataDir != "" {
		args = append(args, "--data-dir", opts.DataDir)
	}

	// 添加节点名称
	if opts.Name != "" {
		args = append(args, "--name", opts.Name)
	}

	// 添加初始集群配置
	if opts.InitialCluster != "" {
		args = append(args, "--initial-cluster", opts.InitialCluster)
	}

	// 添加初始广播对等URL
	if opts.InitialAdvertisePeerURLs != "" {
		args = append(args, "--initial-advertise-peer-urls", opts.InitialAdvertisePeerURLs)
	}

	// 添加跳过哈希检查
	if opts.SkipHashCheck {
		args = append(args, "--skip-hash-check")
	}

	return args
}

// buildEtcdctlRestoreCommand 构建etcdctl恢复命令
func (s *ETCDBackupService) buildEtcdctlRestoreCommand(opts *domain.RestoreOptions) []string {
	args := []string{"snapshot", "restore", opts.SnapshotPath}

	// 添加数据目录
	if opts.DataDir != "" {
		args = append(args, "--data-dir", opts.DataDir)
	}

	// 添加节点名称
	if opts.Name != "" {
		args = append(args, "--name", opts.Name)
	}

	// 添加初始集群配置
	if opts.InitialCluster != "" {
		args = append(args, "--initial-cluster", opts.InitialCluster)
	}

	// 添加初始广播对等URL
	if opts.InitialAdvertisePeerURLs != "" {
		args = append(args, "--initial-advertise-peer-urls", opts.InitialAdvertisePeerURLs)
	}

	// 添加跳过哈希检查
	if opts.SkipHashCheck {
		args = append(args, "--skip-hash-check")
	}

	return args
}

// backupWithTool 使用外部工具进行备份
func (s *ETCDBackupService) backupWithTool(ctx context.Context, opts *domain.BackupOptions) (*domain.BackupResult, error) {
	s.logger.Info("使用外部工具进行备份",
		zap.String("output_path", opts.OutputPath))

	// 确保工具可用
	if err := s.toolManager.EnsureTools(ctx); err != nil {
		return nil, fmt.Errorf("确保工具可用失败: %w", err)
	}

	// 获取备份工具路径
	toolPath, err := s.toolManager.GetBackupRestoreTool()
	if err != nil {
		return nil, fmt.Errorf("获取备份工具失败: %w", err)
	}

	// 生成输出路径（如果未指定）
	if opts.OutputPath == "" {
		if err := s.generateOutputPath(opts); err != nil {
			return nil, fmt.Errorf("生成输出路径失败: %w", err)
		}
	}

	// 构建备份命令
	args, err := s.buildBackupCommand(toolPath, opts)
	if err != nil {
		return nil, fmt.Errorf("构建备份命令失败: %w", err)
	}

	s.logger.Info("执行工具备份命令",
		zap.String("tool", filepath.Base(toolPath)),
		zap.Strings("args", args))

	// 执行备份命令
	startTime := time.Now()
	if err := s.executeCommand(ctx, toolPath, args, opts.Timeout); err != nil {
		return nil, fmt.Errorf("执行备份命令失败: %w", err)
	}
	duration := time.Since(startTime)

	// 获取文件信息
	fileInfo, err := os.Stat(opts.OutputPath)
	if err != nil {
		return nil, fmt.Errorf("获取备份文件信息失败: %w", err)
	}

	result := &domain.BackupResult{
		Status:    "completed",
		Path:      opts.OutputPath,
		Size:      fileInfo.Size(),
		Duration:  duration,
		Method:    "Tool",
		CreatedAt: startTime,
		Message:   fmt.Sprintf("Backup completed successfully using %s", filepath.Base(toolPath)),
	}

	s.logger.Info("工具备份完成",
		zap.String("path", result.Path),
		zap.Int64("size", result.Size),
		zap.Duration("duration", result.Duration))

	return result, nil
}

// Restore 执行恢复操作
func (s *ETCDBackupService) Restore(ctx context.Context, opts *domain.RestoreOptions) (*domain.RestoreResult, error) {
	s.logger.Info("开始执行恢复",
		zap.String("snapshot_path", opts.SnapshotPath),
		zap.Bool("use_sdk", opts.UseSDK))

	if opts.UseSDK {
		return s.restoreWithSDK(ctx, opts)
	}
	return s.restoreWithTool(ctx, opts)
}

// restoreWithSDK 使用SDK进行恢复
func (s *ETCDBackupService) restoreWithSDK(ctx context.Context, opts *domain.RestoreOptions) (*domain.RestoreResult, error) {
	s.logger.Info("使用SDK进行恢复",
		zap.String("snapshot_path", opts.SnapshotPath),
		zap.String("data_dir", opts.DataDir),
		zap.String("name", opts.Name))

	// 创建离线客户端（恢复操作不需要连接到ETCD）
	client := etcd.NewClientForOfflineOps(s.logger)

	// 创建恢复选项
	restoreOpts := &etcd.RestoreOptions{
		SnapshotPath:             opts.SnapshotPath,
		DataDir:                  opts.DataDir,
		Name:                     opts.Name,
		InitialCluster:           opts.InitialCluster,
		InitialAdvertisePeerURLs: opts.InitialAdvertisePeerURLs,
		Timeout:                  opts.Timeout,
		SkipHashCheck:            opts.SkipHashCheck,
		MarkCompacted:            opts.MarkCompacted,
	}

	// 执行恢复
	startTime := time.Now()
	result, err := client.RestoreWithOptions(ctx, restoreOpts)
	if err != nil {
		return nil, fmt.Errorf("SDK恢复失败: %w", err)
	}

	// 转换结果格式
	domainResult := &domain.RestoreResult{
		Success:   true,
		Status:    "completed",
		DataDir:   result.DataDir,
		Duration:  result.Duration,
		Method:    "SDK",
		StartedAt: startTime,
		Message:   "Restore completed successfully using SDK",
	}

	s.logger.Info("SDK恢复完成",
		zap.String("snapshot_path", opts.SnapshotPath),
		zap.String("data_dir", domainResult.DataDir),
		zap.Duration("duration", domainResult.Duration))

	return domainResult, nil
}

// restoreWithTool 使用外部工具进行恢复
func (s *ETCDBackupService) restoreWithTool(ctx context.Context, opts *domain.RestoreOptions) (*domain.RestoreResult, error) {
	s.logger.Info("使用外部工具进行恢复",
		zap.String("snapshot_path", opts.SnapshotPath),
		zap.String("data_dir", opts.DataDir))

	// 确保工具可用
	if err := s.toolManager.EnsureTools(ctx); err != nil {
		return nil, fmt.Errorf("确保工具可用失败: %w", err)
	}

	// 获取恢复工具路径
	toolPath, err := s.toolManager.GetBackupRestoreTool()
	if err != nil {
		return nil, fmt.Errorf("获取恢复工具失败: %w", err)
	}

	// 构建恢复命令
	args, err := s.buildRestoreCommand(toolPath, opts)
	if err != nil {
		return nil, fmt.Errorf("构建恢复命令失败: %w", err)
	}

	s.logger.Info("执行工具恢复命令",
		zap.String("tool", filepath.Base(toolPath)),
		zap.Strings("args", args))

	// 执行恢复命令
	startTime := time.Now()
	if err := s.executeCommand(ctx, toolPath, args, opts.Timeout); err != nil {
		return nil, fmt.Errorf("执行恢复命令失败: %w", err)
	}
	duration := time.Since(startTime)

	result := &domain.RestoreResult{
		Success:   true,
		Status:    "completed",
		DataDir:   opts.DataDir,
		Duration:  duration,
		Method:    "Tool",
		StartedAt: startTime,
		Message:   fmt.Sprintf("Restore completed successfully using %s", filepath.Base(toolPath)),
	}

	s.logger.Info("工具恢复完成",
		zap.String("snapshot_path", opts.SnapshotPath),
		zap.String("data_dir", result.DataDir),
		zap.Duration("duration", result.Duration))

	return result, nil
}

// Verify 验证备份文件
func (s *ETCDBackupService) Verify(ctx context.Context, backupPath string) (*domain.VerifyResult, error) {
	s.logger.Info("开始验证备份文件", zap.String("path", backupPath))

	// 检查文件是否存在
	fileInfo, err := os.Stat(backupPath)
	if err != nil {
		return nil, fmt.Errorf("备份文件不存在: %w", err)
	}

	// 检查文件大小
	if fileInfo.Size() == 0 {
		return &domain.VerifyResult{
			Valid:      false,
			IsValid:    false,
			Size:       0,
			VerifiedAt: time.Now(),
			Message:    "备份文件为空",
		}, nil
	}

	// 尝试使用SDK验证
	result, err := s.verifyWithSDK(ctx, backupPath, fileInfo.Size())
	if err != nil {
		s.logger.Warn("SDK验证失败，尝试工具验证", zap.Error(err))

		// 回退到工具验证
		result, err = s.verifyWithTool(ctx, backupPath, fileInfo.Size())
		if err != nil {
			return nil, fmt.Errorf("验证失败: %w", err)
		}
	}

	s.logger.Info("备份文件验证完成",
		zap.String("path", backupPath),
		zap.Bool("valid", result.Valid),
		zap.Int64("size", result.Size))

	return result, nil
}

// verifyWithSDK 使用SDK验证备份文件
func (s *ETCDBackupService) verifyWithSDK(ctx context.Context, backupPath string, size int64) (*domain.VerifyResult, error) {
	// 创建离线客户端
	client := etcd.NewClientForOfflineOps(s.logger)

	// 创建验证选项
	verifyOpts := &etcd.VerifyOptions{
		SnapshotPath: backupPath,
		Timeout:      30 * time.Second,
		Detailed:     false,
	}

	// 执行验证
	result, err := client.VerifyWithOptions(ctx, verifyOpts)
	if err != nil {
		return nil, fmt.Errorf("SDK验证失败: %w", err)
	}

	// 转换结果格式
	domainResult := &domain.VerifyResult{
		Valid:      result.Valid,
		IsValid:    result.Valid,
		Size:       size,
		Checksum:   fmt.Sprintf("%x", result.Hash),
		VerifiedAt: time.Now(),
		Message:    "Backup verification completed using SDK",
	}

	return domainResult, nil
}

// verifyWithTool 使用工具验证备份文件
func (s *ETCDBackupService) verifyWithTool(ctx context.Context, backupPath string, size int64) (*domain.VerifyResult, error) {
	// 确保工具可用
	if err := s.toolManager.EnsureTools(ctx); err != nil {
		return nil, fmt.Errorf("确保工具可用失败: %w", err)
	}

	// 获取验证工具路径
	toolPath, err := s.toolManager.GetVerifyTool()
	if err != nil {
		return nil, fmt.Errorf("获取验证工具失败: %w", err)
	}

	// 构建验证命令
	args := s.buildVerifyCommand(toolPath, backupPath)

	s.logger.Info("执行工具验证命令",
		zap.String("tool", filepath.Base(toolPath)),
		zap.Strings("args", args))

	// 执行验证命令
	if err := s.executeCommand(ctx, toolPath, args, 30*time.Second); err != nil {
		return &domain.VerifyResult{
			Valid:      false,
			IsValid:    false,
			Size:       size,
			VerifiedAt: time.Now(),
			Message:    fmt.Sprintf("Tool verification failed: %v", err),
		}, nil
	}

	// 验证成功
	result := &domain.VerifyResult{
		Valid:      true,
		IsValid:    true,
		Size:       size,
		VerifiedAt: time.Now(),
		Message:    fmt.Sprintf("Backup verification completed using %s", filepath.Base(toolPath)),
	}

	return result, nil
}

// buildVerifyCommand 构建验证命令参数
func (s *ETCDBackupService) buildVerifyCommand(toolPath, backupPath string) []string {
	toolName := filepath.Base(toolPath)

	switch toolName {
	case "etcdutl":
		return []string{"snapshot", "status", backupPath}
	case "etcdctl":
		return []string{"snapshot", "status", backupPath}
	default:
		// 默认使用etcdctl格式
		return []string{"snapshot", "status", backupPath}
	}
}

// executeCommand 执行命令
func (s *ETCDBackupService) executeCommand(ctx context.Context, toolPath string, args []string, timeout time.Duration) error {
	// 设置超时
	if timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	}

	// 创建命令
	cmd := exec.CommandContext(ctx, toolPath, args...)

	// 设置环境变量
	cmd.Env = os.Environ()

	s.logger.Info("执行命令",
		zap.String("tool", toolPath),
		zap.Strings("args", args),
		zap.Duration("timeout", timeout))

	// 执行命令
	output, err := cmd.CombinedOutput()
	if err != nil {
		s.logger.Error("命令执行失败",
			zap.String("tool", toolPath),
			zap.Strings("args", args),
			zap.String("output", string(output)),
			zap.Error(err))
		return fmt.Errorf("命令执行失败: %w, 输出: %s", err, string(output))
	}

	s.logger.Info("命令执行成功",
		zap.String("tool", toolPath),
		zap.String("output", string(output)))

	return nil
}