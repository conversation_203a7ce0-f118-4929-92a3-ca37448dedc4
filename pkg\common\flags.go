package common

// 公共的命令行标志变量，避免重复定义

// 常用的默认值
const (
	// 默认命名空间
	DefaultNamespace = "default"

	// 默认 kubeconfig 路径
	DefaultKubeconfigPath = "~/.kube/config"

	// 默认 ETCD 数据目录
	DefaultEtcdDataDir = "/var/lib/etcd"

	// 默认 ETCD 恢复目录基础路径
	DefaultEtcdRestoreBaseDir = "/var/lib"

	// 默认 kube-apiserver 配置文件路径
	DefaultAPIServerManifest = "/etc/kubernetes/manifests/kube-apiserver.yaml"

	// 默认 ETCD 备份目录
	DefaultEtcdBackupDir = "/opt/etcd-backups"
)

// ETCD 下载配置常量
const (
	// 默认下载基础 URL
	DefaultEtcdBaseURL = "https://wutong-paas.obs.cn-east-3.myhuaweicloud.com/kubeadm-ansible/etcd"

	// 默认 ETCD 版本
	DefaultEtcdVersion = "v3.5.15"

	// 默认操作系统
	DefaultEtcdOS = "linux"

	// 支持的架构
	EtcdArchAMD64 = "amd64"
	EtcdArchARM64 = "arm64"
)

// 时间格式常量
const (
	// 备份文件时间戳格式
	BackupTimeFormat = "20060102-150405"

	// 日志时间戳格式
	LogTimeFormat = "2006-01-02 15:04:05"
)

// ETCD 操作默认值
const (
	// 默认 SDK 超时时间（分钟）
	DefaultSDKTimeoutMinutes = 5

	// 默认验证超时时间（秒）
	DefaultVerifyTimeoutSeconds = 30

	// 默认节点名称
	DefaultNodeName = "default"

	// 默认初始广播对等URL
	DefaultInitialAdvertisePeerURLs = "http://localhost:2380"

	// 默认 SDK 使用开关
	DefaultUseSDK = true

	// 默认工具回退开关
	DefaultFallbackToTool = true

	// 默认详细验证开关
	DefaultDetailedVerify = false

	// 默认跳过哈希检查开关
	DefaultSkipHashCheck = false

	// 默认标记压缩开关
	DefaultMarkCompacted = false
)

// CronJob 默认值
const (
	// 默认 CronJob 命名空间
	DefaultCronJobNamespace = "kube-system"

	// 默认 CronJob 名称
	DefaultCronJobName = "etcd-backup"

	// 默认 Cron 调度表达式（每天凌晨3点）
	DefaultCronJobSchedule = "0 3 * * *"

	// 默认时区
	DefaultCronJobTimezone = "Asia/Shanghai"

	// 默认备份镜像
	DefaultCronJobImage = "swr.cn-southwest-2.myhuaweicloud.com/wutong/etcd:v3.5.9"

	// 默认备份保留数量
	DefaultBackupRetainCount = 30

	// 默认显示所有命名空间开关
	DefaultAllNamespaces = false
)

// 端口转发默认值
const (
	// 默认本地绑定地址
	DefaultPortForwardAddress = "localhost"
)

// 日志命令默认值
const (
	// 默认跟踪日志开关
	DefaultFollowLogs = false

	// 默认显示时间戳开关
	DefaultShowTimestamps = false

	// 默认显示之前日志开关
	DefaultShowPrevious = false

	// 默认日志行数（-1表示显示所有）
	DefaultTailLines = -1
)

// 清理命令默认值
const (
	// 默认干运行模式开关
	DefaultDryRun = false
)

// 信息命令默认值
const (
	// 默认仅显示节点开关
	DefaultNodesOnly = false
)

// 配置管理默认值
const (
	// 默认日志级别
	DefaultLogLevel = "info"

	// 默认日志格式
	DefaultLogFormat = "text"

	// 默认颜色输出开关
	DefaultColorOutput = true

	// 默认清理时间阈值
	DefaultCleanupOlderThan = "1h"

	// 默认并发工作线程数
	DefaultConcurrentWorkers = 5
)

// 超时和重试配置
const (
	// 默认连接超时时间（秒）
	DefaultConnectionTimeoutSeconds = 30

	// 默认操作重试次数
	DefaultRetryCount = 3

	// 默认重试间隔（秒）
	DefaultRetryIntervalSeconds = 5
)

// 文件路径默认值
const (
	// 默认 kubeconfig 文件名
	DefaultKubeconfigFileName = "config"

	// 默认配置目录名
	DefaultConfigDirName = ".k8s-helper"

	// 默认配置文件名
	DefaultConfigFileName = "config.yaml"
)

// Kubernetes 版本相关常量
const (
	// 支持 CronJob timeZone 字段的最低 Kubernetes 版本
	MinTimeZoneSupportVersion = "1.24.0"

	// 版本比较的主要版本号
	TimeZoneSupportMajor = 1

	// 版本比较的次要版本号
	TimeZoneSupportMinor = 24
)
