import { ThemeConfig } from 'antd';

// Ant Design 主题配置
export const lightTheme: ThemeConfig = {
  token: {
    // 主色调
    colorPrimary: '#3498db',
    colorSuccess: '#27ae60',
    colorWarning: '#f39c12',
    colorError: '#e74c3c',
    colorInfo: '#17a2b8',
    
    // 背景色
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    colorBgLayout: '#f5f7fa',
    
    // 文字颜色
    colorText: '#212529',
    colorTextSecondary: '#6c757d',
    colorTextTertiary: '#adb5bd',
    
    // 边框
    colorBorder: '#dee2e6',
    colorBorderSecondary: '#e9ecef',
    
    // 圆角
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,
    
    // 字体
    fontSize: 14,
    fontSizeLG: 16,
    fontSizeSM: 12,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    
    // 间距
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    
    // 阴影
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    boxShadowSecondary: '0 1px 4px rgba(0, 0, 0, 0.08)',
  },
  components: {
    Layout: {
      headerBg: '#ffffff',
      siderBg: '#2c3e50',
      bodyBg: '#f5f7fa',
    },
    Menu: {
      darkItemBg: '#2c3e50',
      darkItemSelectedBg: '#34495e',
      darkItemHoverBg: '#34495e',
      darkItemColor: '#ecf0f1',
      darkItemSelectedColor: '#ffffff',
    },
    Button: {
      borderRadius: 6,
      controlHeight: 36,
    },
    Card: {
      borderRadius: 8,
      paddingLG: 24,
    },
    Table: {
      borderRadius: 6,
      headerBg: '#fafafa',
    },
    Input: {
      borderRadius: 6,
      controlHeight: 36,
    },
    Select: {
      borderRadius: 6,
      controlHeight: 36,
    },
  },
};

export const darkTheme: ThemeConfig = {
  token: {
    // 主色调
    colorPrimary: '#3498db',
    colorSuccess: '#27ae60',
    colorWarning: '#f39c12',
    colorError: '#e74c3c',
    colorInfo: '#17a2b8',
    
    // 背景色
    colorBgContainer: '#1a1a1a',
    colorBgElevated: '#2d2d2d',
    colorBgLayout: '#141414',
    
    // 文字颜色
    colorText: '#f8f9fa',
    colorTextSecondary: '#adb5bd',
    colorTextTertiary: '#6c757d',
    
    // 边框
    colorBorder: '#495057',
    colorBorderSecondary: '#404040',
    
    // 圆角
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,
    
    // 字体
    fontSize: 14,
    fontSizeLG: 16,
    fontSizeSM: 12,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    
    // 间距
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    
    // 阴影
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
    boxShadowSecondary: '0 1px 4px rgba(0, 0, 0, 0.2)',
  },
  components: {
    Layout: {
      headerBg: '#1a1a1a',
      siderBg: '#1a1a1a',
      bodyBg: '#141414',
    },
    Menu: {
      darkItemBg: '#1a1a1a',
      darkItemSelectedBg: '#2d2d2d',
      darkItemHoverBg: '#2d2d2d',
      darkItemColor: '#f8f9fa',
      darkItemSelectedColor: '#ffffff',
    },
    Button: {
      borderRadius: 6,
      controlHeight: 36,
    },
    Card: {
      borderRadius: 8,
      paddingLG: 24,
    },
    Table: {
      borderRadius: 6,
      headerBg: '#2d2d2d',
    },
    Input: {
      borderRadius: 6,
      controlHeight: 36,
    },
    Select: {
      borderRadius: 6,
      controlHeight: 36,
    },
  },
};

// 主题切换工具函数
export const getThemeConfig = (isDark: boolean): ThemeConfig => {
  return isDark ? darkTheme : lightTheme;
};
