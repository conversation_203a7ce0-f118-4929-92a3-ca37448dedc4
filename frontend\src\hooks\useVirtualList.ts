import { useState, useEffect, useMemo, useCallback } from 'react';

// 虚拟列表配置
export interface VirtualListOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number; // 预渲染的额外项目数量
  scrollingDelay?: number; // 滚动延迟
}

// 虚拟列表项
export interface VirtualItem {
  index: number;
  start: number;
  end: number;
  size: number;
}

/**
 * 虚拟列表Hook
 * 用于优化大量数据的渲染性能
 */
export const useVirtualList = <T>(
  items: T[],
  options: VirtualListOptions
) => {
  const {
    itemHeight,
    containerHeight,
    overscan = 5,
    scrollingDelay = 150
  } = options;

  const [scrollTop, setScrollTop] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollingTimeoutId, setScrollingTimeoutId] = useState<NodeJS.Timeout | null>(null);

  // 计算可见范围
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    );

    return {
      start: Math.max(0, startIndex - overscan),
      end: Math.min(items.length - 1, endIndex + overscan),
    };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  // 计算虚拟项目
  const virtualItems = useMemo(() => {
    const virtualItems: VirtualItem[] = [];
    
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      virtualItems.push({
        index: i,
        start: i * itemHeight,
        end: (i + 1) * itemHeight,
        size: itemHeight,
      });
    }
    
    return virtualItems;
  }, [visibleRange, itemHeight]);

  // 可见的数据项
  const visibleItems = useMemo(() => {
    return virtualItems.map(virtualItem => ({
      ...virtualItem,
      data: items[virtualItem.index],
    }));
  }, [virtualItems, items]);

  // 总高度
  const totalHeight = items.length * itemHeight;

  // 处理滚动
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = event.currentTarget.scrollTop;
    setScrollTop(scrollTop);
    setIsScrolling(true);

    // 清除之前的定时器
    if (scrollingTimeoutId) {
      clearTimeout(scrollingTimeoutId);
    }

    // 设置新的定时器
    const timeoutId = setTimeout(() => {
      setIsScrolling(false);
    }, scrollingDelay);
    
    setScrollingTimeoutId(timeoutId);
  }, [scrollingDelay, scrollingTimeoutId]);

  // 滚动到指定索引
  const scrollToIndex = useCallback((index: number, align: 'start' | 'center' | 'end' = 'start') => {
    let scrollTop: number;
    
    switch (align) {
      case 'start':
        scrollTop = index * itemHeight;
        break;
      case 'center':
        scrollTop = index * itemHeight - containerHeight / 2 + itemHeight / 2;
        break;
      case 'end':
        scrollTop = index * itemHeight - containerHeight + itemHeight;
        break;
      default:
        scrollTop = index * itemHeight;
    }
    
    setScrollTop(Math.max(0, Math.min(scrollTop, totalHeight - containerHeight)));
  }, [itemHeight, containerHeight, totalHeight]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollingTimeoutId) {
        clearTimeout(scrollingTimeoutId);
      }
    };
  }, [scrollingTimeoutId]);

  return {
    virtualItems,
    visibleItems,
    totalHeight,
    isScrolling,
    scrollTop,
    handleScroll,
    scrollToIndex,
    // 容器样式
    containerStyle: {
      height: containerHeight,
      overflow: 'auto',
    },
    // 内容样式
    contentStyle: {
      height: totalHeight,
      position: 'relative' as const,
    },
  };
};

/**
 * 固定大小虚拟列表Hook
 * 适用于所有项目高度相同的场景
 */
export const useFixedSizeList = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan = 5
) => {
  return useVirtualList(items, {
    itemHeight,
    containerHeight,
    overscan,
  });
};

/**
 * 动态大小虚拟列表Hook
 * 适用于项目高度不同的场景
 */
export const useDynamicSizeList = <T>(
  items: T[],
  getItemHeight: (index: number, item: T) => number,
  containerHeight: number,
  overscan = 5
) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);

  // 计算每个项目的位置和大小
  const itemMetadata = useMemo(() => {
    const metadata: Array<{ start: number; end: number; size: number }> = [];
    let currentOffset = 0;

    for (let i = 0; i < items.length; i++) {
      const size = getItemHeight(i, items[i]);
      metadata[i] = {
        start: currentOffset,
        end: currentOffset + size,
        size,
      };
      currentOffset += size;
    }

    return metadata;
  }, [items, getItemHeight]);

  // 总高度
  const totalHeight = itemMetadata.length > 0 
    ? itemMetadata[itemMetadata.length - 1].end 
    : 0;

  // 查找可见范围
  const visibleRange = useMemo(() => {
    if (itemMetadata.length === 0) {
      return { start: 0, end: 0 };
    }

    // 二分查找起始索引
    let startIndex = 0;
    let endIndex = itemMetadata.length - 1;
    
    while (startIndex <= endIndex) {
      const middleIndex = Math.floor((startIndex + endIndex) / 2);
      const metadata = itemMetadata[middleIndex];
      
      if (metadata.end <= scrollTop) {
        startIndex = middleIndex + 1;
      } else if (metadata.start > scrollTop) {
        endIndex = middleIndex - 1;
      } else {
        startIndex = middleIndex;
        break;
      }
    }

    // 查找结束索引
    const viewportEnd = scrollTop + containerHeight;
    let endIdx = startIndex;
    
    while (endIdx < itemMetadata.length && itemMetadata[endIdx].start < viewportEnd) {
      endIdx++;
    }

    return {
      start: Math.max(0, startIndex - overscan),
      end: Math.min(itemMetadata.length - 1, endIdx + overscan),
    };
  }, [scrollTop, containerHeight, itemMetadata, overscan]);

  // 虚拟项目
  const virtualItems = useMemo(() => {
    const virtualItems: VirtualItem[] = [];
    
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      const metadata = itemMetadata[i];
      if (metadata) {
        virtualItems.push({
          index: i,
          start: metadata.start,
          end: metadata.end,
          size: metadata.size,
        });
      }
    }
    
    return virtualItems;
  }, [visibleRange, itemMetadata]);

  // 可见项目
  const visibleItems = useMemo(() => {
    return virtualItems.map(virtualItem => ({
      ...virtualItem,
      data: items[virtualItem.index],
    }));
  }, [virtualItems, items]);

  // 处理滚动
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
    setIsScrolling(true);
    
    // 延迟设置滚动状态
    setTimeout(() => setIsScrolling(false), 150);
  }, []);

  // 滚动到指定索引
  const scrollToIndex = useCallback((index: number) => {
    if (index >= 0 && index < itemMetadata.length) {
      setScrollTop(itemMetadata[index].start);
    }
  }, [itemMetadata]);

  return {
    virtualItems,
    visibleItems,
    totalHeight,
    isScrolling,
    scrollTop,
    handleScroll,
    scrollToIndex,
    containerStyle: {
      height: containerHeight,
      overflow: 'auto',
    },
    contentStyle: {
      height: totalHeight,
      position: 'relative' as const,
    },
  };
};
