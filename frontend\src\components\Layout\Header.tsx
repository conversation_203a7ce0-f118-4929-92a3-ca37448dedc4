import React from 'react';
import { Layout, Breadcrumb, Button, Dropdown, Space, Switch } from 'antd';
import { useLocation } from 'react-router-dom';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BulbOutlined,
  BulbFilled,
} from '@ant-design/icons';
import { getBreadcrumbItems } from '@/config/navigation';

const { Header: AntHeader } = Layout;

interface HeaderProps {
  sidebarCollapsed: boolean;
  onSidebarToggle: () => void;
  className?: string;
  style?: React.CSSProperties;
}

// 面包屑配置已移至 @/config/navigation

/**
 * 顶部导航栏组件
 * 
 * 功能特性：
 * - 侧边栏折叠控制
 * - 面包屑导航
 * - 用户信息显示
 * - 主题切换
 * - 响应式设计
 */
export const Header: React.FC<HeaderProps> = ({
  sidebarCollapsed,
  onSidebarToggle,
  className,
  style,
  ...restProps
}) => {
  const location = useLocation();
  const [isDarkTheme, setIsDarkTheme] = React.useState(false);

  // 生成面包屑导航
  const generateBreadcrumbs = () => {
    return getBreadcrumbItems(location.pathname);
  };

  // 处理主题切换
  const handleThemeChange = (checked: boolean) => {
    setIsDarkTheme(checked);
    // TODO: 实现主题切换逻辑
    console.log('Theme changed to:', checked ? 'dark' : 'light');
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => console.log('Profile clicked'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => console.log('Settings clicked'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => console.log('Logout clicked'),
    },
  ];

  return (
    <AntHeader 
      className={`app-header ${className || ''}`}
      style={{ 
        padding: '0 24px', 
        background: '#fff',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        ...style 
      }}
      {...restProps}
    >
      {/* 左侧区域 */}
      <div className="app-header-left">
        {/* 侧边栏折叠按钮 */}
        <Button
          type="text"
          icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={onSidebarToggle}
          className="app-header-trigger"
          style={{ marginRight: 16 }}
        />

        {/* 面包屑导航 */}
        <Breadcrumb 
          items={generateBreadcrumbs()}
          className="app-header-breadcrumb"
        />
      </div>

      {/* 右侧区域 */}
      <div className="app-header-right">
        <Space size="middle">
          {/* 主题切换 */}
          <div className="app-header-theme-switch">
            <Space>
              <BulbOutlined style={{ color: isDarkTheme ? '#666' : '#1890ff' }} />
              <Switch
                checked={isDarkTheme}
                onChange={handleThemeChange}
                size="small"
                checkedChildren={<BulbFilled />}
                unCheckedChildren={<BulbOutlined />}
              />
            </Space>
          </div>

          {/* 用户信息 */}
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            arrow
          >
            <Button 
              type="text" 
              className="app-header-user"
              style={{ height: 'auto', padding: '4px 8px' }}
            >
              <Space>
                <UserOutlined />
                <span className="app-header-username">管理员</span>
              </Space>
            </Button>
          </Dropdown>
        </Space>
      </div>
    </AntHeader>
  );
};

export default Header;
