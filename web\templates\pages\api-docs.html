{{template "layout/base.html" .}}

{{define "head"}}
<style>
    .api-docs {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .endpoint {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }
    .endpoint-header {
        padding: 15px 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        gap: 15px;
    }
    .method {
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 0.8rem;
        text-transform: uppercase;
    }
    .method.get { background: #d4edda; color: #155724; }
    .method.post { background: #d1ecf1; color: #0c5460; }
    .method.put { background: #fff3cd; color: #856404; }
    .method.delete { background: #f8d7da; color: #721c24; }
    .endpoint-body {
        padding: 20px;
    }
    .test-form {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        margin-top: 15px;
    }
    .param-input {
        margin-bottom: 10px;
    }
    .param-input label {
        display: block;
        font-weight: 500;
        margin-bottom: 5px;
    }
    .response-area {
        background: #1e1e1e;
        color: #d4d4d4;
        padding: 15px;
        border-radius: 6px;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        max-height: 300px;
        overflow-y: auto;
        margin-top: 10px;
    }
</style>
{{end}}

{{define "content"}}
<div class="api-docs">
    <div class="card">
        <div class="card-header">
            <h2>API 概览</h2>
        </div>
        <div class="card-body">
            <p><strong>基础URL:</strong> <code>/api/v1</code></p>
            <p><strong>版本:</strong> {{.APIVersion | default "1.0.0"}}</p>
            <p><strong>描述:</strong> {{.APIDescription | default "K8s-Helper Kubernetes 集群管理工具 API"}}</p>
            <button class="btn btn-primary" onclick="loadAPIDocumentation()">加载API文档</button>
        </div>
    </div>
    
    <div id="api-endpoints"></div>
</div>
{{end}}

{{define "scripts"}}
<script>
    function loadAPIDocumentation() {
        fetch('/api/docs')
            .then(response => response.json())
            .then(data => {
                renderAPIEndpoints(data.endpoints);
            })
            .catch(error => {
                console.error('加载API文档失败:', error);
            });
    }
    
    function renderAPIEndpoints(endpoints) {
        const container = document.getElementById('api-endpoints');
        container.innerHTML = '';
        
        endpoints.forEach(endpoint => {
            const endpointDiv = document.createElement('div');
            endpointDiv.className = 'endpoint';
            endpointDiv.innerHTML = 
                '<div class="endpoint-header">' +
                    '<span class="method ' + endpoint.method.toLowerCase() + '">' + endpoint.method + '</span>' +
                    '<span class="path"><code>' + endpoint.path + '</code></span>' +
                    '<span class="summary">' + endpoint.summary + '</span>' +
                '</div>' +
                '<div class="endpoint-body">' +
                    '<p>' + endpoint.description + '</p>' +
                    renderParameters(endpoint.parameters || []) +
                    renderTestForm(endpoint) +
                '</div>';
            
            container.appendChild(endpointDiv);
        });
    }
    
    function renderParameters(parameters) {
        if (parameters.length === 0) return '';
        
        let html = '<h4>参数</h4><ul>';
        parameters.forEach(param => {
            html += '<li><strong>' + param.name + '</strong> (' + param.type + ')';
            if (param.required) html += ' <em>必需</em>';
            html += ' - ' + param.description;
            if (param.example) html += ' <code>示例: ' + param.example + '</code>';
            html += '</li>';
        });
        html += '</ul>';
        return html;
    }
    
    function renderTestForm(endpoint) {
        const formId = 'form-' + endpoint.method.toLowerCase() + '-' + endpoint.path.replace(/[^a-zA-Z0-9]/g, '');
        let html = '<div class="test-form">';
        html += '<h4>测试接口</h4>';
        html += '<form id="' + formId + '">';
        
        if (endpoint.parameters) {
            endpoint.parameters.forEach(param => {
                if (param.in === 'query' || param.in === 'path') {
                    html += '<div class="param-input">';
                    html += '<label>' + param.name + (param.required ? ' *' : '') + '</label>';
                    html += '<input type="text" name="' + param.name + '" class="form-control" placeholder="' + (param.example || '') + '">';
                    html += '</div>';
                }
            });
        }
        
        html += '<button type="button" class="btn btn-primary" onclick="testEndpoint(\'' + endpoint.method + '\', \'' + endpoint.path + '\', \'' + formId + '\')">测试</button>';
        html += '</form>';
        html += '<div id="response-' + formId + '" class="response-area" style="display: none;"></div>';
        html += '</div>';
        
        return html;
    }
    
    function testEndpoint(method, path, formId) {
        const form = document.getElementById(formId);
        const formData = new FormData(form);
        const params = new URLSearchParams();
        
        for (let [key, value] of formData.entries()) {
            if (value) params.append(key, value);
        }
        
        let url = '/api/v1' + path;
        if (method === 'GET' && params.toString()) {
            url += '?' + params.toString();
        }
        
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        if (method !== 'GET' && params.toString()) {
            const body = {};
            for (let [key, value] of params.entries()) {
                body[key] = value;
            }
            options.body = JSON.stringify(body);
        }
        
        const responseDiv = document.getElementById('response-' + formId);
        responseDiv.style.display = 'block';
        responseDiv.textContent = '请求中...';
        
        fetch(url, options)
            .then(response => {
                return response.json().then(data => ({
                    status: response.status,
                    data: data
                }));
            })
            .then(result => {
                responseDiv.innerHTML = 
                    '<strong>状态码:</strong> ' + result.status + '<br>' +
                    '<strong>响应:</strong><br>' +
                    '<pre>' + JSON.stringify(result.data, null, 2) + '</pre>';
            })
            .catch(error => {
                responseDiv.innerHTML = '<strong>错误:</strong> ' + error.message;
            });
    }
    
    // 页面加载时自动加载API文档
    document.addEventListener('DOMContentLoaded', function() {
        loadAPIDocumentation();
    });
</script>
{{end}}
