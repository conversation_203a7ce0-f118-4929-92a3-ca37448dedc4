import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Tabs,
  Space,
  Button,
  Alert,
  Statistic,
  Card
} from 'antd';
import {
  ReloadOutlined,
  SettingOutlined,
  BellOutlined,
  DashboardOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { MonitoringChart } from '@/components/Charts/MonitoringChart';
import { AlertManager } from '@/components/AlertManager';
import { MetricCard } from '@/components/MetricCard';
import {
  useMonitoringMetrics,
  useAlerts,
  useAlertRules,
  useCreateAlertRule,
  useUpdateAlertRule,
  useDeleteAlertRule,
  useAcknowledgeAlert,
  useResolveAlert,
  useSilenceAlert,
  useAppState,
  useNotifications
} from '@/hooks';
import type { Alert, AlertRule } from '@/components/AlertManager';

/**
 * 监控告警页面
 *
 * 功能特性：
 * - 监控指标展示
 * - 实时图表监控
 * - 告警管理
 * - 告警规则配置
 * - 监控概览
 */
const Monitoring: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState<'1h' | '6h' | '24h' | '7d' | '30d'>('1h');
  const [refreshing, setRefreshing] = useState(false);

  const { setPageTitle } = useAppState();
  const { showSuccess, showError } = useNotifications();

  // 获取监控数据
  const {
    data: metricsData,
    isLoading: metricsLoading,
    refetch: refetchMetrics
  } = useMonitoringMetrics({
    timeRange,
    refetchInterval: 30000, // 30秒刷新
  });

  // 获取告警数据
  const {
    data: alerts = [],
    isLoading: alertsLoading,
    refetch: refetchAlerts
  } = useAlerts({
    refetchInterval: 10000, // 10秒刷新
  });

  // 获取告警规则
  const {
    data: alertRules = [],
    isLoading: rulesLoading,
    refetch: refetchRules
  } = useAlertRules();

  // 告警操作
  const acknowledgeAlertMutation = useAcknowledgeAlert({
    onSuccess: () => {
      showSuccess('告警确认成功', '告警已标记为已确认');
      refetchAlerts();
    },
    onError: (error: any) => {
      showError('告警确认失败', error.message);
    },
  });

  const resolveAlertMutation = useResolveAlert({
    onSuccess: () => {
      showSuccess('告警解决成功', '告警已标记为已解决');
      refetchAlerts();
    },
    onError: (error: any) => {
      showError('告警解决失败', error.message);
    },
  });

  const silenceAlertMutation = useSilenceAlert({
    onSuccess: () => {
      showSuccess('告警静默成功', '告警已静默');
      refetchAlerts();
    },
    onError: (error: any) => {
      showError('告警静默失败', error.message);
    },
  });

  // 规则操作
  const createRuleMutation = useCreateAlertRule({
    onSuccess: () => {
      showSuccess('规则创建成功', '告警规则已创建');
      refetchRules();
    },
    onError: (error: any) => {
      showError('规则创建失败', error.message);
    },
  });

  const updateRuleMutation = useUpdateAlertRule({
    onSuccess: () => {
      showSuccess('规则更新成功', '告警规则已更新');
      refetchRules();
    },
    onError: (error: any) => {
      showError('规则更新失败', error.message);
    },
  });

  const deleteRuleMutation = useDeleteAlertRule({
    onSuccess: () => {
      showSuccess('规则删除成功', '告警规则已删除');
      refetchRules();
    },
    onError: (error: any) => {
      showError('规则删除失败', error.message);
    },
  });

  // 设置页面标题
  useEffect(() => {
    setPageTitle('监控告警 - K8s-Helper');
  }, [setPageTitle]);

  // 手动刷新所有数据
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        refetchMetrics(),
        refetchAlerts(),
        refetchRules(),
      ]);
      showSuccess('刷新成功', '监控数据已更新');
    } catch (error) {
      showError('刷新失败', '无法获取最新数据');
    } finally {
      setRefreshing(false);
    }
  };

  // 构建监控概览指标
  const buildOverviewMetrics = () => {
    const activeAlerts = alerts.filter(alert => alert.status === 'active');
    const criticalAlerts = activeAlerts.filter(alert => alert.level === 'critical');
    const warningAlerts = activeAlerts.filter(alert => alert.level === 'warning');
    const enabledRules = alertRules.filter(rule => rule.enabled);

    return [
      {
        title: '活跃告警',
        value: activeAlerts.length,
        prefix: <BellOutlined />,
        status: criticalAlerts.length > 0 ? 'error' : warningAlerts.length > 0 ? 'warning' : 'healthy',
        description: `${criticalAlerts.length} 严重, ${warningAlerts.length} 警告`,
      },
      {
        title: '告警规则',
        value: enabledRules.length,
        suffix: `/${alertRules.length}`,
        prefix: <WarningOutlined />,
        status: 'healthy',
        description: '已启用的告警规则',
      },
      {
        title: '监控指标',
        value: metricsData?.metrics?.length || 0,
        prefix: <DashboardOutlined />,
        status: 'healthy',
        description: '正在监控的指标数量',
      },
      {
        title: '系统健康度',
        value: `${Math.max(0, 100 - activeAlerts.length * 10)}%`,
        prefix: '💚',
        status: activeAlerts.length === 0 ? 'healthy' : activeAlerts.length < 3 ? 'warning' : 'error',
        description: '基于活跃告警数量计算',
      },
    ];
  };

  // 处理告警操作
  const handleAlertAcknowledge = (alertId: string) => {
    acknowledgeAlertMutation.mutate(alertId);
  };

  const handleAlertResolve = (alertId: string) => {
    resolveAlertMutation.mutate(alertId);
  };

  const handleAlertSilence = (alertId: string, duration: number) => {
    silenceAlertMutation.mutate({ alertId, duration });
  };

  // 处理规则操作
  const handleRuleCreate = (rule: Omit<AlertRule, 'id' | 'createdAt' | 'updatedAt'>) => {
    createRuleMutation.mutate(rule);
  };

  const handleRuleUpdate = (ruleId: string, rule: Partial<AlertRule>) => {
    updateRuleMutation.mutate({ ruleId, ...rule });
  };

  const handleRuleDelete = (ruleId: string) => {
    deleteRuleMutation.mutate(ruleId);
  };

  const handleRuleToggle = (ruleId: string, enabled: boolean) => {
    updateRuleMutation.mutate({ ruleId, enabled });
  };

  // Tab配置
  const tabItems = [
    {
      key: 'overview',
      label: '监控概览',
      children: (
        <div>
          {/* 概览指标 */}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            {buildOverviewMetrics().map((metric, index) => (
              <Col key={index} xs={24} sm={12} md={6}>
                <MetricCard {...metric} />
              </Col>
            ))}
          </Row>

          {/* 监控图表 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <MonitoringChart
                title="CPU使用率"
                data={metricsData?.cpu || []}
                loading={metricsLoading}
                type="area"
                color="#1890ff"
                unit="%"
                max={100}
                timeRange={timeRange}
                onTimeRangeChange={setTimeRange}
                onRefresh={refetchMetrics}
              />
            </Col>
            <Col xs={24} lg={12}>
              <MonitoringChart
                title="内存使用率"
                data={metricsData?.memory || []}
                loading={metricsLoading}
                type="area"
                color="#52c41a"
                unit="%"
                max={100}
                timeRange={timeRange}
                onTimeRangeChange={setTimeRange}
                onRefresh={refetchMetrics}
              />
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
            <Col xs={24} lg={12}>
              <MonitoringChart
                title="网络流量"
                data={metricsData?.network || []}
                loading={metricsLoading}
                type="line"
                color="#722ed1"
                unit="MB/s"
                timeRange={timeRange}
                onTimeRangeChange={setTimeRange}
                onRefresh={refetchMetrics}
              />
            </Col>
            <Col xs={24} lg={12}>
              <MonitoringChart
                title="磁盘I/O"
                data={metricsData?.disk || []}
                loading={metricsLoading}
                type="line"
                color="#fa8c16"
                unit="MB/s"
                timeRange={timeRange}
                onTimeRangeChange={setTimeRange}
                onRefresh={refetchMetrics}
              />
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: 'alerts',
      label: '告警管理',
      children: (
        <AlertManager
          alerts={alerts}
          rules={alertRules}
          loading={alertsLoading || rulesLoading}
          onAlertAcknowledge={handleAlertAcknowledge}
          onAlertResolve={handleAlertResolve}
          onAlertSilence={handleAlertSilence}
          onRuleCreate={handleRuleCreate}
          onRuleUpdate={handleRuleUpdate}
          onRuleDelete={handleRuleDelete}
          onRuleToggle={handleRuleToggle}
        />
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f7fa' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
            监控告警
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            实时监控系统状态和管理告警规则
          </p>
        </div>

        <Space>
          <Button
            icon={<ReloadOutlined />}
            loading={refreshing}
            onClick={handleRefresh}
          >
            刷新
          </Button>
          <Button icon={<SettingOutlined />}>
            监控设置
          </Button>
        </Space>
      </div>

      {/* 告警状态提示 */}
      {alerts.filter(alert => alert.status === 'active' && alert.level === 'critical').length > 0 && (
        <Alert
          message="严重告警"
          description={`检测到 ${alerts.filter(alert => alert.status === 'active' && alert.level === 'critical').length} 个严重告警，请及时处理`}
          type="error"
          showIcon
          closable
          style={{ marginBottom: '24px' }}
        />
      )}

      {alerts.filter(alert => alert.status === 'active' && alert.level === 'warning').length > 0 && (
        <Alert
          message="警告告警"
          description={`检测到 ${alerts.filter(alert => alert.status === 'active' && alert.level === 'warning').length} 个警告告警，建议关注`}
          type="warning"
          showIcon
          closable
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* Tab内容 */}
      <Card
        style={{
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
        bodyStyle={{ padding: '0' }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
          style={{ padding: '0 24px' }}
          tabBarStyle={{ marginBottom: 0 }}
        />
      </Card>
    </div>
  );
};

export default Monitoring;
