{{define "monitoring"}}
<div class="module-content">
    <div class="monitoring-container">
        <!-- 监控概览区域 -->
        <div class="section monitoring-overview">
            <h3>📊 监控概览</h3>
            <div class="section-content">
                <div class="overview-metrics">
                    <div class="metric-card">
                        <div class="metric-icon">🖥️</div>
                        <div class="metric-content">
                            <h4>CPU使用率</h4>
                            <div id="cpu-usage" class="metric-value">--</div>
                            <div class="metric-trend">
                                <span id="cpu-trend" class="trend-indicator">--</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">💾</div>
                        <div class="metric-content">
                            <h4>内存使用率</h4>
                            <div id="memory-usage" class="metric-value">--</div>
                            <div class="metric-trend">
                                <span id="memory-trend" class="trend-indicator">--</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">💿</div>
                        <div class="metric-content">
                            <h4>磁盘使用率</h4>
                            <div id="disk-usage" class="metric-value">--</div>
                            <div class="metric-trend">
                                <span id="disk-trend" class="trend-indicator">--</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">🌐</div>
                        <div class="metric-content">
                            <h4>网络IO</h4>
                            <div id="network-io" class="metric-value">--</div>
                            <div class="metric-trend">
                                <span id="network-trend" class="trend-indicator">--</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Kubernetes状态区域 -->
        <div class="section k8s-status">
            <h3>☸️ Kubernetes 状态</h3>
            <div class="section-content">
                <div class="k8s-metrics">
                    <div class="k8s-metric">
                        <span class="metric-label">运行中Pod:</span>
                        <span id="pods-running" class="metric-value">--</span>
                    </div>
                    <div class="k8s-metric">
                        <span class="metric-label">等待中Pod:</span>
                        <span id="pods-pending" class="metric-value">--</span>
                    </div>
                    <div class="k8s-metric">
                        <span class="metric-label">失败Pod:</span>
                        <span id="pods-failed" class="metric-value">--</span>
                    </div>
                    <div class="k8s-metric">
                        <span class="metric-label">就绪节点:</span>
                        <span id="nodes-ready" class="metric-value">--</span>
                    </div>
                    <div class="k8s-metric">
                        <span class="metric-label">未就绪节点:</span>
                        <span id="nodes-not-ready" class="metric-value">--</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 健康检查区域 -->
        <div class="section health-check">
            <h3>🏥 健康检查</h3>
            <div class="section-content">
                <div class="health-overall">
                    <div class="overall-status">
                        <span class="status-label">整体状态:</span>
                        <span id="overall-status" class="status-indicator">--</span>
                    </div>
                    <div class="uptime">
                        <span class="uptime-label">运行时间:</span>
                        <span id="system-uptime" class="uptime-value">--</span>
                    </div>
                </div>
                
                <div class="health-components">
                    <div class="component-item">
                        <span class="component-name">Kubernetes API</span>
                        <span id="k8s-api-health" class="component-status">--</span>
                        <span id="k8s-api-response" class="response-time">--</span>
                    </div>
                    <div class="component-item">
                        <span class="component-name">ETCD</span>
                        <span id="etcd-health" class="component-status">--</span>
                        <span id="etcd-response" class="response-time">--</span>
                    </div>
                    <div class="component-item">
                        <span class="component-name">数据库</span>
                        <span id="database-health" class="component-status">--</span>
                        <span id="database-response" class="response-time">--</span>
                    </div>
                    <div class="component-item">
                        <span class="component-name">外部服务</span>
                        <span id="external-health" class="component-status">--</span>
                        <span id="external-response" class="response-time">--</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 性能图表区域 -->
        <div class="section performance-charts">
            <h3>📈 性能图表</h3>
            <div class="section-content">
                <div class="charts-grid">
                    <div class="chart-container">
                        <h4>CPU使用率趋势</h4>
                        <div id="cpu-chart" class="chart-placeholder">
                            <div class="chart-content">
                                <div class="chart-bars" id="cpu-bars"></div>
                                <div class="chart-labels">
                                    <span>1h前</span><span>现在</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <h4>内存使用率趋势</h4>
                        <div id="memory-chart" class="chart-placeholder">
                            <div class="chart-content">
                                <div class="chart-bars" id="memory-bars"></div>
                                <div class="chart-labels">
                                    <span>1h前</span><span>现在</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <h4>网络流量</h4>
                        <div id="network-chart" class="chart-placeholder">
                            <div class="chart-content">
                                <div class="network-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">入站:</span>
                                        <span id="network-inbound" class="stat-value">--</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">出站:</span>
                                        <span id="network-outbound" class="stat-value">--</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">总计:</span>
                                        <span id="network-total" class="stat-value">--</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <h4>操作统计</h4>
                        <div id="operations-chart" class="chart-placeholder">
                            <div class="chart-content">
                                <div class="operations-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">总请求:</span>
                                        <span id="total-requests" class="stat-value">--</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">成功:</span>
                                        <span id="successful-requests" class="stat-value">--</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">失败:</span>
                                        <span id="failed-requests" class="stat-value">--</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">响应时间:</span>
                                        <span id="avg-response-time" class="stat-value">--</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 告警管理区域 -->
        <div class="section alerts-management">
            <h3>🚨 告警管理</h3>
            <div class="section-content">
                <div class="alerts-controls">
                    <div class="controls-left">
                        <button id="refresh-alerts" class="btn btn-secondary">
                            <span class="loading" id="alerts-loading" style="display: none;"></span>
                            🔄 刷新告警
                        </button>
                        <button id="create-alert" class="btn btn-primary">
                            ➕ 创建告警
                        </button>
                    </div>
                    <div class="controls-right">
                        <select id="alert-severity-filter" class="form-control">
                            <option value="">所有级别</option>
                            <option value="critical">严重</option>
                            <option value="warning">警告</option>
                            <option value="info">信息</option>
                        </select>
                        <select id="alert-status-filter" class="form-control">
                            <option value="">所有状态</option>
                            <option value="active">活跃</option>
                            <option value="resolved">已解决</option>
                        </select>
                    </div>
                </div>
                
                <div class="alerts-list" id="alerts-list">
                    <div class="loading-placeholder">
                        <p>正在加载告警信息...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 监控配置区域 -->
        <div class="section monitoring-config">
            <h3>⚙️ 监控配置</h3>
            <div class="section-content">
                <div class="config-options">
                    <div class="config-group">
                        <h4>刷新间隔</h4>
                        <select id="refresh-interval" class="form-control">
                            <option value="5">5秒</option>
                            <option value="10" selected>10秒</option>
                            <option value="30">30秒</option>
                            <option value="60">1分钟</option>
                        </select>
                    </div>
                    
                    <div class="config-group">
                        <h4>告警通知</h4>
                        <label class="checkbox-label">
                            <input type="checkbox" id="enable-notifications" checked />
                            <span>启用浏览器通知</span>
                        </label>
                    </div>
                    
                    <div class="config-group">
                        <h4>自动刷新</h4>
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-refresh" checked />
                            <span>自动刷新数据</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 告警创建对话框 -->
    <div id="create-alert-dialog" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>➕ 创建告警</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="create-alert-form">
                    <div class="form-group">
                        <label for="alert-name" class="form-label">告警名称:</label>
                        <input type="text" id="alert-name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="alert-severity" class="form-label">严重级别:</label>
                        <select id="alert-severity" class="form-control" required>
                            <option value="info">信息</option>
                            <option value="warning">警告</option>
                            <option value="critical">严重</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="alert-description" class="form-label">描述:</label>
                        <textarea id="alert-description" class="form-control" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="alert-tags" class="form-label">标签 (逗号分隔):</label>
                        <input type="text" id="alert-tags" class="form-control" placeholder="例如: cpu, performance">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="submit-alert" class="btn btn-primary">创建</button>
                <button id="cancel-alert" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>
</div>
{{end}}
