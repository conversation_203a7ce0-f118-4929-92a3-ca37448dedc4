import { useEffect, useCallback, useRef } from 'react';

// 快捷键配置类型
export interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  meta?: boolean;
  action: () => void;
  description?: string;
  disabled?: boolean;
}

// 快捷键组合类型
export type KeyCombination = {
  key: string;
  modifiers: string[];
};

/**
 * 快捷键管理Hook
 * 
 * 功能特性：
 * - 支持组合键
 * - 快捷键冲突检测
 * - 动态启用/禁用
 * - 快捷键帮助信息
 */
export const useKeyboard = (shortcuts: KeyboardShortcut[]) => {
  const shortcutsRef = useRef<KeyboardShortcut[]>(shortcuts);
  const activeKeysRef = useRef<Set<string>>(new Set());

  // 更新快捷键配置
  useEffect(() => {
    shortcutsRef.current = shortcuts;
  }, [shortcuts]);

  // 标准化按键名称
  const normalizeKey = useCallback((key: string): string => {
    const keyMap: Record<string, string> = {
      ' ': 'Space',
      'ArrowUp': 'Up',
      'ArrowDown': 'Down',
      'ArrowLeft': 'Left',
      'ArrowRight': 'Right',
      'Escape': 'Esc',
      'Delete': 'Del',
    };
    return keyMap[key] || key.toLowerCase();
  }, []);

  // 生成快捷键字符串
  const generateShortcutString = useCallback((shortcut: KeyboardShortcut): string => {
    const modifiers: string[] = [];
    if (shortcut.ctrl) modifiers.push('ctrl');
    if (shortcut.alt) modifiers.push('alt');
    if (shortcut.shift) modifiers.push('shift');
    if (shortcut.meta) modifiers.push('meta');
    
    return [...modifiers, normalizeKey(shortcut.key)].join('+');
  }, [normalizeKey]);

  // 检查快捷键是否匹配
  const isShortcutMatch = useCallback((event: KeyboardEvent, shortcut: KeyboardShortcut): boolean => {
    if (shortcut.disabled) return false;
    
    const eventKey = normalizeKey(event.key);
    const shortcutKey = normalizeKey(shortcut.key);
    
    return (
      eventKey === shortcutKey &&
      !!event.ctrlKey === !!shortcut.ctrl &&
      !!event.altKey === !!shortcut.alt &&
      !!event.shiftKey === !!shortcut.shift &&
      !!event.metaKey === !!shortcut.meta
    );
  }, [normalizeKey]);

  // 处理按键按下
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // 忽略在输入框中的按键
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true'
    ) {
      return;
    }

    const key = normalizeKey(event.key);
    activeKeysRef.current.add(key);

    // 检查是否匹配任何快捷键
    for (const shortcut of shortcutsRef.current) {
      if (isShortcutMatch(event, shortcut)) {
        event.preventDefault();
        event.stopPropagation();
        shortcut.action();
        break;
      }
    }
  }, [isShortcutMatch]);

  // 处理按键释放
  const handleKeyUp = useCallback((event: KeyboardEvent) => {
    const key = normalizeKey(event.key);
    activeKeysRef.current.delete(key);
  }, [normalizeKey]);

  // 绑定事件监听器
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleKeyDown, handleKeyUp]);

  // 获取快捷键帮助信息
  const getShortcutHelp = useCallback((): Array<{ shortcut: string; description: string }> => {
    return shortcutsRef.current
      .filter(shortcut => !shortcut.disabled && shortcut.description)
      .map(shortcut => ({
        shortcut: generateShortcutString(shortcut),
        description: shortcut.description || '',
      }));
  }, [generateShortcutString]);

  // 检查快捷键冲突
  const checkConflicts = useCallback((): string[] => {
    const conflicts: string[] = [];
    const shortcutMap = new Map<string, number>();

    shortcutsRef.current.forEach((shortcut, index) => {
      if (!shortcut.disabled) {
        const shortcutString = generateShortcutString(shortcut);
        if (shortcutMap.has(shortcutString)) {
          conflicts.push(`Conflict: ${shortcutString} (shortcuts ${shortcutMap.get(shortcutString)} and ${index})`);
        } else {
          shortcutMap.set(shortcutString, index);
        }
      }
    });

    return conflicts;
  }, [generateShortcutString]);

  return {
    getShortcutHelp,
    checkConflicts,
    activeKeys: Array.from(activeKeysRef.current),
  };
};

/**
 * 全局快捷键Hook
 * 用于应用级别的快捷键管理
 */
export const useGlobalKeyboard = () => {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: '/',
      action: () => {
        // 聚焦到搜索框
        const searchInput = document.querySelector('input[placeholder*="搜索"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      },
      description: '聚焦搜索框',
    },
    {
      key: 'k',
      ctrl: true,
      action: () => {
        // 打开命令面板
        console.log('Open command palette');
      },
      description: '打开命令面板',
    },
    {
      key: 'r',
      ctrl: true,
      action: () => {
        // 刷新页面
        window.location.reload();
      },
      description: '刷新页面',
    },
    {
      key: 'h',
      ctrl: true,
      action: () => {
        // 显示快捷键帮助
        console.log('Show keyboard shortcuts help');
      },
      description: '显示快捷键帮助',
    },
    {
      key: 'Escape',
      action: () => {
        // 关闭模态框或取消操作
        const closeButtons = document.querySelectorAll('[aria-label="Close"], .ant-modal-close');
        if (closeButtons.length > 0) {
          (closeButtons[closeButtons.length - 1] as HTMLElement).click();
        }
      },
      description: '关闭模态框',
    },
  ];

  return useKeyboard(shortcuts);
};

/**
 * 表格快捷键Hook
 * 用于表格组件的快捷键管理
 */
export const useTableKeyboard = (options: {
  onRefresh?: () => void;
  onAdd?: () => void;
  onDelete?: () => void;
  onEdit?: () => void;
}) => {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'F5',
      action: () => options.onRefresh?.(),
      description: '刷新表格',
      disabled: !options.onRefresh,
    },
    {
      key: 'n',
      ctrl: true,
      action: () => options.onAdd?.(),
      description: '新增记录',
      disabled: !options.onAdd,
    },
    {
      key: 'Delete',
      action: () => options.onDelete?.(),
      description: '删除选中项',
      disabled: !options.onDelete,
    },
    {
      key: 'Enter',
      action: () => options.onEdit?.(),
      description: '编辑选中项',
      disabled: !options.onEdit,
    },
  ];

  return useKeyboard(shortcuts);
};

/**
 * 表单快捷键Hook
 * 用于表单组件的快捷键管理
 */
export const useFormKeyboard = (options: {
  onSave?: () => void;
  onCancel?: () => void;
  onReset?: () => void;
}) => {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 's',
      ctrl: true,
      action: () => options.onSave?.(),
      description: '保存表单',
      disabled: !options.onSave,
    },
    {
      key: 'Escape',
      action: () => options.onCancel?.(),
      description: '取消操作',
      disabled: !options.onCancel,
    },
    {
      key: 'r',
      ctrl: true,
      shift: true,
      action: () => options.onReset?.(),
      description: '重置表单',
      disabled: !options.onReset,
    },
  ];

  return useKeyboard(shortcuts);
};

// 快捷键帮助组件数据
export const getKeyboardShortcutsHelp = () => {
  return [
    { category: '全局', shortcuts: [
      { key: '/', description: '聚焦搜索框' },
      { key: 'Ctrl+K', description: '打开命令面板' },
      { key: 'Ctrl+R', description: '刷新页面' },
      { key: 'Ctrl+H', description: '显示快捷键帮助' },
      { key: 'Esc', description: '关闭模态框' },
    ]},
    { category: '表格', shortcuts: [
      { key: 'F5', description: '刷新表格' },
      { key: 'Ctrl+N', description: '新增记录' },
      { key: 'Delete', description: '删除选中项' },
      { key: 'Enter', description: '编辑选中项' },
    ]},
    { category: '表单', shortcuts: [
      { key: 'Ctrl+S', description: '保存表单' },
      { key: 'Esc', description: '取消操作' },
      { key: 'Ctrl+Shift+R', description: '重置表单' },
    ]},
  ];
};
