package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"k8s-helper/internal/cache"
)

// ClusterInfo 集群信息结构
type ClusterInfo struct {
	ServerVersion *ServerVersionInfo `json:"server_version"`
	Nodes         []NodeInfo         `json:"nodes"`
	Namespaces    []NamespaceInfo    `json:"namespaces"`
	Resources     *ResourceSummary   `json:"resources"`
	Timestamp     time.Time          `json:"timestamp"`
}

// ServerVersionInfo 服务端版本信息
type ServerVersionInfo struct {
	GitVersion   string `json:"git_version"`
	Major        string `json:"major"`
	Minor        string `json:"minor"`
	Platform     string `json:"platform"`
	BuildDate    string `json:"build_date"`
	GoVersion    string `json:"go_version"`
	Compiler     string `json:"compiler"`
	VersionString string `json:"version_string"`
}

// NodeInfo 节点信息
type NodeInfo struct {
	Name         string            `json:"name"`
	Status       string            `json:"status"`
	Roles        []string          `json:"roles"`
	InternalIP   string            `json:"internal_ip"`
	ExternalIP   string            `json:"external_ip"`
	Version      string            `json:"version"`
	OS           string            `json:"os"`
	Architecture string            `json:"architecture"`
	Capacity     map[string]string `json:"capacity"`
	Allocatable  map[string]string `json:"allocatable"`
	Conditions   []NodeCondition   `json:"conditions"`
	CreatedAt    time.Time         `json:"created_at"`
}

// NodeCondition 节点状态条件
type NodeCondition struct {
	Type    string    `json:"type"`
	Status  string    `json:"status"`
	Reason  string    `json:"reason"`
	Message string    `json:"message"`
	LastTransitionTime time.Time `json:"last_transition_time"`
}

// NamespaceInfo 命名空间信息
type NamespaceInfo struct {
	Name      string    `json:"name"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	Labels    map[string]string `json:"labels"`
}

// ResourceSummary 资源汇总信息
type ResourceSummary struct {
	TotalNodes      int `json:"total_nodes"`
	ReadyNodes      int `json:"ready_nodes"`
	TotalNamespaces int `json:"total_namespaces"`
	TotalPods       int `json:"total_pods"`
	RunningPods     int `json:"running_pods"`
	TotalServices   int `json:"total_services"`
}

// ClusterService 集群信息服务
type ClusterService struct {
	clientset    kubernetes.Interface
	cacheManager *cache.CacheManager
	requestMutex sync.Mutex // 防止并发请求重复获取数据
}

// NewClusterService 创建集群信息服务
func NewClusterService(clientset kubernetes.Interface) *ClusterService {
	return &ClusterService{
		clientset:    clientset,
		cacheManager: cache.NewCacheManager(),
		requestMutex: sync.Mutex{},
	}
}

// NewClusterServiceWithCache 创建带缓存的集群信息服务
func NewClusterServiceWithCache(clientset kubernetes.Interface, cacheManager *cache.CacheManager) *ClusterService {
	return &ClusterService{
		clientset:    clientset,
		cacheManager: cacheManager,
		requestMutex: sync.Mutex{},
	}
}

// GetClusterInfo 获取完整的集群信息（优化版本，使用缓存和并发）
func (cs *ClusterService) GetClusterInfo(ctx context.Context) (*ClusterInfo, error) {
	// 尝试从缓存获取完整的集群信息
	cacheKey := "cluster_info"
	if cached, exists := cs.cacheManager.Get(cacheKey); exists {
		if clusterInfo, ok := cached.(*ClusterInfo); ok {
			return clusterInfo, nil
		}
	}

	// 使用互斥锁防止并发请求重复获取数据
	cs.requestMutex.Lock()
	defer cs.requestMutex.Unlock()

	// 再次检查缓存（可能在等待锁的过程中其他请求已经更新了缓存）
	if cached, exists := cs.cacheManager.Get(cacheKey); exists {
		if clusterInfo, ok := cached.(*ClusterInfo); ok {
			return clusterInfo, nil
		}
	}

	// 设置超时上下文（最多5秒）
	timeoutCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	clusterInfo := &ClusterInfo{
		Timestamp: time.Now(),
	}

	// 使用并发获取各种信息
	var wg sync.WaitGroup
	var mu sync.Mutex
	var errors []error

	// 并发获取服务端版本信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		serverVersion, err := cs.getServerVersionCached(timeoutCtx)
		mu.Lock()
		defer mu.Unlock()
		if err != nil {
			errors = append(errors, fmt.Errorf("获取服务端版本失败: %w", err))
		} else {
			clusterInfo.ServerVersion = serverVersion
		}
	}()

	// 并发获取节点信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		nodes, err := cs.getNodesInfoCached(timeoutCtx)
		mu.Lock()
		defer mu.Unlock()
		if err != nil {
			errors = append(errors, fmt.Errorf("获取节点信息失败: %w", err))
		} else {
			clusterInfo.Nodes = nodes
		}
	}()

	// 并发获取命名空间信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		namespaces, err := cs.getNamespacesInfoCached(timeoutCtx)
		mu.Lock()
		defer mu.Unlock()
		if err != nil {
			errors = append(errors, fmt.Errorf("获取命名空间信息失败: %w", err))
		} else {
			clusterInfo.Namespaces = namespaces
		}
	}()

	// 并发获取资源汇总信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		resources, err := cs.getResourceSummaryCached(timeoutCtx)
		mu.Lock()
		defer mu.Unlock()
		if err != nil {
			errors = append(errors, fmt.Errorf("获取资源汇总失败: %w", err))
		} else {
			clusterInfo.Resources = resources
		}
	}()

	// 使用通道等待所有并发操作完成或超时
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 所有操作完成
		if len(errors) > 0 {
			// 即使有错误，也尝试返回部分数据
			if clusterInfo.ServerVersion != nil || len(clusterInfo.Nodes) > 0 {
				// 缓存部分结果（较短时间）
				cs.cacheManager.Set(cacheKey, clusterInfo, 10*time.Second)
				return clusterInfo, nil
			}
			return nil, errors[0]
		}
		// 缓存完整结果（30秒）
		cs.cacheManager.Set(cacheKey, clusterInfo, 30*time.Second)
		return clusterInfo, nil

	case <-timeoutCtx.Done():
		// 超时，返回已获取的部分数据
		if clusterInfo.ServerVersion != nil || len(clusterInfo.Nodes) > 0 {
			// 缓存部分结果（较短时间）
			cs.cacheManager.Set(cacheKey, clusterInfo, 5*time.Second)
			return clusterInfo, nil
		}
		return nil, fmt.Errorf("获取集群信息超时")
	}
}

// getServerVersionCached 获取服务端版本信息（带缓存）
func (cs *ClusterService) getServerVersionCached(ctx context.Context) (*ServerVersionInfo, error) {
	cacheKey := "server_version"

	result, err := cs.cacheManager.GetOrSet(cacheKey, 5*time.Minute, func() (interface{}, error) {
		return cs.getServerVersion(ctx)
	})

	if err != nil {
		return nil, err
	}

	return result.(*ServerVersionInfo), nil
}

// getServerVersion 获取服务端版本信息
func (cs *ClusterService) getServerVersion(ctx context.Context) (*ServerVersionInfo, error) {
	version, err := cs.clientset.Discovery().ServerVersion()
	if err != nil {
		return nil, err
	}

	return &ServerVersionInfo{
		GitVersion:    version.GitVersion,
		Major:         version.Major,
		Minor:         version.Minor,
		Platform:      version.Platform,
		BuildDate:     version.BuildDate,
		GoVersion:     version.GoVersion,
		Compiler:      version.Compiler,
		VersionString: version.String(),
	}, nil
}

// getNodesInfoCached 获取节点信息（带缓存）
func (cs *ClusterService) getNodesInfoCached(ctx context.Context) ([]NodeInfo, error) {
	cacheKey := "nodes_info"

	result, err := cs.cacheManager.GetOrSet(cacheKey, 1*time.Minute, func() (interface{}, error) {
		return cs.getNodesInfo(ctx)
	})

	if err != nil {
		return nil, err
	}

	return result.([]NodeInfo), nil
}

// getNodesInfo 获取节点信息
func (cs *ClusterService) getNodesInfo(ctx context.Context) ([]NodeInfo, error) {
	nodes, err := cs.clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	var nodeInfos []NodeInfo
	for _, node := range nodes.Items {
		nodeInfo := NodeInfo{
			Name:         node.Name,
			Status:       cs.getNodeStatus(node),
			Roles:        cs.getNodeRoles(node.Labels),
			InternalIP:   cs.getNodeIP(node.Status.Addresses, v1.NodeInternalIP),
			ExternalIP:   cs.getNodeIP(node.Status.Addresses, v1.NodeExternalIP),
			Version:      node.Status.NodeInfo.KubeletVersion,
			OS:           node.Status.NodeInfo.OperatingSystem,
			Architecture: node.Status.NodeInfo.Architecture,
			Capacity:     cs.convertResourceList(node.Status.Capacity),
			Allocatable:  cs.convertResourceList(node.Status.Allocatable),
			Conditions:   cs.convertNodeConditions(node.Status.Conditions),
			CreatedAt:    node.CreationTimestamp.Time,
		}
		nodeInfos = append(nodeInfos, nodeInfo)
	}

	return nodeInfos, nil
}

// getNamespacesInfoCached 获取命名空间信息（带缓存）
func (cs *ClusterService) getNamespacesInfoCached(ctx context.Context) ([]NamespaceInfo, error) {
	cacheKey := "namespaces_info"

	result, err := cs.cacheManager.GetOrSet(cacheKey, 2*time.Minute, func() (interface{}, error) {
		return cs.getNamespacesInfo(ctx)
	})

	if err != nil {
		return nil, err
	}

	return result.([]NamespaceInfo), nil
}

// getNamespacesInfo 获取命名空间信息
func (cs *ClusterService) getNamespacesInfo(ctx context.Context) ([]NamespaceInfo, error) {
	namespaces, err := cs.clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	var namespaceInfos []NamespaceInfo
	for _, ns := range namespaces.Items {
		namespaceInfo := NamespaceInfo{
			Name:      ns.Name,
			Status:    string(ns.Status.Phase),
			CreatedAt: ns.CreationTimestamp.Time,
			Labels:    ns.Labels,
		}
		namespaceInfos = append(namespaceInfos, namespaceInfo)
	}

	return namespaceInfos, nil
}

// getResourceSummaryCached 获取资源汇总信息（带缓存）
func (cs *ClusterService) getResourceSummaryCached(ctx context.Context) (*ResourceSummary, error) {
	cacheKey := "resource_summary"

	result, err := cs.cacheManager.GetOrSet(cacheKey, 30*time.Second, func() (interface{}, error) {
		return cs.getResourceSummary(ctx)
	})

	if err != nil {
		return nil, err
	}

	return result.(*ResourceSummary), nil
}

// getResourceSummary 获取资源汇总信息（优化版本，使用并发）
func (cs *ClusterService) getResourceSummary(ctx context.Context) (*ResourceSummary, error) {
	summary := &ResourceSummary{}

	// 使用并发获取各种资源信息
	var wg sync.WaitGroup
	var mu sync.Mutex
	var errors []error

	// 并发获取节点信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		nodes, err := cs.clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
		mu.Lock()
		defer mu.Unlock()
		if err != nil {
			errors = append(errors, err)
			return
		}
		summary.TotalNodes = len(nodes.Items)
		for _, node := range nodes.Items {
			if cs.getNodeStatus(node) == "Ready" {
				summary.ReadyNodes++
			}
		}
	}()

	// 并发获取命名空间信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		namespaces, err := cs.clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
		mu.Lock()
		defer mu.Unlock()
		if err != nil {
			errors = append(errors, err)
			return
		}
		summary.TotalNamespaces = len(namespaces.Items)
	}()

	// 并发获取Pod信息（只获取字段子集以提高性能）
	wg.Add(1)
	go func() {
		defer wg.Done()
		pods, err := cs.clientset.CoreV1().Pods("").List(ctx, metav1.ListOptions{
			FieldSelector: "status.phase!=Failed,status.phase!=Succeeded", // 过滤掉已完成的Pod
			Limit:         1000, // 限制返回数量
		})
		mu.Lock()
		defer mu.Unlock()
		if err != nil {
			errors = append(errors, err)
			return
		}
		summary.TotalPods = len(pods.Items)
		for _, pod := range pods.Items {
			if pod.Status.Phase == v1.PodRunning {
				summary.RunningPods++
			}
		}
	}()

	// 并发获取Service信息（只获取必要字段）
	wg.Add(1)
	go func() {
		defer wg.Done()
		services, err := cs.clientset.CoreV1().Services("").List(ctx, metav1.ListOptions{
			Limit: 500, // 限制返回数量
		})
		mu.Lock()
		defer mu.Unlock()
		if err != nil {
			errors = append(errors, err)
			return
		}
		summary.TotalServices = len(services.Items)
	}()

	// 等待所有并发操作完成
	wg.Wait()

	// 检查是否有错误
	if len(errors) > 0 {
		return nil, errors[0]
	}

	return summary, nil
}

// 辅助方法
func (cs *ClusterService) getNodeStatus(node v1.Node) string {
	for _, condition := range node.Status.Conditions {
		if condition.Type == v1.NodeReady {
			if condition.Status == v1.ConditionTrue {
				return "Ready"
			} else {
				return "NotReady"
			}
		}
	}
	return "Unknown"
}

func (cs *ClusterService) getNodeRoles(labels map[string]string) []string {
	var roles []string
	roleLabels := []string{
		"node-role.kubernetes.io/master",
		"node-role.kubernetes.io/control-plane",
		"node-role.kubernetes.io/worker",
		"node-role.kubernetes.io/etcd",
	}

	for _, roleLabel := range roleLabels {
		if _, exists := labels[roleLabel]; exists {
			parts := strings.Split(roleLabel, "/")
			if len(parts) > 1 {
				roles = append(roles, parts[len(parts)-1])
			}
		}
	}

	return roles
}

func (cs *ClusterService) getNodeIP(addresses []v1.NodeAddress, addressType v1.NodeAddressType) string {
	for _, addr := range addresses {
		if addr.Type == addressType {
			return addr.Address
		}
	}
	return ""
}

func (cs *ClusterService) convertResourceList(resources v1.ResourceList) map[string]string {
	result := make(map[string]string)
	for name, quantity := range resources {
		result[string(name)] = quantity.String()
	}
	return result
}

func (cs *ClusterService) convertNodeConditions(conditions []v1.NodeCondition) []NodeCondition {
	var result []NodeCondition
	for _, condition := range conditions {
		result = append(result, NodeCondition{
			Type:    string(condition.Type),
			Status:  string(condition.Status),
			Reason:  condition.Reason,
			Message: condition.Message,
			LastTransitionTime: condition.LastTransitionTime.Time,
		})
	}
	return result
}
