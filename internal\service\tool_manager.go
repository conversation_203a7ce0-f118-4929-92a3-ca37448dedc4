package service

import (
	"archive/tar"
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"time"

	"go.uber.org/zap"
	"k8s-helper/internal/domain"
	"k8s-helper/pkg/common"
)

// toolManager 工具管理器实现
type toolManager struct {
	logger *zap.Logger
}

// NewToolManager 创建工具管理器
func NewToolManager(logger *zap.Logger) domain.ToolManager {
	return &toolManager{
		logger: logger,
	}
}

// EnsureTools 确保工具可用（使用默认配置）
func (tm *toolManager) EnsureTools(ctx context.Context) error {
	// 首先软检查工具是否可用
	if err := tm.checkToolsSoft(); err != nil {
		tm.logger.Warn("系统中未找到etcd工具，尝试下载", zap.Error(err))

		// 只有在软检查失败时才尝试下载
		if err := tm.downloadTools(ctx); err != nil {
			return fmt.Errorf("无法获取etcd工具: %w", err)
		}
	}
	return nil
}

// EnsureToolsWithConfig 确保工具可用（使用自定义配置）
func (tm *toolManager) EnsureToolsWithConfig(ctx context.Context, baseURL, version, os string) error {
	// 首先软检查工具是否可用
	if err := tm.checkToolsSoft(); err != nil {
		tm.logger.Warn("系统中未找到etcd工具，尝试使用自定义配置下载",
			zap.Error(err),
			zap.String("baseURL", baseURL),
			zap.String("version", version),
			zap.String("os", os))

		// 使用自定义配置下载
		if err := tm.DownloadTools(ctx, baseURL, version, os); err != nil {
			return fmt.Errorf("无法获取etcd工具: %w", err)
		}
	}
	return nil
}

// GetBackupRestoreTool 获取备份恢复工具路径
func (tm *toolManager) GetBackupRestoreTool() (string, error) {
	// 优先检查 etcdutl（更新的工具）
	if path, err := exec.LookPath("etcdutl"); err == nil {
		tm.logger.Debug("找到etcdutl工具", zap.String("path", path))
		return path, nil
	}

	// 备选检查 etcdctl（向后兼容）
	if path, err := exec.LookPath("etcdctl"); err == nil {
		tm.logger.Debug("找到etcdctl工具", zap.String("path", path))
		return path, nil
	}

	return "", fmt.Errorf("未找到可用的ETCD工具（etcdutl或etcdctl）")
}

// GetVerifyTool 获取验证工具路径
func (tm *toolManager) GetVerifyTool() (string, error) {
	// 验证操作专门使用 etcdutl
	if path, err := exec.LookPath("etcdutl"); err == nil {
		tm.logger.Debug("找到etcdutl验证工具", zap.String("path", path))
		return path, nil
	}

	return "", fmt.Errorf("未找到etcdutl工具（验证操作专用）")
}

// DownloadTools 下载工具
func (tm *toolManager) DownloadTools(ctx context.Context, baseURL, version, osType string) error {
	downloadDir := "/tmp/etcd_download"
	installDir := "/usr/local/bin"

	// 检测系统架构
	arch := runtime.GOARCH
	tm.logger.Info("检测到系统架构", zap.String("arch", arch))

	// 验证架构支持
	if arch != common.EtcdArchAMD64 && arch != common.EtcdArchARM64 {
		return fmt.Errorf("不支持的系统架构: %s，支持的架构: %s, %s",
			arch, common.EtcdArchAMD64, common.EtcdArchARM64)
	}

	// 生成下载 URL
	downloadURL := tm.generateDownloadURL(baseURL, version, osType, arch)
	tm.logger.Info("准备下载ETCD工具", zap.String("url", downloadURL))

	// 创建下载目录
	if err := os.MkdirAll(downloadDir, 0755); err != nil {
		return fmt.Errorf("创建下载目录失败: %w", err)
	}
	defer os.RemoveAll(downloadDir)

	// 下载文件
	tarFilename := filepath.Base(downloadURL)
	tarPath := filepath.Join(downloadDir, tarFilename)

	if err := tm.downloadFile(downloadURL, tarPath); err != nil {
		return fmt.Errorf("下载失败: %w", err)
	}

	// 解压并安装 ETCD 工具
	installedTools, err := tm.extractAndInstallTools(tarPath, downloadDir, installDir)
	if err != nil {
		return fmt.Errorf("解压安装失败: %w", err)
	}

	for _, tool := range installedTools {
		tm.logger.Info("工具安装成功",
			zap.String("name", tool.Name),
			zap.String("path", tool.Path))
	}

	return nil
}

// checkToolsSoft 软检查工具是否可用，不自动下载
func (tm *toolManager) checkToolsSoft() error {
	// 优先检查 etcdutl（更新的工具）
	if _, err := exec.LookPath("etcdutl"); err == nil {
		return nil
	}

	// 备选检查 etcdctl（向后兼容）
	if _, err := exec.LookPath("etcdctl"); err == nil {
		return nil
	}

	return fmt.Errorf("系统中未找到 etcdctl 或 etcdutl 工具")
}

// downloadTools 下载工具的内部实现
func (tm *toolManager) downloadTools(ctx context.Context) error {
	// 使用默认配置下载
	return tm.DownloadTools(ctx,
		common.DefaultEtcdBaseURL,
		common.DefaultEtcdVersion,
		common.DefaultEtcdOS)
}

// generateDownloadURL 生成下载URL
func (tm *toolManager) generateDownloadURL(baseURL, version, os, arch string) string {
	return fmt.Sprintf("%s/etcd-%s-%s-%s.tar.gz", baseURL, version, os, arch)
}

// downloadFile 下载文件
func (tm *toolManager) downloadFile(url, filepath string) error {
	tm.logger.Info("开始下载文件", zap.String("url", url), zap.String("path", filepath))

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Minute,
	}

	// 发送下载请求
	resp, err := client.Get(url)
	if err != nil {
		return fmt.Errorf("下载请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 创建目标文件
	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	// 下载文件内容
	written, err := io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	tm.logger.Info("文件下载完成",
		zap.String("path", filepath),
		zap.Int64("size", written))

	return nil
}

// ToolInfo 工具信息
type ToolInfo struct {
	Name string
	Path string
}

// extractAndInstallTools 解压并安装工具
func (tm *toolManager) extractAndInstallTools(tarPath, downloadDir, installDir string) ([]ToolInfo, error) {
	tm.logger.Info("开始解压安装工具",
		zap.String("tarPath", tarPath),
		zap.String("downloadDir", downloadDir),
		zap.String("installDir", installDir))

	// 确保安装目录存在
	if err := os.MkdirAll(installDir, 0755); err != nil {
		return nil, fmt.Errorf("创建安装目录失败: %w", err)
	}

	// 打开tar.gz文件
	file, err := os.Open(tarPath)
	if err != nil {
		return nil, fmt.Errorf("打开tar文件失败: %w", err)
	}
	defer file.Close()

	// 创建gzip读取器
	gzReader, err := gzip.NewReader(file)
	if err != nil {
		return nil, fmt.Errorf("创建gzip读取器失败: %w", err)
	}
	defer gzReader.Close()

	// 创建tar读取器
	tarReader := tar.NewReader(gzReader)

	var installedTools []ToolInfo
	targetTools := []string{"etcdctl", "etcdutl"}

	// 遍历tar文件中的条目
	for {
		header, err := tarReader.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("读取tar条目失败: %w", err)
		}

		// 检查是否是我们需要的工具
		fileName := filepath.Base(header.Name)
		isTargetTool := false
		for _, tool := range targetTools {
			if fileName == tool {
				isTargetTool = true
				break
			}
		}

		if !isTargetTool || header.Typeflag != tar.TypeReg {
			continue
		}

		// 创建目标文件路径
		targetPath := filepath.Join(installDir, fileName)

		// 创建目标文件
		targetFile, err := os.Create(targetPath)
		if err != nil {
			return nil, fmt.Errorf("创建目标文件失败: %w", err)
		}

		// 复制文件内容
		_, err = io.Copy(targetFile, tarReader)
		targetFile.Close()
		if err != nil {
			return nil, fmt.Errorf("复制文件内容失败: %w", err)
		}

		// 设置执行权限
		if err := os.Chmod(targetPath, 0755); err != nil {
			return nil, fmt.Errorf("设置执行权限失败: %w", err)
		}

		installedTools = append(installedTools, ToolInfo{
			Name: fileName,
			Path: targetPath,
		})

		tm.logger.Info("工具安装成功",
			zap.String("tool", fileName),
			zap.String("path", targetPath))
	}

	if len(installedTools) == 0 {
		return nil, fmt.Errorf("未找到任何可安装的工具")
	}

	tm.logger.Info("所有工具安装完成", zap.Int("count", len(installedTools)))
	return installedTools, nil
}
