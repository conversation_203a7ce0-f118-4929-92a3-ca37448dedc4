import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Select,
  Checkbox,
  Tag,
  Tooltip,
  Alert,
  Progress,
  Statistic,
  Divider,
  Input,
  InputNumber,
  Switch,
  message
} from 'antd';
import {
  DeleteOutlined,
  ReloadOutlined,
  ScanOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  ToolOutlined,
  PlusOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { DataTable, StatusTag } from '@/components/DataTable';
import {
  useResourceScan,
  useCleanupResources,
  useCleanupRules,
  useCreateCleanupRule,
  useAppState,
  useNotifications
} from '@/hooks';

const { Option } = Select;

// 资源类型
export type ResourceType = 'pod' | 'service' | 'deployment' | 'configmap' | 'secret' | 'pvc' | 'job';

// 清理状态
export type CleanupStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

// 资源项
export interface ResourceItem {
  id: string;
  name: string;
  namespace: string;
  type: ResourceType;
  age: string;
  ageInDays: number;
  status: string;
  size?: string;
  lastUsed?: string;
  labels: Record<string, string>;
  annotations: Record<string, string>;
  canDelete: boolean;
  reason?: string;
}

// 清理规则
export interface CleanupRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  resourceTypes: ResourceType[];
  namespaces: string[];
  conditions: {
    maxAge?: number; // 天数
    status?: string[];
    labels?: Record<string, string>;
    annotations?: Record<string, string>;
  };
  dryRun: boolean;
  createdAt: string;
}

// 清理任务
export interface CleanupTask {
  id: string;
  name: string;
  status: CleanupStatus;
  totalResources: number;
  processedResources: number;
  deletedResources: number;
  failedResources: number;
  startTime: string;
  endTime?: string;
  error?: string;
}

/**
 * 资源清理管理页面
 * 
 * 功能特性：
 * - 资源扫描和分析
 * - 清理规则配置
 * - 批量清理操作
 * - 操作确认和审计
 * - 清理任务监控
 */
const ResourceCleanup: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'scan' | 'rules' | 'tasks'>('scan');
  const [scanModalVisible, setScanModalVisible] = useState(false);
  const [cleanupModalVisible, setCleanupModalVisible] = useState(false);
  const [ruleModalVisible, setRuleModalVisible] = useState(false);
  const [selectedResources, setSelectedResources] = useState<string[]>([]);
  const [scanForm] = Form.useForm();
  const [ruleForm] = Form.useForm();
  
  const { setPageTitle } = useAppState();
  const { showSuccess, showError, showWarning } = useNotifications();

  // 获取扫描结果
  const { 
    data: scanResult, 
    isLoading: scanLoading, 
    refetch: refetchScan 
  } = useResourceScan();

  // 获取清理规则
  const { 
    data: cleanupRules = [], 
    isLoading: rulesLoading, 
    refetch: refetchRules 
  } = useCleanupRules();

  // 清理资源
  const cleanupMutation = useCleanupResources({
    onSuccess: (result: any) => {
      showSuccess('清理完成', `成功清理 ${result.deleted} 个资源`);
      setCleanupModalVisible(false);
      setSelectedResources([]);
      refetchScan();
    },
    onError: (error: any) => {
      showError('清理失败', error.message);
    },
  });

  // 创建清理规则
  const createRuleMutation = useCreateCleanupRule({
    onSuccess: () => {
      showSuccess('规则创建成功', '清理规则已创建');
      setRuleModalVisible(false);
      ruleForm.resetFields();
      refetchRules();
    },
    onError: (error: any) => {
      showError('规则创建失败', error.message);
    },
  });

  // 设置页面标题
  useEffect(() => {
    setPageTitle('资源清理 - K8s-Helper');
  }, [setPageTitle]);

  // 资源类型配置
  const resourceTypeConfig = {
    pod: { color: 'blue', text: 'Pod' },
    service: { color: 'green', text: 'Service' },
    deployment: { color: 'purple', text: 'Deployment' },
    configmap: { color: 'orange', text: 'ConfigMap' },
    secret: { color: 'red', text: 'Secret' },
    pvc: { color: 'cyan', text: 'PVC' },
    job: { color: 'magenta', text: 'Job' },
  };

  // 资源表格列定义
  const resourceColumns: ColumnsType<ResourceItem> = [
    {
      title: '资源名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: ResourceItem) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.namespace}
          </div>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: ResourceType) => (
        <Tag color={resourceTypeConfig[type].color}>
          {resourceTypeConfig[type].text}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === 'Running' ? 'green' : status === 'Failed' ? 'red' : 'default'}>
          {status}
        </Tag>
      ),
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      width: 100,
      render: (age: string, record: ResourceItem) => (
        <div>
          <div>{age}</div>
          {record.ageInDays > 30 && (
            <Tag color="orange" size="small">老旧</Tag>
          )}
        </div>
      ),
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
      render: (size?: string) => size || '-',
    },
    {
      title: '最后使用',
      dataIndex: 'lastUsed',
      key: 'lastUsed',
      width: 160,
      render: (time?: string) => time ? new Date(time).toLocaleString() : '-',
    },
    {
      title: '可删除',
      dataIndex: 'canDelete',
      key: 'canDelete',
      width: 80,
      render: (canDelete: boolean, record: ResourceItem) => (
        <div>
          {canDelete ? (
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
          ) : (
            <Tooltip title={record.reason}>
              <ExclamationCircleOutlined style={{ color: '#faad14' }} />
            </Tooltip>
          )}
        </div>
      ),
    },
  ];

  // 清理规则表格列定义
  const ruleColumns: ColumnsType<CleanupRule> = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: CleanupRule) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.description}
          </div>
        </div>
      ),
    },
    {
      title: '资源类型',
      dataIndex: 'resourceTypes',
      key: 'resourceTypes',
      width: 200,
      render: (types: ResourceType[]) => (
        <div>
          {types.map(type => (
            <Tag key={type} color={resourceTypeConfig[type].color} size="small">
              {resourceTypeConfig[type].text}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: '条件',
      key: 'conditions',
      width: 200,
      render: (_, record: CleanupRule) => (
        <div style={{ fontSize: '12px' }}>
          {record.conditions.maxAge && (
            <div>年龄 &gt; {record.conditions.maxAge} 天</div>
          )}
          {record.conditions.status && (
            <div>状态: {record.conditions.status.join(', ')}</div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 80,
      render: (enabled: boolean) => (
        <Tag color={enabled ? 'green' : 'default'}>
          {enabled ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '模式',
      dataIndex: 'dryRun',
      key: 'dryRun',
      width: 80,
      render: (dryRun: boolean) => (
        <Tag color={dryRun ? 'blue' : 'orange'}>
          {dryRun ? '预览' : '执行'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (time: string) => new Date(time).toLocaleString(),
    },
  ];

  // 处理资源扫描
  const handleScan = async (values: any) => {
    try {
      await refetchScan();
      setScanModalVisible(false);
      showSuccess('扫描完成', '资源扫描已完成');
    } catch (error) {
      showError('扫描失败', '资源扫描失败');
    }
  };

  // 处理批量清理
  const handleBatchCleanup = () => {
    if (selectedResources.length === 0) {
      showWarning('请选择资源', '请先选择要清理的资源');
      return;
    }
    setCleanupModalVisible(true);
  };

  // 确认清理
  const confirmCleanup = () => {
    cleanupMutation.mutate({
      resourceIds: selectedResources,
      dryRun: false,
    });
  };

  // 处理创建规则
  const handleCreateRule = async (values: any) => {
    const rule = {
      name: values.name,
      description: values.description,
      enabled: values.enabled !== false,
      resourceTypes: values.resourceTypes,
      namespaces: values.namespaces || [],
      conditions: {
        maxAge: values.maxAge,
        status: values.status,
      },
      dryRun: values.dryRun !== false,
    };
    
    createRuleMutation.mutate(rule);
  };

  // 构建统计信息
  const buildStats = () => {
    if (!scanResult?.resources) return null;

    const resources = scanResult.resources;
    const deletableCount = resources.filter((r: ResourceItem) => r.canDelete).length;
    const oldResourcesCount = resources.filter((r: ResourceItem) => r.ageInDays > 30).length;
    
    return {
      total: resources.length,
      deletable: deletableCount,
      oldResources: oldResourcesCount,
      totalSize: scanResult.totalSize || '0 MB',
    };
  };

  const stats = buildStats();

  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f7fa' }}>
      {/* 页面头部 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '24px' 
      }}>
        <div>
          <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
            资源清理
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            扫描和清理集群中的无用资源
          </p>
        </div>
        
        <Space>
          <Button 
            icon={<ScanOutlined />}
            onClick={() => setScanModalVisible(true)}
          >
            开始扫描
          </Button>
          <Button 
            icon={<ReloadOutlined />} 
            loading={scanLoading}
            onClick={() => refetchScan()}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 统计信息 */}
      {stats && (
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总资源数"
                value={stats.total}
                prefix={<ToolOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="可清理资源"
                value={stats.deletable}
                valueStyle={{ color: '#cf1322' }}
                prefix={<DeleteOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="老旧资源"
                value={stats.oldResources}
                valueStyle={{ color: '#faad14' }}
                prefix={<WarningOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总占用空间"
                value={stats.totalSize}
                prefix="💾"
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Tab切换 */}
      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Button 
            type={activeTab === 'scan' ? 'primary' : 'default'}
            onClick={() => setActiveTab('scan')}
          >
            扫描结果
          </Button>
          <Button 
            type={activeTab === 'rules' ? 'primary' : 'default'}
            onClick={() => setActiveTab('rules')}
          >
            清理规则
          </Button>
          <Button 
            type={activeTab === 'tasks' ? 'primary' : 'default'}
            onClick={() => setActiveTab('tasks')}
          >
            清理任务
          </Button>
        </Space>
      </div>

      {/* 扫描结果 */}
      {activeTab === 'scan' && (
        <Card
          title="扫描结果"
          extra={
            selectedResources.length > 0 && (
              <Button 
                type="primary" 
                danger
                icon={<DeleteOutlined />}
                onClick={handleBatchCleanup}
              >
                批量清理 ({selectedResources.length})
              </Button>
            )
          }
        >
          <Table
            columns={resourceColumns}
            dataSource={scanResult?.resources || []}
            loading={scanLoading}
            rowKey="id"
            rowSelection={{
              selectedRowKeys: selectedResources,
              onChange: setSelectedResources,
              getCheckboxProps: (record: ResourceItem) => ({
                disabled: !record.canDelete,
              }),
            }}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
          />
        </Card>
      )}

      {/* 清理规则 */}
      {activeTab === 'rules' && (
        <Card
          title="清理规则"
          extra={
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => setRuleModalVisible(true)}
            >
              创建规则
            </Button>
          }
        >
          <Table
            columns={ruleColumns}
            dataSource={cleanupRules}
            loading={rulesLoading}
            rowKey="id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
          />
        </Card>
      )}

      {/* 扫描模态框 */}
      <Modal
        title="资源扫描"
        open={scanModalVisible}
        onCancel={() => setScanModalVisible(false)}
        onOk={() => scanForm.submit()}
        width={500}
      >
        <Form
          form={scanForm}
          layout="vertical"
          onFinish={handleScan}
        >
          <Form.Item
            name="namespaces"
            label="扫描范围"
          >
            <Select
              mode="multiple"
              placeholder="选择命名空间（留空扫描所有）"
              allowClear
            >
              <Option value="default">default</Option>
              <Option value="kube-system">kube-system</Option>
              <Option value="kube-public">kube-public</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="resourceTypes"
            label="资源类型"
            initialValue={['pod', 'service', 'deployment']}
          >
            <Checkbox.Group>
              <Row>
                <Col span={8}><Checkbox value="pod">Pod</Checkbox></Col>
                <Col span={8}><Checkbox value="service">Service</Checkbox></Col>
                <Col span={8}><Checkbox value="deployment">Deployment</Checkbox></Col>
                <Col span={8}><Checkbox value="configmap">ConfigMap</Checkbox></Col>
                <Col span={8}><Checkbox value="secret">Secret</Checkbox></Col>
                <Col span={8}><Checkbox value="pvc">PVC</Checkbox></Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <Alert
            message="扫描说明"
            description="扫描将分析集群中的资源，识别可能需要清理的无用资源。扫描过程不会对资源进行任何修改。"
            type="info"
            showIcon
          />
        </Form>
      </Modal>

      {/* 清理确认模态框 */}
      <Modal
        title="确认清理"
        open={cleanupModalVisible}
        onCancel={() => setCleanupModalVisible(false)}
        onOk={confirmCleanup}
        confirmLoading={cleanupMutation.isPending}
        okText="确认清理"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <Alert
          message="危险操作警告"
          description={`您即将删除 ${selectedResources.length} 个资源，此操作不可撤销。请确认您已经充分了解此操作的后果。`}
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
        />
        
        <p>将要删除的资源：</p>
        <ul>
          {scanResult?.resources
            ?.filter((r: ResourceItem) => selectedResources.includes(r.id))
            ?.slice(0, 10)
            ?.map((resource: ResourceItem) => (
              <li key={resource.id}>
                {resource.type}/{resource.namespace}/{resource.name}
              </li>
            ))}
          {selectedResources.length > 10 && (
            <li>... 还有 {selectedResources.length - 10} 个资源</li>
          )}
        </ul>
      </Modal>

      {/* 创建规则模态框 */}
      <Modal
        title="创建清理规则"
        open={ruleModalVisible}
        onCancel={() => setRuleModalVisible(false)}
        onOk={() => ruleForm.submit()}
        confirmLoading={createRuleMutation.isPending}
        width={600}
      >
        <Form
          form={ruleForm}
          layout="vertical"
          onFinish={handleCreateRule}
        >
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="请输入规则名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="规则描述"
          >
            <Input.TextArea placeholder="请输入规则描述" rows={3} />
          </Form.Item>

          <Form.Item
            name="resourceTypes"
            label="资源类型"
            rules={[{ required: true, message: '请选择资源类型' }]}
          >
            <Select mode="multiple" placeholder="请选择资源类型">
              <Option value="pod">Pod</Option>
              <Option value="service">Service</Option>
              <Option value="deployment">Deployment</Option>
              <Option value="configmap">ConfigMap</Option>
              <Option value="secret">Secret</Option>
              <Option value="pvc">PVC</Option>
              <Option value="job">Job</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="maxAge"
            label="最大年龄(天)"
          >
            <InputNumber 
              placeholder="资源最大年龄"
              style={{ width: '100%' }}
              min={1}
            />
          </Form.Item>

          <Form.Item
            name="enabled"
            label="启用规则"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="dryRun"
            label="预览模式"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="预览" unCheckedChildren="执行" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ResourceCleanup;
